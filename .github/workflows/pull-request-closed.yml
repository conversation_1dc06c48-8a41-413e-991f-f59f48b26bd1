name: Pull Request (Closed)

on:
  pull_request:
    types:
      - closed

jobs:
  cleanup:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Cleanup actions cache
        run: |
          gh extension install actions/gh-actions-cache
          cacheKeys=$(gh actions-cache list -R ${{ github.repository }} -B refs/pull/${{ github.event.number }}/merge | cut -f 1 )
          set +e
          for cacheKey in $cacheKeys
          do
            gh actions-cache delete $cacheKey -R ${{ github.repository }} -B refs/pull/${{ github.event.number }}/merge --confirm
          done
          echo "Done"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    timeout-minutes: 5
