name: <PERSON><PERSON>

on:
  workflow_dispatch:

jobs:
  baseline:
    name: Baseline - ${{ matrix.target }}
    runs-on: ubuntu-24.04
    steps:
      - name: <PERSON><PERSON>an
        uses: zaproxy/action-baseline@7c4deb10e6261301961c86d65d54a516394f9aed # v0.14.0
        with:
          artifact_name: ${{ matrix.target }}
          issue_title: ZAP Scan Baseline Report - ${{ matrix.target }}
          target: ${{ format('https://{0}', matrix.target) }}
    strategy:
      fail-fast: false
      matrix:
        target:
          [
            'sites.pod-point.com',
            'mobile-api.pod-point.com',
            'installer-api.pod-point.com',
            'identity.pod-point.com',
            'roaming.pod-point.com',
          ]
