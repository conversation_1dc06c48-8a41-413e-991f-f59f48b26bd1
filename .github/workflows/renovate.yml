name: Renovate

on:
  push:
    branches:
      - 'main'
    paths:
      - '.github/workflows/renovate.yml'
      - 'renovate.json'
  schedule:
    - cron: '0 8-16 * * 1-5'
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}

env:
  RENOVATE_ALLOWED_POST_UPGRADE_COMMANDS: '["^echo .* > .nvmrc$", "^go mod tidy$"]'
  RENOVATE_ALLOW_POST_UPGRADE_COMMAND_TEMPLATING: true
  RENOVATE_ALLOW_SCRIPTS: true
  RENOVATE_EXPOSE_ALL_ENV: true
  RENOVATE_REPOSITORIES: ${{ github.repository }}
  RENOVATE_VERSION: 40.26.1 # renovate: datasource=docker depName=renovate packageName=ghcr.io/renovatebot/renovate

jobs:
  renovate:
    name: Renovate
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup Node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'package-lock.json'

      - name: Authenticate
        id: fetch-auth-token
        uses: getsentry/action-github-app-token@d4b5da6c5e37703f8c3b3e43abb5705b46e159cc # v3.0.0
        with:
          app_id: ${{ secrets.RENOVATE_APP_ID }}
          private_key: ${{ secrets.RENOVATE_APP_PRIVATE_KEY }}

      - name: Renovate
        uses: renovatebot/github-action@85b17ebd5abf43d1c34c01bd4c8dbb8d45bbc2c7 # v43.0.7
        with:
          configurationFile: renovate.json
          env-regex: "^(?:RENOVATE_\\w+|LOG_LEVEL|GITHUB_COM_TOKEN|NODE_OPTIONS|AWS_TOKEN)$"
          renovate-version: ${{ env.RENOVATE_VERSION }}
          token: 'x-access-token:${{ steps.fetch-auth-token.outputs.token }}'
        env:
          LOG_LEVEL: 'debug'
    timeout-minutes: 30
