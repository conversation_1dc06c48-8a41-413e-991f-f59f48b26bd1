name: Analyse

on:
  push:
    branches:
      - main
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  config-cat:
    name: ConfigCat
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: <PERSON><PERSON>
        uses: configcat/scan-repository@75abf0b1f25bc71fd1e9d35476e9c8d961557d5c # v2.7.1
        with:
          api-user: ${{ secrets.CONFIGCAT_API_USER }}
          api-pass: ${{ secrets.CONFIGCAT_API_PASS }}
          config-id: 08dcb7a4-2585-4f97-8b92-dd8b0138477c
    timeout-minutes: 5

  crowd-in:
    name: CrowdIn
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Upload
        uses: crowdin/github-action@590c05e09a29f392b203faf4d6aa8e0cd32c7835 # v2.9.1
        with:
          config: crowdin.yml
          upload_sources: true
          upload_translations: false
          download_translations: false
        env:
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
    timeout-minutes: 5
