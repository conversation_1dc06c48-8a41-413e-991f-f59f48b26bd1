name: Pull Request (Format Check)

on:
  merge_group:
  pull_request:

permissions:
  actions: read
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  main:
    if: ${{ !contains(github.event.pull_request.title, '[no ci]') }}
    name: Main (Format Check)
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
      - name: Run
        run: npx nx format:check
    timeout-minutes: 10

  verify:
    if: always()
    name: Verify (Format Check)
    needs: [main]
    runs-on: ubuntu-24.04
    steps:
      - name: Success
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: exit 0
      - name: Failure
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1
    timeout-minutes: 5
