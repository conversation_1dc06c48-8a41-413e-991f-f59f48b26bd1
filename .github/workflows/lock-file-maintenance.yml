name: Lock File Maintenance

on:
  workflow_dispatch:

jobs:
  maintain:
    name: Maintain
    runs-on: ubuntu-24.04
    steps:
      - name: Authenticate
        id: fetch-auth-token
        uses: getsentry/action-github-app-token@d4b5da6c5e37703f8c3b3e43abb5705b46e159cc # v3.0.0
        with:
          app_id: ${{ secrets.RELEASE_APP_ID }}
          private_key: ${{ secrets.RELEASE_APP_PRIVATE_KEY }}

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          token: ${{ steps.fetch-auth-token.outputs.token }}

      - name: Setup
        uses: ./.github/actions/setup

      - name: Maintain
        id: maintain
        run: |
          rm -rf node_modules
          rm package-lock.json
          npm install --package-lock-only --no-audit --ignore-scripts

      - name: Verify
        uses: tj-actions/verify-changed-files@a1c6acee9df209257a246f2cc6ae8cb6581c1edf # v20.0.4
        id: verify
        with:
          files: package-lock.json

      - name: Submit
        if: steps.verify.outputs.files_changed == 'true'
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          body: '🔧 This pull request updates lock files to use the latest dependency versions.'
          branch: 'lock-file-maintenance'
          commit-message: 'chore(deps): lock file maintenance'
          title: 'chore(deps): lock file maintenance'
          token: ${{ steps.fetch-auth-token.outputs.token }}
