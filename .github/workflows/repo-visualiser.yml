name: Repository Visualiser

on:
  workflow_dispatch:

jobs:
  maintain:
    name: Visualise
    runs-on: ubuntu-24.04
    steps:
      - name: Authenticate
        id: fetch-auth-token
        uses: getsentry/action-github-app-token@d4b5da6c5e37703f8c3b3e43abb5705b46e159cc # v3.0.0
        with:
          app_id: ${{ secrets.RELEASE_APP_ID }}
          private_key: ${{ secrets.RELEASE_APP_PRIVATE_KEY }}

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          token: ${{ steps.fetch-auth-token.outputs.token }}

      - name: Visualise
        uses: githubocto/repo-visualizer@a999615bdab757559bf94bda1fe6eef232765f85 # v0.9.1
        with:
          excluded_paths: 'dist,node_modules'
          output_file: 'docs/domain/monorepo.svg'
          should_push: false

      - name: Submit
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          body: '🔧 This pull request updates a diagram visualising the monorepo.'
          branch: 'repo-visualizer'
          commit-message: 'chore(docs): regenerate repository visualisation'
          title: 'chore(docs): regenerate repository visualisation'
          token: ${{ steps.fetch-auth-token.outputs.token }}
