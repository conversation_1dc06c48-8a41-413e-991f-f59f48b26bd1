name: Release
run-name: Release - ${{ github.run_number }}

# GitHub actions schedule is in UTC time.
# Schedule twice but only publish release when UK time is 0830 (see prepare step).
# Schedule at the bottom of the hour to ensure one run will be between 0800 and 0859 UK time.
on:
  schedule:
    - cron: '30 7,8 * * 1-5'
  workflow_dispatch:

permissions:
  contents: write
  id-token: write

concurrency:
  group: ${{ github.workflow }}

jobs:
  prepare:
    name: Prepare
    outputs:
      release: ${{ steps.prepare.outputs.release }}
    runs-on: ubuntu-24.04
    steps:
      - name: Validate
        if: github.ref_name != 'main'
        run: |
          echo "This workflow should not be triggered with workflow_dispatch on a branch other than main."
          exit 1
      - name: Prepare
        id: prepare
        env:
          EVENT_NAME: ${{ github.event_name }}
          TZ: Europe/London
        run: |
          if [[ ${EVENT_NAME} == 'schedule' ]]; then
            if [ $(date +%H) -eq '08' ]; then
              echo "release=true" >> "$GITHUB_OUTPUT"
            else
              echo "release=false" >> "$GITHUB_OUTPUT"
            fi
          else
            echo "release=true" >> "$GITHUB_OUTPUT"
          fi

  release:
    if: needs.prepare.outputs.release == 'true'
    name: Release
    needs: [prepare]
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Authenticate
        id: fetch-auth-token
        uses: getsentry/action-github-app-token@d4b5da6c5e37703f8c3b3e43abb5705b46e159cc # v3.0.0
        with:
          app_id: ${{ secrets.RELEASE_APP_ID }}
          private_key: ${{ secrets.RELEASE_APP_PRIVATE_KEY }}
      - name: Release
        run: gh release create $(date +"%Y.%m.%d")-${RUN_NUMBER} --generate-notes
        env:
          GITHUB_TOKEN: ${{ steps.fetch-auth-token.outputs.token }}
          RUN_NUMBER: ${{ github.run_number }}
    timeout-minutes: 5
