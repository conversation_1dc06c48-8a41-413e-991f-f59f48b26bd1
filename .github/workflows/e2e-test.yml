name: E2E Test
run-name: E2E Test ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        type: environment

permissions:
  contents: read
  id-token: write
  actions: read

concurrency:
  group: End-To-End Tests
  cancel-in-progress: false

jobs:
  e2e-test:
    environment: ${{ inputs.environment }}
    name: 💨 E2E Test
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: E2E Test
        uses: ./.github/actions/e2e-test
        with:
          account-id: ${{ vars.AWS_ACCOUNT_ID }}
          slack-notification-webhook: ${{ secrets.SLACK_WORKFLOW_E2E_NOTIFICATIONS }}
    timeout-minutes: 20
