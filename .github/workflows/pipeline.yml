name: Pipeline

on:
  push:
    branches:
      - main
    paths-ignore:
      - '**/.env.local.sample'
      - '**/.gitignore'
      - '**/README.md'
      - '**/backstage.yaml'
      - '.editorconfig'
      - '.github/CODEOWNERS'
      - '.github/pull_request_template.md'
      - '.github/workflows/analyse.yml'
      - '.github/workflows/api-client-generation.yml'
      - '.github/workflows/deploy.yml'
      - '.github/workflows/e2e-test.yml'
      - '.github/workflows/lock-file-maintenance.yml'
      - '.github/workflows/nx-migrate.yml'
      - '.github/workflows/nx-run.yml'
      - '.github/workflows/package-and-deploy.yml'
      - '.github/workflows/package.yml'
      - '.github/workflows/pull-request-closed.yml'
      - '.github/workflows/pull-request-commit-lint.yml'
      - '.github/workflows/pull-request-format-check.yml'
      - '.github/workflows/pull-request-package.yml'
      - '.github/workflows/pull-request-security-checks.yml'
      - '.github/workflows/pull-request.yml'
      - '.github/workflows/release.yml'
      - '.github/workflows/renovate.yml'
      - '.github/workflows/translate.yml'
      - '.github/workflows/upload-vehicles.yml'
      - '.github/workflows/zap.yml'
      - '.gitignore'
      - '.husky'
      - '.run/**'
      - 'README.md'
      - 'catalog-info.yaml'
      - 'commitlint.config.js'
      - 'crowdin.update.sh'
      - 'docs/**'
      - 'init/**'
      - 'renovate.json'
  release:
    types: [published]

permissions:
  actions: write
  contents: read
  id-token: write

concurrency:
  group: ${{ github.workflow }}-${{ github.event_name }}
  cancel-in-progress: false

env:
  CYPRESS_VERIFY_TIMEOUT: 60000

jobs:
  prepare:
    name: 🧭 Prepare
    outputs:
      affected_apps_with_e2e_target: ${{ steps.prepare.outputs.affected_apps_with_e2e_target }}
      affected_apps_with_package_tag: ${{ steps.prepare.outputs.affected_apps_with_package_tag }}
      affected_projects_with_commercial_tag: ${{ steps.prepare.outputs.affected_projects_with_commercial_tag }}
      affected_projects_with_data_platform_tag: ${{ steps.prepare.outputs.affected_projects_with_data_platform_tag }}
      affected_projects_with_mobile_tag: ${{ steps.prepare.outputs.affected_projects_with_mobile_tag }}
      affected_projects_with_shared_tag: ${{ steps.prepare.outputs.affected_projects_with_shared_tag }}
      out_of_hours: ${{ steps.prepare.outputs.out_of_hours }}
      version: ${{ steps.prepare.outputs.version }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
      - name: Prepare
        id: prepare
        uses: ./.github/actions/prepare
    timeout-minutes: 10

  main-commercial:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_commercial_tag)[0] != null }}
    name: 🧮 Main (Commercial)
    needs: [prepare]
    runs-on: ${{ matrix.runs-on }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: commercial-${{ matrix.targets }}
      - name: Run
        run: npx nx affected --exclude '!tag:commercial,tag:package' --targets ${{ matrix.targets }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-commercial-${{ matrix.targets }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        include:
          - targets: test --runInBand --parallel=4
            runs-on: ubuntu-22.04-4xCPU
          - targets: build --parallel=4
            runs-on: ubuntu-22.04-4xCPU
    timeout-minutes: 30

  main-data-platform:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_data_platform_tag)[0] != null }}
    name: 🧮 Main (Data Platform)
    needs: [prepare]
    runs-on: ${{ matrix.runs-on }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: data-platform-${{ matrix.targets }}
      - name: Run
        run: npx nx affected --exclude '!tag:data-platform,tag:package' --targets ${{ matrix.targets }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-data-platform-${{ matrix.targets }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        include:
          - targets: test --parallel=4
            runs-on: ubuntu-22.04-4xCPU
          - targets: build
            runs-on: ubuntu-22.04
    timeout-minutes: 30

  main-mobile:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_mobile_tag)[0] != null }}
    name: 🧮 Main (Mobile)
    needs: [prepare]
    runs-on: ${{ matrix.runs-on }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: mobile-${{ matrix.targets }}
      - name: Run
        run: npx nx affected --exclude '!tag:mobile,tag:package' --targets ${{ matrix.targets }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-mobile-${{ matrix.targets }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        include:
          - targets: test --runInBand
            runs-on: ubuntu-22.04
          - targets: build
            runs-on: ubuntu-22.04
    timeout-minutes: 30

  main-shared:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_shared_tag)[0] != null }}
    name: 🧮 Main (Shared)
    needs: [prepare]
    runs-on: ${{ matrix.runs-on }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: shared-${{ matrix.targets }}
      - name: Run
        run: npx nx affected --exclude '!tag:shared,!tag:support,tag:package' --targets ${{ matrix.targets }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-shared-${{ matrix.targets }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        include:
          - targets: test --runInBand --parallel=4
            runs-on: ubuntu-22.04-4xCPU
          - targets: build build-storybook
            runs-on: ubuntu-22.04
    timeout-minutes: 30

  e2e:
    if: ${{ fromJSON(needs.prepare.outputs.affected_apps_with_e2e_target)[0] != null }}
    name: 🖥️ E2E
    needs: [prepare]
    runs-on: ${{ (contains(matrix.affected, 'data-platform') || contains(matrix.affected, 'design-system') || contains(matrix.affected, 'webapp')) && 'ubuntu-22.04-4xCPU' || 'ubuntu-22.04' }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: e2e-${{ matrix.affected }}
      - name: Login ECR
        uses: ./.github/actions/ecr-login
        with:
          build-account-id: ${{ vars.AWS_BUILD_ACCOUNT_ID }}
          iam-role: github-ecr-data-platform-api
      - name: E2E
        run: npx nx e2e ${{ matrix.affected }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-${{ matrix.affected }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        affected: ${{ fromJSON(needs.prepare.outputs.affected_apps_with_e2e_target) }}
    timeout-minutes: 20

  package:
    if: ${{ fromJSON(needs.prepare.outputs.affected_apps_with_package_tag)[0] != null }}
    name: 🏗️ Package
    needs: [prepare]
    outputs:
      packaged: ${{ needs.prepare.outputs.affected_apps_with_package_tag }}
      version: ${{ needs.prepare.outputs.version }}
    runs-on: ubuntu-24.04
    strategy:
      fail-fast: false
      matrix:
        affected: ${{ fromJSON(needs.prepare.outputs.affected_apps_with_package_tag) }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: package-${{ matrix.affected }}
      - name: Package
        uses: ./.github/actions/package
        with:
          sentry_auth_token: ${{ secrets.SENTRY_AUTH_TOKEN }}
          service: ${{ matrix.affected }}
          version: ${{ needs.prepare.outputs.version }}
    timeout-minutes: 20

  deploy-dev:
    if: ${{ !cancelled() && !(contains(needs.*.result, 'failure')) && needs.package.result != 'skipped' && github.event_name != 'release' }}
    environment: Development
    name: 🚀 Deploy (Development)
    needs:
      [
        main-commercial,
        main-data-platform,
        main-mobile,
        main-shared,
        e2e,
        package,
      ]
    outputs:
      deployed: ${{ needs.package.outputs.packaged }}
      version: ${{ needs.package.outputs.version }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Sleep
        run: sleep $(( ${{ strategy.job-index }} * 1 ))s
      - name: Deploy
        uses: ./.github/actions/deploy
        with:
          environment: ${{ vars.ENVIRONMENT_NAME }}
          service: ${{ matrix.packaged }}
          version: ${{ needs.package.outputs.version }}
          wait-for-service-stability: false
    strategy:
      fail-fast: false
      matrix:
        packaged: ${{ fromJSON(needs.package.outputs.packaged) }}
    timeout-minutes: 5

  deploy-stage:
    if: ${{ !cancelled() && !(contains(needs.*.result, 'failure')) && github.event_name == 'release' && github.event.action == 'published' }}
    environment: Staging
    name: 🚀 Deploy (Staging)
    needs:
      [
        main-commercial,
        main-data-platform,
        main-mobile,
        main-shared,
        e2e,
        package,
      ]
    outputs:
      deployed: ${{ needs.package.outputs.packaged }}
      version: ${{ needs.package.outputs.version }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Sleep
        run: sleep $(( ${{ strategy.job-index }} * 1 ))s
      - name: Deploy
        uses: ./.github/actions/deploy
        with:
          environment: ${{ vars.ENVIRONMENT_NAME }}
          service: ${{ matrix.packaged }}
          version: ${{ needs.package.outputs.version }}
          wait-for-service-stability: true
    strategy:
      fail-fast: false
      matrix:
        packaged: ${{ fromJSON(needs.package.outputs.packaged) }}
    timeout-minutes: 15

  smoke-stage:
    if: ${{ !cancelled() && !(contains(needs.*.result, 'failure')) && github.event_name == 'release' && github.event.action == 'published' }}
    environment: Staging (Smoke Test)
    name: 💨 Smoke Test (Staging)
    needs: [deploy-stage]
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Smoke Test
        uses: ./.github/actions/post-deploy
        with:
          account-id: ${{ vars.AWS_ACCOUNT_ID }}
    timeout-minutes: 20

  test-stage:
    if: ${{ !cancelled() && !(contains(needs.*.result, 'failure')) && github.event_name == 'release' && github.event.action == 'published' }}
    strategy:
      matrix:
        include:
          - test-name: '🌆 End-To-End Tests'
            workflow-file: 'e2e-test.yml'
          - test-name: '🏎️ Performance Tests'
            workflow-file: 'performance-test.yml'
    name: ${{ matrix.test-name }} (Staging)
    needs: [smoke-stage]
    runs-on: ubuntu-24.04

    steps:
      - name: Trigger the ${{ matrix.test-name }} pipeline on Staging (fire and forget)
        run: |
          curl -i -X POST \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            https://api.github.com/repos/Pod-Point/experience/actions/workflows/${{ matrix.workflow-file }}/dispatches \
            -d '{
                  "ref": "${{ github.ref }}",
                  "inputs": {
                    "environment": "Staging"
                  }
                }'

  deploy-prod:
    if: ${{ !cancelled() && !(contains(needs.*.result, 'failure')) && github.event_name == 'release' && github.event.action == 'published' }}
    environment: Production
    name: 🚀 Deploy (Production)
    needs: [deploy-stage, smoke-stage]
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Sleep
        run: sleep $(( ${{ strategy.job-index }} * 1 ))s
      - name: Deploy
        uses: ./.github/actions/deploy
        with:
          environment: ${{ vars.ENVIRONMENT_NAME }}
          service: ${{ matrix.deployed }}
          version: ${{ needs.deploy-stage.outputs.version }}
          wait-for-service-stability: true
    strategy:
      fail-fast: false
      matrix:
        deployed: ${{ fromJSON(needs.deploy-stage.outputs.deployed) }}
    timeout-minutes: 15

  post-deploy-prod:
    if: ${{ !cancelled() && !(contains(needs.*.result, 'failure')) && github.event_name == 'release' && github.event.action == 'published' }}
    environment: Production (Smoke Test)
    name: 💨 Smoke Test (Production)
    needs: [deploy-prod]
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Smoke Test
        uses: ./.github/actions/post-deploy
        with:
          account-id: ${{ vars.AWS_ACCOUNT_ID }}
    timeout-minutes: 20

  verify:
    if: always()
    name: Verify
    needs:
      [
        prepare,
        main-commercial,
        main-data-platform,
        main-mobile,
        main-shared,
        e2e,
        package,
        deploy-dev,
        deploy-stage,
        smoke-stage,
        deploy-prod,
        post-deploy-prod,
      ]
    runs-on: ubuntu-24.04
    steps:
      - name: Failure
        if: ${{ contains(needs.*.result, 'failure') }}
        uses: slackapi/slack-github-action@91efab103c0de0a537f72a35f6b8cda0ee76bf0a # v2.1.1
        with:
          payload: |
            {
              "github_actor": "${{ github.actor }}",
              "github_event_name": "${{ github.event_name }}",
              "github_run_attempt": "${{ github.run_attempt }}",
              "github_run_id": "${{ github.run_id }}",
              "github_triggering_actor": "${{ github.triggering_actor }}"
            }
          webhook-type: webhook-trigger
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    timeout-minutes: 5
