name: Performance Test
run-name: Performance Test ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        type: choice
        options:
          - Development
          - Staging

permissions:
  contents: read
  id-token: write

concurrency:
  group: End-To-End Tests
  cancel-in-progress: false

jobs:
  performance-test:
    environment: ${{ inputs.environment }}
    name: 🏎️ Performance Test
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Performance Test
        uses: ./.github/actions/performance-test
        with:
          account-id: ${{ vars.AWS_ACCOUNT_ID }}
    timeout-minutes: 20
