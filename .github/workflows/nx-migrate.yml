name: Nx Migrate

on:
  schedule:
    - cron: '59 11 * * 1-5'
  workflow_dispatch:

jobs:
  migrate:
    name: Migrate
    runs-on: ubuntu-24.04
    steps:
      - name: Authenticate
        id: fetch-auth-token
        uses: getsentry/action-github-app-token@d4b5da6c5e37703f8c3b3e43abb5705b46e159cc # v3.0.0
        with:
          app_id: ${{ secrets.RELEASE_APP_ID }}
          private_key: ${{ secrets.RELEASE_APP_PRIVATE_KEY }}

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          token: ${{ steps.fetch-auth-token.outputs.token }}

      - name: Setup
        uses: ./.github/actions/setup

      - name: Migrate
        id: migrate
        run: |
          npx nx migrate latest
          npx nx migrate --run-migrations --if-exists
          echo "version=$(npm view nx version)" >> $GITHUB_OUTPUT
          npm install
          npx nx format:write --uncommitted

      - name: Verify
        uses: tj-actions/verify-changed-files@a1c6acee9df209257a246f2cc6ae8cb6581c1edf # v20.0.4
        id: verify
        with:
          files: package.json

      - name: Submit
        if: steps.verify.outputs.files_changed == 'true'
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          body: 'https://github.com/nrwl/nx/releases/tag/${{ steps.migrate.outputs.version }}'
          branch: 'migrate-to-nx-v${{ steps.migrate.outputs.version }}'
          commit-message: 'fix(deps): migrate to nx v${{ steps.migrate.outputs.version }}'
          title: 'fix(deps): migrate to nx v${{ steps.migrate.outputs.version }}'
          token: ${{ steps.fetch-auth-token.outputs.token }}
