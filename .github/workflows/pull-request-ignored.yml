name: Pull Request (Ignored)

# Keep paths in sync with paths-ignore on pull-request.yml to ensure required status check runs.
# https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/managing-protected-branches/troubleshooting-required-status-checks#handling-skipped-but-required-checks
on:
  merge_group:
    paths:
      - '**/.env.local.sample'
      - '**/.gitignore'
      - '**/README.md'
      - '**/backstage.yaml'
      - '.aws/**'
      - '.codebuild'
      - '.editorconfig'
      - '.github/CODEOWNERS'
      - '.github/actions/deploy/*'
      - '.github/actions/e2e-test/*'
      - '.github/actions/package/*'
      - '.github/actions/post-deploy/*'
      - '.github/actions/cdn-upload/*'
      - '.github/pull_request_template.md'
      - '.github/workflows/analyse.yml'
      - '.github/workflows/api-client-generation.yml'
      - '.github/workflows/deploy.yml'
      - '.github/workflows/e2e-test.yml'
      - '.github/workflows/lock-file-maintenance.yml'
      - '.github/workflows/nx-migrate.yml'
      - '.github/workflows/nx-run.yml'
      - '.github/workflows/package-and-deploy.yml'
      - '.github/workflows/package.yml'
      - '.github/workflows/pipeline.yml'
      - '.github/workflows/pull-request-closed.yml'
      - '.github/workflows/pull-request-commit-lint.yml'
      - '.github/workflows/pull-request-format-check.yml'
      - '.github/workflows/pull-request-package.yml'
      - '.github/workflows/pull-request-security-checks.yml'
      - '.github/workflows/release.yml'
      - '.github/workflows/renovate.yml'
      - '.github/workflows/repo-visualiser.yml'
      - '.github/workflows/translate.yml'
      - '.github/workflows/upload-vehicles.yml'
      - '.github/workflows/zap.yml'
      - '.gitignore'
      - '.husky'
      - '.run/**'
      - 'README.md'
      - 'catalog-info.yaml'
      - 'commitlint.config.js'
      - 'crowdin.update.sh'
      - 'docs/**'
      - 'init/**'
      - 'renovate.json'
  pull_request:
    paths:
      - '**/.env.local.sample'
      - '**/.gitignore'
      - '**/README.md'
      - '**/backstage.yaml'
      - '.aws/**'
      - '.codebuild'
      - '.editorconfig'
      - '.github/CODEOWNERS'
      - '.github/actions/deploy/*'
      - '.github/actions/e2e-test/*'
      - '.github/actions/package/*'
      - '.github/actions/post-deploy/*'
      - '.github/actions/cdn-upload/*'
      - '.github/pull_request_template.md'
      - '.github/workflows/analyse.yml'
      - '.github/workflows/api-client-generation.yml'
      - '.github/workflows/deploy.yml'
      - '.github/workflows/e2e-test.yml'
      - '.github/workflows/lock-file-maintenance.yml'
      - '.github/workflows/nx-migrate.yml'
      - '.github/workflows/nx-run.yml'
      - '.github/workflows/package-and-deploy.yml'
      - '.github/workflows/package.yml'
      - '.github/workflows/pipeline.yml'
      - '.github/workflows/pull-request-closed.yml'
      - '.github/workflows/pull-request-commit-lint.yml'
      - '.github/workflows/pull-request-format-check.yml'
      - '.github/workflows/pull-request-package.yml'
      - '.github/workflows/pull-request-security-checks.yml'
      - '.github/workflows/release.yml'
      - '.github/workflows/renovate.yml'
      - '.github/workflows/repo-visualiser.yml'
      - '.github/workflows/translate.yml'
      - '.github/workflows/upload-vehicles.yml'
      - '.github/workflows/zap.yml'
      - '.gitignore'
      - '.husky'
      - '.run/**'
      - 'README.md'
      - 'catalog-info.yaml'
      - 'commitlint.config.js'
      - 'crowdin.update.sh'
      - 'docs/**'
      - 'renovate.json'

jobs:
  verify:
    name: Verify
    runs-on: ubuntu-24.04
    steps:
      - name: Success
        run: exit 0
    timeout-minutes: 5
