name: 'End to End Tests'
description: 'Run e2e tests against deployed applications.'

inputs:
  account-id:
    description: 'ID of the account where e2e tests will run.'
    required: true
  slack-notification-webhook:
    description: 'Webhook for the slack workflow to send notifications.'
    required: true

runs:
  using: 'composite'

  steps:
    - name: Assume role needed to run codebuild
      uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
      with:
        aws-region: eu-west-1
        role-to-assume: arn:aws:iam::${{ inputs.account-id }}:role/github-codebuild-destination-e2e-test
        role-duration-seconds: 1200
        role-skip-session-tagging: true

    - name: Run e2e stage on codebuild
      uses: aws-actions/aws-codebuild-run-build@4d15a47425739ac2296ba5e7eee3bdd4bfbdd767 # v1.0.18
      with:
        project-name: destination-e2e-test

    - name: Get link to current running job
      if: failure()
      id: get-link-to-job
      env:
        GH_TOKEN: ${{ github.token }}
      shell: bash
      run: echo "job_link=$(gh run --repo ${{ github.repository }} view ${{ github.run_id }} --json jobs --jq '.jobs[0] | .url')" >> $GITHUB_OUTPUT

      # Managed by slack workflow https://slack.com/shortcuts/Ft07NHD4N1FF/a4e6bffa3d614ebaec0a5caeef6c106e
      # Alternative access: Open slack --> More --> Automations --> Workflows --> domain-experience-e2e-notifications
    - name: Notify slack using slack workflow
      if: failure()
      uses: slackapi/slack-github-action@91efab103c0de0a537f72a35f6b8cda0ee76bf0a # v2.1.1
      with:
        payload: |
          {
            "ref": "${{ github.ref_name }}",
            "link": "${{ steps.get-link-to-job.outputs.job_link }}"
          }
        webhook-type: webhook-trigger
      env:
        SLACK_WEBHOOK_URL: ${{ inputs.slack-notification-webhook }}
