name: 'Package'
description: 'Package application into a docker image.'

inputs:
  sentry_auth_token:
    description: 'Sentry auth token for uploading webapp releases to sentry on package'
    required: true
  service:
    description: 'Name of service to package.'
    required: true
  version:
    description: 'Version of service to package.'
    required: true

runs:
  using: 'composite'

  steps:
    - name: Read properties from build properties file
      id: read-build-properties
      run: |
        echo "account_id=$(cat .aws/build/${{ inputs.service }}/build.properties | grep "account.id" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "docker_file=$(cat .aws/build/${{ inputs.service }}/build.properties | grep "docker.file" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
      shell: bash

    - name: Build production version of app
      env:
        SENTRY_AUTH_TOKEN: ${{ inputs.sentry_auth_token }}
        SENTRY_RELEASE: ${{ format('{0}_{1}', inputs.service, inputs.version) }}
      run: |
        npx nx run-many -t lint test -p ${{ inputs.service }}
        npx nx build ${{ inputs.service }} -c production
      shell: bash

    - name: Build, tag, and push image to Amazon ECR
      uses: Pod-Point-Platform/build-publish-ecr@v3.1.0
      with:
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.account_id }}:role/github-ecr-${{ inputs.service }}
        aws_region: eu-west-1
        build-args: |
          APPLICATION_VERSION=${{ inputs.version }}
          NODE_AUTH_TOKEN=${{ github.token }}
          SENTRY_AUTH_TOKEN=${{ inputs.sentry_auth_token }}
          SENTRY_RELEASE=${{ format('{0}_{1}', inputs.service, inputs.version) }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        context_path: '.'
        dockerfile_path: ${{ steps.read-build-properties.outputs.docker_file }}
        ecr_repository_name: ${{ inputs.service }}
        provenance: false
