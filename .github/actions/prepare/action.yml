name: 'Prepare'
description: 'Prepare for pipeline execution.'

outputs:
  affected_apps_with_e2e_target:
    description: List of services affected by the current change which have a e2e target.
    value: ${{ steps.print-affected.outputs.affected_apps_with_e2e_target }}
  affected_apps_with_package_tag:
    description: List of services affected by the current change which have a package target.
    value: ${{ steps.print-affected.outputs.affected_apps_with_package_tag }}
  affected_projects_with_commercial_tag:
    description: List of services affected by the current change which have a commercial tag.
    value: ${{ steps.print-affected.outputs.affected_projects_with_commercial_tag }}
  affected_projects_with_data_platform_tag:
    description: List of services affected by the current change which have a data_platform tag.
    value: ${{ steps.print-affected.outputs.affected_projects_with_data_platform_tag }}
  affected_projects_with_mobile_tag:
    description: List of services affected by the current change which have a mobile tag.
    value: ${{ steps.print-affected.outputs.affected_projects_with_mobile_tag }}
  affected_projects_with_shared_tag:
    description: List of services affected by the current change which have a shared tag.
    value: ${{ steps.print-affected.outputs.affected_projects_with_shared_tag }}
  out_of_hours:
    description: Flag indicating if the current workflow is being run out of hours.
    value: ${{ steps.print-out-of-hours.outputs.out_of_hours }}
  version:
    description: Version for the current workflow.
    value: ${{ steps.print-version.outputs.version }}

runs:
  using: 'composite'

  steps:
    - name: Print affected apps with a package target
      id: print-affected
      run: |
        echo "affected_apps_with_e2e_target=$(npx nx show projects --affected --with-target e2e --json | jq -r tostring)" >> $GITHUB_OUTPUT
        echo "affected_apps_with_package_tag=$(npx nx show projects --affected --exclude '!tag:package' --json | jq -r tostring)" >> $GITHUB_OUTPUT
        echo "affected_projects_with_commercial_tag=$(npx nx show projects --affected --exclude '!tag:commercial' --json | jq -r tostring)" >> $GITHUB_OUTPUT
        echo "affected_projects_with_data_platform_tag=$(npx nx show projects --affected --exclude '!tag:data-platform' --json | jq -r tostring)" >> $GITHUB_OUTPUT
        echo "affected_projects_with_mobile_tag=$(npx nx show projects --affected --exclude '!tag:mobile' --json | jq -r tostring)" >> $GITHUB_OUTPUT
        echo "affected_projects_with_shared_tag=$(npx nx show projects --affected --exclude '!tag:shared,!tag:support' --json | jq -r tostring)" >> $GITHUB_OUTPUT
      shell: bash

    - name: Print version
      id: print-version
      run: |
        echo "version=${{ github.sha }}" >> $GITHUB_OUTPUT
      shell: bash

    - name: Print out of hours
      id: print-out-of-hours
      env:
        TZ: Europe/London
      run: |
        if [[ $(date +%A) = "Saturday" ]]; then
          echo "out_of_hours=true" >> $GITHUB_OUTPUT
        elif [[ $(date +%A) = "Sunday" ]]; then
          echo "out_of_hours=true" >> $GITHUB_OUTPUT
        elif [[ $(date +%H:%M) < "08:00" ]]; then
          echo "out_of_hours=true" >> $GITHUB_OUTPUT
        elif [[ $(date +%H:%M) > "18:00" ]]; then
          echo "out_of_hours=true" >> $GITHUB_OUTPUT
        else
          echo "out_of_hours=false" >> $GITHUB_OUTPUT
        fi
      shell: bash
