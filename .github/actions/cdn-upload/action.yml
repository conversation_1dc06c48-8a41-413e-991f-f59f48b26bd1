name: 'CDN upload'
description: 'Upload file to CDN and invalidate cache.'

inputs:
  origin:
    description: 'Origin path for file or directory to be uploaded to the CDN.'
    required: true
  destination:
    description: 'Destination path for file or directory to be uploaded to the CDN.'
    required: true

runs:
  using: 'composite'

  steps:
    - name: Assume role for CDN usage
      uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
      with:
        aws-region: eu-west-1
        role-to-assume: arn:aws:iam::959744386191:role/github-actions-pod-point-cdn
        role-duration-seconds: 1200
        role-skip-session-tagging: true

    - name: Upload to CDN
      run: |
        aws s3 cp ${{ inputs.origin }} s3://cdn.pod-point.com/${{ inputs.destination }}
      shell: bash

    - name: Invalidate CloudFront for CDN
      run: |
        aws cloudfront create-invalidation --distribution-id E33DUSE8HPNWZO --paths "/${{ inputs.destination }}"
      shell: bash
