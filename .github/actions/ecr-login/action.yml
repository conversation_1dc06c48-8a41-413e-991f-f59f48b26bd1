name: 'ECR Login'
description: 'Login to aws and ecr using github IAM role'

inputs:
  build-account-id:
    description: 'Build account ID.'
    required: true
  iam-role:
    description: 'Build account IAM role name to assume.'
    required: true
outputs:
  registry:
    description: 'The URI of the ECR Private or ECR Public registry.'
    value: ${{ steps.login-amazon-ecr.outputs.registry }}

runs:
  using: 'composite'

  steps:
    - name: Assume role for build environment
      uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
      with:
        aws-region: eu-west-1
        role-to-assume: arn:aws:iam::${{ inputs.build-account-id }}:role/${{ inputs.iam-role }}
        role-duration-seconds: 900
        role-skip-session-tagging: true

    - name: Login to Amazon ECR
      id: login-amazon-ecr
      uses: aws-actions/amazon-ecr-login@062b18b96a7aff071d4dc91bc00c4c1a7945b076 # v2.0.1
      with:
        mask-password: 'true'
