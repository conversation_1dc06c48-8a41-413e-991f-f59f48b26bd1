name: 'Post Deploy Smoke Test'
description: 'Post deploy smoke test of applications.'

inputs:
  account-id:
    description: 'ID of the account into which to perform the smoke test.'
    required: true

runs:
  using: 'composite'

  steps:
    - name: Assume role needed to run codebuild
      uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
      with:
        aws-region: eu-west-1
        role-to-assume: arn:aws:iam::${{ inputs.account-id }}:role/github-codebuild-destination-post-deploy
        role-duration-seconds: 1200
        role-skip-session-tagging: true

    - name: Run post-deploy stage on codebuild
      uses: aws-actions/aws-codebuild-run-build@4d15a47425739ac2296ba5e7eee3bdd4bfbdd767 # v1.0.18
      with:
        project-name: destination-post-deploy
