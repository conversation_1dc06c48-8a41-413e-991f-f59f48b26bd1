{"extends": ["plugin:react/recommended", "plugin:react/jsx-runtime", "plugin:storybook/recommended"], "root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx", "eslint-plugin-react", "no-only-tests", "prefer-arrow-functions", "sort-imports-es6-autofix", "sort-exports", "unused-imports", "unicorn"], "overrides": [{"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {"@typescript-eslint/no-extra-semi": "error", "no-extra-semi": "off"}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {"@typescript-eslint/no-extra-semi": "error", "no-extra-semi": "off"}}, {"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "commercial", "onlyDependOnLibsWithTags": ["commercial", "data-platform", "shared"]}, {"sourceTag": "data-platform", "onlyDependOnLibsWithTags": ["data-platform", "shared"]}, {"sourceTag": "mobile", "onlyDependOnLibsWithTags": ["mobile", "data-platform", "shared"]}, {"sourceTag": "shared", "onlyDependOnLibsWithTags": ["shared"]}, {"sourceTag": "support", "onlyDependOnLibsWithTags": ["commercial", "mobile", "shared", "support"]}]}], "arrow-body-style": ["error", "as-needed"], "no-console": ["error", {}], "no-only-tests/no-only-tests": "error", "prefer-arrow-functions/prefer-arrow-functions": ["error", {"classPropertiesAllowed": false, "disallowPrototype": false, "returnStyle": "unchanged", "singleReturnOnly": false}], "prefer-destructuring": ["error", {"object": true, "array": true}], "react/jsx-no-leaked-render": "error", "sort-imports-es6-autofix/sort-imports-es6": ["error", {"ignoreCase": false, "ignoreMemberSort": false, "memberSyntaxSortOrder": ["none", "all", "multiple", "single"]}], "unicorn/catch-error-name": "error", "unicorn/empty-brace-spaces": "error", "unicorn/escape-case": "error", "unicorn/explicit-length-check": "error", "unicorn/filename-case": ["error", {"case": "kebabCase"}], "unicorn/no-array-push-push": "error", "unicorn/no-instanceof-array": "error", "unicorn/no-thenable": "error", "unicorn/no-unnecessary-await": "error", "unicorn/no-unreadable-array-destructuring": "error", "unicorn/no-useless-promise-resolve-reject": "error", "unicorn/no-useless-spread": "error", "unicorn/no-useless-switch-case": "error", "unicorn/prefer-array-find": "error", "unicorn/prefer-at": "error", "unicorn/prefer-includes": "error", "unicorn/prefer-logical-operator-over-ternary": "error", "unicorn/prefer-native-coercion-functions": "error", "unicorn/prefer-object-from-entries": "error", "unicorn/prefer-spread": "error", "unicorn/prefer-string-slice": "error", "unicorn/prefer-switch": "error", "unicorn/require-number-to-fixed-digits-argument": "error", "unicorn/throw-new-error": "error", "unused-imports/no-unused-imports": "error", "@typescript-eslint/array-type": ["error", {"default": "array"}], "@typescript-eslint/no-unused-vars": ["error", {"ignoreRestSiblings": true}], "@typescript-eslint/no-explicit-any": "error"}}, {"files": ["index.ts", "index.js"], "rules": {"sort-exports/sort-exports": ["error", {"sortDir": "asc"}]}}, {"files": ["pages/api/**/*.ts"], "rules": {"unicorn/filename-case": "off"}}], "rules": {"storybook/no-uninstalled-addons": ["error", {"ignore": ["@nx/react/plugins/storybook"]}]}}