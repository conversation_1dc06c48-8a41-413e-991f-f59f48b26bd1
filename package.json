{"name": "experience", "version": "0.0.0", "license": "MIT", "scripts": {"affected:commercial": "nx affected --exclude '*,!tag:commercial' --targets lint test build", "affected:data-platform": "nx affected --exclude '*,!tag:data-platform' --targets lint test build", "affected:mobile": "nx affected --exclude '*,!tag:mobile' --targets lint test build", "affected:shared": "nx affected --exclude '*,!tag:shared' --targets lint test build", "build": "nx run-many --target=build --all", "build:affected": "nx affected --target=build", "compose:up": "nx run-many --target=compose --configuration up --all", "compose:down": "nx run-many --target=compose --configuration down --all", "compose:remove": "nx run-many --target=compose --configuration remove --all", "compose:recreate": "nx run-many --target=compose --configuration recreate --all", "e2e": "nx run-many --target=e2e --all --parallel=1", "e2e:update-snapshots": "nx run-many --target=e2e --all --parallel=1 -u", "e2e:affected": "nx affected --target=e2e --parallel=1", "e2e:affected:update-snapshots": "nx affected --target=e2e --parallel=1 -u", "e2e:integration": "nx run-many --target=e2e:integration --all", "generate-mocks": "nx run-many --target=generate-mocks --all", "generate-mocks:affected": "nx affected --target=generate-mocks", "generate-sources": "nx run-many --target=generate-sources --all", "generate-sources:contract": "nx run-many --target=generate-sources --projects=data-platform-api-contract,data-platform-api-docs -c docker --parallel=1", "generate-sources:mobile": "nx run-many --target=generate-sources --exclude '*,!tag:mobile'", "generate-sources:prisma": "npx nx run-many --targets generate-sources:prisma", "generate-swagger:mobile": "nx run-many --target=generate-swagger --exclude '*,!tag:mobile'", "lint": "nx run-many --target=lint --all && golangci-lint run ./...", "lint:affected": "nx affected --target=lint && golangci-lint run ./...", "lint:fix": "nx run-many --target=lint --all --fix && golangci-lint run ./... --fix", "lint:fix:affected": "nx affected --target=lint --fix && golangci-lint run ./... --fix", "package": "nx run-many --target=package --all", "performance-test": "nx run-many --target=performance-test --all", "postinstall": "patch-package && npm run generate-sources:prisma", "serve": "nx run-many --target=serve --all", "serve:data-platform": "nx run-many --target=serve --projects=data-platform-api", "serve:destination": "nx run-many --target=serve --projects=destination-site-admin-api,destination-site-admin-webapp", "serve:installer": "nx run-many --target=serve --projects=installer-api,installer-bff", "serve:internal": "nx run-many --target=serve --projects=internal-site-admin-api,internal-site-admin-webapp", "serve:loyalty-card-service": "nx run-many --target=serve --projects=loyalty-card-service-api,loyalty-card-service-queue-worker", "serve:mobile": "nx run-many --target=serve --projects=mobile-api", "serve:ocpi-service": "nx run-many --target=serve --projects=ocpi-service-webapp,ocpi-service-api,ocpi-service-queue-worker", "serve:onboarding-service": "nx run-many --target=serve --projects=onboarding-service-webapp", "serve:statement-service": "nx run-many --target=serve --projects=statement-service-webapp,statement-service-api,statement-service-queue-worker", "serve:support-tool": "nx run-many --target=serve --projects=support-tool-webapp,support-tool-api", "smoke-test": "nx run-many --target=smoke-test --all", "test": "nx run-many --target=test --all --code-coverage", "test:affected": "nx affected --target=test --code-coverage", "test:affected:update-snapshots": "nx affected --target=test --code-coverage -u", "test:update-snapshots": "nx run-many --target=test --all --code-coverage -u", "fix-repo": "./init/fix-repo.sh"}, "private": true, "dependencies": {"@aws-crypto/sha256-js": "5.2.0", "@aws-sdk/client-cloudfront": "3.857.0", "@aws-sdk/client-cloudwatch": "3.857.0", "@aws-sdk/client-eventbridge": "3.857.0", "@aws-sdk/client-s3": "3.857.0", "@aws-sdk/client-secrets-manager": "3.857.0", "@aws-sdk/client-sesv2": "3.857.0", "@aws-sdk/client-sns": "3.857.0", "@aws-sdk/credential-provider-node": "3.857.0", "@aws-sdk/rds-signer": "3.857.0", "@aws-sdk/s3-request-presigner": "3.857.0", "@floating-ui/react": "0.27.15", "@fontsource-variable/quicksand": "5.2.8", "@headlessui/react": "2.2.7", "@heroicons/react": "2.2.0", "@hookform/resolvers": "5.2.1", "@mdx-js/loader": "3.1.0", "@mdx-js/react": "3.1.0", "@nestjs/axios": "4.0.1", "@nestjs/cache-manager": "3.0.1", "@nestjs/common": "11.1.5", "@nestjs/config": "4.0.2", "@nestjs/core": "11.1.5", "@nestjs/mapped-types": "2.1.0", "@nestjs/passport": "11.0.5", "@nestjs/platform-express": "11.1.5", "@nestjs/schedule": "6.0.0", "@nestjs/sequelize": "11.0.0", "@nestjs/swagger": "11.2.0", "@nestjs/terminus": "11.0.0", "@nestjs/typeorm": "11.0.0", "@next/mdx": "14.2.31", "@opentelemetry/api": "1.9.0", "@opentelemetry/auto-instrumentations-node": "0.62.0", "@opentelemetry/id-generator-aws-xray": "2.0.0", "@opentelemetry/propagator-aws-xray": "2.1.0", "@prisma/client": "6.13.0", "@prisma/extension-read-replicas": "0.4.1", "@prisma/instrumentation": "6.13.0", "@react-firebase/auth": "0.2.10", "@segment/analytics-node": "2.3.0", "@sentry/nextjs": "9.45.0", "@sentry/node": "9.45.0", "@sentry/opentelemetry": "9.45.0", "@smithy/protocol-http": "5.1.2", "@smithy/signature-v4": "5.1.2", "@tailwindcss/postcss": "4.1.11", "@tanstack/react-table": "8.21.3", "@types/mime-types": "3.0.1", "@types/ua-parser-js": "0.7.39", "@vis.gl/react-google-maps": "1.5.4", "axios": "1.11.0", "browser-update": "3.3.60", "cache-manager": "7.1.0", "class-transformer": "0.5.1", "class-validator": "0.14.2", "classnames": "2.5.1", "cls-hooked": "4.2.2", "company-email-validator": "1.1.0", "compare-versions": "6.1.1", "configcat-node": "11.4.0", "core-js": "3.44.0", "csv-parse": "6.1.0", "csv-stringify": "6.6.0", "dayjs": "1.11.13", "debounce": "2.2.0", "decimal.js": "10.6.0", "detect-browser": "5.3.0", "dotenv": "17.2.1", "driver.js": "1.3.6", "fast-sort": "3.4.1", "firebase": "12.0.0", "firebase-admin": "13.4.0", "flagged": "3.0.0", "form-data": "4.0.4", "gcip-cloud-functions": "0.2.0", "helmet": "8.1.0", "hibp": "15.0.1", "http-proxy-middleware": "3.0.5", "immutable": "5.1.3", "json-to-csv-export": "3.1.2", "jsonwebtoken": "9.0.2", "jwks-rsa": "3.2.0", "match-sorter": "8.1.0", "mime-types": "3.0.1", "mimetext": "3.0.24", "mysql2": "3.14.3", "nest-commander": "3.18.0", "nestjs-cls": "6.0.1", "nestjs-i18n": "10.5.1", "nestjs-pino": "4.4.0", "nestjs-zod": "5.0.0-beta.20250719T005107", "next": "14.2.31", "next-auth": "4.24.11", "next-intl": "4.3.4", "next-runtime-env": "3.3.0", "nocache": "4.0.0", "on-headers": "1.1.0", "passport-jwt": "4.0.1", "pdf2json": "3.2.0", "pg": "8.16.3", "pluralize": "8.0.0", "prisma": "6.13.0", "prisma-field-encryption": "1.6.0", "puppeteer": "24.15.0", "react": "18.3.1", "react-cookie": "8.0.1", "react-dom": "18.3.1", "react-hook-form": "7.62.0", "react-hot-toast": "2.5.2", "react-infinite-scroll-component": "6.1.0", "react-photo-album": "3.1.0", "react-qr-code": "2.0.18", "react-router-dom": "7.8.0", "recharts": "3.1.0", "reflect-metadata": "0.2.2", "regenerator-runtime": "0.14.1", "rxjs": "7.8.2", "sales-tax": "2.19.0", "sequelize": "6.37.7", "sequelize-typescript": "2.1.6", "sqs-consumer": "12.0.0", "stripe": "18.4.0", "stripe-event-types": "3.1.0", "swr": "2.3.4", "tailwind-merge": "3.3.1", "ts-toolbelt": "9.6.0", "tslib": "2.8.1", "typeorm": "0.3.25", "typeorm-naming-strategies": "4.1.0", "ua-parser-js": "1.0.40", "usehooks-ts": "3.1.1", "uuid": "11.1.0", "yet-another-react-lightbox": "3.25.0", "zod": "4.0.14", "zustand": "5.0.7"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/preset-react": "7.27.1", "@babel/preset-typescript": "7.27.1", "@chromatic-com/storybook": "4.0.1", "@commitlint/config-conventional": "19.8.1", "@golevelup/ts-jest": "0.7.0", "@maizzle/cli": "2.0.0", "@maizzle/framework": "5.2.2", "@mswjs/http-middleware": "0.10.3", "@nestjs/schematics": "11.0.7", "@nestjs/testing": "11.1.5", "@nx-go/nx-go": "3.3.1", "@nx/cypress": "21.3.11", "@nx/devkit": "21.3.11", "@nx/eslint": "21.3.11", "@nx/eslint-plugin": "21.3.11", "@nx/jest": "21.3.11", "@nx/js": "21.3.11", "@nx/nest": "21.3.11", "@nx/next": "21.3.11", "@nx/node": "21.3.11", "@nx/playwright": "21.3.11", "@nx/react": "21.3.11", "@nx/storybook": "21.3.11", "@nx/web": "21.3.11", "@nx/webpack": "21.3.11", "@nx/workspace": "21.3.11", "@playwright/test": "1.54.1", "@pmmmwh/react-refresh-webpack-plugin": "0.6.1", "@smithy/util-stream": "4.2.3", "@storybook/addon-a11y": "9.0.18", "@storybook/addon-docs": "9.0.18", "@storybook/nextjs": "9.0.18", "@storybook/react": "9.0.18", "@svgr/webpack": "8.1.0", "@swc-node/register": "1.10.10", "@swc/cli": "0.7.8", "@swc/core": "1.13.3", "@swc/helpers": "0.5.17", "@swc/jest": "0.2.39", "@tailwindcss/forms": "0.5.10", "@testing-library/cypress": "10.0.3", "@testing-library/jest-dom": "6.6.4", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/browser-update": "3.3.3", "@types/cls-hooked": "4.3.9", "@types/debounce": "1.2.4", "@types/express": "5.0.3", "@types/jest": "30.0.0", "@types/mdx": "2.0.13", "@types/multer": "2.0.0", "@types/node": "22.17.0", "@types/on-headers": "1.0.4", "@types/passport-jwt": "4.0.1", "@types/pluralize": "0.0.33", "@types/react": "18.3.23", "@types/react-dom": "18.3.7", "@types/react-helmet": "6.1.11", "@types/react-router-dom": "5.3.3", "@types/segment-analytics": "0.0.38", "@types/supertest": "6.0.3", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "autoprefixer": "10.4.21", "aws-sdk-client-mock": "4.1.0", "aws-sdk-client-mock-jest": "4.1.0", "babel-jest": "30.0.5", "babel-loader": "10.0.0", "commitlint": "19.8.1", "css-loader": "7.1.2", "cypress": "14.5.3", "cypress-downloadfile": "1.2.4", "eslint": "8.57.1", "eslint-config-next": "15.4.5", "eslint-config-prettier": "10.1.8", "eslint-plugin-cypress": "3.6.0", "eslint-plugin-deprecation": "3.0.0", "eslint-plugin-import": "2.32.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-no-only-tests": "3.3.0", "eslint-plugin-playwright": "2.2.2", "eslint-plugin-prefer-arrow-functions": "3.6.2", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-sort-exports": "0.9.1", "eslint-plugin-sort-imports-es6-autofix": "0.6.0", "eslint-plugin-storybook": "0.12.0", "eslint-plugin-unicorn": "56.0.1", "eslint-plugin-unused-imports": "3.2.0", "firebase-tools": "14.11.2", "husky": "9.1.7", "jest": "30.0.5", "jest-environment-jsdom": "30.0.5", "jest-environment-node": "30.0.5", "jest-mock-extended": "4.0.0", "jest-util": "30.0.5", "mockdate": "3.0.5", "msw-auto-mock": "0.31.0", "nx": "21.3.11", "patch-package": "8.0.0", "pdf-visual-diff": "0.14.0", "pino-pretty": "13.1.1", "postcss": "8.5.6", "prettier": "2.8.8", "react-refresh": "0.17.0", "rimraf": "6.0.1", "sequelize-auto": "0.8.8", "start-server-and-test": "2.0.12", "storybook": "9.0.18", "storybook-addon-swc": "1.2.0", "strict-event-emitter": "0.5.1", "style-loader": "4.0.0", "stylus": "0.64.0", "stylus-loader": "8.1.1", "supertest": "7.1.4", "tailwindcss": "4.1.11", "tailwindcss-box-shadow": "2.0.3", "tailwindcss-email-variants": "3.0.4", "tailwindcss-mso": "2.0.2", "testcontainers": "11.4.0", "ts-jest": "29.4.0", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.8.3", "url-loader": "4.1.1", "vite": "7.0.6", "webpack": "5.101.0", "webpack-merge": "6.0.1", "whatwg-fetch": "3.6.20", "yaml": "2.8.0"}, "engines": {"node": "22.18.0"}, "overrides": {"@prisma/extension-read-replicas": {"@prisma/client": "6.13.0"}, "eslint-plugin-prefer-arrow-functions": {"eslint": "8.57.1"}, "eslint-plugin-storybook": {"typescript": "5.8.3"}, "tailwindcss-email-variants": {"tailwindcss": "4.1.11"}, "tailwindcss-mso": {"tailwindcss": "4.1.11"}}}