{"$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"build": {"dependsOn": ["^build", {"target": "build", "dependencies": true}], "inputs": ["production", "^production"], "cache": true}, "e2e:cypress": {"inputs": ["default", "^production"]}, "e2e": {"inputs": ["default", "^production"], "cache": true}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "build-storybook": {"inputs": ["default", "^production", "{workspaceRoot}/.storybook/**/*", "{projectRoot}/.storybook/**/*", "{projectRoot}/tsconfig.storybook.json"], "cache": true}, "serve": {"dependsOn": [{"target": "build", "dependencies": true}]}, "generate-sources": {"cache": true}, "@nx/jest:jest": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "e2e-ci--**/*": {"dependsOn": ["^next:build"]}}, "generators": {"@nx/react": {"application": {"style": "css", "linter": "eslint", "bundler": "webpack", "babel": true}, "library": {"style": "css", "linter": "eslint", "unitTestRunner": "jest"}, "component": {"style": "css"}}, "@nx/next": {"application": {"style": "css", "linter": "eslint"}}}, "defaultProject": "destination-site-admin-webapp", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals", "projectSpecificFiles"], "sharedGlobals": ["{workspaceRoot}/.github/actions/**/action.yml", "{workspaceRoot}/.github/workflows/pipeline.yml", "{workspaceRoot}/.github/workflows/pull-request.yml", "{workspaceRoot}/babel.config.json", {"env": "GITHUB_REF"}, {"env": "NX_CLOUD_CACHE_BUSTER"}, {"runtime": "uname -srm"}], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/.storybook/**/*", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/tsconfig.storybook.json"], "projectSpecificFiles": []}, "parallel": 2, "defaultBase": "main", "plugins": [{"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint", "extensions": ["ts", "tsx", "js", "jsx", "html", "vue"]}}, {"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "next:build", "devTargetName": "dev", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}], "pluginsConfig": {"@nx/js": {"projectsAffectedByDependencyUpdates": "auto"}}}