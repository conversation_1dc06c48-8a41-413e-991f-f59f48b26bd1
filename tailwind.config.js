const { join } = require('path');

module.exports = {
  content: [
    join(__dirname, 'apps/**/*.{js,ts,jsx,mdx,tsx}'),
    join(__dirname, 'libs/**/*.{js,ts,jsx,mdx,tsx}'),
  ],
  theme: {
    extend: {
      colors: {
        espresso: '#461908',
        plum: '#550041',
        navy: '#163E4C',
        amber: '#C85F14',
        brick: '#800000',
        beige: '#F2DAB9',
        blush: '#E89A96',
        cornflower: '#87B4E1',
        sunshine: '#FFDC50',
        rose: '#FFCDD7',
        forest: '#005F41',
        chocolate: '#7A4600',
        ink: '#000546',
        charcoal: '#32322D',
        gunmetal: '#5A5A5F',
        lime: '#C8D72D',
        gold: '#FFB950',
        sky: '#9BDCE1',
        smoke: '#DCD7D2',
        mist: '#DCDCE6',

        // Semantic color aliases for migration from old color system
        primary: '#163E4C', // navy - for primary actions and branding
        error: '#800000', // brick - for error states and destructive actions
        warning: '#C85F14', // amber - for warning states and caution
        info: '#163E4C', // navy - for informational states and links
        neutral: '#5A5A5F', // gunmetal - for neutral grays
        success: '#005F41', // forest - for success states
      },
      fontFamily: {
        sans: ["'TTCommonsPro', 'Arial', sans-serif"],
      },
      lineClamp: {
        7: '7',
        8: '8',
        9: '9',
        10: '10',
      },
      padding: {
        3.75: '15px',
      },
      fontSize: {
        xxs: '0.625rem',
        xs: '0.75rem',
        sm: '0.875rem',
        md: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        xxl: '1.5rem',
        '2xl': '2.25rem',
        '3xl': '3rem',
      },
      width: {
        112: '28rem',
        128: '32rem',
        160: '40rem',
        192: '48rem',
        224: '56rem',
        256: '64rem',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    ({ addUtilities }) => {
      addUtilities(
        {
          '.print-color-adjust': {
            '-webkit-print-color-adjust': 'exact',
            'print-color-adjust': 'exact',
          },
        },
        ['print']
      );
    },
  ],
};
