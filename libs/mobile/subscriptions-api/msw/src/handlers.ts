/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';

// Map to store counters for each API endpoint
const apiCounters = new Map();

const next = (apiKey) => {
  let currentCount = apiCounters.get(apiKey) ?? 0;
  if (currentCount === Number.MAX_SAFE_INTEGER - 1) {
    currentCount = 0;
  }
  apiCounters.set(apiKey, currentCount + 1);
  return currentCount;
};

export const handlers = [
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [getHealthControllerCheck200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /health`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/subscriptions`, async () => {
    const resultArray = [
      [getSubscriptionsControllerSearch200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /subscriptions`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/subscriptions`, async () => {
    const resultArray = [
      [getSubscriptionsControllerCreate201Response(), { status: 201 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /subscriptions`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/subscriptions/:subscriptionId`, async () => {
    const resultArray = [
      [
        getSubscriptionsControllerGetBySubscriptionId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /subscriptions/:subscriptionId`) % resultArray.length
      ]
    );
  }),
  http.patch(`${baseURL}/subscriptions/:subscriptionId`, async () => {
    const resultArray = [
      [
        getSubscriptionsControllerTransferSubscription200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`patch /subscriptions/:subscriptionId`) % resultArray.length
      ]
    );
  }),
  http.delete(`${baseURL}/subscriptions/:subscriptionId`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /subscriptions/:subscriptionId`) % resultArray.length
      ]
    );
  }),
  http.get(
    `${baseURL}/subscriptions/:subscriptionId/actions/:actionId`,
    async () => {
      const resultArray = [
        [getSubscriptionsControllerGetByActionId200Response(), { status: 200 }],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`get /subscriptions/:subscriptionId/actions/:actionId`) %
            resultArray.length
        ]
      );
    }
  ),
  http.patch(
    `${baseURL}/subscriptions/:subscriptionId/actions/:actionId`,
    async () => {
      const resultArray = [
        [
          getSubscriptionsControllerUpdateActionById200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`patch /subscriptions/:subscriptionId/actions/:actionId`) %
            resultArray.length
        ]
      );
    }
  ),
  http.get(
    `${baseURL}/subscriptions/:subscriptionId/direct-debit`,
    async () => {
      const resultArray = [
        [
          getSubscriptionsControllerGetSubscriptionDirectDebit200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`get /subscriptions/:subscriptionId/direct-debit`) %
            resultArray.length
        ]
      );
    }
  ),
  http.get(`${baseURL}/subscriptions/:subscriptionId/documents`, async () => {
    const resultArray = [
      [
        getSubscriptionsControllerGetSubscriptionDocuments200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /subscriptions/:subscriptionId/documents`) %
          resultArray.length
      ]
    );
  }),
];

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}

export function getSubscriptionsControllerSearch200Response() {
  return {
    subscriptions: [
      {
        id: 'c0b6a139-35f5-45ba-a2c6-c454006bf909',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'PENDING',
        order: {
          id: '9536077c-436c-4b0a-b559-81bfb4200e5e',
          origin: 'SALESFORCE',
          orderedAt: '2025-01-01T00:00:00.000Z',
          ppid: 'ff27ea21-9335-4ab6-a24c-5e2104e5e3e7',
        },
        actions: [
          {
            id: 'a92611d7-bc3d-44a0-ad01-9bbd1a181e46',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: 'f8d79216-9259-4e7a-98f4-c2f8e571d5e4',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              'af9ca20d-31c0-44ed-8fd6-b73a47673009',
              '5e57872e-886d-4f42-8593-91ac595e1560',
              '10b9782c-f8e7-42d1-b098-4bfc649558f5',
              '78083f5f-b1a3-471d-afee-182ce312e216',
              'df4e4457-7183-4109-9c9e-47b622b59149',
              '10e8916d-26aa-4a3d-85b0-594aa67c33f7',
              '66da69b6-25cf-4ca5-b2cc-1704bc076c87',
              '928b0444-93d9-42a4-aed8-e60b6f4c3b5f',
              'd6c24562-bc9d-466f-b3f7-fb2cfcf1ccee',
              '0f3c6cb3-f790-4cce-88d0-14a788190a20',
              'b1eaf732-3cac-46e1-9871-c9d0d370ffbc',
              '0aff0c03-a7c6-46db-86c8-4ec0d1b431ef',
              'e3dd0408-7be9-470b-b041-ead244313785',
              '5090a580-8765-4500-bf82-d94a1f6e1f54',
              'aaf61bb6-263a-4d49-b5cf-bee99469c74b',
              '509e8274-84c7-497c-96b8-79fc28fe82a9',
            ],
            data: { ppid: '0a90a58c-6924-47d8-bb83-90057f0167e4' },
          },
          {
            id: '452c62e8-abd5-4003-af4f-8c37df9350c9',
            type: 'CHECK_AFFORDABILITY_V1',
            subscriptionId: '02b83570-0433-4257-bc90-a7ba6d5e676d',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              '40bc4cf0-51f8-46cf-bc29-c191d8651958',
              'f104567b-6179-43ec-9a3c-326aac39eb86',
              '99a3737f-e86a-45fb-9183-28c6bfe19ca3',
              '2c1f166d-e6e9-4ba0-90dc-28b9080b29a1',
              '573e6937-cd83-4822-8786-be6f87e78b76',
              'dae4a160-d518-4cfb-85a6-977ce9c79af6',
              '03985c34-fd43-47f9-a150-9887c3896611',
              '16d4d482-c1ab-4959-9dc1-4d28cafefdcb',
              '72a70ea1-d7aa-4f03-90fe-372185334358',
              '5d6ff814-6dc7-4263-94cc-5ac40719b0c8',
              '4abfa13f-e954-4e56-aae5-943a63eb0003',
              'f05c4ca8-6298-4ec0-8942-e0a4185f18dd',
              '6f2be914-a662-43a8-a62f-13306414568b',
              'd7b34fb9-b373-410b-a965-b6bcc2e66e6e',
            ],
            data: { applicationId: 1407569099091190, loanId: 799212370589779 },
          },
          {
            id: 'bfda5ea5-eb60-4b9f-b18c-11bcc1057781',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: '0fc265c9-9710-4d3e-940c-594cd5b63372',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              '5495b720-9b58-4354-a70b-188bfa07721e',
              'fd0108b3-95b0-4781-b62e-612d1db3ffaf',
              'c3cb961a-719d-4217-9a7a-05937d3eec04',
              '0adc8543-e4d1-4064-b401-abf65d83c014',
              '54480ace-49c2-4c1e-80ca-061dd40232b7',
              '16e1dbf0-d88a-47cf-880a-8af766b4b39d',
            ],
            data: { revision: 'abscido crux clarus' },
          },
          {
            id: '9fe81f2c-acd2-47cb-a0eb-f2a06a99b62d',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: 'c79d5c64-8522-4a16-aa72-d4c4bb25b5fe',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: ['f0f05dd6-eafb-4dff-9163-868ac16f155f'],
            data: { surveyUrl: 'https://amazing-character.info' },
          },
          {
            id: '008bb5ae-7c75-4a2f-9dfa-5287904bb33b',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: '73c63c12-73d2-4b10-b0cd-f7c3bce332f6',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              '37a0a22c-3fb6-4d88-b183-4c1eace7d793',
              '5e37f7e1-7540-4e35-a4b1-c55bab071a84',
              '3dbfd159-1790-4290-9bdb-8375ee3ff7d6',
              '198bbc50-83b4-49eb-896e-46a79f997d58',
              '360405d7-eda4-4c48-9f0b-4bb726320efa',
              'a4c28025-a8c6-4ad4-8837-3193ef654d5e',
              '2a4938a2-ccab-426b-bcd3-3e8a90fe7f72',
              'fb1a6784-cdd0-4914-b7d3-46bfb3873e05',
              '9816cf50-957a-4328-970a-14857b977550',
              '3377541f-5383-4a0d-8ca4-ec1875c2851b',
              'a32c3d1b-bcc6-4e2a-a349-76daf5058240',
              '5de1f58e-72a7-47da-a337-de416cb0635c',
              '7df52f05-eb85-43ae-8f4e-1f25ddd00317',
            ],
            data: { surveyUrl: 'https://odd-milestone.org' },
          },
          {
            id: '2bee8d11-2045-4d2d-9171-ac2c89b065ae',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '634ca582-62f2-4429-ab6b-fdb7abb01734',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              '93b2df04-5adb-4511-8765-4e68557a0b08',
              'fe955e26-e956-4d02-b8a9-b87e1b2570b4',
              '0d0b9603-d91d-49ec-912e-31b5081662ba',
              '1e10774f-8d5b-44fb-963e-5e636c5340c0',
              'dfd38b12-a5cd-4c2a-834b-e4d4ba7746dc',
              '4a3c6baf-486f-4e36-ab5f-39bfcb4a3b74',
              '0ae9bb97-3434-451f-af96-be4f8afc4e85',
              '56def81d-d86f-46fa-8f9f-074c2d7573ba',
              '30d0626c-29a2-4360-92ed-bdb18fe83e72',
              'c9fbe97a-c7d8-4a7d-89f7-22b07e5306cc',
              '0c10664e-1f55-4f49-afa0-a5bb17be42c2',
              '2ee3aeaa-bafc-4adb-abb1-7d9bd99756fc',
              '26494825-c955-484d-b4bf-fb75bf5c8778',
              '4ea7a618-85be-4765-8b27-f66132b6c093',
              'e86d31bd-7ff8-487e-8554-9daf9025c34f',
              'b84c42e7-5a49-455a-bc71-96a28914d65e',
            ],
            data: { ppid: '75df4df4-a7e0-4bf9-9e8b-ef028346ca4e' },
          },
          {
            id: '95be230c-c86e-4cca-9b2d-500ac7a121a3',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: 'c1b9e734-2144-4b02-bcb4-51ecfac4c16d',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              'e28edbc7-cedf-4a3b-9476-2ee80c0335e2',
              '5e10860f-add5-43b4-894b-a27d736f39b9',
              '41381d39-fbce-40e0-975a-f0f3b4e71ea1',
            ],
            data: {},
          },
          {
            id: '5a7e6157-2cf8-4bbe-9324-f1b1c4dc2c69',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: 'cff90ec3-0973-4a00-a28c-36cff47eb194',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              '9e54ddc5-5e84-417f-a2fa-8d4709b03926',
              '12df2c40-643a-46ff-b78e-d66209c9bfa8',
              'aed57107-9b35-4489-8905-47274f2847c6',
              '6efcd5b6-4c6c-49fc-ba70-72e83be99831',
            ],
            data: {},
          },
          {
            id: '2bf495ea-84ee-4662-a0a3-1a1393813bd5',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: '935b3cae-bdba-42fe-ba59-70818b885761',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              'ac55bbe6-bf45-4107-9d96-5c04ae52f8fb',
              'c0532c53-67cd-44fd-a9e8-04b6711b7504',
              '5f5aed54-92fc-48be-bb71-c6ef1d26c6e0',
              'b9a096a5-43e0-4e8d-b745-03fe732e5ac3',
              'f18210dd-edd3-4659-9382-f6470b1fa1e2',
              '840d885d-87d5-4c5f-914d-c0ffbf6ac6a8',
              'fc1bedd1-cc5e-4778-86fa-d19cc73b9b38',
              '318de996-962a-47ed-81aa-5d8b3ac06f9f',
              'a5e9e954-331a-4523-895e-d40c1b91ce56',
              '9bfded92-7e56-4f38-a5fa-9a84bb05470f',
              '7fe4b114-d340-4da3-99dd-29a205520fce',
              'd4a79865-3d59-4609-b835-1eb391abea8c',
              'f75b7427-a1f7-412f-899a-4fb74d42be34',
              '81cab5c5-c533-4faf-b613-c9f4e6020f4d',
              '88728377-3c29-4568-866b-2cd3582f9cae',
              'eb0759b8-189c-4b7d-9de1-536bb9a18519',
              '6d50d105-525f-4f97-b172-e274479b04ee',
              '2b2b5ca2-7a16-415b-a013-d347be95a74e',
              '9b4df845-2dd7-4978-8973-29495e7a69cc',
            ],
            data: {},
          },
          {
            id: '6df1c0cb-31d9-41f1-92fb-895ba48c2cd0',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: '74674aad-041e-4b25-8d50-b900f1612ed9',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '4db4a2b7-fa90-4089-bc5e-a3ef8b7974c7',
              '8526a5bc-58d6-40f9-ad64-442eda089aba',
              'bffed2a5-530d-4a0b-b442-7a086223ac73',
              'c3e420c1-8208-44a7-b98d-d4fc3cbd229a',
              'f27d5f1a-d168-4e18-8e57-d2358e733d4f',
            ],
            data: {},
          },
          {
            id: 'cc85a3e4-4ada-4b86-b0a9-f91227dc179f',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: '78eccdce-f25e-4c28-adcd-d0a094eb85a0',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '5fea971d-62d2-49dd-854f-7ce7fbfe3dcd',
              'fc6b6901-7f1c-49ad-aec7-b42cc18cb8e8',
              'b67124c4-4899-44be-ac16-317b3727d232',
              'bf3c3f56-680c-40ee-bd2e-bedae23143e4',
              '7db8c5fe-51d6-4174-a4d9-b5bb60e0fe20',
              'd5b2543b-21bd-47e1-9413-4afb30c7fdd0',
              'fc937161-c3d1-403c-85ff-3b6f5b8bd24d',
            ],
            data: {},
          },
          {
            id: '9111aa79-3c7c-4e23-901b-02378ab58353',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: 'd48c3422-609b-4bc2-883b-e3a283c751e2',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              '9a5371b0-c382-4cb8-9142-170f8e5aac85',
              'ead95658-f6b0-4fc5-8b0d-6ab190033742',
              '7638ab81-3d62-4450-a85d-0f86677a6bbd',
              '276ceaae-40e6-49f1-93e6-ac51b42cd34f',
              'ea668de4-62da-4ff2-9909-24f53f1d4e1e',
              '61e243de-6eaf-4c2f-8331-4b4a1e3cb0b7',
              '9e91505e-db57-4de1-b90f-21d37c7209a9',
              'e81de692-be6c-4450-ab17-77b43bfe44d6',
            ],
            data: {},
          },
          {
            id: 'ff4ed198-8eaf-4def-89e2-4848523516ac',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: '8c949d12-e2d2-489b-ba01-b88ca48c1d87',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '859a430a-a128-4710-bd95-c235541d59e4',
              'f8e6851c-fd55-4564-8f89-0624ea4f68a8',
              'a4098f33-5e71-463c-b68c-82b5d3c7c244',
              '917834c5-3833-4a79-b2bf-755f178d02ee',
              'f596e72a-9a8a-46e1-986a-09977de4f4c0',
              '363f4541-ff5d-4105-af83-778a7c6f8786',
              '4761e05e-1e42-49d7-9e54-eee1327d2584',
              '11ed4d7a-5cf3-4455-a4d5-df2739de52be',
            ],
            data: {},
          },
          {
            id: '4a15513c-ee2c-4a71-9feb-f9c87e3f2758',
            type: 'CHECK_AFFORDABILITY_V1',
            subscriptionId: 'cf648619-7266-4d7c-82f9-df4e9e6a04cc',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              '54f78e41-5543-4d02-93fc-3f71ece0c7cc',
              '65664dcd-821a-4088-a4d1-0ec34ea0589b',
              '2fc2ceb3-e9df-4f8f-8fcf-776b35147dda',
              'd50c66d5-3ab1-44aa-88ec-c80183e3ccaa',
              'c90f6f49-ddaa-4a58-b7de-297c97d9a5ce',
              '620a7a16-2076-4cb6-9319-f485511e4a48',
              '49de592c-f57d-4f8a-a506-39f6843b7dd9',
            ],
            data: { applicationId: 6246364103518437, loanId: 6418058770567706 },
          },
          {
            id: 'dd8b7503-16ff-48bf-ad43-decbe9bea69e',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: 'fd0204d4-a2da-407a-a7ed-bb3f732106cd',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              '2dfcb59d-2416-4e7c-8b14-97f2d00d99db',
              '3689553b-bc55-488c-823f-34105420c163',
              '64e99886-3823-4b08-91b6-7f3afd638e57',
              '2c7c0e7b-cdc4-42fc-aafd-4b9b4d1a3479',
              'ab3f5e2d-da42-4bce-bae7-ac151d70eb30',
              'a515e7f9-113e-42ca-abb1-d20fdec071f6',
              'a47b294f-a10d-4843-bc34-0c935c5eff29',
              '66b806f2-6163-4d2c-bbc5-4e872c7cfd60',
              '5c7a31b5-da84-450f-974a-743d92eca60b',
              '0e9642ef-211c-4e8b-a7db-23cf40dba3e2',
              '567c6288-442c-4ca0-a963-edf5eeba1ace',
              '6300f136-f1cb-4c4b-bf05-185f055193a0',
              '8ebec9b6-76ae-4171-b65e-12e50163da36',
              'dd54708d-a4e4-4617-96f3-7082725aef20',
            ],
            data: { revision: 'acies stultus aliqua' },
          },
          {
            id: '7c59ac05-52a6-4d46-b5a7-5f9be78243ce',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: '17174366-9895-4b94-8d00-775b9da01795',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              '665a74c0-7527-4cf6-a5c6-ca85d7c8b35e',
              '5627ea74-b9bd-4e7d-a2cf-99c3c54cdbf7',
              '68082823-194b-4e7a-b2e4-a929d4153cad',
              '529ebec4-8fd7-4d1d-b2a6-cf3e31d92c92',
              '931c9582-39f3-46c4-8f7e-50e9337dff2a',
              '741f43d0-4a96-415c-8fa1-cee8085757bd',
            ],
            data: {
              documents: [
                {
                  code: 'rca',
                  signingUrl: 'https://worthwhile-distinction.info/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://unfit-hamburger.org',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://probable-brush.org',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://sociable-best-seller.com',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://splendid-intellect.biz/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://mammoth-settler.com/',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://unwilling-fundraising.net',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://noteworthy-mathematics.biz',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://thrifty-leading.org',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://wiggly-climb.net',
                  signed: true,
                },
              ],
            },
          },
          {
            id: '51e14740-ba93-4a53-bda1-22525fa3dd13',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: '1e3b910f-2439-41aa-a23b-03621ac00eeb',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              '47ed228d-03d1-422c-bc46-039f644bd915',
              '5a043fef-9123-4145-ba0d-7d9c66cee80a',
            ],
            data: { revision: 'aedificium tergeo conor' },
          },
          {
            id: 'f1ec3c0a-a34a-4cca-a56e-a0718379aef4',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: 'fff1882e-b4ca-4d8a-84ab-f1a2774ed934',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '61c63c30-8d52-4acc-a617-4148af41ccbd',
              'b996a336-cbd7-4081-a945-a6f62cf308c2',
              'acbf2e0b-5287-4547-b069-18191411a412',
              'e9018dd7-8e5d-4243-a0e5-98d1c5b6ec02',
              'f4abc307-62d7-424b-beed-9c663bd5bf6e',
              '84406866-e960-40b1-9456-b1bfba9e2844',
              '67f55a5e-34be-4515-ad45-ec14768bd62c',
              '632c8e2a-a072-4a36-bae3-051cb126c86b',
              'd960f979-bf83-4b05-8a73-72547e304103',
              '2558bc2a-0fd1-43a1-8a2c-f6c2ac7ff0af',
              '62163b4c-8002-47ba-83e7-831bc370cd2a',
            ],
            data: {},
          },
          {
            id: '2877f6b3-cb8c-4635-b1c1-235e461543a6',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: '09326d2d-18a9-459b-9812-bf442b2657cc',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              '516a18d5-61c3-4fcd-9be5-76f6b0b36f81',
              '06a54e9a-f430-45ab-88cf-5d7ea761f545',
              'dddc8500-4089-47ae-873e-f42b5f92460f',
              '1a3af376-d1d5-464e-935c-74b53354cc44',
              'f855057d-6ff5-4cd4-8f3e-75839243122d',
              '89b23b56-f58e-46a9-b82c-79247fc2f2db',
              '5b8a31c7-11b4-4a6c-8719-3418c589d7b2',
              '34d12f45-b512-45da-b21c-603584cefd58',
              'c1ef660e-b555-42a6-9472-5ef722576fa4',
              '2c3381a9-48b7-457f-b6fc-7636f3ed20b8',
              '1552396a-d401-42ea-8407-07cb0b68394c',
              'eda9b270-c657-4a80-a51a-12745363222a',
              'd56fd486-4bd3-4139-91dc-696bdefb0629',
              '15e32545-e0a2-4261-959d-9c94cde9674e',
              '3f6c1d4d-63dc-408a-a45e-a3512a968fee',
              '1a20d0f3-e823-4a69-aeeb-0c858f98e7e3',
              '95c90bb1-e154-419c-9504-2eb07ece43f4',
            ],
            data: { surveyUrl: 'https://wonderful-integer.net' },
          },
          {
            id: '24f951f1-831a-47b7-b523-aca6720eb169',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: '6e96832c-26c5-47fe-a518-b1be6a7db658',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '3a4eaec0-c50d-4071-9771-0bbf5ccd8028',
              '46fff963-52b9-4036-8a58-fe852eaf6e4e',
              'a6bbfb21-3fb0-4e01-b7db-7d8fc5765820',
              'bcd718d9-74c9-4f75-9a28-e7f4a66a9349',
              '6a1b5a2e-fa29-4488-9433-ebac2125cebc',
              '96249ff9-4704-41e7-92c0-3710ddbe6803',
              '8610c03e-1a92-48d9-863f-ae30a9db44f2',
              '5126138e-f207-413c-b93b-5c1922565f96',
              '5e2ca00b-4fd0-4f13-8268-12c98956493a',
              '3d62c8f8-3a92-4be2-ae4d-58cbe42fb6fd',
              '58f5c9d6-371f-4760-98e3-d4238aaaf164',
              '97d505b2-3d75-482a-a60c-bac5943c5844',
              '4c9058b0-4411-4d0d-af39-223292ee1719',
              '39aa7110-3c39-45db-9ff0-e742f4639327',
              '01fa6978-09e2-4aab-b1ca-693308c96df6',
              '0e994907-22f5-4e6a-b887-1621f47f0de4',
              '57d516d3-07c9-46d7-bbf0-3ee1c0fd080d',
            ],
            data: { surveyUrl: 'https://determined-councilman.name' },
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: 'ad8c5fe9-cc50-4f2d-8b48-784a99c5ed15',
          type: 'POD_DRIVE',
          productCode: 'acquiro tero viridis',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          upfrontFeePounds: 99,
          discountedUpfrontFeePounds: 45,
          monthlyFeePounds: 35,
          contractDurationMonths: 18,
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2025-04-18T17:18:13.753Z',
        },
      },
      {
        id: '6f8d3c8c-68dc-4e92-99ed-fb542601e041',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'PENDING',
        order: {
          id: '8ef78bb6-30ab-4466-b863-2969f2c06cb6',
          origin: 'SALESFORCE',
          orderedAt: '2025-01-01T00:00:00.000Z',
          ppid: '25c1f4c2-1e1c-49cf-9332-6a7340b4bfa2',
        },
        actions: [
          {
            id: 'f1a4e731-75d9-4e30-ae06-296a4415c407',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '2f5f9fe1-be6e-4712-ac44-539999d9cbaa',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              'c04760a4-b9e7-4515-855f-5f291c99abb3',
              '3b663749-56e5-49cf-9d48-a22e530df617',
            ],
            data: { ppid: '076065c8-9ed9-4cbb-aed1-7fd35e6a8235' },
          },
          {
            id: 'f7941841-88c7-4c11-91a4-6b55ddb416de',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '8165a3be-db59-4246-9e5c-8e976b6e9e96',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              'b72ba689-cf26-4292-8041-919a5d3d9481',
              'f5b41ad5-986a-4dd6-8719-1d7f1fbe7547',
              'bb0ccfd0-9787-43c1-8a4e-968653115263',
              'f501b86e-1286-44d7-a57c-973482c57011',
              '2bba3dce-035f-498a-b921-12867cfc01ad',
              'e46539db-6b2d-4d61-8045-fc0f10246a12',
              '821d3d0f-782a-4add-9632-12cccb731e2f',
              'b308a418-3a61-4fbf-ba3a-031b6754ef25',
              '147d18e6-860d-4715-a5ef-bac57bbd0155',
              '476d3f35-6483-4fe5-b0d7-beb22dfee877',
              '3b25e414-c695-4074-8a86-8720aca199d2',
              'cea1f1ee-1818-44d3-b978-736182736ae2',
              '02da78ad-d54e-4925-9fb4-db31c56c97b6',
              'b6220712-c242-4afa-b272-a76618d75c4a',
            ],
            data: { ppid: '9a6c18b0-ff1b-451b-905a-ae575efcafd6' },
          },
          {
            id: '78c093d9-7e47-4801-8e82-4c799887c738',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: 'a31f5a44-3797-44c3-acb3-4f5e8630b41f',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              '46549961-257c-4a53-9989-cdcd8bd02bb6',
              '84c8dc03-6e9e-4bf6-b33c-808b3c8400aa',
              'ff4ec68c-fe5b-4ba3-afc2-37a7c3cad081',
              'eac9d9d9-f9d0-41ae-b981-de2e13354f32',
              'bcda9a80-b6d5-4bd3-9999-8c6611367170',
              'c35568ff-0c66-48ca-997e-c05822ccefd5',
              '5d51e32b-d352-47e5-971f-253df4f75a5f',
              '4878ffd4-72ca-42c1-b1a5-a17a4d8fc30b',
              '91a3599c-407e-42f9-8ea2-c9189a3e4e01',
            ],
            data: { ppid: '522af0ba-fa7d-43ca-9048-fcd96b9b68a1' },
          },
          {
            id: '997e1fba-3651-4bde-9de6-ddad87ccc619',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: '21aaf5da-a15e-4cc7-a0c6-45f0542b7504',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              '3928d874-fa32-4e3b-b808-375a9b8ba80d',
              '198c5cff-dbe9-4887-9aa2-55f30e65c2e2',
              '4a7d5881-1fd0-4528-b660-73f5bf8ea33b',
              'f9a323b9-50ba-40e0-898e-ca572b1167cc',
              'a011e500-7c2c-4434-920b-0242f325c59b',
              'eec63856-ee6e-4595-a3bb-0f25bab93a6a',
              'c5e577e3-ce4b-432a-b1fa-4a6049c218b2',
              '8bcd5862-92c9-4648-b862-2c72c45925d9',
              '7a794e60-c331-42e3-a4f3-263c50975743',
              '62432147-040a-4448-91cf-3049caa5f9d1',
              '38fe14ea-5913-4c7b-ba43-5a1726b6e826',
              '775a8312-2777-4179-ba1c-d0b965e16397',
              'ba26ab29-db1f-47ad-a980-0212f3ced6ef',
            ],
            data: { surveyUrl: 'https://woeful-resolve.net' },
          },
          {
            id: '7b12a62a-cbec-44ac-bdd2-920bdc55838c',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: '3234ee86-8707-4eec-9581-6b29ac1e0f60',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              '8b38ad16-1e6b-4899-b359-27651da155bd',
              'a42ce775-6060-4d35-af57-b9b261f05a5d',
              'b4ce4be6-6216-4b7e-8d79-71ab82457359',
              'e5b1960a-da18-4c66-ae96-3c2ac7beba60',
              '9c487cf9-96c0-46ff-a673-938ad4af4f0d',
              'ea9ad59d-d15e-4e01-9877-c208225b0c37',
              'bc620781-2645-4fde-9274-49dfaa4af068',
              '954db486-72c5-4628-97b7-25d60faa4dd3',
              '418d1e9f-7ae4-43bc-a357-a9faa76251f8',
              '455c413c-0883-41e8-9c18-ef91e707904c',
              'f4137354-3ed3-4527-8bdf-9def4e2492c5',
              'c9ff6e4f-6a85-4d58-a6f9-5d0c6e790027',
              '12e8f8fc-8540-445e-a51b-7f00e0c398b5',
              '71ddec5e-80c9-45e6-b1d0-c15378d4c3ff',
            ],
            data: { surveyUrl: 'https://untimely-sonnet.com/' },
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: 'b8ec346e-0761-488c-a480-de56fc844e20',
          type: 'POD_DRIVE_REWARDS',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2024-08-10T18:34:32.615Z',
        },
      },
      {
        id: 'c21bc26b-8796-447c-9345-5b28445a7956',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'SUSPENDED',
        order: {
          id: '9aa13bb7-9a65-4d70-acea-d36e353a4f9f',
          origin: 'POD_DRIVE_REWARDS_SELF_SERVE',
          orderedAt: '2025-01-01T00:00:00.000Z',
          ppid: '3bff15c6-4559-4785-9a40-f54497b85196',
        },
        actions: [
          {
            id: 'fa06a1da-9abe-4ee3-9f0a-c4bcd0f085fd',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: '232c7608-9583-4fb6-8aac-2c41393fd772',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              '4faf1815-e632-49b8-a887-3586144be253',
              '51549b64-890a-4050-89d6-2a8c93aa0ba2',
            ],
            data: {},
          },
          {
            id: 'd59b79df-0af3-4bdc-b799-58bf34eb0bf6',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: 'f7fea00e-2584-4446-93f2-4454e3f71f32',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '4b6e3d66-081f-4f29-8a1f-06f9878f9fcc',
              '1968ae56-28a3-4783-8026-9652f95d8a7d',
              'c605ac24-3de5-430c-ad02-fbe4e7840321',
            ],
            data: { surveyUrl: 'https://frank-precedent.name/' },
          },
          {
            id: '4db52729-fb01-410d-b9ea-f8484cfcc693',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: 'ee8467d2-6606-468f-a0b8-8698de95a1ec',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              'f48ae26d-5c2c-4ce5-bc35-2c74e2d282cc',
              '8ba7bb83-749e-4ea0-ba55-cb12186a0f89',
              '8e61a9a0-993f-4043-a059-14b20f1c3626',
              '4d3683a4-c7a0-412b-983a-37e40203c0ef',
              'ce63bdc0-9235-496b-a9fd-40f5cce0dea1',
              '6ec8f043-46c6-46f3-bafb-33ba81e513d9',
              '7cc60b89-1b85-488f-99c0-4a4a8da05bb0',
              'd072285c-9e8f-422d-9121-75de33d5afb1',
              '09768768-08cc-4fe6-a489-2f07acbe913f',
              '5746de46-3b16-4e4b-926d-9ccadda97f17',
              '63c123e0-3160-446b-b2ad-5f58e336080a',
              'c85f9e2a-0f8f-466e-880c-dd083b77e932',
              '62956051-118a-4ac8-832a-099c3084f7d8',
              '21d5f1a3-2c38-4e5e-b1b4-39b575ba6282',
            ],
            data: { surveyUrl: 'https://illustrious-cassava.org' },
          },
          {
            id: 'efb3d3fa-824f-4ebe-98cc-b423b58a116d',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: '0abfdd39-16f2-4419-a8d3-d9ec8a8a5dc1',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              'fc0afb65-643b-46b4-b2e8-662a395a1a03',
              'bd82e5eb-4401-4d53-96c1-2322ce648d69',
              'f1470256-d1f4-4122-8a16-4fbfe25ff41a',
              '78449953-b2a0-4c8c-98f5-4d24a1a72c32',
              '75d6e2d7-f6fc-4fd2-8a16-5700b98be719',
              '06e8898e-8575-421a-94ff-bc0a1301ed1d',
              '458133f9-5630-4e75-bf3c-65478f108f49',
              '7b9f6074-5600-4fd1-8523-51464e6623c3',
              '6e7afd3b-85fe-4065-981a-3beb8a002125',
              '4ae4687d-162a-45b5-b2a7-ddd14ed6cd58',
              'd2136edf-f773-409c-8d91-7cf91e98e080',
              '073794f5-3226-4763-9c2c-79d22dacbf51',
              'e15e9111-fef5-43ef-98c1-ea755bf0bb46',
            ],
            data: { ppid: 'eadf5614-4a80-4b09-a63c-10a776b421f2' },
          },
          {
            id: 'f4de82cf-4024-4da0-84d1-578d0982c489',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: '3e90a02d-3366-4924-a45c-12a827d25b5f',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              'c20a869f-07ec-42b7-88d9-7043eba1914a',
              '62782119-0670-4a7a-a01a-c2da53692369',
              '868b3359-e52c-4d3c-b259-537240619b30',
              '0c55c730-7082-49ab-ab0a-0023cdd032e0',
              'f1da42e3-77ad-4390-8e08-66b397d1e8af',
              '80b13752-a7f9-4020-87e6-eae73965b31f',
              '594cd71f-59a8-4193-85e6-b1654701956b',
              '136f1779-48b5-453d-bcaa-1a42861ab60e',
              '50f651df-e533-488d-ae1d-fe7e78e026e6',
              '0ee29c41-22d8-4a8c-91d4-64ee703f7f8c',
              'e72455e6-0952-4cd4-9fe0-44e42a607c6f',
              'c8eb15bc-a325-480b-b45d-b84acd3b5372',
              '738fb283-c228-4a72-b609-0121edb7ca39',
              '752cda41-e060-47e0-a983-e2bb47ad7fa6',
              '8c1f7992-ef7e-484a-a84f-18317f777ff8',
              '7201b607-ea59-424b-a8b0-727d8839298d',
              '77aec4be-201f-4f1f-9f29-5188c0ceefbe',
              'ec02c04f-c47e-4961-8d47-76ae2dc40445',
              'f1cb46c9-5f4a-4f43-aca8-6c63780ce160',
            ],
            data: {
              documents: [
                {
                  code: 'rca',
                  signingUrl: 'https://cooperative-mantua.info/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://forceful-responsibility.net',
                  signed: true,
                },
              ],
            },
          },
          {
            id: '5b6564df-6cde-48eb-a349-d51b7eb8bf12',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: '050340c5-4b21-4e8c-8a55-22c8ab9e3eed',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: ['a464bca0-a39e-433c-92a8-5b4ae0177e31'],
            data: { surveyUrl: 'https://crafty-unique.biz' },
          },
          {
            id: 'cb13b219-ed20-498f-a35e-86656d955b96',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: 'bad122b9-eeec-4c50-a9d8-2bbf00df0171',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '24a9fc54-8629-41f0-8319-dad08ed371b2',
              '8e361e2c-9ad7-4db2-8555-6e9e540a74f1',
              '26d835cb-d8e6-4376-a6a9-9b129cc4ab85',
              '6d2ad2b8-130c-43fd-8289-2667379c9607',
              '40fd3f39-ffe8-4238-a1d4-c0d061fa9711',
              'adbb8db1-bb8f-44fe-914b-e21d7f791bfd',
              '1ea274ba-b900-4666-9dd9-2406352771c4',
              '62a84a04-ec30-4d8a-8e63-8144bde20905',
              '885a51f6-07cd-4559-abfc-2ed772c0de1d',
              '39849651-d25d-457e-8737-fe48f3b29f46',
              '09942d09-f110-4b3f-b93a-ee0497784fa7',
              '056da427-6dec-46b8-bfe1-dbdae4b8373a',
              'a63673dc-0c23-4fea-9159-c32c58fa943d',
              '7bce2fa5-e73a-4cf4-bd43-e8a4f6babc72',
              '751740a6-890a-481d-8078-134cb5134646',
              '12842044-558f-47a2-89b0-0d4587769329',
              'acead09f-7b15-42fd-812d-888b58139298',
              'b009f571-7cfe-480f-a8ff-ffd2869b05e3',
              '8ef56d58-2ea4-4da0-bc9a-b3061b51f770',
              '332bc03d-4faf-4483-98db-7ad0c07677ac',
            ],
            data: { revision: 'adsum bellum varius' },
          },
          {
            id: '2d20b95c-1e2b-47ab-8d5f-bf625393875a',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: '9888bcd9-2bd3-4e09-824b-1ae6c8e96220',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              'a641d14d-38fc-4305-80ac-9501f208f9a6',
              'ca8a44e0-8984-4815-8651-4d524c891a9b',
              '59d542b6-3665-4f83-8efe-2697cd05d7fc',
              '51aea4c8-5c6c-41ce-8b23-2bab59c8e7a6',
              '452ecd8a-ccc9-46e4-99fe-f371d722c88b',
              'f4665267-cdab-493a-b0b1-e0fcf012e13b',
              '210a8661-61cb-43c7-8292-79c4d5d4b7eb',
              '40a8f968-62f9-4ffe-9d01-54d30c9aaa36',
              '6471094b-4625-4ac1-987e-5668c913111a',
              '68c381eb-3b02-4d62-bd02-e7821d840901',
              '572a443e-1767-4898-a3c5-94660b6a52bc',
              '5cb2c251-5bbe-4364-902a-622e7ba25604',
              '36a20587-a17f-4932-a8f3-7e0de9d93433',
              '0c20d7aa-c606-4ae0-bf05-3b8c9c205246',
              '0015a019-d47f-494d-9613-70ee84fc85e4',
              'f55ea19a-cd9d-431a-9e8e-bffc8d04690f',
            ],
            data: { revision: 'denuncio colo audentia' },
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: 'eab0c7b3-a09e-41a4-abf8-a9528b0875d7',
          type: 'POD_DRIVE',
          productCode: 'derelinquo vitium cubitum',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          upfrontFeePounds: 99,
          discountedUpfrontFeePounds: 45,
          monthlyFeePounds: 35,
          contractDurationMonths: 18,
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2024-08-29T03:55:42.677Z',
        },
      },
      {
        id: 'c51031bc-71e7-4c3a-ba6b-32680ae551de',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'SUSPENDED',
        order: {
          id: '20d45086-d026-4eda-9087-14d9dd9302eb',
          origin: 'SALESFORCE_TEST',
          orderedAt: '2025-01-01T00:00:00.000Z',
          address: {
            line1: 'textor accusator casso',
            line2: 'comitatus porro atque',
            line3: 'acies fuga depulso',
            postcode: 'arca assentator defendo',
          },
          mpan: 'assumenda dolor desparatus',
          firstName: 'Darlene Ledner',
          lastName: 'Janice Schinner',
          email: '<EMAIL>',
          phoneNumber: 'bis acervus pariatur',
          eCommerceId: '19c8a404-8b41-4005-89d5-fb5467df988d',
        },
        actions: [
          {
            id: '9f932636-d51b-4bda-bb45-f1071f14b4e3',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '88496e31-c0ae-49dc-a14c-83198842b50d',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              'ca6105ea-ca04-42fc-9230-8f06645a5963',
              '183d4050-3a4b-41a9-bdf8-bff8af571d3c',
              '930ea049-a161-4038-9b74-260d77e254df',
              '44b775f7-88e9-4e1f-b379-e0b52babf54c',
              '10e082b2-0a1e-4ff8-9cb1-a8064569f4c4',
              '1c8c3abe-99ae-42e3-8a3d-666c0d840025',
            ],
            data: { ppid: '55d7c37c-9b5e-4bcd-a340-10b32dc809e5' },
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: 'a6b2c6bb-a90d-4bf2-aa67-0e4c70b9031d',
          type: 'POD_DRIVE_REWARDS',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2025-07-10T23:47:33.889Z',
        },
      },
      {
        id: '7a89a9e9-6fba-4cf4-8eac-ce8e3c71fa36',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'REJECTED',
        order: {
          id: '4fcfc9bd-3363-477a-9d52-ccfbf59f119a',
          origin: 'SALESFORCE',
          orderedAt: '2025-01-01T00:00:00.000Z',
          address: {
            line1: 'asporto auctus validus',
            line2: 'villa vesper vacuus',
            line3: 'viduo benevolentia trans',
            postcode: 'astrum ab territo',
          },
          mpan: 'compono vestigium undique',
          firstName: 'Eric Keebler MD',
          lastName: 'Barbara Fritsch',
          email: '<EMAIL>',
          phoneNumber: 'laboriosam adversus tamen',
          eCommerceId: 'afbb47c3-eaaa-4bca-873e-29a5a8041f2f',
        },
        actions: [
          {
            id: '68b524db-fbe3-4ec1-817f-0df521e41c7e',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: 'ec3fd131-c72f-4b91-b1b3-509b43c1cafc',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              'ecb88995-8985-4232-8e5b-7aae9cb070db',
              '866ae21e-89a8-41de-a60e-cc193ef4959a',
              'c7b8908a-80a6-4854-8e72-0333d448c067',
              '98ad726f-005c-40fb-afa1-55fc3ada73ec',
              '3bbecb16-b2e9-4cd1-85f6-e8e8d7fd9ed7',
              'b90e33bf-ea9d-46e0-a590-8d6f871b2d89',
            ],
            data: {},
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: 'b2f3a250-566b-48ed-807f-fe63fd42a32b',
          type: 'POD_DRIVE_REWARDS',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2024-10-04T12:17:03.128Z',
        },
      },
      {
        id: '615229a8-aba7-48ae-b957-a72bd59a7f0a',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'CANCELLED',
        order: {
          id: 'c0dc1dbe-ce76-479f-a06f-88bbcf3e2069',
          origin: 'POD_DRIVE_REWARDS_SELF_SERVE',
          orderedAt: '2025-01-01T00:00:00.000Z',
          address: {
            line1: 'stips vulariter aegrus',
            line2: 'subito atqui mollitia',
            line3: 'ascit porro civis',
            postcode: 'magnam balbus acsi',
          },
          mpan: 'abstergo substantia contabesco',
          firstName: 'Adrienne Auer',
          lastName: 'Bradley Schoen II',
          email: '<EMAIL>',
          phoneNumber: 'caecus theca argumentum',
          eCommerceId: 'f02057e9-f9b8-42ba-84f4-5fc4fe3a8e44',
        },
        actions: [
          {
            id: '2d62c94a-ab62-4b29-98d8-0649cfd1b44a',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: '18294152-19bc-4b4a-ab3c-c2fe7a2b5801',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              '690545ea-be48-4d88-a5c0-e488bd6a7bf5',
              '9f140161-5c88-41dd-b753-dbc15645e17c',
              'd4f1d703-f092-42cd-922b-69ebfefcdb7c',
              '30f09565-04c9-4cb7-937e-02533f144d88',
              '9c840274-791c-44ba-a0d6-c24f2b70714f',
              'a6352f9b-de28-441d-88cd-0b0a76dc8c44',
              'b0105126-2f94-4e4c-bd3f-961b19b1ec2b',
              '5d22f8a3-c32d-4096-8fb6-faa48b1d5aff',
              'a3fa344d-ecaf-41c1-83f6-dc26bcaecee4',
              'c0ee01d3-a34e-4792-970b-aada923c3741',
              '497a7b80-3af5-46e6-8d86-da8021eade2b',
              '529ccc41-e60f-4438-b55f-17e1f7c4847a',
              '0828e0bc-9bcd-4efe-9a77-56a6dbce0973',
              '3a966af8-2ff4-41b2-b286-b97c116b8708',
              '41c625dd-4ddb-40a7-9e77-7009e9021a95',
              '43bc3851-c7fb-4eb2-88ee-eb044216f10e',
              '596ecff8-b423-4eeb-a551-066ff00e3dc1',
              '8a90c2bb-9270-44a7-98a9-dd99dba6ba6e',
              '9b827728-fdc8-40ac-a84d-731c8ca2675a',
              'a4c17f7c-3342-454b-9b42-2dd7d7b0e426',
            ],
            data: {},
          },
          {
            id: '28b4c471-8e7e-49f1-8b62-77fa02277136',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: '22ec18fe-c484-491a-8efa-f12a5ea42ec9',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '5224bb87-45bf-41c0-8725-9bf347815d12',
              'b340f342-09c4-4118-ba22-b453e83247eb',
              '4e2f5035-7d18-4fb7-9c18-9e0645f5ba5a',
              'c7bb0c0a-3db5-4916-a432-0b0adb341ff6',
              '76d0eb89-79e5-4eeb-907a-ee8f8e00b4ab',
              'c9d21ac6-e384-4609-825e-ad4b30d2cbca',
              '36f8b187-2009-4436-b086-4906a38905b5',
            ],
            data: {},
          },
          {
            id: 'ef466006-54f4-47a4-85c4-7d3f23b38230',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: '87cb6d76-bf93-42b7-a32d-5d4a39f4078d',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              'fcf77435-3f5f-46f7-b136-63ff3dfc4c97',
              '638c85c9-ad40-4d5c-bfd6-1791d3763003',
              '8453c7d9-f8f0-43f8-b32d-82fac98e8ca9',
              '6e0984ad-f184-426c-8748-e8b184a7822b',
              '91fd93fc-3d3b-43cd-a95c-425b0b27e6f4',
              '3ef8b0a5-4aa1-47d4-afd8-d70d41986990',
              '2982d46f-9d0b-43fc-b143-888c19555c30',
              '910e6c4d-5090-455d-a04c-63653858c4a0',
              '41f43b14-700c-44b1-80d8-c58c943dbee2',
              'ee1f746e-2c17-420b-8ba4-fa7076b62763',
              '682cb449-959e-4fab-9421-0bab13b4abaf',
              'a3f7be94-1267-4b16-af8f-a336e314ed85',
              'a608701d-5d92-43e4-a336-c0a05dac49b0',
              '948e5390-129e-4420-a4a6-3d5d1cff18df',
              '61c20adc-5a9b-416e-bacd-4c90e5c4e513',
              '4982e233-0906-4343-8203-0161da7d39e3',
              'ee91989f-3c1d-482a-8245-c4dc5d93061d',
              '52f8d503-f2dc-47d5-b435-e03bcd9c20fa',
            ],
            data: { revision: 'tepidus adficio thermae' },
          },
          {
            id: 'a59fe68c-aec3-4ece-9876-b1d4dd8c12a8',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: '27bbaefe-d281-4f47-8637-173a26700223',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '72077535-de5f-4f8a-a081-0177ed771389',
              'd8ff080f-a02e-4842-bca6-913ae5676b36',
              'be3c0c29-1572-450d-8d9f-d63b16c64754',
              '101e532c-14dd-44f8-a288-d21d9bc16780',
              'affc0ad7-e9f6-41cc-85ff-b89c1fc4fd75',
              '9d1558a3-96b7-4147-9212-7f54990b6c4b',
              '92366c74-e078-46bf-b3d5-1661f7413bda',
              'af8ca61d-de48-4022-b138-80c44aa4be1d',
              '9e9b49f8-eb85-4fc9-89e8-3963c22f748f',
              'af33dc73-7374-4426-981e-aea1b2b6e379',
              'b759458c-4304-494f-8723-ade7bc2b7a6c',
              '63ccc4fe-424c-4522-88b0-bd35060bf570',
              'caa8e506-d102-45d2-a56b-0555584ab7ea',
              'a11992bd-13d0-4f96-aaa0-9cf798c1aa1c',
              '5cc2add2-ec85-4c56-a289-cf17438c504e',
            ],
            data: {
              documents: [
                {
                  code: 'ha',
                  signingUrl: 'https://sad-minister.com/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://scared-meander.org',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://far-off-sundae.org',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://spanish-junior.info',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://infamous-granny.org',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://that-tenement.com/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://winding-diagram.org',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://unlucky-graffiti.info',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://fruitful-instructor.biz',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://usable-signature.net/',
                  signed: false,
                },
              ],
            },
          },
          {
            id: '763d91a9-b171-4eb7-820d-9df66291c8f4',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: '026a1b26-aa7f-4302-b0d1-8134ce0b024d',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              'e919961d-8377-46e0-b574-61953f695629',
              '7bb6490d-b813-4683-a429-d0f65e9421da',
              '51ee8372-9dcb-4e8c-931e-274cfcd184a7',
              'c698c784-b9c0-4693-98f1-81853475779d',
              'd02a96f2-a1cd-4f27-8fde-15cab6dda414',
              '5bf936f5-a29a-4bdd-a2b6-2b52204d3e3f',
              '257b796b-afa4-4b59-9480-0143158ec2a2',
              '7aa42156-cc54-4043-9228-bd11e6bef8c9',
              '599b566a-9e42-4d75-b512-66cf7a07fb6d',
              '2a1866dc-c6d4-4505-9e89-a8984a3081d2',
              '4c5887ff-7233-45b0-bab4-4e021615b48a',
              'cd36e435-743f-4c58-ac66-d9e0f1c7480e',
              '953ebc26-3532-409c-8445-dae9037e70c4',
            ],
            data: { revision: 'tener ad suppono' },
          },
          {
            id: '268f1cff-238a-4af5-82a8-3f859063c5af',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: 'ff049cea-0e62-47ec-87ed-944fe1502510',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              'aa6e4b14-d4b7-4355-8f80-155173271ef9',
              'fa7019ec-7ea9-4dda-9d23-4e3884834678',
              '9a92b5ae-1595-4810-bec2-e6e9920c1249',
              '5c66d759-acc2-42d6-b62b-b61752751f04',
              '0c894d37-cf14-467b-9120-e0cf04cd025b',
              'ba700366-5220-4266-b2e3-ee1920416c03',
              'a2a7a94c-f161-46ea-b919-69802f40ba25',
              'b343a2b1-5118-438a-9aeb-954a757297b2',
              '977b35db-0617-466e-9e4a-2bfa5f9cc181',
              '771cd3b1-cd60-430f-ab47-cd5724d56113',
              '84de066f-daba-4a7c-b3c9-283200c0d62b',
              'fb7a9327-c24f-47bd-aa22-51e067782282',
              '5cd755c9-46b1-4958-9ea9-e5e9ef184e34',
              '5cabf782-b13e-4339-83b6-89246517e131',
              'd76425df-c15c-437d-9d71-ea950519f93a',
            ],
            data: {
              documents: [
                {
                  code: 'rca',
                  signingUrl: 'https://flustered-spear.net',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://squeaky-suspension.biz/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://faint-flint.net',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://elderly-instruction.name/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://mediocre-coliseum.info',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://specific-peony.net',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://yummy-bid.com',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://unlawful-strait.org/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://selfish-role.net/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://firsthand-dream.name',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://mushy-designation.info',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://tedious-palate.info/',
                  signed: true,
                },
              ],
            },
          },
          {
            id: 'e51b045d-979d-40c9-87bf-5f00aff55fda',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: '192cc20c-f109-4b36-ad0e-41b4b4859191',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              'ff9e8054-f480-4abe-a23c-a1d089282b36',
              '118c40b0-c8be-4b2c-bc37-de9e57db8105',
              '4fe31031-760b-4f03-b1e7-7cf26709376b',
              'a83c910a-fc7f-4b21-8b36-18bf3ebef704',
              'b230e2b5-8c00-4d01-adf8-9dc1ef1f999a',
              '56419e72-7b6c-48c1-80eb-35a17ebdc79b',
              '58b2b228-5877-4afb-b68f-c18d72992db7',
            ],
            data: { ppid: '3ec6ee5e-25ce-4612-9e95-790cccc5fb8c' },
          },
          {
            id: 'ab78d134-24a8-487e-90ed-e8e83e47d51f',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: '75477d13-825a-40bd-a69b-8b2a4ca0b23f',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '918966b6-975a-49ee-b3d2-6760d678a55d',
              '1b918f68-c6d0-47c2-a8d4-c12bc5e01186',
              '04435891-d6af-422e-835c-486e871ccf4e',
              'dd3dce3a-1b4e-4617-8fcb-9b7e3ec183c7',
              '6ded6445-848e-4047-9ed8-2283f6f4a19c',
            ],
            data: { revision: 'explicabo alienus admoneo' },
          },
          {
            id: '3569d569-a8a7-4aaf-9d5e-879af0d40d93',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: 'a6fb3cbf-e73d-4401-8c71-b2f81796ca77',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: ['dbf10cad-5926-4811-b361-c443f6dbc3a5'],
            data: { ppid: '79a46489-4fec-41a4-9cdd-757ceafe587b' },
          },
          {
            id: 'fdd11f80-26d4-4297-bbba-459c820cc82d',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: '6b477952-edb7-4edc-9567-b9a4a4c512b8',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              '3337796f-309d-4aa7-ae9e-1eb28dc355f9',
              'a5b6c741-cdf4-4d1e-ac67-365d89a1e1fb',
              '841cf6ab-f8e1-4ddb-8fa3-a43464decb7c',
              'd91d836d-93bb-45fe-b743-aaf99353348e',
              '79d1bc58-43cd-44c7-9387-42f4828e1c5a',
              'c16ca0d2-dcba-4814-bc8c-f2f1b2a23a43',
              'ac3a9c61-2dd3-4666-903c-c8a24657ec4c',
              '4c378209-1edc-49c9-9aaa-7aae9f7b9293',
              '440f3168-f438-4466-90d8-4c5cd3876dee',
              '805bffbd-2251-4c1e-94d6-f55d176d45f7',
              '47fbe10d-e4a5-401d-b071-0ba5c33631e1',
              '10b8b889-7c48-406a-8b88-f50dcfd4bbcc',
              'fdaf4e4a-3795-4a37-bd19-0a182608863d',
              'ad774c96-fbd0-4e84-af65-b6368df52553',
              '9f63c640-3479-4623-935e-fceb5687d135',
              '5f5eebaf-55a7-4209-87e8-63f32b1fb74d',
              '88ea891d-6709-4b64-8bbc-e63bbfe5b81a',
              '2e673ae8-be0c-408a-a023-90a9caf0b099',
              '075b865b-6b3a-43bd-bb63-efc6e4090b7b',
            ],
            data: { ppid: '4aa17676-dbc7-4f4d-9001-60e8966474ac' },
          },
          {
            id: '9275e848-3e53-4e18-9277-b7631d04d726',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: '9308acd5-19e2-4fce-a923-3676d51ef078',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              '8c192b04-0442-4595-b78e-de1f2a4e04f4',
              'e68cbf4e-bdbb-442d-96f0-821e2b9bc5a1',
              '54945b68-2ba8-4ea0-8cab-04ad6992d3bd',
              '96968f66-0e92-4a7a-bb0a-48a7ee4ba5c7',
              'd80a6c26-c7de-4a4d-a42a-5db9a918c1ce',
              '0945ec98-25bc-474c-95a9-2f505ecebfbe',
              '1755b38b-7827-4148-8681-87eb83f6868a',
              '2f10fee6-fc4f-4bd8-9319-88cda382c2b0',
              '8d23f1ec-43f1-4798-b1f0-04f6c51a2ee6',
              '77b9a553-4b6a-4433-ae8b-133a2ba21abe',
              '262c9d15-8bb6-404c-a77b-54bf2c3e4b16',
              'b945c14e-db29-4967-b830-cf224d3e3805',
              '9876656b-cfe5-431a-8599-9a84fb19f0fd',
              '6e74e8d9-4a14-475f-b7a7-5e3298400e98',
              '7aea5d9a-a917-4247-ae7a-2e19b5b3abe7',
              'd216a0bd-4fa1-4561-ab18-7ffed5b8bd06',
              '3338bbe1-5595-4443-b193-4ec1d2a1c423',
              '8374c202-daa3-46cd-9d0f-efc87ae2f6db',
              'afc5a560-a85d-4da1-a35f-0e4ccd1ddb39',
            ],
            data: { ppid: '4e8cfee4-0669-4b3a-8170-907648660115' },
          },
          {
            id: 'e583b283-c162-4af9-9d42-188baf1c4fb6',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: 'a3ea496c-a589-4e9c-9f27-b38d98c4c4b0',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              '97fd6b85-bac4-4e7c-a222-b8d9d3a3df17',
              'c6f3280d-56cd-4937-81e3-352e346fc10f',
              '274447b7-a3b1-4f8c-9718-4f7fd8aff202',
              'f5da4212-0fca-4e08-81ca-fa9e7cc456dd',
              'd4b2770d-009a-4bcf-aeba-91c375e6df35',
              '8e28b8df-8d7f-4630-bbb3-913cc17cd15c',
              '1a4ad1b9-b30e-4985-bc84-b3690b09c3be',
              'fd5c74e6-edb8-427b-ad61-b29b7e2f31e7',
              'e202ae88-f487-4481-b06b-bc2372268bed',
              '113682f2-e859-4ee4-87c2-9cc197b2ca11',
              '02ce5dac-30bb-4534-94cd-7ccb5d51a4f6',
              '9a94955d-329a-4cb0-9ff5-60fe2b05f44a',
              '4f6bb53f-018c-4e9f-bb0c-2629f4885ea8',
              '1fab842c-bfd2-411c-b7e8-91a79923f93e',
              'f22eaf52-fdc6-4b4a-a0eb-adca21844cb2',
              '53edb92c-0166-4dcc-bb3c-ebf110c7a375',
              'e41023f7-fd07-45fa-914b-b48feb6509f5',
              'a691df16-cec9-41f0-8387-88568209eb55',
              'd6a69364-b010-4d8f-8cda-4fc31a1445f7',
              'a466ff0a-51f3-4981-bb08-fb1ecda17919',
            ],
            data: { ppid: 'b41b8669-c661-499a-89a0-a567b9fe5467' },
          },
          {
            id: '1e8c6920-3a95-4f9c-a478-a140cbd63cc1',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: 'a6973c45-c358-4517-a57f-274db2eaa6d8',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              '43cefcb2-bf2f-4769-9741-6d3cbe9774b4',
              '4ee6ba31-9293-44fe-9888-c676e31d3450',
              '15640d3b-7dc2-462b-bc21-818cc22c46ce',
              'bc85d401-820e-40d9-86e3-5f926f4e4229',
              '8b5d05a3-81a9-4686-9db3-94168ce1f7d1',
              '556f31a7-fdc5-45e8-a7f4-bb0badb093a1',
              'e33643bd-4a0e-460d-bc5e-69c146123622',
              'ece31779-b71e-461d-8e65-8fafa4fdda68',
              'b92466cd-17e3-4b26-be8a-853eccef4e0f',
              '65a91ace-64be-4026-81e7-e9c20feaac67',
              '4504e2b7-9072-45c8-98bc-2285093c6c1e',
              '9d0e5f85-8397-48d8-9593-29c50769af5a',
              'f152057d-2840-4300-bc69-4dac36aafde9',
              '0509fc09-117e-4f0a-b906-d801cc9627f0',
              '55f3dda1-96bf-46fa-ad8a-766401e82ea3',
              '64eb54ef-ebeb-414f-a279-99004158a55c',
            ],
            data: { ppid: '0d96ad23-1975-4d35-8d23-9b7a623f9ac5' },
          },
          {
            id: '80047978-3ad8-4b19-814d-503d37b1c1b4',
            type: 'CHECK_AFFORDABILITY_V1',
            subscriptionId: 'e7a23fef-9012-4055-a277-ba224f053f00',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              '09758293-6fe9-4a4e-a5ef-2272cfa57e76',
              '62d57d9b-c88e-47f0-8d63-9e5e0a3e0eea',
              'c6f45a08-e1f8-47c3-a915-1c5c117b4739',
              'bd1ff8ae-244e-4a91-9aee-27bc6dc22163',
              '0cf6dd56-4803-4e84-8300-b4fbf3a756fa',
              '96583b0b-2687-4013-8349-1f2136bd912c',
              'fdbf2e7e-5784-46d0-ab21-8a0f901acc09',
              '8c50077f-2c64-4bea-8ee8-96b1f0e60156',
              '70711288-00b5-4831-9353-e7e7f33c60e6',
              '1da51b50-7e7c-4201-b1e4-0052c2cbefde',
              '635c25ca-b974-4aef-b8ab-e038c0243c74',
              'd1cd444d-ec82-4649-ad8d-591affd20863',
              'c26eae19-b23b-48b8-86dc-d8679a16e508',
              '5ba51dd9-9ff5-4cb5-9ea7-0cf8dd1b78d4',
              '7cf6a560-3a03-48e9-bc4f-a187a226b469',
              'ea89cc82-06c2-414c-b566-faf98601b8fb',
            ],
            data: { applicationId: 1131403239142046, loanId: 6757220571498229 },
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: '11c039c9-a0b2-483a-87a8-d5f954807893',
          type: 'POD_DRIVE_REWARDS',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2025-05-22T04:08:37.535Z',
        },
      },
      {
        id: '2cbe79a3-cb23-4401-af83-9954d29ae951',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'ACTIVE',
        order: {
          id: 'c4a6a1d5-7a4f-4da6-aa99-e4a9d25027eb',
          origin: 'POD_DRIVE_REWARDS_SELF_SERVE',
          orderedAt: '2025-01-01T00:00:00.000Z',
          address: {
            line1: 'defetiscor assumenda apostolus',
            line2: 'admiratio audeo aestus',
            line3: 'dapifer harum bardus',
            postcode: 'tubineus celer titulus',
          },
          mpan: 'colo valde vulnus',
          firstName: 'Dan Wilkinson II',
          lastName: 'Opal Funk',
          email: '<EMAIL>',
          phoneNumber: 'synagoga audax thesis',
          eCommerceId: '41c10f8b-b4be-4d99-a515-547030aba759',
        },
        actions: [
          {
            id: '99f62a8f-c193-4db9-ae5d-027cd7076e71',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: 'cbf07bf5-6ff8-4330-9b3e-f8db41e5b7d2',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              '12c05934-fbfb-4536-84be-90ed9f8a3d7f',
              '10d9896f-d789-4b28-bcc9-16a4b8650bc3',
              '73cf5978-d6d8-49f2-bdac-e995bdab6b12',
              '787514a7-c7d3-4317-8440-65c75fa94abe',
              '4d657f35-793b-4af2-8c1b-e173c9f7fd66',
              '89d7203e-8416-4516-9563-f72557a12010',
              '31d0c282-9ceb-4c74-8873-ba4c0dd153bc',
              '67832ce8-d70f-4bbf-a740-e74e07481c0f',
            ],
            data: {
              documents: [
                {
                  code: 'ha',
                  signingUrl: 'https://agreeable-pantyhose.org',
                  signed: false,
                },
              ],
            },
          },
          {
            id: '92840ab2-ad05-4c17-ad27-3d43add0daf8',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: 'f5ddd11d-14e4-4bc7-aa9a-17f81fb6dce7',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              'f4b27d7f-6f9b-4f6f-8015-b8c7df75744b',
              'fb438187-c856-452d-a6e7-bcafd98dedd6',
              '43e198b9-c11b-4da3-a77b-e68ed1627216',
              '4e22ac09-0920-4163-8d9c-282bd9d46dcd',
              '9592c54c-3578-44c0-a852-e7631cfb1ceb',
              '17e80cf1-bbee-4a6a-9383-5ec0e06838c2',
              'a09b54c9-c420-42ac-8d82-89094ac80e0d',
              '87e6e716-e29a-4469-8abd-dae3a7d8c1be',
              '5841bcfd-a317-48cf-9e4d-09cbc180271b',
              '7f433ba9-e3f1-42b8-85a3-27c426d3b335',
              '97bea953-4f33-4e90-877a-87421c268e97',
              '13bcea4e-f4e0-4f3a-a1f7-1ee391f818c1',
            ],
            data: {},
          },
          {
            id: 'd62a1926-bf00-4391-8ce4-3b719bc4d11b',
            type: 'CHECK_AFFORDABILITY_V1',
            subscriptionId: '9135fa40-4506-4156-9a60-349416bf099e',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              'e6d7306d-7184-46f4-ab0a-f50f20ddde72',
              '326e1b00-1550-4369-8426-2465bddf2964',
              '479ba3b5-9f71-4948-9a22-7827871c9987',
              '3200d745-f177-4435-a011-539fbf8cbbf8',
              '1016422f-6eb9-4aac-83b8-914b8f30980a',
              '615fadc7-7201-4078-bf3a-797378a557e2',
              'c8ff16e2-5754-415f-a451-84c3eff3d70e',
              'bb1ef7ba-1022-4f76-aac0-c1f78fdaf65a',
              'b5121ae1-5519-49bb-9cf0-5154b9b43674',
              '4dbb6fda-06f7-4d20-be9f-479442716ba0',
              'f62c76a5-e8da-4764-880a-b7bb72f86a1e',
              'a272b784-a650-4bd8-9e29-03290c3149dd',
              '61130659-af2a-43e2-9221-f25a0a01d4cd',
              'c9d7167c-ca3e-4213-b747-0ade51d91b6f',
              '3c42c826-816a-4fc0-94e2-59f0557c73c4',
              '40c57428-fa8f-46ac-bbed-ca419d162918',
              '5c135cb3-0dcd-42c0-98af-f309900d8f95',
            ],
            data: { applicationId: 2031755191324893, loanId: 1154537457507980 },
          },
          {
            id: 'fbaa95f4-0f64-4537-b107-dad4a7266e32',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: '4ee7357d-503c-4546-b4e2-a85e4902b781',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: ['3eec83e0-193f-45c8-8188-185bca610191'],
            data: {},
          },
          {
            id: '94934cfc-f791-4345-87da-afe586b90d5e',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: 'e83c7890-1f04-4218-a9cd-0cead87165c8',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              '52f3dbc7-ab72-4206-81ec-c0c6437e15dc',
              '009e1e5f-fa14-4e24-b543-c8589413491d',
              '5f91f763-5ee2-4b7c-a5c2-fd09c5364764',
              '8f49ee25-d530-464c-b911-0f258a2cf41c',
              'f404f6cc-2e00-460c-b3d0-916db8a8cfe8',
              '3b9184b7-9cd6-4568-9b7b-d4284176331a',
              '0af28603-c775-436a-926c-a0111b77609c',
              'e58f9a39-10f0-4478-981a-67c1a6ea4d6b',
              '677ae911-7fe6-4239-9312-dd6bde1d5339',
              'c3ecab07-57b1-43f0-8a4c-b5ef524f389a',
              '4fdcc079-6d41-4827-b383-fa309560e0b8',
              '3960a6f9-22d9-4c6c-9743-a165496bf1a8',
              'ff2068b1-3062-48a9-9e48-cfeeb9518950',
            ],
            data: { ppid: '12e0f47b-9fbf-49a3-86c0-c4858cb244f7' },
          },
          {
            id: '9011ec27-106d-493c-b0b7-63d89765adc7',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: '749b4b40-9cd8-47c6-a835-82f21f2c5dc1',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              'dafed9ae-b118-4c30-b89b-3657c326fb24',
              '911b4a62-621c-4cc4-bd46-e4ffa7bcfaf3',
              '936e8200-33b9-409e-ac45-63d300ec11cc',
              '2cdf1a29-7502-4822-b7ad-dcff29f80958',
              '9c942a66-afe0-4ba6-8675-e1bd314cc8d5',
              '0d20dcb8-904f-4625-aeed-814961af5875',
              'fc971079-b84c-4901-bda1-48ce6d20eb22',
              '314bc841-9e7f-48f7-b668-97b1af48ea98',
              'fb5d9a92-03ea-4c3c-9c40-b4a02e86cbeb',
              '49a86556-a507-4f1e-85d0-8fa30585780a',
              '0fd9face-dc0b-4e9a-864f-53ddbfeb7541',
              '4bdb5772-5558-4378-8bce-71504f2f1209',
              '653c38fb-c912-469f-83ce-fd01745f1ea2',
              '7523bfbf-f345-4fc5-b64a-45967d3fe2c9',
              '2240f4bf-b830-4bfe-9724-1afb964d0a3e',
              'c151d374-6c86-480c-acd0-3f2eda7f3c5b',
              'a5ae9713-7b2e-4725-8764-18cc6e0a9031',
              '0719e0b4-ca01-41ff-94bf-452148fbce38',
              'df4d8df0-8219-47d8-adcf-908356a98a94',
            ],
            data: {},
          },
          {
            id: '23ff79d3-c5b5-4541-b235-62587e0abdb1',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '64b769e9-1aa3-4df8-8131-23d8d75f347f',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              'efa45882-ee3f-4044-b8f1-b52f841d75b1',
              'df6ef795-c7fa-4bbd-8127-0bd6579a0510',
              '9507a7ca-ca42-4892-88ee-be893a8c29db',
              '4c6c6964-637a-4f4c-8f14-e2fdf28a605a',
              '1c28f0a9-19e6-4cca-bfe7-888775c14b09',
              '2d51812c-fde2-4a26-b0e9-dfb626a182f5',
              '888a1806-deeb-44da-9425-15da98538dda',
              'd25cdf83-2db7-4a2c-91e9-f012db15d56f',
              '97eea944-c69f-4d0a-989e-f4dcafc15014',
              'f72f0cca-777a-4ab6-a94d-0a44f7ed18dc',
            ],
            data: { ppid: 'fd34510a-2960-4df5-8c46-a0a080ee8aea' },
          },
          {
            id: 'fe23f425-4672-4427-9517-475c41d51bae',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '9e65a8d9-b9c4-499d-8210-da10f6ed1265',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              'a1cf5158-90e3-411a-979e-17c4453199fc',
              '5bd96660-5068-4598-9c12-314e0f06cbf0',
              '3f8b9885-f72d-4245-b022-19392c77d21f',
              '6b99ffb6-5152-4c3f-995d-e5c957d4872b',
              '20f73c61-3542-4899-8d7d-04678a4fad40',
              '43b552a4-33af-4494-837e-aea3e43d30ea',
              'd6347bed-2ae0-474b-aa46-2a629cbd788c',
              'aa56fda5-712f-4d09-9cd2-807fea4fbde4',
            ],
            data: { ppid: '94f140ea-ef79-48c0-a895-b6659ae69a06' },
          },
          {
            id: 'ac1c5383-f57a-4c95-aa09-efed4fc59873',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: '77b4857b-6093-4179-aba3-4bd3a4e5417d',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              'ed994fbd-21c7-4911-b262-5f2276695e68',
              '27367570-5c14-4d04-a610-6699d9267aff',
              'bd209139-44bb-4706-b281-216d4cbf40cf',
              '92290cd6-91b3-4d96-9bbd-4f7278865d34',
              'f61af26b-9df2-4afe-bc72-ac6ec55b801b',
              '5918db3e-c4e3-47ee-835a-11901072ef4e',
              'ea8ef416-f007-44b0-a1c6-10d1cdb2a8d6',
              '639cf4a7-c669-4af8-97be-6186f3303594',
              'd8fc75bc-8020-46e8-8f82-426f9243f4af',
              'bc5ba3ea-5db3-42f7-ac1c-c9570c4044c5',
              '5f3d4062-bab3-4097-8c07-503ed6f83e96',
              '8ff22ba4-1246-4e27-80e2-80f365fdff15',
              '295f9e47-58ad-4e54-9150-59a664404cf2',
              'fc997a7a-f4bb-421e-af2a-eb52053bf286',
            ],
            data: {
              documents: [
                {
                  code: 'rca',
                  signingUrl: 'https://rich-subexpression.name/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://strong-spirit.info',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://frightened-eyeliner.name',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://gentle-gym.org',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://grouchy-whale.info/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://unlawful-daddy.biz',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://political-fun.name',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://essential-switch.biz/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://puny-toothbrush.com/',
                  signed: true,
                },
              ],
            },
          },
          {
            id: 'b90c5c8a-b182-4a3e-81ea-9c02bfb38942',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '8a28d89f-41e9-43ce-80b3-0798a7e3c8d6',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              '82b1a8af-af03-4705-8130-a7429864fdc3',
              '1d9cc0b7-8bd1-467b-ae1e-b57701f9ae9b',
              'd38622a9-284d-4cb3-9721-35b507799574',
              '36331acd-60ea-4b84-9ed6-98f404a4a278',
              '166c31be-6a99-4a82-9a4c-fcbc4c02a863',
              '1c68319f-e30a-4aa6-9f9d-8a3a5b928ceb',
              'a5cea412-98e0-47fb-bb14-3a04bbd29b77',
            ],
            data: { ppid: '72021fc9-fa12-43cc-ae98-87c893bee6a1' },
          },
          {
            id: '26cd38ce-dc76-4421-bf88-48a126eef589',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: 'e451816e-ca1b-4ca1-8614-12d682cfcb1d',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              '70af2187-9323-4642-8a93-e646b3b4f888',
              'ecbe7ae5-bb99-4596-86c7-0325f627bed3',
              'ed04f47e-0da8-41b9-b859-f60e17ff1d1e',
              'e56a117f-beca-451a-9189-620ee7f3c198',
              '1dc09101-3b7a-4950-bda7-aef7d3ed4f8d',
              'f11c24b6-99ba-4023-a786-ba8c1a8e813d',
              '308c6e98-85ee-44c3-b0b9-8c8c583c7f24',
              'bcd8468b-72db-4d53-80aa-91558dcd05f7',
              '9175a048-2db5-48dd-bf8f-723566dc7860',
              '74e74d2d-bddd-4b9e-8c85-1e66deaa3ced',
              '5d5646f8-9b4b-4f9f-9b7a-127357aadaa0',
              '408eb9a6-94c7-4792-b0f7-1f486723002c',
              '441ed1c8-aeea-4fd8-9496-48fba3d75abc',
              '1b91e152-3a12-44a0-9f75-8c7fd140b8f4',
              'db0f1027-0487-4ee0-8199-35b8a75edd1d',
              'd88a8cfa-88f5-42fc-86d1-44e3a2fc0d2b',
              '57f8754c-10bc-4ca2-8d5a-b0b9b63c4a35',
            ],
            data: {
              documents: [
                {
                  code: 'ha',
                  signingUrl: 'https://kosher-affect.name',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://humble-hepatitis.info',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://overcooked-expansion.com/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://pricey-community.name/',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://extroverted-omelet.name',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://agile-quinoa.net',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://smoggy-secrecy.org',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://blushing-bug.org/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://steep-vol.name',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://spiteful-invite.net/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://content-babushka.info',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://forceful-barracks.com/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://kaleidoscopic-handle.net',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://stormy-affect.info',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://aggressive-laughter.info',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://poor-vicinity.net/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://precious-wallaby.net/',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://portly-retrospectivity.name/',
                  signed: false,
                },
              ],
            },
          },
          {
            id: '70aeea96-131a-4c7a-b739-d39e047a5cc3',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: '8f23910b-d673-4996-97ea-ed158c037d44',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              '1f0ac42f-6d95-4075-a3ea-c984f778de7e',
              '968df7e5-1804-4cd8-aecb-5d2aa2a6c19d',
              '314e0fa5-bcda-4028-b6ae-32944d6613cb',
              'b33fc5c3-f404-438e-93d5-a043a8f35c08',
              '41e630fa-91d6-4c3a-8774-5486e61e526e',
              'a386dfdb-2948-43c4-a3c2-389ca3532762',
              '8bcff2fc-a012-4a16-a280-20dab396994d',
              '9071f33c-5594-4531-9355-ba1097e3226a',
              '3227e487-905c-4b19-85dc-14b0f190690a',
              '39894aa9-8db9-43d6-b136-1d9776def0c1',
              '9b0af08b-8847-464a-94e8-3284580290a4',
              '5c20a07d-0656-4bbf-b55f-2c43933b346a',
              '5801bd07-3daf-4d74-875e-4e49da767076',
              '920c9696-edd6-4525-ac17-e9c3b032cf83',
              '86114ea2-0220-482f-8aa3-38d498b44888',
              'e9b1c26c-7a07-4233-a7c0-3ba66c73e893',
              '5faa113b-e2db-463c-a230-83f82091214d',
              '7fc01a8f-41dd-4186-8b17-d39e0c477af5',
              '9ff0d503-72a7-429a-8425-75b9dda8a105',
              '94675a10-90de-4299-b1ed-3ec514697a81',
            ],
            data: {
              documents: [
                {
                  code: 'rca',
                  signingUrl: 'https://phony-pliers.net/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://inferior-disadvantage.biz',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://fussy-wilderness.biz/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://frail-avalanche.org',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://cheerful-sonar.name',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://another-cricket.biz/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://creative-citizen.org/',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://indelible-drug.info/',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://vast-schnitzel.org',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://gray-stay.name/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://svelte-sauerkraut.net',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://buttery-haircut.info',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://messy-ecliptic.net/',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://outrageous-legging.com/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://poor-co-producer.biz',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://steel-approach.com',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://imaginary-derby.com',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://lucky-relative.org/',
                  signed: false,
                },
              ],
            },
          },
          {
            id: 'a87a47f9-0b49-44dd-a78c-c70193a354f4',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: 'a2499ead-7c28-4351-a8b8-dcb582bdac4e',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              'd3f5fe6b-6909-4652-a1cd-62fc89f965f9',
              'd1d9743c-f050-4075-8b6c-1a4e6d18f1d2',
              '42e9d9e6-5da7-4071-b8f7-f922ea015374',
              'd538a273-cb3e-4850-b2f8-9d63390ccf21',
              '23cd71ab-f0cf-4dee-8302-b457f31dd0e3',
              '90c8fa82-8d62-494a-8891-ea75cea16078',
              'aa9087da-3d34-4e23-bd15-7ad18c6f4256',
              '642455cc-87fd-482e-98bc-2a8bc7557394',
              '11959e39-2d3c-4622-8773-d3f6a782997f',
              '9f794624-e524-4004-8ac9-7032e3c3c856',
              '5482f3f2-0555-4ad8-8fd5-d7df4876a7f7',
              '044a4a46-2237-427c-acd4-9a8dd7aa7d72',
              '0bce1531-2f0f-4f3f-b55a-6cac5b8e35dc',
              '2896c944-2b6a-4cde-908b-109c83634a24',
              'eb91c6b4-a2a3-4b89-9dbf-23bf8aa38bed',
              '06752f29-d5af-4f80-a731-f449d05606e3',
              '916bbed3-dc06-4beb-902d-a3d9ee16830d',
              'c978bea6-3b23-46b4-a86a-d68be1d9ead4',
            ],
            data: {},
          },
          {
            id: '53ad74bd-865b-4c79-95ad-1b902991ab0c',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: 'bd866b1d-b6e0-40ce-8ad8-1226c80169d3',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: [
              'ff89452e-52fe-42d9-89c8-f62058ac7e82',
              '03ce63b1-6c63-442e-9794-449f193ddcbe',
              '98fda9a3-79a9-4440-9d1f-ab5bef9ce6bc',
              '9b41267c-d84a-4baf-99f0-466997de74b6',
              '8264b6da-8df3-4051-88c2-8aba4f9703c4',
              'dd5c2212-50e5-4ecf-802c-9b68c43a60a1',
              '3f196d0e-ffab-4d64-8871-1593203f858e',
              'c0a878ad-a156-413e-8ca6-80bc0f1dada9',
              '7082679a-af59-4b8d-af21-55369dd23bca',
              '0f5ab43d-ee09-4d84-b031-97f9d8f68cda',
              '4a68331a-2fe4-4eae-b10e-50d71f33477f',
              '0de8fbc5-57a7-49ff-b798-0c56f7c0ff04',
              'fc10a26c-8383-47af-bd05-4b2205d1610c',
              'ff07916e-066e-48ac-b887-bd1460720001',
              'f605997c-f9f6-41c4-88ad-1aaa5b869ce0',
              '19409ab0-2607-45f1-9e11-5883f457b2b0',
              '64faac97-f520-4d63-832b-535acded27f4',
              '2e27795d-9ece-46e5-bf2d-b111f670a997',
              '76d93234-0799-4027-af6a-6804380b2be7',
              '563d1083-938f-43ab-b611-cb6f7c2ace91',
            ],
            data: {},
          },
          {
            id: '27c823a2-a86a-409d-b40c-754652e2163b',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: 'c4461abb-97f8-4c24-be4b-5b4bb226dd18',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: ['9049f68c-167d-4239-9d95-12bd3b9493f6'],
            data: {
              documents: [
                {
                  code: 'ha',
                  signingUrl: 'https://handy-underneath.com',
                  signed: false,
                },
              ],
            },
          },
          {
            id: '5566ec44-70c9-474b-acd0-fe235ae197e2',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '6f10751f-0b09-478c-86fc-154404088f0e',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              'a6f9b878-e51b-493d-a29f-fe95058eb220',
              'b53406ec-92d9-46b2-be90-706dcbbdc87a',
              'a86b7bb6-4adf-4493-854d-52d9569d34a6',
              '2000a4e7-84a5-4c8f-aded-a9905f6d97b6',
              '7555425a-ff54-4ee1-962e-248e968da976',
              '7b854360-a2aa-4253-94fe-a5b3786820cf',
            ],
            data: { ppid: '4eef5558-acde-4537-9e4b-b58ab9cb42b9' },
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: 'fbf6e458-8899-4cd5-a5d2-01c9ac2a4547',
          type: 'POD_DRIVE',
          productCode: 'auxilium tum adsidue',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          upfrontFeePounds: 99,
          discountedUpfrontFeePounds: 45,
          monthlyFeePounds: 35,
          contractDurationMonths: 18,
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2025-04-06T15:18:16.542Z',
        },
      },
      {
        id: '8d2b1ebe-5637-454b-a521-43e9b9070f28',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'ACTIVE',
        order: {
          id: '24bc7225-4ca2-4fe9-b91d-b8b277e82580',
          origin: 'POD_DRIVE_REWARDS_SELF_SERVE',
          orderedAt: '2025-01-01T00:00:00.000Z',
          address: {
            line1: 'odit sopor infit',
            line2: 'hic ut avaritia',
            line3: 'celebrer doloribus tolero',
            postcode: 'umquam ad eius',
          },
          mpan: 'cogito beneficium strenuus',
          firstName: 'Ernesto Zemlak',
          lastName: 'Mr. Eugene Blick',
          email: '<EMAIL>',
          phoneNumber: 'natus demulceo ulciscor',
          eCommerceId: 'b1259167-2495-416e-ba79-ec1f96e5c6bf',
        },
        actions: [
          {
            id: 'f5e45537-6b16-4865-90c2-c8087ce0a470',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: 'cdf518d3-d822-4b16-a84d-22cd2dc378b4',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              '24982d5c-c2f8-4e20-9d31-c1e6910456a2',
              '3b032421-b837-4c33-bd32-c6238739f55d',
              'f509b4de-e49b-40a5-9903-32a5d44c55a0',
              'fab846e0-f054-4ed6-8c9e-ef3830f6c06c',
              'bdb9f83b-51a3-4cba-a63d-7d80c9483961',
              '77e3c2ff-8161-4ce0-9e74-5a89c967cacf',
              '34cfac0d-76d4-497b-96d5-8d5e853a1938',
            ],
            data: { surveyUrl: 'https://spherical-e-mail.biz' },
          },
          {
            id: '85c1dcb5-3db2-40e6-9fcf-6c542fbf6601',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: 'eae16788-7d23-47c9-84f9-885a058de599',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              'cabc291f-a543-4b5a-ad4f-d28741516113',
              '11dfa9b7-58eb-4335-b468-332aa0a01a98',
              '0479014f-8479-4c45-b305-31e82e152944',
              '79ed0e11-5730-4690-8792-700a6211f575',
              '2813b153-98bb-4e9b-8593-56497988ec14',
              'eea145c0-5cd6-4dc3-ac5f-538789fb0dff',
              '7e8fc369-4d4c-4166-8ae7-c0b5d0d36c98',
              '5ea71962-9cb8-4508-8950-632bfa8c68c1',
              '047aa3a0-0b7d-4e03-8dfc-ea0b49fc246f',
              'd5717da2-cf28-41bc-9360-ba93d9e18354',
              'f1071243-ab7d-4112-b580-6f2444005c8d',
              '5a7c49df-2d33-4613-b3c7-0d8e162d87d2',
              '48e6df3a-5160-4afc-ba5e-31269f1a178c',
              '5e031d1a-3d4d-40e2-9dc1-8b28be2ec682',
              '28796de7-735d-4fe1-b05d-5c9ce0c3bd01',
              'a524da44-ab0c-4c7c-9608-2e33453f4d14',
              '887b22fe-6151-46d1-88ca-be2e16236832',
            ],
            data: {},
          },
          {
            id: '24af19bc-fac7-437c-bc41-1c46a02718f5',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: 'c1b12e91-e645-49a3-a8be-4c71e745df06',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              '3ec426c7-e517-47af-9302-29a9a80b915f',
              '6b31e9d8-ba30-4251-85dc-5e684b19abbd',
              '9672bcf6-b89b-4076-84a1-4c43d7f419f7',
              'f7c622a9-65ff-4678-927a-77b13419aede',
              '10e7c45d-c56c-4185-9f17-7515216e58ee',
              '6f06cc36-57bc-4e64-a096-08255b01d368',
              'eb2c767e-ca7f-44fe-9a12-081889fad267',
              '47a54f8f-d227-4a31-9ac6-032ecb1a23a0',
              '986a997c-5bc6-40cb-bd51-6a200f5613ed',
              'fbe4f97b-f905-483e-91d0-2c8740759ba2',
              'f9b29cc4-2707-4890-9f68-575ecdebbe91',
              'ea9b4bdf-56fc-4349-8302-36f2c926425e',
              'f5dd5ffb-3062-4a76-869a-fe977cf63b44',
              'a331b4a7-97a7-4a7b-9911-c34187ef16c9',
              '90415279-664b-41cc-87ff-898efe87ac72',
              'a6f584ed-dd76-4d79-8f1c-fcae7e95f9f4',
              '41906506-b26a-4d8e-bc78-3aa3adfc8373',
              '51b62fa1-9671-44f7-82d0-4c6e68b16b76',
              '6bfab68c-1e13-47e2-81af-44479e36b30f',
              '51c05180-6494-48ca-b703-ec2268cad5aa',
            ],
            data: { revision: 'super cras deleo' },
          },
          {
            id: '3c9f5008-1430-4627-81fa-9aaad2d2ce89',
            type: 'CHECK_AFFORDABILITY_V1',
            subscriptionId: 'cf62cb30-e4ae-4a9d-ad7c-51812803b0d8',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              'ba12fc9d-f671-4b6b-8fdb-f4c23a66faea',
              '90fd1c33-a460-4324-a9d8-84d63dbc4448',
              '2b60b188-2403-4ca7-9516-8ea2c1c222e6',
              'fedbd9c2-16a1-4e85-accc-20e48601993e',
            ],
            data: { applicationId: 7501436303100695, loanId: 8799910241879184 },
          },
          {
            id: 'a021ce90-3cb6-4807-8bc4-b1bf524d91d2',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: '1738f1da-f0cd-4fe6-ac49-1422465dba01',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              'b13aa4c0-953d-437e-b67b-6dcfa46ff08f',
              '0fa2dfa3-c463-433c-9c0c-2d7f4deab20d',
              '4cd651b9-661a-4722-a937-aa8d2e51f0ec',
              'e8fa090c-8b8f-4de2-98f5-c47b8b706c8b',
              'b23e6354-50e9-4409-b0da-9156b1055349',
              '7fbc7ff3-a81b-4ada-8169-1a373daa4b54',
              '71acabb1-87b5-4c96-86b1-f27b9e288806',
              'b5b0f3d1-410b-40a8-adfb-8ce02395ec40',
              'e229d45b-fd35-440f-99cf-ddbdd8442a86',
              '5af4fd4c-f515-43f2-82d4-aea78a30e718',
              '1c7b4069-d57f-4681-8581-2b3faddac1d5',
              'de160b8e-b883-416b-8354-fbb5fd142a1f',
              '9b21bf32-b3f2-4833-8f31-980faa8a05d1',
              'c9cb9d69-30ed-4004-900d-d8af24ca0928',
              '2b4692d1-a357-4ddc-8698-de069c142b2d',
              'b891366c-2b11-4b75-863f-e99f17613767',
              'fc20925e-3053-488b-81ec-4cae3666cae9',
              'e76e463f-3dd2-43c6-98f8-ef6fe60ddb0f',
              '1ffde9df-2a2d-4e3b-9368-c46e28938099',
            ],
            data: {},
          },
          {
            id: '47ad71bd-0371-455f-b0d2-69b4cbb2a3f6',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: 'a1190953-214c-4064-8301-e882d9d82ff8',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              'd19e9283-9021-4b93-83a1-c9b214ed70d9',
              '6d7dceba-6170-4657-b3cc-95e07edd938d',
              'ebcad0e2-956d-4ab7-a8cd-557a3bd4cf50',
              'd8aeeca6-fa9a-40bd-aa14-2bc03e658450',
              '378af492-542c-4361-b501-caa1cf2f4567',
              '422cd2df-a630-475b-aa8c-bff619e6a075',
              '90012e9f-8e87-4ae8-8b80-83ec654b3c3f',
              '2ec53f95-7ea2-4615-9b12-c9ac61c78570',
              '68be41b8-1f38-4d33-975c-cfe40a465c32',
              '1caaa030-c35a-4bd1-891d-21649184e8d5',
              '288230fc-1551-4d0f-b1ac-da9ff1e9faa8',
              '59242763-dfca-4448-b3dd-4b8f719a63d9',
            ],
            data: {
              documents: [
                {
                  code: 'rca',
                  signingUrl: 'https://self-reliant-hornet.org',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://broken-equal.com/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://grumpy-cork.info/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://close-zebra.com/',
                  signed: false,
                },
              ],
            },
          },
          {
            id: 'db13a8a2-e410-43e5-b2d1-378f83146d95',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: '6b44150f-8407-4bdf-b797-4736754ba194',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              'cd3f611e-1a81-46ea-a95e-e0ac26ce5e11',
              '5b1f4417-7e8f-4d22-987e-127c8824338c',
              'ebf3c7ee-82ee-4af5-85c4-ca48280f504a',
              'b8b704e9-4f5d-48f3-8074-8a54c9653cca',
            ],
            data: { ppid: 'c41cb0d7-2d6a-41cc-9ad2-ceec28ba6720' },
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: '82f575ee-3feb-4801-8e1d-551a64b56e51',
          type: 'POD_DRIVE',
          productCode: 'sono dolores commemoro',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          upfrontFeePounds: 99,
          discountedUpfrontFeePounds: 45,
          monthlyFeePounds: 35,
          contractDurationMonths: 18,
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2024-09-10T05:29:51.664Z',
        },
      },
      {
        id: '22e2a9ef-ce61-4b9e-947a-fa141ff4595f',
        userId: '5357be96-1495-4951-8046-c2d59ba76c33',
        status: 'ENDED',
        order: {
          id: '6d362ec3-0f57-433c-b6ee-e6def0e67e30',
          origin: 'POD_DRIVE_REWARDS_SELF_SERVE',
          orderedAt: '2025-01-01T00:00:00.000Z',
          ppid: 'eb3f43aa-9e24-4a25-b0d9-9258598a4531',
        },
        actions: [
          {
            id: '9e91fb3a-31f4-465a-8a6b-4e5d0621370c',
            type: 'INSTALL_CHARGING_STATION_V1',
            subscriptionId: 'dcf4f8f8-7285-4874-8b8b-c317531e45cf',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '63976abf-4706-42e3-88f8-3689287a5dd2',
              '5b38e4e0-e25f-4682-80ac-6b1277536fc0',
              '3a5f1ce4-6850-4264-b7e4-81a1e29f93f3',
              '9b07c9d0-63ea-4511-a96b-493fa21339e8',
            ],
            data: { ppid: '92b0a656-b80c-4409-a16a-b929d486ef58' },
          },
          {
            id: 'ade17f3d-683a-4811-b8c2-37efbdad6a32',
            type: 'PAY_UPFRONT_FEE_V1',
            subscriptionId: '1230d6df-70a9-40e7-9388-58e510c8b474',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              '0a22ff74-6f7c-4c15-8b8a-4ba42bc03b4e',
              '920383d1-641c-43a9-826a-4ddf759aa3d7',
              'f263b5c4-be58-4914-aeb2-92ef21576900',
              'b160853b-6ceb-40d8-912b-8be3244bf733',
              '98d07e59-551c-482c-a589-5c2b95895f55',
              'cabf4b8b-22c6-49f7-ad18-a1813990c4a5',
              'e40efec9-da3e-4897-b7ae-0f6a25862462',
              '6d8479ea-85d8-42af-ac55-2478239aa569',
              '785949b2-7f00-4d6c-ae75-39fd168af44b',
              'bd85a7f6-3d2e-4ecd-b70f-95c02805e9ec',
            ],
            data: {},
          },
          {
            id: '30678d1d-3f82-4f4e-b705-f8c8d49b158f',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: 'b11808f6-952f-4f97-b17a-d2a0223f8375',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '17674d0e-5f65-464f-8157-47e7b00f60bb',
              'a3e584c3-61e8-4144-9820-dbcdc3a355f4',
              '9f7ef85d-18e0-4829-94fa-dd3d8f250777',
              '1984fd9d-6bc9-46cd-996d-9d4a4973d6eb',
              'a27c1db0-01f7-4e03-849c-8cd1f8a0c924',
              '87f31571-0bf1-4b51-adb7-0b66c1ec12ef',
              '9d148b37-5db7-4abb-868f-921465a873e4',
              'dd62fb1b-e472-47e0-b45e-e96453b5206f',
              'd84c4d43-3053-492c-8609-33e267c01b5a',
              '250e9dad-4504-4332-a43e-045b64542ffb',
              'e3b986cc-abfe-4c06-8da7-1a6f44639185',
              'ce80b038-c318-41da-879d-22dfc3f9e44a',
              'a24ed58a-f259-4336-b08e-2328000d69db',
              '4122aff6-1365-4417-843b-8f6cbe2d847f',
              'c4a34e5a-5e0e-4551-afbb-245184419365',
              'ffed15cc-fbd3-4a88-9c07-e69ac8a98d68',
              '9c097489-2ed8-47c3-b06e-b89b799aaa57',
              '3b215d11-038b-4a8b-aee3-4d6c835e45b0',
              '25942995-4389-4cb9-ac82-465894db1622',
            ],
            data: { ppid: 'e8d36fba-5a99-4421-a402-ad91fa2a4266' },
          },
          {
            id: 'a1b820ca-0355-41b0-a630-f75b74b055b1',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: '23f46b15-1154-419a-9b4e-5f83549d9203',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: ['afc63618-7d9a-4886-959b-c70a189aa69a'],
            data: { revision: 'vociferor creptio timidus' },
          },
          {
            id: 'f92386c5-dc77-4d7c-b4c2-4eb7ca12fc98',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: 'c7ed6606-0695-41a3-af7d-3049805a3d52',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              'efb1db71-6df5-4d03-9328-c3121a329b3e',
              '487b6fd6-31d7-4ed1-9f59-79f9dd1cc324',
              'aad947c8-ba1a-4ed5-b1fb-47ba03bfc2c1',
              '06bfb0e9-053f-4f56-b2de-1df45b0c40b5',
              '991001f5-89e3-4a3f-aa00-78ea91ad15db',
              '65d3eac1-a6e1-4ab1-b814-4391829931eb',
              '7edd4d0d-c11b-402a-a882-6c4c0c04a365',
            ],
            data: {
              documents: [
                {
                  code: 'ha',
                  signingUrl: 'https://unrealistic-tail.com',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://yellowish-forage.net/',
                  signed: false,
                },
              ],
            },
          },
          {
            id: 'a272105a-e0c6-4329-8496-1a62f2a4553a',
            type: 'LINK_EXISTING_CHARGER_V1',
            subscriptionId: '3d38fbf5-7bc5-44ca-9836-a4626b1b1c78',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              '12f066ff-8c53-4f03-9226-085d878e64bc',
              '3ae3494c-488f-449a-ad69-36f2d8066f83',
              '10bc2734-9879-49a3-a2ad-9b7de0357634',
              'e1dda335-754d-43b8-899c-7f7cf7f71753',
              'ed838118-f3ce-4430-ba1f-4974631f3af5',
              'a7bb2728-3c3d-48a5-b515-d2be45366df1',
              'f4de3e6b-9609-4a50-a3d7-8aab711907d3',
              'd5af874c-b3ac-4fe9-aafc-3bcab1dd35a3',
              'd5b9f899-018b-4285-b3e9-cdf24c6c9e36',
              'b7fb7189-d472-4c22-b778-e41aa663917f',
              'a5940201-56b9-440b-a2e5-867bce49c4dc',
              'e192a7f3-f819-46e0-9576-40e807e9e606',
              '35c177a1-cf3e-4801-b0bd-8ad1e97d6a30',
              '710bb36f-4b7e-4b41-bd60-799ff94c2c12',
              'bc34b515-1ce3-4d49-9bcd-e4e598419bdb',
              'bb716261-1eb0-4246-930c-1f630272892b',
              '3b80cd81-1446-48bf-a218-a7a065a8e875',
              'fb3bd1fe-1149-4e60-89c1-16f0e02e6919',
              'fbbc8456-7e36-4692-8844-4c696f61f275',
            ],
            data: { ppid: 'd32d0f87-0a9d-47c6-92c2-c1e43b1518e5' },
          },
          {
            id: '545f48e7-698a-49d0-9d34-924570c88e8d',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: '6de11c1d-e7c0-48bc-a276-14f7938fd790',
            owner: 'USER',
            status: 'PENDING',
            dependsOn: ['89d4b3fd-ba00-4554-a44b-7609a8934045'],
            data: {
              documents: [
                {
                  code: 'rca',
                  signingUrl: 'https://zesty-mentor.info',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://mild-mozzarella.net',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://true-electronics.org',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://ragged-vision.info/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://infatuated-petal.org',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://nutritious-doubter.org',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://glum-carnival.name/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://liquid-middle.net',
                  signed: true,
                },
              ],
            },
          },
          {
            id: 'd9c23e75-025e-42ab-a94b-b4afa9687bfc',
            type: 'SETUP_DIRECT_DEBIT_V1',
            subscriptionId: 'ba8d234e-5d3e-4996-90fa-7df42711d860',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              'b4b2a68b-25d6-4a18-a432-6d87ba6a7f0d',
              '753d17b0-b787-4702-9a6d-0e2b071e0cc1',
              'f00ce60c-e560-4513-8822-e28fd0b2d094',
              '2177054c-5c81-453d-97d6-d60762435e26',
              'cdd07598-5c1b-49a5-876d-7c9cdc439e02',
              'bab4d725-f94d-44ad-884c-04018aa09fca',
              'ab90b55e-4e65-4819-99f5-c68eb84abe88',
              '115586ce-f057-44d2-ac68-a7ea2c405db3',
              '0c236e86-312e-4e1a-860b-7e4d5850e4d8',
              '87a95308-abf1-4b9b-ac54-85eb819d9645',
              'ba035f57-3b64-42e4-826f-45cea6bd18ef',
              '1d38c896-cd53-4399-bc9c-21a057f00034',
              'de911ea8-040e-46fc-a33a-0e86d2cc534b',
              'ff499cf1-73b4-4bcc-88f0-ce9560386922',
              '0c5e749d-0752-4cf2-b0fc-a2a3e5bb2009',
              '704c53a6-c7f7-4c28-85a2-8673741203a0',
              'db8e33d4-4b17-4a12-9cd7-fa953b7e68ac',
            ],
            data: {},
          },
          {
            id: '7a3df32d-aca8-4369-ac1b-1c88444b401f',
            type: 'SIGN_REWARDS_TOS_V1',
            subscriptionId: '137ba55c-b65f-457d-abf0-b480e54e0c1e',
            owner: 'USER',
            status: 'FAILURE',
            dependsOn: [
              '71c7b190-2815-43fa-a654-946d8b975f74',
              'fa30dc8b-5ce6-4a58-bd08-606363bfb14e',
              'e9e1e232-269e-4e70-b302-4821bf502b70',
              'e883773c-0a57-43d9-acd5-8a82dfef5df8',
              '24191771-f73e-4927-9c26-f1f8053116cd',
              '71ac4126-8912-4dd4-8391-df19cfbebe53',
              '8e3aac6b-acaa-4e3d-b17a-bfd74734f316',
              '507ff01c-8157-4705-8798-45f15774a23a',
              '754c0159-8e96-4ede-b44f-773945942e0d',
              '40248da4-6790-4275-87a4-0dbfa50af0e9',
              '2dbcd80a-dc30-4766-9345-331b38024795',
              '3ddba64e-f050-4e58-b14b-0f6591f21858',
              '386b6acc-4dac-48d8-937a-1ebe14b2c052',
              'e9a6a28a-d202-477e-9078-aeddf4ae715a',
              'd33e9c1f-ef59-4c9d-8ec5-363227e05310',
              '4b5fca7a-0eb9-42ce-974f-af59d085b4d3',
              '3b457b15-5822-41ff-bcaf-6bfaa5558384',
            ],
            data: { revision: 'alienus tandem amitto' },
          },
          {
            id: '1c14ec0d-1d9d-4ac3-9308-c0338ddfd26e',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: '07937fcb-cd53-47fc-b553-8ef1a7f88294',
            owner: 'SYSTEM',
            status: 'PENDING',
            dependsOn: [
              'd4edbf68-e885-4780-abaf-10d32313d7a6',
              'e6b35008-2aea-460d-9c99-f287d39182c3',
              '970235f0-8b16-4014-bb80-e0a455bb6dc2',
              '8a703cde-a801-4eb0-87c7-fae3d6bb4209',
            ],
            data: { surveyUrl: 'https://needy-passport.org/' },
          },
          {
            id: 'd0038ebb-f7eb-4cf5-8490-d9510d17f7fc',
            type: 'COMPLETE_HOME_SURVEY_V1',
            subscriptionId: '9cc1b14c-44ec-4929-87bd-cb49bd22d2b9',
            owner: 'USER',
            status: 'SUCCESS',
            dependsOn: [
              '294bc272-c937-4aa0-84b5-2be5b989c0df',
              'bd8cab79-b32d-49b5-9c64-cf87bf4fb34d',
              'a3cf467e-1abd-456a-b47f-0cd02a6da947',
              'c0519e84-5903-45e6-a897-1777f37e4958',
              'acd8dce1-3f40-424b-8cb0-8d012b03367e',
              'e99a29b7-4f7c-4cb6-93bf-13101aa57f87',
              'c217aab1-dabd-429b-8839-594278044496',
              '41963357-99ab-47fa-9fd6-3a3bdcf4b99b',
              '56ad5a32-951e-4ddf-af83-ed49da039a9a',
              '20e705d4-65c4-4218-8d22-fdec13ac7009',
              'ce8585ee-43b1-49bc-be29-ec714454e02f',
              'ead01e72-e7e8-4bef-883d-5487969f2756',
              '7667ca0d-5a9b-4b66-a831-f72d3d01facf',
              '760b2197-8bf9-4bd1-b361-c22de8c25a5f',
              '68302d80-20fe-4eb3-a95a-71113cd1d0e2',
              '6ecc3463-8005-437f-bc06-04aa2b2385e6',
              '222f8ba1-95ec-4f90-8867-d71bfe9e5cd2',
            ],
            data: { surveyUrl: 'https://agreeable-formamide.biz' },
          },
          {
            id: '33346b75-1578-49f9-b55f-2ed503c437b9',
            type: 'CHECK_AFFORDABILITY_V1',
            subscriptionId: '5eb3daa8-4f3f-4bc6-90bb-56aab2e3288e',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              '11d28f3c-4b7a-408f-9175-8fe5b5ad85ec',
              'f759dde1-a5cc-49e5-bda0-6d2bf348e5ea',
              '4589c6e6-1d24-4da3-8343-8e2003b6a2cf',
              '0c2a26cf-5e0c-4573-af1e-fb0c545aeb1f',
              'd1a17d28-b8f9-4ab0-a93a-51079ff3d612',
              '218d0b24-54a0-49dc-b01c-41d981f55232',
              'c0fd4d96-4bfd-467f-b069-50c7711a1e6f',
            ],
            data: { applicationId: 1515225310664044, loanId: 2686907596449009 },
          },
          {
            id: 'ee8cf4f9-c533-416a-ae69-6a681f0012c7',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: '04970f67-1883-45da-8578-e795502d4cab',
            owner: 'SYSTEM',
            status: 'FAILURE',
            dependsOn: [
              'a4d73b46-15d0-4d16-bc8a-d4a57c074c34',
              '7a3b4443-e771-4d21-8583-a25a84ffab72',
              '87d5e5e0-4a32-40a6-a8d8-62db49b05f71',
              'e21fc955-2a80-4c00-b1e1-107652b1c83a',
              'a7f47774-701f-411d-b091-a6dba14f87ab',
              '744a25ba-80f1-481d-b389-92849829644f',
              'e087a7ea-4e69-4c57-b9c9-3a10c505b569',
              '9287455f-9703-47cf-b273-6a38c6f8b1e7',
              '67f5ef81-0e3f-497b-b014-1fa63941c24f',
              '5d6de81d-de75-4518-9217-469f21e0bea8',
              'f0eac5e0-8cf7-42c3-b2a5-81508e174a1c',
              '3a42f057-ed02-40ab-a37c-dcd99ebf7d27',
            ],
            data: {
              documents: [
                {
                  code: 'rca',
                  signingUrl: 'https://musty-colonialism.org',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://fearless-beret.org/',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://soggy-dividend.name/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://supportive-reward.net',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://best-mainstream.biz',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://oblong-diagram.com',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://moral-midwife.info/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://devoted-airline.org',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://shabby-retention.org',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://glittering-gray.com',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://intelligent-chairperson.info/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://majestic-marksman.info',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://sore-coast.biz',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://zesty-topsail.name/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://burly-vision.biz',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://blaring-duffel.org',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://radiant-dividend.org',
                  signed: false,
                },
              ],
            },
          },
          {
            id: '792b237f-69d0-4614-a4a6-98fc1e5d9a91',
            type: 'SIGN_DOCUMENTS_V1',
            subscriptionId: '34321ca3-4670-48ad-addb-58a2892526ce',
            owner: 'SYSTEM',
            status: 'SUCCESS',
            dependsOn: [
              '92063614-2be3-4ce4-8245-9f71c6bdefa6',
              '4d25e42d-8756-4c1f-9251-7b12f20762d1',
              'b9a94ba2-da48-4994-bf87-86709edf8a88',
            ],
            data: {
              documents: [
                {
                  code: 'ha',
                  signingUrl: 'https://frightened-catalyst.org/',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://winding-planula.com/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://swift-skyline.net',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://utilized-detective.net/',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://hoarse-affiliate.com',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://next-spirit.net/',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://glorious-pupil.com',
                  signed: true,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://profitable-pillbox.biz',
                  signed: false,
                },
                {
                  code: 'ha',
                  signingUrl: 'https://gaseous-polarisation.org',
                  signed: false,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://frightened-disk.com/',
                  signed: true,
                },
                {
                  code: 'rca',
                  signingUrl: 'https://alert-cannon.net/',
                  signed: false,
                },
              ],
            },
          },
        ],
        activatedAt: '2025-01-01T00:00:00.000Z',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
        deletedAt: '2025-01-01T00:00:00.000Z',
        plan: {
          id: '5447914f-6a2d-405b-b005-2638c138659f',
          type: 'POD_DRIVE',
          productCode: 'alii vilitas concido',
          allowanceMiles: 10000,
          allowancePeriod: 'ANNUAL',
          upfrontFeePounds: 99,
          discountedUpfrontFeePounds: 45,
          monthlyFeePounds: 35,
          contractDurationMonths: 18,
          ratePencePerMile: 2.28,
          rateMilesPerKwh: 3.5,
          milesRenewalDate: '2024-09-17T06:06:02.272Z',
        },
      },
    ],
  };
}

export function getSubscriptionsControllerCreate201Response() {
  return {
    id: 'e5a4d45a-06b9-431f-8610-19ed0b7004b2',
    userId: '5357be96-1495-4951-8046-c2d59ba76c33',
    status: 'ACTIVE',
    order: {
      id: '8fac9116-9469-46b5-b4b9-2c782e09667e',
      origin: 'SALESFORCE',
      orderedAt: '2025-01-01T00:00:00.000Z',
      ppid: '4fce7b78-c8a0-4272-b048-11d1bc0778b8',
    },
    actions: [
      {
        id: '7cdf5282-677a-4019-bfdc-2d796b167c2e',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: '66598241-735d-46df-a9c0-ef2343204590',
        owner: 'SYSTEM',
        status: 'FAILURE',
        dependsOn: [
          '70d3429d-3459-449c-b4d7-ebe71d1ff873',
          'fcd51443-392e-4455-b43f-ff4dc2b496e5',
          '14a55473-3df6-4c56-b6f2-b6bd111dac82',
          '260216c5-511c-4440-a1fe-b6f35e690715',
          '2902282e-5c4a-410e-a227-0d2a85484ff3',
          '80cc70af-45a8-4bf8-af7e-ab71fe2d47e6',
          'ac3a3824-4631-4929-80c5-f68de4ff79c1',
        ],
        data: { surveyUrl: 'https://unlined-government.info/' },
      },
      {
        id: '3dde193b-b0b4-4055-9315-772a82b0dcda',
        type: 'CHECK_AFFORDABILITY_V1',
        subscriptionId: '1e0be69b-7d6d-4ebf-9f77-9cec219878fd',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          'a84e8492-d6c4-4211-bd3b-8ed796a381a3',
          '74e47190-1f5b-4a97-92cc-8da9010464f5',
          '5f566676-4851-4798-b846-b981073be22b',
          '1c33a276-0860-4c22-8f51-838effc1ef9b',
          '97c367fd-e0a3-4b77-b47e-69abf94cef8c',
          '7801c3eb-58a5-4b05-8c15-c03221d8b03c',
          '0727eceb-d81c-415c-ba60-87a95179e85a',
          'f27d60f9-5d5e-46e0-bcbe-46eea3f06ce3',
          '8bb2480c-f856-4a88-917e-07bfcccf39c2',
          '742adc84-c477-40fd-a8c9-767bde801ea5',
          '8f76f362-e13d-41fb-a794-af0dceb8c6a5',
          'e7046000-9205-4f91-81a2-93d3fe7a3a3c',
          '60264ba8-7a74-40c8-8c7d-7e6a50e2890b',
          '8aea3da4-1de6-47df-86c6-bb1a715c804e',
          'ebab664f-e521-4922-8e19-23836d766b93',
          'e7f891e2-b6cf-457a-99f9-012f3b7bd641',
          '2ecb825a-22f8-4c46-9c00-53e8bdd74f27',
          'e362c74c-374f-444e-82ce-094072b220b2',
          'c8827a02-aafb-42fe-bf88-d93ac7b7db20',
          '549bcdb2-9282-4aee-a38d-039c7f72ffe7',
        ],
        data: { applicationId: 2696642351576722, loanId: 793429139315321 },
      },
      {
        id: 'e56e112d-8618-49f0-bf12-f6a001f81cd5',
        type: 'SIGN_DOCUMENTS_V1',
        subscriptionId: '09f30fc9-c1c8-4ba9-964e-5489df42800a',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          'e01d8c90-fcc1-4cad-9ab5-945e1c7f2e52',
          'c2b214e7-92ad-489b-aab6-a4ec195a0a47',
          '6f70da29-76ad-49ef-b40b-b310ad1e3b76',
          '0b1e2a09-efba-4768-b356-27c66d600033',
          'dafe921e-3c21-4765-bf98-f4451b6a0a62',
          '73b41795-f247-44e3-9ca2-a8f96a4a4dc6',
          'c0f76230-4ee6-4595-b725-c434bf330581',
          '3ec9bc2d-7b6d-4f9a-84d3-0da190c86abf',
          '94be4ae2-11de-41e2-952b-016be3bd9788',
          '0a26b9da-d83e-4526-af6e-55051bd89cea',
          'f92a3bd6-68af-44b5-9252-b2b1ee281174',
          '37686a16-4104-42dd-a5d8-2f738e40619b',
        ],
        data: {
          documents: [
            {
              code: 'ha',
              signingUrl: 'https://familiar-vicinity.com/',
              signed: false,
            },
          ],
        },
      },
      {
        id: '298d9c2f-8194-427a-ab7c-b8a18c664c52',
        type: 'CHECK_AFFORDABILITY_V1',
        subscriptionId: '6e444150-89e9-475a-b630-6e6f67e0203e',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          '01c837c3-a58c-41c4-b586-105f36fb5f08',
          '9d4058ba-f56c-4971-9467-9933d6a606a3',
          'ea0c5fb3-75a9-4470-bff4-9cee4b8a6648',
          'f45ae29a-d115-4264-96e0-5b5fcc5a209d',
          '13d54f3f-0631-434a-8098-b744f1e38cd5',
          '47a4902f-57cb-4e08-a5ff-46ce5f8a27dc',
          '35fedfd3-0941-4365-8cfa-5886828a553d',
          'c9af5099-707e-46e1-ab96-9bdd2779ee12',
          'f4bdfa34-9816-4c7e-ae98-339813cdb452',
          '8c5197f3-96c0-4a57-af81-f4fe10c3d0c5',
          '02c43e5f-8a09-426a-b728-faa8e099b0a7',
        ],
        data: { applicationId: 7251108519391381, loanId: 1192999433668943 },
      },
    ],
    activatedAt: '2025-01-01T00:00:00.000Z',
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:00:00.000Z',
    deletedAt: '2025-01-01T00:00:00.000Z',
    plan: {
      id: 'b0e4b176-6a8d-4b0e-b21f-8c5ddc72fa43',
      type: 'POD_DRIVE',
      productCode: 'maxime consuasor valetudo',
      allowanceMiles: 10000,
      allowancePeriod: 'ANNUAL',
      upfrontFeePounds: 99,
      discountedUpfrontFeePounds: 45,
      monthlyFeePounds: 35,
      contractDurationMonths: 18,
      ratePencePerMile: 2.28,
      rateMilesPerKwh: 3.5,
      milesRenewalDate: '2024-12-19T01:30:08.722Z',
    },
  };
}

export function getSubscriptionsControllerGetBySubscriptionId200Response() {
  return {
    id: '935f32ce-29c8-47fc-8dc5-3d60a6fa46a3',
    userId: '5357be96-1495-4951-8046-c2d59ba76c33',
    status: 'PENDING',
    order: {
      id: '9d5558c5-c039-40f7-a238-873477e68f45',
      origin: 'SALESFORCE',
      orderedAt: '2025-01-01T00:00:00.000Z',
      address: {
        line1: 'ter dicta vis',
        line2: 'similique cunabula labore',
        line3: 'officiis hic victoria',
        postcode: 'calcar viscus ultio',
      },
      mpan: 'tempora adamo decet',
      firstName: 'Bennie Cassin',
      lastName: 'Maureen Kshlerin MD',
      email: '<EMAIL>',
      phoneNumber: 'comptus adulatio beatus',
      eCommerceId: 'a89e8611-cb1a-49d7-bbd7-8295cb36708b',
    },
    actions: [
      {
        id: 'a71d61b7-3e8f-428f-976a-17188fa3e10d',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: 'eac5e6ef-b387-4739-93fb-576fbda5f560',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          'b8e2dc59-fb11-4f11-b498-968e1f7decab',
          '4c60f7f6-6743-42a6-91a8-d4d943f33812',
          'afefe0cb-0ddf-4b06-9b83-cdfa8f0c2e17',
          '4688b061-3d1b-4dbb-8d03-c7db96dfe86c',
          '4ef28836-50f6-46bb-b317-183c727f6d2b',
          '8547e68d-65e2-4b7b-a602-b9038eec8168',
          'fe10d704-67fd-446f-b68c-7aa208634f78',
          '04f8be52-1997-4a09-983e-0ffdbf4a11c3',
          'ebd6f202-4473-43a2-b1f8-9dffc28c611a',
          '34aaaf9b-2ee2-47e2-904f-1b88cacf1297',
          '64172ef7-a95a-4d6f-aeaf-8d29c2359e08',
          'c6f8d981-c821-4896-a86b-b48649713fd2',
          'fba7c7eb-9b37-4709-b437-21f342d0ad2d',
          '9a602976-683b-43a7-8d42-b3204e085338',
          '1a2aea63-35b6-4701-b5bd-417f2afd1b5e',
          'a8e73ffe-070e-4840-87b9-b100c6501cd8',
          'e1b8cfd7-9145-4a27-8711-8d14cd2d0cd7',
        ],
        data: { surveyUrl: 'https://black-and-white-fisherman.com/' },
      },
      {
        id: '0cb41175-b27d-4966-a462-29166af74467',
        type: 'LINK_EXISTING_CHARGER_V1',
        subscriptionId: 'f1c8deac-4833-4b80-92a3-16eaa8e3d0df',
        owner: 'SYSTEM',
        status: 'FAILURE',
        dependsOn: [
          'a04637e2-0cce-4725-af7b-72c4799c540a',
          '7976cf88-6e6a-47d9-a166-79618c1e632c',
          '4dbc98a5-7da6-4f0e-8e17-781b8b0425e4',
          'a62c4f05-721e-40a1-aa26-dd52a26d221a',
          'c839400c-0f0e-48a2-be4a-7439fc958bd0',
        ],
        data: { ppid: '0528c61a-5f39-4da2-976f-f5cc459c2c76' },
      },
      {
        id: 'cfbdb1ca-2782-46d6-9091-b88faa082aec',
        type: 'CHECK_AFFORDABILITY_V1',
        subscriptionId: '7acc44ba-0d80-437c-a169-ea04066ec8c2',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          'd813eb29-b53d-468e-b776-9a994729cfa2',
          '225e3def-135f-4eff-883c-41be6a68f0c6',
          '87fef8bd-5cc0-4cb9-8652-fb36b9633e8a',
          '35817b25-80df-4e42-9e00-04dda2a8d32c',
          '70b24666-bc35-4b7e-b678-2bf898d95887',
          '09de6643-3f29-4d8a-a71b-e747e6844d5c',
          '8f5c12c0-3ec4-4431-a940-c8b257ff5f2d',
          'f8fa1545-278c-4bfd-9282-f74bd06ce20e',
          '7c5d94d2-422c-43af-9792-4feea15e2d58',
          'afa712d7-5fd2-46f5-9c99-4a7795f38433',
          'c302cd21-ef51-4bb8-ba24-d3a6badde26f',
          'bb70e204-5d1b-4583-8180-d5f3583085eb',
          'a8d7b893-616e-47ad-a4af-6341629a8ad6',
          '628cfb96-11a8-4384-a5a8-928049bc5717',
          '75138459-0b4a-4fa3-b5f7-49856c3ea629',
          '9ffe0f75-3d49-4549-b7df-42e9dd3db713',
          '9a080aef-a859-4d3c-a1ec-3c7ec23a46a6',
          '51bd2116-4518-4c5e-9085-e7a7ece9beea',
          '90b68ea5-13da-4252-a595-9f4f1d806f9e',
          '4b963cce-deb4-4d0c-8419-4ca52650ead5',
        ],
        data: { applicationId: 5698570973972538, loanId: 5672057897201858 },
      },
      {
        id: '265acae9-8c3e-4722-8d6a-a37084323901',
        type: 'PAY_UPFRONT_FEE_V1',
        subscriptionId: '3dca9eea-2b75-4e43-8421-2d8981b85bcf',
        owner: 'USER',
        status: 'FAILURE',
        dependsOn: [
          '71ad80fc-c425-4a99-98c2-acc384d11d94',
          '99e69334-9b7f-41c1-9ab3-92af7c1f5b6a',
          '0496268d-ad3f-4969-a8c1-55649c5b387f',
          'b61d050d-44cf-43c2-8426-185c27251164',
          'c37ca0f3-23d7-4504-b52b-1872c631a3fe',
          'f77b6231-4f47-45fa-98a4-4d6bdf79485f',
          'ec982033-598d-4156-a7d5-b011fd67dd7e',
          'a602e750-8939-48f5-969e-0a5caeab511e',
          '27bb25bd-0a76-4f99-8bbd-2006f68ec6c3',
          'e3f0e894-eda7-48a5-a906-a889b10f1155',
          'e30a1374-9c33-4342-88e5-62aaa4788589',
          '8de0b14c-18f4-4107-a68f-1dfefc355a5b',
          '4c95ba59-e9b3-4f6b-9522-aa68e45ed3cd',
          '21d78209-b392-4443-bbe5-0f6057cf0872',
          '2fe06066-6c7b-4578-867d-bb61a134bfbc',
          '3c9033eb-ec22-477b-9672-b54d046e2016',
          'a19076b8-74c6-4ceb-83b6-986be31a8915',
          '0af963f5-c762-4ab5-870c-58c43f4d39ff',
          '4c021315-21fc-41c0-8f32-a14e92249d7f',
        ],
        data: {},
      },
      {
        id: '3da8a147-8fc4-4c35-b258-09ded56a03a5',
        type: 'SIGN_REWARDS_TOS_V1',
        subscriptionId: 'd1c6b7c9-d217-4688-8f10-97be3a21c2fd',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          'ef7e92b1-abc7-4635-aac0-c78f42655735',
          'a99985b4-7dfc-4691-94dd-4308f38aedae',
          '69ea023f-350b-49aa-b9b3-b0712cb6b283',
          '45a029ad-ff9a-4f25-8af6-80d3228b6661',
          '2abee922-2243-498c-86f7-25e1f2215a66',
          '8734f718-243a-4fcc-840c-44b3eeb8cd2c',
          '97a7162b-9e56-446e-8da2-0dda58db4144',
          '146e3314-6d66-4047-9142-ca89f30a42e5',
          '79f34fe1-7e87-4ff2-bc7a-ea445528bfb7',
          '91170b9a-03f1-4be0-b4a5-9c374d082bbb',
        ],
        data: { revision: 'administratio super pauper' },
      },
      {
        id: '07e211d5-dd21-40cd-bef6-b68e967afe42',
        type: 'SIGN_DOCUMENTS_V1',
        subscriptionId: '0ca38b0a-2a1a-4a2b-b9aa-e7f59fe1ca04',
        owner: 'USER',
        status: 'PENDING',
        dependsOn: [
          '4991dee4-65df-4a10-9cc7-9951b8cf9591',
          '7245d05e-12d1-41c4-8a54-5293e2913d34',
        ],
        data: {
          documents: [
            {
              code: 'ha',
              signingUrl: 'https://cute-eyebrow.org',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://other-address.biz',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://unusual-seagull.com',
              signed: true,
            },
            {
              code: 'rca',
              signingUrl: 'https://confused-gray.org/',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://well-worn-integer.org/',
              signed: false,
            },
          ],
        },
      },
      {
        id: '9315a811-6dc6-402c-9c99-0cb7bc1713c1',
        type: 'CHECK_AFFORDABILITY_V1',
        subscriptionId: '6a71e888-926a-4e59-a84d-f1fac08d28b8',
        owner: 'USER',
        status: 'FAILURE',
        dependsOn: [
          '2af7a1d8-062c-4fb7-8c43-43fe8e54b140',
          '4c3955de-3ab9-4444-bab1-f30d25035359',
          '158bb1ac-8ba1-4741-93c6-4d7650c88352',
          '545daba8-0ca1-4f9d-9c42-f35bad279441',
          '5b06d434-8d4e-446e-9f77-2d8a1026014c',
          '85ef5ac5-89e1-4106-8bfb-32abecbf7da5',
          'e363e739-f73d-437c-a9ff-bdd8d04f43f3',
          '3fad26f8-137c-4a96-a864-2a3bc9c5a906',
          '5f7e2027-32ca-4e64-b464-d26e0e006143',
          '5bb007df-4a79-4868-9871-10a101204034',
          'ab5d4889-cd57-4367-8064-3e5a517883a6',
          'b6147ffe-077a-4be7-82c7-b0c5e999e01d',
          'cb9157a2-46ab-4388-a82e-ee8b0224cce7',
          '88097e45-ef28-433c-98ef-344d49627d6a',
          '8041c103-2d64-4d0b-ac28-7c830b7a090b',
          '6469908b-6b3a-4889-a2ab-b0150393c016',
          'b415f5d1-d77a-417d-b138-3c61821d4a94',
        ],
        data: { applicationId: 961942708303067, loanId: 6461123785074518 },
      },
      {
        id: 'c7259b45-d78e-4304-926e-6b920695bb78',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: '5c04fcb6-ab15-42f6-b62a-3ed3b2e7550c',
        owner: 'SYSTEM',
        status: 'SUCCESS',
        dependsOn: [
          'cfed8113-e222-40ee-a4cf-d3653fe26dcc',
          'edd720be-5046-47d3-b175-feacbf00a444',
          '1b4b9529-123d-4c57-b267-7c55a4193fd5',
          '8418db59-2b54-4f54-848f-2b9f838fd9a2',
          'ad5e753a-d5f6-4ce5-86be-9f32f3c9bc9c',
          '414c9cf2-fbf4-4fa6-aefd-32c280b96b34',
          '264e364c-9fa4-4895-aff3-67fa2d21355a',
        ],
        data: { surveyUrl: 'https://sure-footed-order.org' },
      },
      {
        id: '2ee3da6c-b6ec-46c5-b81a-8f543bf8ccfd',
        type: 'INSTALL_CHARGING_STATION_V1',
        subscriptionId: 'c4556e05-4d19-437f-817d-104f0239adc9',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          'b8bb7520-21b4-40fd-bd47-194bc66a2bea',
          'dcb0d824-f7a2-4565-bd27-f7f95638217c',
          '1148ee19-ef8a-4e87-b412-d28b4c3e52b9',
          '93ade042-112a-4540-99ee-c16779721274',
          'b5ad914a-982c-4f98-ad08-9fd66d15ffda',
          '33fd3472-4fa8-46b0-9960-d845f27342b1',
          '270c9d4f-3c53-4924-aa46-1969cd16af5f',
          'f28dd9be-6a3b-4b74-8b00-b981b30d0925',
          'f9da9d77-9469-4dab-9492-f22ba92e57ac',
          'df2ccf50-98e6-400f-9892-45bb3f560c23',
          '903b9abb-feb9-4875-8809-72fea7d66422',
          '9a72045c-01cc-477a-be19-fa826a771ced',
          'bbeb8ced-632a-44ea-99c1-51e5ae96e484',
          '5cbed839-ccf7-459f-9224-c971ed49213e',
          '7440b989-f9e7-4af7-98b1-e698a84c37e7',
          '141162c7-9648-4bf9-b5e5-4c1ef3597d42',
          'e800762a-89a5-4139-bd6e-cf13b7ec075a',
        ],
        data: { ppid: '1be539ab-2373-4ced-8785-b2573a9c5c55' },
      },
      {
        id: '1812235f-c56f-4ce4-a3d7-ce7df77b072c',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: '15d63fb2-0fa7-4b48-9ede-30bc2aae28d7',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          'be3d8a74-0438-40c8-a479-78e187e0a2a0',
          'b9f311d4-855c-4b7e-84a5-e7ab64c197b2',
          '0c44940d-3ccd-403d-bd59-dcd0df69c753',
          '7a879b67-0bd5-47bc-9d73-59a9f2c2048e',
          '74701cb1-a091-4ab5-90c7-9c759238c0cd',
          '02b6a589-fac1-4774-bd50-57457ff704e8',
          '0909682a-fcc3-40b9-8d8a-c555ccbaed32',
          '07761289-3b28-4396-9a1a-bf0a8521d58e',
          'bf26ddcb-c35a-4aea-bad1-e9f1b1770e14',
          'f0fa8b34-16a4-4453-984f-1677a1fe9b34',
          '6f3bd49a-3e91-4b72-9035-27f28bc0cafe',
        ],
        data: { surveyUrl: 'https://clear-cut-hexagon.biz/' },
      },
      {
        id: '6b5706b4-a92e-41bb-86e4-7aaa905ee188',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: 'a6349d8b-223c-42d0-a5fc-67a42d4b5e1f',
        owner: 'USER',
        status: 'PENDING',
        dependsOn: [
          '8ed7bb1d-156a-4998-a1e2-8816afda94b4',
          'c700528d-b02b-4371-bbe0-7293f24047cf',
          '4cda0c2f-7213-41a6-8932-4519dcd4dec2',
          'c530a106-a684-478d-947d-d1bb4de8996a',
          'bad79027-d93e-4abd-aa07-79b7d94f443c',
          '8a7809a1-b824-4d6f-a56a-e96b46e71d17',
          '5593ac85-1b06-46e2-840e-d19c9772d959',
          '17dd52c5-4445-4b84-92a8-4b8a1ac30626',
          '2fce1089-e8db-4a67-9783-482bcdf7a95f',
          '871f5ceb-e917-43ae-9a04-109062bb5c7c',
          '559045e0-bf88-4323-b9c3-1175a4716876',
          'c055e342-1c43-4ef3-881f-c3094af4492c',
          '244416e0-015d-488c-9655-0055358134b4',
          '09a030ae-0ab4-4c0a-a541-b458e2030fd7',
          'cd44e77a-a3b8-4a44-a4d6-1e91c25b1f59',
          'c4a9f3cb-8ddd-427c-bb0a-f81efebbbcfc',
          '9326a19a-7688-4ebe-9f2d-7acc7e84b488',
        ],
        data: { surveyUrl: 'https://rotten-wilderness.net' },
      },
      {
        id: 'ea662e19-806b-436e-9e7e-0706520ce1f0',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: 'd6e8f919-b25b-47ee-9a0f-78c6bef274bf',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          'e4e200a0-2654-4c96-8501-6657093dbe79',
          'e1514536-d0ea-44c4-95f9-0c3b28bfb137',
        ],
        data: { surveyUrl: 'https://separate-nudge.name/' },
      },
      {
        id: 'd8bfb79a-58da-4e3d-b29a-1ee8d8cc3d8a',
        type: 'SETUP_DIRECT_DEBIT_V1',
        subscriptionId: '5d071b5b-58a0-42c7-8ab9-5994119a20d8',
        owner: 'USER',
        status: 'PENDING',
        dependsOn: [
          '96778ed6-a09d-449c-9301-995a10f1160f',
          'ad8e096e-3016-4fad-a308-b67e530dee33',
          '75effcdf-7c0d-4c0c-96f8-1ab709dd66d7',
          '4aaa1d6b-a4d3-4f5d-a5a9-4ed6b9b70cd6',
          '6a13e689-2d0b-47f0-a88f-c3528a99ec2b',
          '2bc4d36c-79fd-4b54-ad04-b655c86ebb38',
          'b7d7f857-39bb-4e89-963b-c24adcdd309f',
          '071123c3-36e5-41dd-9d8b-fdd42e55e13a',
          'e404ef92-516a-48c1-90be-e33cda910f6b',
          '96074b9b-2d45-48c7-9c3d-6750a122ad10',
          'ab9e34bd-2b5f-49e7-b59e-b0386b104d99',
        ],
        data: {},
      },
      {
        id: '3e891d9f-f96f-4a96-aa2a-0857b9ce4bde',
        type: 'SIGN_REWARDS_TOS_V1',
        subscriptionId: '8295b6bd-af9d-4206-aa62-d94ba89f0ea3',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          '09dc1c76-f332-4d69-80c3-7c7e93ba8413',
          'd1a89f6a-7b44-41cf-ac05-999fc2e849cb',
          '56ba4aa7-0326-4ac5-8c9f-6a4098b485f9',
          'dfef5110-5d0b-4b3e-8814-311422de8081',
          '56183961-4436-4ab2-9e63-650fbe06315c',
          'da19e094-7087-4436-b0ce-b474cb745103',
          '64ede059-d39b-4231-bf42-047bf16e5a64',
          'a6be6529-34a1-4aad-a80d-17c483e33263',
          '6449c518-50ba-4579-8975-762b02dd1bc7',
          'a3e832fb-5370-4d15-9fcf-9efb3eb3142f',
          '24ca2813-8a72-40cf-8619-e840214ff333',
          'a01a277e-b3aa-4966-a6df-04aa3a362ad3',
          'b4ff65c0-9b01-4176-a036-69c4c4b6945f',
        ],
        data: { revision: 'teres arbitro curto' },
      },
      {
        id: '935318d0-a376-45a5-81da-db621a1c6cc2',
        type: 'LINK_EXISTING_CHARGER_V1',
        subscriptionId: 'bde7eb4e-6035-4995-828b-18e6492d6401',
        owner: 'USER',
        status: 'PENDING',
        dependsOn: [
          '321f6533-87e8-4961-9c7d-647b235fbe7d',
          '32bf3eae-6206-4cb7-b2f4-e79238c7f340',
          '6fec77a1-d6f2-4d84-9d9c-423589ffef8c',
          '89ab97dc-1fa4-4525-9181-85ebb218a4e1',
          '33660cd4-04af-483c-8529-7a5ca72021c1',
          '436a34b4-ef9e-4851-b83c-20217e53b013',
          '4dae0697-2d71-4b71-a0bd-632adc6195ef',
          '576dfd6d-cbf0-412a-9d41-5624fea4e677',
          'c71cf25f-d918-4f33-bed8-0dafea991c29',
          '14225175-d317-4c70-845e-4756ee8f1a86',
          '2d3809ba-f972-4624-a527-198bbf468b96',
          'eb716864-4a34-43e0-8302-23b07722165d',
          'c71e308d-cf41-43fb-b6cf-c5c3730e7af4',
          'ba8a3b48-d494-4923-b545-901c5f9e4ac9',
          '44815a4f-a4a1-43f0-80e2-e3e452962ba0',
          'a6ca79ac-bf88-4565-8800-f26e39ed6176',
          '51380144-6266-461a-8e56-7da19064f574',
        ],
        data: { ppid: 'f0652964-7884-4a91-b214-f95b15a37055' },
      },
      {
        id: 'a687695e-5874-4446-aeac-d0456c29d3b8',
        type: 'INSTALL_CHARGING_STATION_V1',
        subscriptionId: '43b18e51-e409-451b-9ff6-3d6b22673e9c',
        owner: 'USER',
        status: 'PENDING',
        dependsOn: [
          '6423a3b3-a8f0-4f46-8d93-11ff25e21b87',
          '93a62ce1-5ee8-424e-87e1-6760b05e01e2',
          '1f1eeda1-f8a2-4b57-b229-58126bc456ad',
          '12d89f28-77ad-4d78-a15b-2eaa98fc2b43',
          '45d52141-216e-4fb7-b2e6-c9eb244526b8',
          'adc47a0a-f504-42df-b000-0d0a1e5f2279',
          '70b740db-fc7d-4d51-80b8-2875a3a06a3b',
          'ce02ccf2-c3c0-4bcd-97d7-e4633c5de36b',
          '6a5d00ff-061f-4533-a17b-f18b3d828092',
        ],
        data: { ppid: '72669741-311b-455c-a130-b7e9ca1aedff' },
      },
      {
        id: 'e7c496e4-028e-49e1-b1bc-7e75439f39f3',
        type: 'PAY_UPFRONT_FEE_V1',
        subscriptionId: '0e2e13f1-7c9c-4478-a003-90f053fc1ed5',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          'afc17263-d7e5-495d-a0bb-6727d08cc23e',
          '450aa040-8734-498e-8ae3-5efb73c70e95',
          'fc2c2f08-8243-4e1e-bf59-4dc2dfb0f1a8',
          '3b699fd9-b2f8-45ff-abf2-0ec86664bf7d',
          '0f4ca3bc-21cb-408f-96f3-e21de03d48f0',
          '145652ac-47af-4fc7-9b9f-157e0ac2ad22',
          'ecafa2b3-3969-4b00-90b8-804d1e222bea',
          'c0ec3f3e-70fe-47cd-9761-16a3226432a7',
          '10b27a52-69dd-4552-b970-6721d54f9875',
          '2a61eec0-a867-46de-a79e-3b53aea72606',
        ],
        data: {},
      },
      {
        id: '78bcaa2b-4192-466a-9967-5e4296271ef6',
        type: 'PAY_UPFRONT_FEE_V1',
        subscriptionId: 'fd07baaa-dd52-4a95-807d-6771ea27a504',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          '6fbe3f56-a347-47a3-8b6d-2f525756acff',
          '45334589-c1b0-4bea-b9e4-635ab21c4064',
          'c24d3b33-b7f4-4ae7-b16f-4c76605a47c8',
          '227b3b65-8304-4088-b272-a2980c50bdcf',
        ],
        data: {},
      },
      {
        id: '5889e91d-1de2-406a-ba5c-338bfa6194d6',
        type: 'CHECK_AFFORDABILITY_V1',
        subscriptionId: 'be60d4da-d1e4-4449-8b15-4972b1ad72fe',
        owner: 'USER',
        status: 'PENDING',
        dependsOn: [
          'f28249af-780a-4de3-a72e-98674a22e0f2',
          'df8a922f-35d6-4316-be5c-6ae09a2eb213',
          'af1fffba-734c-4b9f-86d9-cb61cb97edaf',
        ],
        data: { applicationId: 360623399169158, loanId: 508699547815258 },
      },
    ],
    activatedAt: '2025-01-01T00:00:00.000Z',
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:00:00.000Z',
    deletedAt: '2025-01-01T00:00:00.000Z',
    plan: {
      id: 'a87a03f4-597f-4d17-a38e-0c48d43f291c',
      type: 'POD_DRIVE_REWARDS',
      allowanceMiles: 10000,
      allowancePeriod: 'ANNUAL',
      ratePencePerMile: 2.28,
      rateMilesPerKwh: 3.5,
      milesRenewalDate: '2025-04-06T06:39:27.069Z',
    },
  };
}

export function getSubscriptionsControllerTransferSubscription200Response() {
  return {
    id: '3768da05-3d1d-4984-9d45-e3a0eab8c3e0',
    userId: '5357be96-1495-4951-8046-c2d59ba76c33',
    status: 'REJECTED',
    order: {
      id: '3376f089-96ab-47e4-b7e2-0c5270363d1d',
      origin: 'POD_DRIVE_REWARDS_SELF_SERVE',
      orderedAt: '2025-01-01T00:00:00.000Z',
      ppid: 'adfc1e03-cb71-4a87-a338-fe5dea7b94a1',
    },
    actions: [
      {
        id: 'd1741713-7eee-45c7-ac8d-159d28b73324',
        type: 'PAY_UPFRONT_FEE_V1',
        subscriptionId: 'd6bb771c-c79b-4360-a43b-9877ec2c3d35',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          'c548e9e8-b027-490b-8bf9-23ff4d2b2034',
          'de829c9b-9682-42e1-9a8c-56ea519b956d',
          'fdf8c165-2710-4452-b4bb-64a43d3ee2a6',
          '4e408b07-54d6-4a32-b061-f16f25854cb0',
        ],
        data: {},
      },
      {
        id: '507bb929-42f6-43e1-bc8b-df0a2da88d5d',
        type: 'PAY_UPFRONT_FEE_V1',
        subscriptionId: 'd9c556f0-4575-4b52-a067-cb0643b7d01f',
        owner: 'SYSTEM',
        status: 'SUCCESS',
        dependsOn: ['036d1cd2-e75f-4f7e-b090-b0abcb826d32'],
        data: {},
      },
      {
        id: 'b47097f2-015c-4036-a4f2-47895d4ba590',
        type: 'INSTALL_CHARGING_STATION_V1',
        subscriptionId: '5886f2a8-d163-4ca4-9166-eaf6197decba',
        owner: 'SYSTEM',
        status: 'FAILURE',
        dependsOn: [
          'e6190019-82a1-4c4e-b044-d3097895a4fb',
          '024169af-4678-45a1-8c6a-06e6dfda5e89',
          '65701fed-4867-483f-a28c-0e674f16bb67',
          '81ab157f-b161-47b3-9592-867726626228',
          '17c5ccc6-80c1-45c9-960e-abcecff82970',
          'a89f92d7-ec71-4326-bffe-2883f6633843',
          '0f0e1023-8cd4-43c8-8467-2a78ddcdce55',
          'c7e8d6bb-3f7d-41e4-b701-32966de66d26',
          '236aa4f5-fc49-491e-9608-0b8f26531f06',
          'bb55723e-ce15-43a6-9880-39dd6c045552',
          '8c3d46a1-0736-47d4-b8ac-f0a535cdb6ac',
          'e32a1c7a-0648-43ae-a37b-bac552f01c6a',
          'f5ad4ec7-40f9-43f4-83d3-0e2c53c5347c',
        ],
        data: { ppid: '2eeb742f-b2db-4830-b56f-cdd09965c5e4' },
      },
      {
        id: '1ea2ba85-8693-498a-8e9d-6318dab9fcde',
        type: 'SIGN_DOCUMENTS_V1',
        subscriptionId: 'da3308b2-a52d-4819-9819-0602a624dfbd',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          'c9d3275f-14ed-42fd-b61f-03f5c016e05d',
          'f9f90758-0778-4b65-92a5-5d2595178aac',
        ],
        data: {
          documents: [
            {
              code: 'rca',
              signingUrl: 'https://simple-obligation.info',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://narrow-euphonium.biz/',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://ethical-sweatshop.info/',
              signed: true,
            },
            {
              code: 'ha',
              signingUrl: 'https://short-term-creature.info/',
              signed: true,
            },
            {
              code: 'ha',
              signingUrl: 'https://nice-ethyl.name/',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://devoted-guacamole.com/',
              signed: true,
            },
            {
              code: 'rca',
              signingUrl: 'https://unsung-fireplace.com',
              signed: true,
            },
            {
              code: 'rca',
              signingUrl: 'https://vivacious-slipper.info/',
              signed: true,
            },
            {
              code: 'ha',
              signingUrl: 'https://illiterate-wallaby.com/',
              signed: true,
            },
            {
              code: 'rca',
              signingUrl: 'https://subdued-jellyfish.com',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://lean-disappointment.name',
              signed: true,
            },
            {
              code: 'rca',
              signingUrl: 'https://rapid-rationale.net/',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://abandoned-gift.com',
              signed: true,
            },
          ],
        },
      },
      {
        id: '1b64995b-826c-4f7f-ae50-8f30a506b918',
        type: 'LINK_EXISTING_CHARGER_V1',
        subscriptionId: '4c5bce8a-8fc6-4bf0-8ce1-00d5532c87b0',
        owner: 'USER',
        status: 'PENDING',
        dependsOn: [
          '49bedffb-30a4-4d0a-8a45-f927f15ba4fb',
          '4457f6d0-318e-4e7e-ba46-acd3aa2055c7',
          '20704415-b12e-44ba-80c7-09c56c4d76d8',
          'fb68d482-bae5-4c10-b605-6d9de7ce494f',
          '30a59767-c214-44bb-9694-945bf6833080',
          'b8895c61-9b73-4c33-a841-8fad7a3fe8f0',
          'f7f8e114-c917-42df-a34d-7808ae5a4dbc',
          '50993370-cea3-4881-a21e-c5adfe6b8aa5',
          '5d06e9a5-d2f6-4da0-a267-2884d71482de',
          '535c2aa0-739f-459c-9897-798347c8f671',
          '8dcd1489-5891-4155-9a4c-587ae1d603b7',
          'b4e698a1-34e7-4261-824e-856fc73455c5',
          'e1a8ee5a-b84f-4706-9985-a0ca02155a98',
          '57d35ea5-2966-4154-9872-04a4f531bd37',
          '66d8f1b7-859b-4a5b-b1a5-91e82e73f6b7',
          'ff2b5016-879b-4fca-8d68-6e5177692092',
          'f9b7c8df-456d-44e9-96c1-c6c09cbc9722',
          '36bba7cc-961e-43bf-a527-5e4efa979571',
          '69d817cf-7e82-492a-8f6c-34f93af04f21',
        ],
        data: { ppid: '5435786d-537d-4415-a83f-1059559d57e9' },
      },
      {
        id: '1bfaf93e-d4ff-4e1e-83a5-54de765666a7',
        type: 'INSTALL_CHARGING_STATION_V1',
        subscriptionId: '9507e4f1-9d7c-464b-bfc2-9dfa8c094b5c',
        owner: 'SYSTEM',
        status: 'SUCCESS',
        dependsOn: [
          'c2cfd2de-0da2-4f8e-85c3-e38e7bbbe521',
          'dc1f9e96-7c30-4120-8401-a35f193a4db2',
          '1ef8608c-c82c-4ef4-86e4-63f924609dd0',
          '348b0b15-64b4-484a-b3bb-a8a9451cc20d',
          '4dc0b01f-36ad-4546-8578-13f0a5c7da60',
          '0479c14f-3971-4244-b387-3e793272a819',
          'b16cd588-f567-46ee-aceb-9b2711797b3b',
          '697c5637-22bc-4a2c-9d66-b5a401fc5438',
          'c1595a7a-bac8-488a-bcab-94d241e14571',
          '1bc82d35-d7bb-4359-81aa-348f8493d09a',
          '1020edec-322f-4b92-b9cd-b1a93085fb88',
          '15a791db-b61c-4ed9-9e1b-d02cc80e663a',
          'ce2969cf-423d-42aa-a400-c79e28181882',
          '6d4bed28-5114-472c-98f5-9b173af23bdf',
          'a1273094-4ee1-4aaa-8e34-0dff02031118',
          '390d0ddf-e7ed-4c25-9ca9-fa3177d7579d',
          'b21d0165-9bdd-4c35-aa82-faa07d0e4400',
          '38f1eb70-5070-4512-961d-d3bc33c21e58',
          '82b8335e-2294-4709-abd5-a28fc9c0dbc2',
        ],
        data: { ppid: 'cf5f22da-2a09-4560-a63f-a02b50e4eb8d' },
      },
      {
        id: '34840111-c32b-45be-b48d-01961da09323',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: '91b27382-de50-4dfd-93d9-f582327135bd',
        owner: 'USER',
        status: 'FAILURE',
        dependsOn: [
          '9e8c6a56-6b06-4184-9081-066220f4cb9d',
          'e1ab9660-83e3-45a2-a044-6d4b15b8bcc6',
          '38330024-9059-4f22-9877-2faab72fff2c',
          '8fe1d46e-ccd4-4412-b136-7cfc95ed292d',
          'e779b62d-a8cf-49dd-883c-cfb8b2382cc5',
          '51830f14-5bcf-49c3-9e64-2e88688c6b0d',
          '67c6ad5a-e509-4670-93df-b0ec885b0504',
          '6f1e9764-27d4-4638-bcbc-fe2ecfe85285',
          '714f8838-2d3a-4cbf-aaf8-cf3d0866b642',
          '7188c985-0648-4c97-9a9e-25a6a4c79603',
        ],
        data: { surveyUrl: 'https://well-lit-acquaintance.com' },
      },
      {
        id: 'a26f88c2-3618-43b1-b7f2-741a0b7510a4',
        type: 'SIGN_REWARDS_TOS_V1',
        subscriptionId: 'f8490d7f-5090-4dbf-81cb-d7a2dad752d7',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          '55e034dd-c7c7-4b7b-b4c3-02196c690431',
          'f8e78518-b6c9-4802-9131-689adaf75126',
          '7e5a8128-cee7-41c0-830c-dc50703d698e',
          '0cc31dad-e610-4f44-a237-de49b9484b42',
          '2b998c39-698c-4d25-9376-60488ae64b13',
          'c04d6c47-548b-4a0c-8b9f-a5cc666644c5',
          '25b2c163-3d28-43c9-b1f6-50565d95330f',
          '1e8ccaf4-05b5-4fb3-ae9c-984a67f8c2a2',
          '8163add8-5396-400b-bb51-fb0f9cc4cd06',
          'e4252a19-a537-4c5f-9bd2-459b3050596b',
          '0c9e375b-1bd9-4dcb-bd75-1b9e2d014b42',
          '0c53f042-73a7-4dc6-b101-2f5b27bf3d2d',
          '42ff2441-1014-4ae2-98ca-2934bc38c741',
          '6a3dc47d-6843-4d44-9a04-61ddcc06be51',
          '56d9b854-2f6f-400b-a309-e7a9f15ae165',
          '60cbe20f-03f8-4dae-bcd0-79e1693fb352',
        ],
        data: { revision: 'urbanus amoveo trucido' },
      },
      {
        id: '647f0cfc-2bc5-4555-93e3-3cef48060521',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: '7dd0803d-0f6c-4f14-9ca6-4a5f8582ad28',
        owner: 'SYSTEM',
        status: 'SUCCESS',
        dependsOn: [
          '639b78f0-6e32-4b1c-9da1-8f7dc78694b9',
          'ee37ade2-56dc-4181-80a6-850f9c3fc3eb',
          'd797c07a-6cd0-491e-ae45-70eb540d7cde',
          '215db7b4-90a7-4a5e-a1e0-e75b5f3f2b38',
          '57cf7e42-22c4-4a72-b1e1-e27aedd4b12a',
          '02745158-8291-40d2-b2d4-fa2c6d7b8852',
          '8209a340-41f5-49dc-ad8c-a46bc3a73adf',
          '6003ed80-17a5-4fdf-854c-7fffd17cb9bc',
          '4a373896-655c-401e-924b-a94341a3ac19',
          '0844c4ec-6b78-4965-b945-91c7661d6d47',
          '1b1e2e87-251e-42fe-b620-127ffb349841',
          '9f27a67a-24ab-4a2f-9a1b-aed62bf9bfe0',
        ],
        data: { surveyUrl: 'https://coarse-extension.net' },
      },
      {
        id: '848e1d9f-a3bd-4a99-a1ce-73e7c8cdbda9',
        type: 'LINK_EXISTING_CHARGER_V1',
        subscriptionId: '07840f23-6ea8-4461-b140-1fd8ab5527ec',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          '5a932668-dacb-4443-b48f-49b86bed3ae5',
          '9274b982-fd9c-4e45-a62b-11d30c2b75ee',
          'b0d6856d-8eef-4bc4-8b22-94f9d5100c76',
          'c187e6c8-427d-4a13-a192-b3894d675896',
          '91ccd18c-bbbf-45d2-bfb5-889997bf8ef4',
          'a2a0f7f5-be52-4672-8003-ecac8b658c21',
          '1fbb9418-efe6-4b53-ab9a-1fe10be5560e',
          'd52af19a-81aa-4ed2-9feb-d3f832d5c797',
          'a5ca8251-70d1-421a-b010-d044c9415487',
          'e6a70c48-63e8-4d9d-adde-bd64b48e9974',
          '6ff895c9-684d-4acf-9645-147e9217dd27',
          '739b127e-cbbc-4787-ac36-76ced46b3be2',
        ],
        data: { ppid: 'b9f94c2d-1b19-48a8-8921-70035b20b77f' },
      },
      {
        id: '00074410-9228-4003-b3fc-5c6b4d8528b7',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: '1380b9a8-1ba0-4e9c-a2ea-a58222b713af',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: ['34dd11b1-5740-47b9-846b-e80cae0ec973'],
        data: { surveyUrl: 'https://far-off-gradient.info/' },
      },
      {
        id: 'baaca1bc-037d-422a-87e6-49158c08998e',
        type: 'SIGN_DOCUMENTS_V1',
        subscriptionId: 'b31611f7-1d96-4b0a-83a6-0769ceefbd00',
        owner: 'SYSTEM',
        status: 'SUCCESS',
        dependsOn: [
          '1413907a-0252-4d55-b29a-4cddef93167a',
          'ae9b0f98-47a8-4d72-ba78-70911909ec10',
          '20d1c53f-293f-4218-aca6-d277f7c8fbdc',
          '7a86f00b-6ee5-4950-9776-f89f66d06366',
          'daeb0df0-82ca-4166-9daf-a8c6a2e95481',
        ],
        data: {
          documents: [
            {
              code: 'rca',
              signingUrl: 'https://ironclad-heartbeat.biz',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://ecstatic-poppy.info/',
              signed: true,
            },
            {
              code: 'ha',
              signingUrl: 'https://tricky-unblinking.biz/',
              signed: true,
            },
            { code: 'ha', signingUrl: 'https://sore-fork.com', signed: true },
            {
              code: 'rca',
              signingUrl: 'https://shadowy-deed.net',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://stark-coordination.info/',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://criminal-molasses.com',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://back-inspection.biz',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://average-resource.biz/',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://queasy-publication.name',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://livid-tomatillo.name/',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://sizzling-airman.net',
              signed: true,
            },
            { code: 'rca', signingUrl: 'https://rare-case.org', signed: true },
            {
              code: 'ha',
              signingUrl: 'https://meaty-redesign.com',
              signed: true,
            },
            {
              code: 'rca',
              signingUrl: 'https://monthly-someplace.org/',
              signed: false,
            },
          ],
        },
      },
      {
        id: 'a8d2c8e0-ae17-45e9-8199-a727eabc9971',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: 'd57edd1b-ec82-49fd-bb9a-36cd554f842f',
        owner: 'USER',
        status: 'FAILURE',
        dependsOn: [
          'e0eb9c25-c304-4265-9ade-292433da6a71',
          '8ce5bedf-f117-47ab-a435-6cbfbefdf579',
          '8d34b883-95b1-48da-b8bd-226949ade4a5',
          '3db33750-806c-43d8-aa85-8947a6976891',
          'e9817e3a-cd6a-4faf-8080-310a4a20edfd',
          '0a3f7b64-d2a7-45ef-bd94-61f533cb622f',
          '60c8b159-dede-49f5-8579-af32bf4dbc6e',
          'e39a8286-c5e3-4561-bffb-374fe3dd0dc5',
          'd3c1721c-3a5e-4386-bc59-cdd5fa3eab7c',
          'd9e514e8-99e8-401a-98ca-bdd75de35df9',
          '5f550375-5aaa-4375-be28-216e82622404',
          'b89135d3-414d-4b6a-8c11-9342158040b2',
          '8311e26f-6b95-4cab-a49f-843332bd28ce',
          '622a6155-558a-476e-911b-e620c41ada20',
          '30d51a0f-9b1e-4815-8737-9cec25029e8a',
          '29e1ecf6-0fa1-4582-abc0-22e21d100db8',
          '2c0f1d44-71d1-463d-8b69-6b44d860bbbe',
          '600d03e3-1bfc-4fff-89d2-5c92cd0533c7',
          '491461ef-499c-451a-9b0e-4a21d39644eb',
          '4848e45d-2a98-4df2-92ee-e14bcf9c6275',
        ],
        data: { surveyUrl: 'https://mad-toothbrush.com' },
      },
      {
        id: '83b3ca41-d3dc-44ac-ae59-aaf3c2d31929',
        type: 'SIGN_DOCUMENTS_V1',
        subscriptionId: '6c531fee-d797-4b23-8ffc-f7ae908e7cbf',
        owner: 'USER',
        status: 'PENDING',
        dependsOn: [
          '4b7e263f-db91-487a-9551-3434fad193fe',
          '2ac321dc-a9c3-447f-8db1-f5208265a054',
          'd865efd4-a655-42fd-b65f-f9e36547fc0b',
          '1282eee4-1b53-46e6-9400-817f7715bce9',
          'c1494bfc-09e6-4feb-833a-4409d0665d65',
          '1a3b6d4d-7bba-419f-9628-2348311cef34',
          'f5783975-09ad-407a-a96e-22eddd6f4b93',
          'd3e3eb12-9ffd-48da-b398-5e4d567fe4b9',
          '71c15b55-f3a2-4c2b-a64b-02d494b6d700',
          '60dc750a-d070-4e34-a13a-b0b00655a4a0',
        ],
        data: {
          documents: [
            {
              code: 'ha',
              signingUrl: 'https://outlying-coordination.info/',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://square-summer.info',
              signed: true,
            },
            {
              code: 'rca',
              signingUrl: 'https://prickly-legging.org/',
              signed: false,
            },
            {
              code: 'rca',
              signingUrl: 'https://criminal-merit.com/',
              signed: false,
            },
            {
              code: 'ha',
              signingUrl: 'https://glaring-illusion.org',
              signed: true,
            },
            {
              code: 'ha',
              signingUrl: 'https://elementary-summary.info/',
              signed: true,
            },
          ],
        },
      },
      {
        id: '24e44e20-ca18-4940-ac4d-59ff0176030e',
        type: 'PAY_UPFRONT_FEE_V1',
        subscriptionId: 'f77b4ebf-61ef-4464-b63f-07303196648b',
        owner: 'SYSTEM',
        status: 'PENDING',
        dependsOn: [
          '51369ada-f46c-4632-8ebb-17aecfb4df3a',
          '4ff2e8b1-7e00-4773-a240-50939eb9a7c1',
          '3dd42577-9a6d-4900-8e20-99edbc88b519',
          '4d98e532-dc55-4ece-b40d-6f23e80e43fb',
          '94b1ef0f-c98a-4ca7-81c0-130fe84c714b',
          '575203c1-10f2-47b5-9773-d6eab264b873',
          '24811c28-c98b-4393-a6f8-34a38a272c1d',
          '2d4b968f-bc95-417a-8296-b92518d83068',
          'bfe14d63-8bec-4154-88a2-a589e194aa2d',
          'ae6c4c02-25d4-44eb-bc8e-de8a3b854827',
          'f120406a-1897-4498-87ba-6723d985e2db',
          '8978464d-2e48-4177-9ad6-5cff47ca3d03',
        ],
        data: {},
      },
      {
        id: 'acc10d18-67ec-4fd0-93db-48bf4eeffbdd',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: '84b9af71-ec17-48ca-8bd5-ffc48e50efda',
        owner: 'USER',
        status: 'FAILURE',
        dependsOn: [
          '83a87343-7318-415f-9936-9bf5f6fbd908',
          'bf68d763-6a19-4cac-82b9-c4dcaad1802e',
          '68c24f7f-3bd4-4d3c-a064-fbcf915c00af',
        ],
        data: { surveyUrl: 'https://huge-blight.net' },
      },
      {
        id: '38f88f2e-600d-4672-91f8-e967dc6687d2',
        type: 'COMPLETE_HOME_SURVEY_V1',
        subscriptionId: '20441d17-0508-43b6-86de-4a90acb260de',
        owner: 'USER',
        status: 'SUCCESS',
        dependsOn: [
          '9d3c8f8f-6d1c-4ffe-90ad-4197574e01c0',
          '5730bb39-abee-49a6-b37b-62337b5de223',
          'b42060bd-f066-4c1e-9eb8-a94a2d8b77d2',
          'b7e56284-354c-49fc-a026-26e54a96333f',
          '358cf6af-b39e-4458-b625-c9d9e2ddbc5c',
          '61757d33-8c88-485e-afe2-96dbe981cd4c',
          '53c7bb2d-beca-4588-8295-9d2b28ec097f',
          '83bb51a4-7ef5-4a1f-8f5e-12f653ef11bf',
        ],
        data: { surveyUrl: 'https://inferior-bog.biz' },
      },
    ],
    activatedAt: '2025-01-01T00:00:00.000Z',
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:00:00.000Z',
    deletedAt: '2025-01-01T00:00:00.000Z',
    plan: {
      id: '873860a9-fc4b-4a28-8381-6ad748910f76',
      type: 'POD_DRIVE',
      productCode: 'sint defessus bene',
      allowanceMiles: 10000,
      allowancePeriod: 'ANNUAL',
      upfrontFeePounds: 99,
      discountedUpfrontFeePounds: 45,
      monthlyFeePounds: 35,
      contractDurationMonths: 18,
      ratePencePerMile: 2.28,
      rateMilesPerKwh: 3.5,
      milesRenewalDate: '2025-07-11T06:00:31.162Z',
    },
  };
}

export function getSubscriptionsControllerGetByActionId200Response() {
  return {
    id: 'fd30c8a6-a294-4635-a3c4-feae435970d0',
    type: 'INSTALL_CHARGING_STATION_V1',
    subscriptionId: '2bb2b5a5-f565-469f-8d85-5845d30527f1',
    owner: 'USER',
    status: 'FAILURE',
    dependsOn: [
      '4fc4d7af-a5c2-48ba-b2a6-e67b6e6022cf',
      'aa2cd59e-d3cc-4080-898b-2d155bbda6b0',
      'ea01c5bc-16df-403f-945a-bda268132a8c',
      '4b1ceb3a-f15e-4887-9c32-ae6c3552314a',
      '59934755-c186-457b-8ed1-c848a45fae03',
      '362182f6-749a-41ee-8941-7b182a9c422d',
      '95af2f6e-18d3-48b5-ba61-4ca08446ab89',
      'e7334848-a924-4acb-a35d-a9c47a877295',
      '02a6506e-d6a7-4f65-afca-f02a1bf58e3c',
      'ecc4a3ce-af3f-4bd2-8fd3-3dfebfca51af',
      'ac2a5c41-8227-46bd-ad03-a578f62f35e9',
      'eae765a8-e299-43a4-a488-60724f0517de',
      '0486fa4a-7fc5-4b34-b321-e0da434297f1',
      'f13cdd02-908d-47d9-9df5-edc430ab479d',
      '587b0085-572d-4b63-adb6-5207719cf251',
    ],
    data: { ppid: '7661da34-1bf7-4482-973e-869fc8be0bb3' },
  };
}

export function getSubscriptionsControllerUpdateActionById200Response() {
  return {
    id: 'b5dca9d8-b573-4566-8cab-850746e2b622',
    type: 'LINK_EXISTING_CHARGER_V1',
    subscriptionId: '666090f3-2cf3-4381-b534-f4a91e36c88e',
    owner: 'SYSTEM',
    status: 'FAILURE',
    dependsOn: [
      'fe4ff0a7-a8da-44da-aa6d-36555c5ec140',
      '3803c73e-37dd-4b24-b0ce-172348ed254b',
      'b88ca5e6-cdca-4180-b678-751b04809bd0',
      '97c963a0-a778-48ec-b090-f14e599d89c5',
      'eadba74b-a538-44f2-a423-94e436402b45',
      '985eb52f-c265-499d-82e2-084c0d0e4f4d',
      'b4b9f903-42b6-4d22-b393-dadce0a8a27d',
      'fa8521a6-4234-404f-9583-b5e568763d48',
    ],
    data: { ppid: 'd3ca0385-c0c7-455b-bcb9-e294bd1b4fbf' },
  };
}

export function getSubscriptionsControllerGetSubscriptionDirectDebit200Response() {
  return {
    accountNumberLastDigits: '1234',
    sortCodeLastDigits: '23',
    nameOnAccount: 'Mr Tom Wallace',
    monthlyPaymentDay: 13,
  };
}

export function getSubscriptionsControllerGetSubscriptionDocuments200Response() {
  return {
    documents: [
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'rca',
      },
      {
        issued: '2025-05-28T18:28:21.959Z',
        link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
        format: 'PDF',
        active: true,
        type: 'ha',
      },
    ],
  };
}
handlers.push(
  http.get(
    `${baseURL}/subscriptions/:subscriptionId/documents/:documentId`,
    () => {
      const stream = new ReadableStream({
        start: (controller) => controller.close(),
      });

      return new HttpResponse(stream, {
        headers: {
          'Content-Type': 'application/pdf',
        },
      });
    }
  )
);
