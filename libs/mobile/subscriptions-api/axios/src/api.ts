/* tslint:disable */
/* eslint-disable */
/**
 * Subscription API
 * Subscription API Service
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @enum {string}
 */

export const ActionOwner = {
  User: 'USER',
  System: 'SYSTEM',
} as const;

export type ActionOwner = (typeof ActionOwner)[keyof typeof ActionOwner];

/**
 *
 * @export
 * @enum {string}
 */

export const ActionStatus = {
  Pending: 'PENDING',
  Success: 'SUCCESS',
  Failure: 'FAILURE',
} as const;

export type ActionStatus = (typeof ActionStatus)[keyof typeof ActionStatus];

/**
 *
 * @export
 * @interface CheckAffordabilityActionDTO
 */
export interface CheckAffordabilityActionDTO {
  /**
   *
   * @type {string}
   * @memberof CheckAffordabilityActionDTO
   */
  id: string;
  /**
   * The type of action
   * @type {string}
   * @memberof CheckAffordabilityActionDTO
   */
  type: CheckAffordabilityActionDTOTypeEnum;
  /**
   *
   * @type {string}
   * @memberof CheckAffordabilityActionDTO
   */
  subscriptionId: string;
  /**
   *
   * @type {ActionOwner}
   * @memberof CheckAffordabilityActionDTO
   */
  owner: ActionOwner;
  /**
   *
   * @type {ActionStatus}
   * @memberof CheckAffordabilityActionDTO
   */
  status: ActionStatus;
  /**
   *
   * @type {Array<string>}
   * @memberof CheckAffordabilityActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {CheckAffordabilityDataDTO}
   * @memberof CheckAffordabilityActionDTO
   */
  data: CheckAffordabilityDataDTO;
}

export const CheckAffordabilityActionDTOTypeEnum = {
  CheckAffordabilityV1: 'CHECK_AFFORDABILITY_V1',
} as const;

export type CheckAffordabilityActionDTOTypeEnum =
  (typeof CheckAffordabilityActionDTOTypeEnum)[keyof typeof CheckAffordabilityActionDTOTypeEnum];

/**
 *
 * @export
 * @interface CheckAffordabilityDataDTO
 */
export interface CheckAffordabilityDataDTO {
  /**
   * The application id of the check affordability check
   * @type {number}
   * @memberof CheckAffordabilityDataDTO
   */
  applicationId: number | null;
  /**
   * The loan id from the check affordability check
   * @type {number}
   * @memberof CheckAffordabilityDataDTO
   */
  loanId: number | null;
}
/**
 *
 * @export
 * @interface CreatePodDriveRewardsSubscriptionDTO
 */
export interface CreatePodDriveRewardsSubscriptionDTO {
  /**
   * The ID of the user who the subscription belongs to
   * @type {string}
   * @memberof CreatePodDriveRewardsSubscriptionDTO
   */
  userId: string;
  /**
   * The type of the plan
   * @type {string}
   * @memberof CreatePodDriveRewardsSubscriptionDTO
   */
  planType: CreatePodDriveRewardsSubscriptionDTOPlanTypeEnum;
  /**
   * The subscription order
   * @type {CreatePodDriveRewardsSubscriptionOrderDTO}
   * @memberof CreatePodDriveRewardsSubscriptionDTO
   */
  order: CreatePodDriveRewardsSubscriptionOrderDTO;
}

export const CreatePodDriveRewardsSubscriptionDTOPlanTypeEnum = {
  PodDriveRewards: 'POD_DRIVE_REWARDS',
} as const;

export type CreatePodDriveRewardsSubscriptionDTOPlanTypeEnum =
  (typeof CreatePodDriveRewardsSubscriptionDTOPlanTypeEnum)[keyof typeof CreatePodDriveRewardsSubscriptionDTOPlanTypeEnum];

/**
 *
 * @export
 * @interface CreatePodDriveRewardsSubscriptionOrderDTO
 */
export interface CreatePodDriveRewardsSubscriptionOrderDTO {
  /**
   * The PPID of the charger the subscription is for
   * @type {string}
   * @memberof CreatePodDriveRewardsSubscriptionOrderDTO
   */
  ppid: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HomeSurveyActionDTO
 */
export interface HomeSurveyActionDTO {
  /**
   *
   * @type {string}
   * @memberof HomeSurveyActionDTO
   */
  id: string;
  /**
   * The type of action
   * @type {string}
   * @memberof HomeSurveyActionDTO
   */
  type: HomeSurveyActionDTOTypeEnum;
  /**
   *
   * @type {string}
   * @memberof HomeSurveyActionDTO
   */
  subscriptionId: string;
  /**
   *
   * @type {ActionOwner}
   * @memberof HomeSurveyActionDTO
   */
  owner: ActionOwner;
  /**
   *
   * @type {ActionStatus}
   * @memberof HomeSurveyActionDTO
   */
  status: ActionStatus;
  /**
   *
   * @type {Array<string>}
   * @memberof HomeSurveyActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {HomeSurveyActionDataDTO}
   * @memberof HomeSurveyActionDTO
   */
  data: HomeSurveyActionDataDTO;
}

export const HomeSurveyActionDTOTypeEnum = {
  CompleteHomeSurveyV1: 'COMPLETE_HOME_SURVEY_V1',
} as const;

export type HomeSurveyActionDTOTypeEnum =
  (typeof HomeSurveyActionDTOTypeEnum)[keyof typeof HomeSurveyActionDTOTypeEnum];

/**
 *
 * @export
 * @interface HomeSurveyActionDataDTO
 */
export interface HomeSurveyActionDataDTO {
  /**
   * URL of the survey
   * @type {string}
   * @memberof HomeSurveyActionDataDTO
   */
  surveyUrl: string;
}
/**
 *
 * @export
 * @interface InstallChargingStationActionDTO
 */
export interface InstallChargingStationActionDTO {
  /**
   *
   * @type {string}
   * @memberof InstallChargingStationActionDTO
   */
  id: string;
  /**
   * The type of action
   * @type {string}
   * @memberof InstallChargingStationActionDTO
   */
  type: InstallChargingStationActionDTOTypeEnum;
  /**
   *
   * @type {string}
   * @memberof InstallChargingStationActionDTO
   */
  subscriptionId: string;
  /**
   *
   * @type {ActionOwner}
   * @memberof InstallChargingStationActionDTO
   */
  owner: ActionOwner;
  /**
   *
   * @type {ActionStatus}
   * @memberof InstallChargingStationActionDTO
   */
  status: ActionStatus;
  /**
   *
   * @type {Array<string>}
   * @memberof InstallChargingStationActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {InstallChargingStationDataDTO}
   * @memberof InstallChargingStationActionDTO
   */
  data: InstallChargingStationDataDTO;
}

export const InstallChargingStationActionDTOTypeEnum = {
  InstallChargingStationV1: 'INSTALL_CHARGING_STATION_V1',
} as const;

export type InstallChargingStationActionDTOTypeEnum =
  (typeof InstallChargingStationActionDTOTypeEnum)[keyof typeof InstallChargingStationActionDTOTypeEnum];

/**
 *
 * @export
 * @interface InstallChargingStationDataDTO
 */
export interface InstallChargingStationDataDTO {
  /**
   * The PPID of the charging station. Set once installed.
   * @type {string}
   * @memberof InstallChargingStationDataDTO
   */
  ppid: string | null;
}
/**
 *
 * @export
 * @interface LinkExistingChargerActionDTO
 */
export interface LinkExistingChargerActionDTO {
  /**
   *
   * @type {string}
   * @memberof LinkExistingChargerActionDTO
   */
  id: string;
  /**
   * The type of action
   * @type {string}
   * @memberof LinkExistingChargerActionDTO
   */
  type: LinkExistingChargerActionDTOTypeEnum;
  /**
   *
   * @type {string}
   * @memberof LinkExistingChargerActionDTO
   */
  subscriptionId: string;
  /**
   *
   * @type {ActionOwner}
   * @memberof LinkExistingChargerActionDTO
   */
  owner: ActionOwner;
  /**
   *
   * @type {ActionStatus}
   * @memberof LinkExistingChargerActionDTO
   */
  status: ActionStatus;
  /**
   *
   * @type {Array<string>}
   * @memberof LinkExistingChargerActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {LinkExistingChargerActionDataDTO}
   * @memberof LinkExistingChargerActionDTO
   */
  data: LinkExistingChargerActionDataDTO;
}

export const LinkExistingChargerActionDTOTypeEnum = {
  LinkExistingChargerV1: 'LINK_EXISTING_CHARGER_V1',
} as const;

export type LinkExistingChargerActionDTOTypeEnum =
  (typeof LinkExistingChargerActionDTOTypeEnum)[keyof typeof LinkExistingChargerActionDTOTypeEnum];

/**
 *
 * @export
 * @interface LinkExistingChargerActionDataDTO
 */
export interface LinkExistingChargerActionDataDTO {
  /**
   * The PPID of the charger which has been linked
   * @type {string}
   * @memberof LinkExistingChargerActionDataDTO
   */
  ppid: string | null;
}
/**
 *
 * @export
 * @interface ListSubscriptionsDTO
 */
export interface ListSubscriptionsDTO {
  /**
   * list of subscriptions
   * @type {Array<PersistedSubscriptionDTO>}
   * @memberof ListSubscriptionsDTO
   */
  subscriptions: Array<PersistedSubscriptionDTO>;
}
/**
 *
 * @export
 * @interface OrderAddressDTO
 */
export interface OrderAddressDTO {
  /**
   * Line 1 of the address
   * @type {string}
   * @memberof OrderAddressDTO
   */
  line1: string;
  /**
   * Line 1 of the address
   * @type {string}
   * @memberof OrderAddressDTO
   */
  line2: string | null;
  /**
   * Line 1 of the address
   * @type {string}
   * @memberof OrderAddressDTO
   */
  line3: string | null;
  /**
   * Postcode of the address
   * @type {string}
   * @memberof OrderAddressDTO
   */
  postcode: string;
}
/**
 *
 * @export
 * @interface PayUpfrontFeeActionDTO
 */
export interface PayUpfrontFeeActionDTO {
  /**
   *
   * @type {string}
   * @memberof PayUpfrontFeeActionDTO
   */
  id: string;
  /**
   * The type of action
   * @type {string}
   * @memberof PayUpfrontFeeActionDTO
   */
  type: PayUpfrontFeeActionDTOTypeEnum;
  /**
   *
   * @type {string}
   * @memberof PayUpfrontFeeActionDTO
   */
  subscriptionId: string;
  /**
   *
   * @type {ActionOwner}
   * @memberof PayUpfrontFeeActionDTO
   */
  owner: ActionOwner;
  /**
   *
   * @type {ActionStatus}
   * @memberof PayUpfrontFeeActionDTO
   */
  status: ActionStatus;
  /**
   *
   * @type {Array<string>}
   * @memberof PayUpfrontFeeActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {object}
   * @memberof PayUpfrontFeeActionDTO
   */
  data: object;
}

export const PayUpfrontFeeActionDTOTypeEnum = {
  PayUpfrontFeeV1: 'PAY_UPFRONT_FEE_V1',
} as const;

export type PayUpfrontFeeActionDTOTypeEnum =
  (typeof PayUpfrontFeeActionDTOTypeEnum)[keyof typeof PayUpfrontFeeActionDTOTypeEnum];

/**
 *
 * @export
 * @interface PersistedSubscriptionDTO
 */
export interface PersistedSubscriptionDTO {
  /**
   * subscription id
   * @type {string}
   * @memberof PersistedSubscriptionDTO
   */
  id: string;
  /**
   * firebase user id
   * @type {string}
   * @memberof PersistedSubscriptionDTO
   */
  userId: string;
  /**
   * subscription status
   * @type {string}
   * @memberof PersistedSubscriptionDTO
   */
  status: PersistedSubscriptionDTOStatusEnum;
  /**
   *
   * @type {PersistedSubscriptionDTOOrder}
   * @memberof PersistedSubscriptionDTO
   */
  order: PersistedSubscriptionDTOOrder;
  /**
   * actions associated with the subscription
   * @type {Array<PersistedSubscriptionDTOActionsInner>}
   * @memberof PersistedSubscriptionDTO
   */
  actions: Array<PersistedSubscriptionDTOActionsInner>;
  /**
   * subscription activation date
   * @type {string}
   * @memberof PersistedSubscriptionDTO
   */
  activatedAt: string | null;
  /**
   * subscription creation date
   * @type {string}
   * @memberof PersistedSubscriptionDTO
   */
  createdAt: string;
  /**
   * subscription last updated date
   * @type {string}
   * @memberof PersistedSubscriptionDTO
   */
  updatedAt: string;
  /**
   * subscription deleted date
   * @type {string}
   * @memberof PersistedSubscriptionDTO
   */
  deletedAt: string | null;
  /**
   *
   * @type {PersistedSubscriptionDTOPlan}
   * @memberof PersistedSubscriptionDTO
   */
  plan: PersistedSubscriptionDTOPlan;
}

export const PersistedSubscriptionDTOStatusEnum = {
  Pending: 'PENDING',
  Active: 'ACTIVE',
  Cancelled: 'CANCELLED',
  Suspended: 'SUSPENDED',
  Ended: 'ENDED',
  Rejected: 'REJECTED',
} as const;

export type PersistedSubscriptionDTOStatusEnum =
  (typeof PersistedSubscriptionDTOStatusEnum)[keyof typeof PersistedSubscriptionDTOStatusEnum];

/**
 * @type PersistedSubscriptionDTOActionsInner
 * @export
 */
export type PersistedSubscriptionDTOActionsInner =
  | CheckAffordabilityActionDTO
  | HomeSurveyActionDTO
  | InstallChargingStationActionDTO
  | LinkExistingChargerActionDTO
  | PayUpfrontFeeActionDTO
  | SetupDirectDebitActionDTO
  | SignDocumentsActionDTO
  | SignRewardsTOSActionDTO;

/**
 * @type PersistedSubscriptionDTOOrder
 * order associated with the subscription
 * @export
 */
export type PersistedSubscriptionDTOOrder =
  | SalesforceOrderDTO
  | SelfServiceOrderDTO;

/**
 * @type PersistedSubscriptionDTOPlan
 * The plan information for the given subscription - currently only PodDrive
 * @export
 */
export type PersistedSubscriptionDTOPlan =
  | PodDrivePlanDTO
  | PodDriveRewardsPlanDTO;

/**
 *
 * @export
 * @interface PodDrivePlanDTO
 */
export interface PodDrivePlanDTO {
  /**
   * The ID of plan
   * @type {string}
   * @memberof PodDrivePlanDTO
   */
  id: string;
  /**
   * The type of plan
   * @type {string}
   * @memberof PodDrivePlanDTO
   */
  type: PodDrivePlanDTOTypeEnum;
  /**
   * The product code
   * @type {string}
   * @memberof PodDrivePlanDTO
   */
  productCode: string;
  /**
   * The number of miles allowed to be claimed
   * @type {number}
   * @memberof PodDrivePlanDTO
   */
  allowanceMiles: number;
  /**
   * How often the allowance resets
   * @type {string}
   * @memberof PodDrivePlanDTO
   */
  allowancePeriod: PodDrivePlanDTOAllowancePeriodEnum;
  /**
   * How much the plan costs upfront in GBP
   * @type {number}
   * @memberof PodDrivePlanDTO
   */
  upfrontFeePounds: number;
  /**
   * The discounted upfront fee in GBP
   * @type {number}
   * @memberof PodDrivePlanDTO
   */
  discountedUpfrontFeePounds?: number;
  /**
   * How much the plan costs per month in GBP
   * @type {number}
   * @memberof PodDrivePlanDTO
   */
  monthlyFeePounds: number;
  /**
   * How many months the contract is for
   * @type {number}
   * @memberof PodDrivePlanDTO
   */
  contractDurationMonths: number;
  /**
   * The conversion rate between pence and miles earned
   * @type {number}
   * @memberof PodDrivePlanDTO
   */
  ratePencePerMile: number;
  /**
   * The conversion rate between miles and Kwh
   * @type {number}
   * @memberof PodDrivePlanDTO
   */
  rateMilesPerKwh: number;
  /**
   * When the miles renew
   * @type {string}
   * @memberof PodDrivePlanDTO
   */
  milesRenewalDate: string | null;
}

export const PodDrivePlanDTOTypeEnum = {
  PodDrive: 'POD_DRIVE',
} as const;

export type PodDrivePlanDTOTypeEnum =
  (typeof PodDrivePlanDTOTypeEnum)[keyof typeof PodDrivePlanDTOTypeEnum];
export const PodDrivePlanDTOAllowancePeriodEnum = {
  Annual: 'ANNUAL',
} as const;

export type PodDrivePlanDTOAllowancePeriodEnum =
  (typeof PodDrivePlanDTOAllowancePeriodEnum)[keyof typeof PodDrivePlanDTOAllowancePeriodEnum];

/**
 *
 * @export
 * @interface PodDriveRewardsPlanDTO
 */
export interface PodDriveRewardsPlanDTO {
  /**
   * The ID of plan
   * @type {string}
   * @memberof PodDriveRewardsPlanDTO
   */
  id: string;
  /**
   * The type of plan
   * @type {string}
   * @memberof PodDriveRewardsPlanDTO
   */
  type: PodDriveRewardsPlanDTOTypeEnum;
  /**
   * The number of miles allowed to be claimed
   * @type {number}
   * @memberof PodDriveRewardsPlanDTO
   */
  allowanceMiles: number;
  /**
   * How often the allowance resets
   * @type {string}
   * @memberof PodDriveRewardsPlanDTO
   */
  allowancePeriod: PodDriveRewardsPlanDTOAllowancePeriodEnum;
  /**
   * The conversion rate between pence and miles earned
   * @type {number}
   * @memberof PodDriveRewardsPlanDTO
   */
  ratePencePerMile: number;
  /**
   * The conversion rate between miles and Kwh
   * @type {number}
   * @memberof PodDriveRewardsPlanDTO
   */
  rateMilesPerKwh: number;
  /**
   * When the miles renew
   * @type {string}
   * @memberof PodDriveRewardsPlanDTO
   */
  milesRenewalDate: string | null;
}

export const PodDriveRewardsPlanDTOTypeEnum = {
  PodDriveRewards: 'POD_DRIVE_REWARDS',
} as const;

export type PodDriveRewardsPlanDTOTypeEnum =
  (typeof PodDriveRewardsPlanDTOTypeEnum)[keyof typeof PodDriveRewardsPlanDTOTypeEnum];
export const PodDriveRewardsPlanDTOAllowancePeriodEnum = {
  Annual: 'ANNUAL',
} as const;

export type PodDriveRewardsPlanDTOAllowancePeriodEnum =
  (typeof PodDriveRewardsPlanDTOAllowancePeriodEnum)[keyof typeof PodDriveRewardsPlanDTOAllowancePeriodEnum];

/**
 *
 * @export
 * @interface SalesforceOrderDTO
 */
export interface SalesforceOrderDTO {
  /**
   * id of the order
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  id: string;
  /**
   * origin of the order
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  origin: SalesforceOrderDTOOriginEnum;
  /**
   * date order was placed
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  orderedAt: string;
  /**
   *
   * @type {OrderAddressDTO}
   * @memberof SalesforceOrderDTO
   */
  address: OrderAddressDTO;
  /**
   * The person who placed the order\'s MPAN
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  mpan: string;
  /**
   * The first name of the person who placed the order
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  firstName: string;
  /**
   * The last name of the person who placed the order
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  lastName: string;
  /**
   * The email address of the person who placed the order
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  email: string;
  /**
   * The phone number of the person who placed the order
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  phoneNumber: string;
  /**
   * The eCommerceId associated with the order
   * @type {string}
   * @memberof SalesforceOrderDTO
   */
  eCommerceId: string;
}

export const SalesforceOrderDTOOriginEnum = {
  Salesforce: 'SALESFORCE',
  SalesforceTest: 'SALESFORCE_TEST',
  PodDriveRewardsSelfServe: 'POD_DRIVE_REWARDS_SELF_SERVE',
} as const;

export type SalesforceOrderDTOOriginEnum =
  (typeof SalesforceOrderDTOOriginEnum)[keyof typeof SalesforceOrderDTOOriginEnum];

/**
 *
 * @export
 * @interface SelfServiceOrderDTO
 */
export interface SelfServiceOrderDTO {
  /**
   * id of the order
   * @type {string}
   * @memberof SelfServiceOrderDTO
   */
  id: string;
  /**
   * origin of the order
   * @type {string}
   * @memberof SelfServiceOrderDTO
   */
  origin: SelfServiceOrderDTOOriginEnum;
  /**
   * date order was placed
   * @type {string}
   * @memberof SelfServiceOrderDTO
   */
  orderedAt: string;
  /**
   * The PPID of the charger the subscription is for
   * @type {string}
   * @memberof SelfServiceOrderDTO
   */
  ppid: string;
}

export const SelfServiceOrderDTOOriginEnum = {
  Salesforce: 'SALESFORCE',
  SalesforceTest: 'SALESFORCE_TEST',
  PodDriveRewardsSelfServe: 'POD_DRIVE_REWARDS_SELF_SERVE',
} as const;

export type SelfServiceOrderDTOOriginEnum =
  (typeof SelfServiceOrderDTOOriginEnum)[keyof typeof SelfServiceOrderDTOOriginEnum];

/**
 *
 * @export
 * @interface SetupDirectDebitActionDTO
 */
export interface SetupDirectDebitActionDTO {
  /**
   *
   * @type {string}
   * @memberof SetupDirectDebitActionDTO
   */
  id: string;
  /**
   * The type of action
   * @type {string}
   * @memberof SetupDirectDebitActionDTO
   */
  type: SetupDirectDebitActionDTOTypeEnum;
  /**
   *
   * @type {string}
   * @memberof SetupDirectDebitActionDTO
   */
  subscriptionId: string;
  /**
   *
   * @type {ActionOwner}
   * @memberof SetupDirectDebitActionDTO
   */
  owner: ActionOwner;
  /**
   *
   * @type {ActionStatus}
   * @memberof SetupDirectDebitActionDTO
   */
  status: ActionStatus;
  /**
   *
   * @type {Array<string>}
   * @memberof SetupDirectDebitActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {object}
   * @memberof SetupDirectDebitActionDTO
   */
  data: object;
}

export const SetupDirectDebitActionDTOTypeEnum = {
  SetupDirectDebitV1: 'SETUP_DIRECT_DEBIT_V1',
} as const;

export type SetupDirectDebitActionDTOTypeEnum =
  (typeof SetupDirectDebitActionDTOTypeEnum)[keyof typeof SetupDirectDebitActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SignDocumentDTO
 */
export interface SignDocumentDTO {
  /**
   * The document type
   * @type {string}
   * @memberof SignDocumentDTO
   */
  code: SignDocumentDTOCodeEnum;
  /**
   * The URL for the user to sign the loan agreement.
   * @type {string}
   * @memberof SignDocumentDTO
   */
  signingUrl: string | null;
  /**
   * Is the document signed.
   * @type {boolean}
   * @memberof SignDocumentDTO
   */
  signed: boolean;
}

export const SignDocumentDTOCodeEnum = {
  Rca: 'rca',
  Ha: 'ha',
} as const;

export type SignDocumentDTOCodeEnum =
  (typeof SignDocumentDTOCodeEnum)[keyof typeof SignDocumentDTOCodeEnum];

/**
 *
 * @export
 * @interface SignDocumentsActionDTO
 */
export interface SignDocumentsActionDTO {
  /**
   *
   * @type {string}
   * @memberof SignDocumentsActionDTO
   */
  id: string;
  /**
   * The type of action
   * @type {string}
   * @memberof SignDocumentsActionDTO
   */
  type: SignDocumentsActionDTOTypeEnum;
  /**
   *
   * @type {string}
   * @memberof SignDocumentsActionDTO
   */
  subscriptionId: string;
  /**
   *
   * @type {ActionOwner}
   * @memberof SignDocumentsActionDTO
   */
  owner: ActionOwner;
  /**
   *
   * @type {ActionStatus}
   * @memberof SignDocumentsActionDTO
   */
  status: ActionStatus;
  /**
   *
   * @type {Array<string>}
   * @memberof SignDocumentsActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {SignDocumentsDataDTO}
   * @memberof SignDocumentsActionDTO
   */
  data: SignDocumentsDataDTO;
}

export const SignDocumentsActionDTOTypeEnum = {
  SignDocumentsV1: 'SIGN_DOCUMENTS_V1',
} as const;

export type SignDocumentsActionDTOTypeEnum =
  (typeof SignDocumentsActionDTOTypeEnum)[keyof typeof SignDocumentsActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SignDocumentsDataDTO
 */
export interface SignDocumentsDataDTO {
  /**
   * A list of documents to be signed.
   * @type {Array<SignDocumentDTO>}
   * @memberof SignDocumentsDataDTO
   */
  documents: Array<SignDocumentDTO>;
}
/**
 *
 * @export
 * @interface SignRewardsTOSActionDTO
 */
export interface SignRewardsTOSActionDTO {
  /**
   *
   * @type {string}
   * @memberof SignRewardsTOSActionDTO
   */
  id: string;
  /**
   * The type of action
   * @type {string}
   * @memberof SignRewardsTOSActionDTO
   */
  type: SignRewardsTOSActionDTOTypeEnum;
  /**
   *
   * @type {string}
   * @memberof SignRewardsTOSActionDTO
   */
  subscriptionId: string;
  /**
   *
   * @type {ActionOwner}
   * @memberof SignRewardsTOSActionDTO
   */
  owner: ActionOwner;
  /**
   *
   * @type {ActionStatus}
   * @memberof SignRewardsTOSActionDTO
   */
  status: ActionStatus;
  /**
   *
   * @type {Array<string>}
   * @memberof SignRewardsTOSActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {SignRewardsTOSActionDataDTO}
   * @memberof SignRewardsTOSActionDTO
   */
  data: SignRewardsTOSActionDataDTO;
}

export const SignRewardsTOSActionDTOTypeEnum = {
  SignRewardsTosV1: 'SIGN_REWARDS_TOS_V1',
} as const;

export type SignRewardsTOSActionDTOTypeEnum =
  (typeof SignRewardsTOSActionDTOTypeEnum)[keyof typeof SignRewardsTOSActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SignRewardsTOSActionDataDTO
 */
export interface SignRewardsTOSActionDataDTO {
  /**
   * The revision of the T&Cs which have been signed
   * @type {string}
   * @memberof SignRewardsTOSActionDataDTO
   */
  revision: string | null;
}
/**
 *
 * @export
 * @interface SubscriptionDirectDebitDTO
 */
export interface SubscriptionDirectDebitDTO {
  /**
   * The last 4 digits of the account number
   * @type {string}
   * @memberof SubscriptionDirectDebitDTO
   */
  accountNumberLastDigits: string;
  /**
   * The last 2 digits of the sort code
   * @type {string}
   * @memberof SubscriptionDirectDebitDTO
   */
  sortCodeLastDigits: string;
  /**
   * The name associated with the direct debit
   * @type {string}
   * @memberof SubscriptionDirectDebitDTO
   */
  nameOnAccount: string;
  /**
   * The day of the month in which payment is due
   * @type {number}
   * @memberof SubscriptionDirectDebitDTO
   */
  monthlyPaymentDay: number;
}
/**
 *
 * @export
 * @interface SubscriptionDocumentDTO
 */
export interface SubscriptionDocumentDTO {
  /**
   * When the subscription was last updated
   * @type {string}
   * @memberof SubscriptionDocumentDTO
   */
  issued: string;
  /**
   * URL to the document
   * @type {string}
   * @memberof SubscriptionDocumentDTO
   */
  link: string;
  /**
   * Format of the document
   * @type {string}
   * @memberof SubscriptionDocumentDTO
   */
  format: string;
  /**
   * If this document is active
   * @type {boolean}
   * @memberof SubscriptionDocumentDTO
   */
  active: boolean;
  /**
   * The type of document
   * @type {string}
   * @memberof SubscriptionDocumentDTO
   */
  type: SubscriptionDocumentDTOTypeEnum;
}

export const SubscriptionDocumentDTOTypeEnum = {
  Rca: 'rca',
  Ha: 'ha',
} as const;

export type SubscriptionDocumentDTOTypeEnum =
  (typeof SubscriptionDocumentDTOTypeEnum)[keyof typeof SubscriptionDocumentDTOTypeEnum];

/**
 *
 * @export
 * @interface SubscriptionDocumentsDTO
 */
export interface SubscriptionDocumentsDTO {
  /**
   * An array of documents associated with the subscription
   * @type {Array<SubscriptionDocumentDTO>}
   * @memberof SubscriptionDocumentsDTO
   */
  documents: Array<SubscriptionDocumentDTO>;
}
/**
 * @type SubscriptionsControllerGetByActionId200Response
 * @export
 */
export type SubscriptionsControllerGetByActionId200Response =
  | CheckAffordabilityActionDTO
  | HomeSurveyActionDTO
  | InstallChargingStationActionDTO
  | LinkExistingChargerActionDTO
  | PayUpfrontFeeActionDTO
  | SetupDirectDebitActionDTO
  | SignDocumentsActionDTO
  | SignRewardsTOSActionDTO;

/**
 * @type SubscriptionsControllerUpdateActionById200Response
 * @export
 */
export type SubscriptionsControllerUpdateActionById200Response =
  | CheckAffordabilityActionDTO
  | HomeSurveyActionDTO
  | LinkExistingChargerActionDTO
  | SetupDirectDebitActionDTO
  | SignDocumentsActionDTO
  | SignRewardsTOSActionDTO;

/**
 * @type SubscriptionsControllerUpdateActionByIdRequest
 * @export
 */
export type SubscriptionsControllerUpdateActionByIdRequest =
  | UpdateAffordabilityActionDTO
  | UpdateDirectDebitActionDTO
  | UpdateHomeSurveyActionDTO
  | UpdateLinkExistingChargerActionDTO
  | UpdateSignDocumentsActionDTO
  | UpdateSignRewardsTOSActionDTO;

/**
 *
 * @export
 * @interface UpdateAffordabilityActionDTO
 */
export interface UpdateAffordabilityActionDTO {
  /**
   *
   * @type {string}
   * @memberof UpdateAffordabilityActionDTO
   */
  type: UpdateAffordabilityActionDTOTypeEnum;
  /**
   *
   * @type {UpdateCheckAffordabilityDataDTO}
   * @memberof UpdateAffordabilityActionDTO
   */
  data: UpdateCheckAffordabilityDataDTO;
}

export const UpdateAffordabilityActionDTOTypeEnum = {
  CheckAffordabilityV1: 'CHECK_AFFORDABILITY_V1',
} as const;

export type UpdateAffordabilityActionDTOTypeEnum =
  (typeof UpdateAffordabilityActionDTOTypeEnum)[keyof typeof UpdateAffordabilityActionDTOTypeEnum];

/**
 *
 * @export
 * @interface UpdateCheckAffordabilityBillingAddress
 */
export interface UpdateCheckAffordabilityBillingAddress {
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityBillingAddress
   */
  flat?: string;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityBillingAddress
   */
  number?: string;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityBillingAddress
   */
  name?: string;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityBillingAddress
   */
  street: string;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityBillingAddress
   */
  town: string;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityBillingAddress
   */
  county?: string;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityBillingAddress
   */
  postcode?: string;
}
/**
 *
 * @export
 * @interface UpdateCheckAffordabilityDataDTO
 */
export interface UpdateCheckAffordabilityDataDTO {
  /**
   * Title of the customer
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  title: UpdateCheckAffordabilityDataDTOTitleEnum;
  /**
   * First name of the customer
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  firstName: string;
  /**
   * Middle name(s) of the customer
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  middleNames?: string;
  /**
   * Last name of the customer
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  lastName: string;
  /**
   * Email address of the customer
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  email: string;
  /**
   * Telephone number of the customer
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  phoneNumber: string;
  /**
   * Date of birth of the customer
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  dateOfBirth: string;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  maritalStatus: UpdateCheckAffordabilityDataDTOMaritalStatusEnum;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  residentialStatus: UpdateCheckAffordabilityDataDTOResidentialStatusEnum;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  dependencies: UpdateCheckAffordabilityDataDTODependenciesEnum;
  /**
   * At least one of billingAddress\' flat, number or name fields must be provided
   * @type {UpdateCheckAffordabilityBillingAddress}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  billingAddress: UpdateCheckAffordabilityBillingAddress;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  employmentStatus: UpdateCheckAffordabilityDataDTOEmploymentStatusEnum;
  /**
   *
   * @type {number}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  monthlyTakeHomePay: number;
  /**
   *
   * @type {number}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  monthlyHousePayments: number;
  /**
   *
   * @type {number}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  monthlyTravelAndLivingExpenses: number;
  /**
   *
   * @type {number}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  monthlyHouseholdExpenses: number;
  /**
   *
   * @type {number}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  monthlyCreditPayments: number;
  /**
   *
   * @type {string}
   * @memberof UpdateCheckAffordabilityDataDTO
   */
  circumstancesRequireSupport: UpdateCheckAffordabilityDataDTOCircumstancesRequireSupportEnum;
}

export const UpdateCheckAffordabilityDataDTOTitleEnum = {
  Mr: 'MR',
  Mrs: 'MRS',
  Miss: 'MISS',
  Ms: 'MS',
  Mx: 'MX',
  Dr: 'DR',
  Other: 'OTHER',
} as const;

export type UpdateCheckAffordabilityDataDTOTitleEnum =
  (typeof UpdateCheckAffordabilityDataDTOTitleEnum)[keyof typeof UpdateCheckAffordabilityDataDTOTitleEnum];
export const UpdateCheckAffordabilityDataDTOMaritalStatusEnum = {
  Single: 'SINGLE',
  LivingTogether: 'LIVING_TOGETHER',
  CommonLaw: 'COMMON_LAW',
  Married: 'MARRIED',
  Seperated: 'SEPERATED',
  Divorced: 'DIVORCED',
  Widowed: 'WIDOWED',
  Other: 'OTHER',
} as const;

export type UpdateCheckAffordabilityDataDTOMaritalStatusEnum =
  (typeof UpdateCheckAffordabilityDataDTOMaritalStatusEnum)[keyof typeof UpdateCheckAffordabilityDataDTOMaritalStatusEnum];
export const UpdateCheckAffordabilityDataDTOResidentialStatusEnum = {
  LivingWithParents: 'LIVING_WITH_PARENTS',
  CouncilTenant: 'COUNCIL_TENANT',
  HomeownerWithMortgage: 'HOMEOWNER_WITH_MORTGAGE',
  HomeownerWithoutMortgage: 'HOMEOWNER_WITHOUT_MORTGAGE',
  PrivateTenant: 'PRIVATE_TENANT',
  HousingAssociation: 'HOUSING_ASSOCIATION',
  Other: 'OTHER',
} as const;

export type UpdateCheckAffordabilityDataDTOResidentialStatusEnum =
  (typeof UpdateCheckAffordabilityDataDTOResidentialStatusEnum)[keyof typeof UpdateCheckAffordabilityDataDTOResidentialStatusEnum];
export const UpdateCheckAffordabilityDataDTODependenciesEnum = {
  _0: '0',
  _1: '1',
  _2: '2',
  _3: '3+',
} as const;

export type UpdateCheckAffordabilityDataDTODependenciesEnum =
  (typeof UpdateCheckAffordabilityDataDTODependenciesEnum)[keyof typeof UpdateCheckAffordabilityDataDTODependenciesEnum];
export const UpdateCheckAffordabilityDataDTOEmploymentStatusEnum = {
  SelfEmployed: 'SELF_EMPLOYED',
  PartTime: 'PART_TIME',
  FullTime: 'FULL_TIME',
  Contract: 'CONTRACT',
  Retired: 'RETIRED',
  ArmedForces: 'ARMED_FORCES',
  HomeMaker: 'HOME_MAKER',
  SingleParent: 'SINGLE_PARENT',
  Disabled: 'DISABLED',
  Unemployed: 'UNEMPLOYED',
} as const;

export type UpdateCheckAffordabilityDataDTOEmploymentStatusEnum =
  (typeof UpdateCheckAffordabilityDataDTOEmploymentStatusEnum)[keyof typeof UpdateCheckAffordabilityDataDTOEmploymentStatusEnum];
export const UpdateCheckAffordabilityDataDTOCircumstancesRequireSupportEnum = {
  SupportRequested: 'SUPPORT_REQUESTED',
  NoSupportRequested: 'NO_SUPPORT_REQUESTED',
} as const;

export type UpdateCheckAffordabilityDataDTOCircumstancesRequireSupportEnum =
  (typeof UpdateCheckAffordabilityDataDTOCircumstancesRequireSupportEnum)[keyof typeof UpdateCheckAffordabilityDataDTOCircumstancesRequireSupportEnum];

/**
 *
 * @export
 * @interface UpdateDirectDebitActionDTO
 */
export interface UpdateDirectDebitActionDTO {
  /**
   *
   * @type {string}
   * @memberof UpdateDirectDebitActionDTO
   */
  type: UpdateDirectDebitActionDTOTypeEnum;
  /**
   *
   * @type {UpdateSetupDirectDebitDataDTO}
   * @memberof UpdateDirectDebitActionDTO
   */
  data: UpdateSetupDirectDebitDataDTO;
}

export const UpdateDirectDebitActionDTOTypeEnum = {
  SetupDirectDebitV1: 'SETUP_DIRECT_DEBIT_V1',
} as const;

export type UpdateDirectDebitActionDTOTypeEnum =
  (typeof UpdateDirectDebitActionDTOTypeEnum)[keyof typeof UpdateDirectDebitActionDTOTypeEnum];

/**
 *
 * @export
 * @interface UpdateHomeSurveyActionDTO
 */
export interface UpdateHomeSurveyActionDTO {
  /**
   *
   * @type {string}
   * @memberof UpdateHomeSurveyActionDTO
   */
  type: UpdateHomeSurveyActionDTOTypeEnum;
  /**
   *
   * @type {object}
   * @memberof UpdateHomeSurveyActionDTO
   */
  data?: object;
}

export const UpdateHomeSurveyActionDTOTypeEnum = {
  CompleteHomeSurveyV1: 'COMPLETE_HOME_SURVEY_V1',
} as const;

export type UpdateHomeSurveyActionDTOTypeEnum =
  (typeof UpdateHomeSurveyActionDTOTypeEnum)[keyof typeof UpdateHomeSurveyActionDTOTypeEnum];

/**
 *
 * @export
 * @interface UpdateLinkExistingChargerActionDTO
 */
export interface UpdateLinkExistingChargerActionDTO {
  /**
   *
   * @type {string}
   * @memberof UpdateLinkExistingChargerActionDTO
   */
  type: UpdateLinkExistingChargerActionDTOTypeEnum;
}

export const UpdateLinkExistingChargerActionDTOTypeEnum = {
  LinkExistingChargerV1: 'LINK_EXISTING_CHARGER_V1',
} as const;

export type UpdateLinkExistingChargerActionDTOTypeEnum =
  (typeof UpdateLinkExistingChargerActionDTOTypeEnum)[keyof typeof UpdateLinkExistingChargerActionDTOTypeEnum];

/**
 *
 * @export
 * @interface UpdateSetupDirectDebitDataDTO
 */
export interface UpdateSetupDirectDebitDataDTO {
  /**
   * Bank account number of the customer
   * @type {string}
   * @memberof UpdateSetupDirectDebitDataDTO
   */
  accountNumber: string;
  /**
   * Sort code of the customer’s bank
   * @type {string}
   * @memberof UpdateSetupDirectDebitDataDTO
   */
  sortCode: string;
  /**
   * Name of account holder
   * @type {string}
   * @memberof UpdateSetupDirectDebitDataDTO
   */
  accountName: string;
  /**
   * Request is from either one or more authorised signatory
   * @type {boolean}
   * @memberof UpdateSetupDirectDebitDataDTO
   */
  requiresMoreThanOneSignatory: boolean;
  /**
   * The account holder understands the direct debit guarantee
   * @type {boolean}
   * @memberof UpdateSetupDirectDebitDataDTO
   */
  understandsDirectDebitGuarantee: boolean;
}
/**
 *
 * @export
 * @interface UpdateSignDocumentsActionDTO
 */
export interface UpdateSignDocumentsActionDTO {
  /**
   *
   * @type {string}
   * @memberof UpdateSignDocumentsActionDTO
   */
  type: UpdateSignDocumentsActionDTOTypeEnum;
  /**
   *
   * @type {object}
   * @memberof UpdateSignDocumentsActionDTO
   */
  data?: object;
}

export const UpdateSignDocumentsActionDTOTypeEnum = {
  SignDocumentsV1: 'SIGN_DOCUMENTS_V1',
} as const;

export type UpdateSignDocumentsActionDTOTypeEnum =
  (typeof UpdateSignDocumentsActionDTOTypeEnum)[keyof typeof UpdateSignDocumentsActionDTOTypeEnum];

/**
 *
 * @export
 * @interface UpdateSignRewardsTOSActionDTO
 */
export interface UpdateSignRewardsTOSActionDTO {
  /**
   *
   * @type {string}
   * @memberof UpdateSignRewardsTOSActionDTO
   */
  type: UpdateSignRewardsTOSActionDTOTypeEnum;
  /**
   *
   * @type {UpdateSignRewardsTOSActionDataDTO}
   * @memberof UpdateSignRewardsTOSActionDTO
   */
  data: UpdateSignRewardsTOSActionDataDTO;
}

export const UpdateSignRewardsTOSActionDTOTypeEnum = {
  SignRewardsTosV1: 'SIGN_REWARDS_TOS_V1',
} as const;

export type UpdateSignRewardsTOSActionDTOTypeEnum =
  (typeof UpdateSignRewardsTOSActionDTOTypeEnum)[keyof typeof UpdateSignRewardsTOSActionDTOTypeEnum];

/**
 *
 * @export
 * @interface UpdateSignRewardsTOSActionDataDTO
 */
export interface UpdateSignRewardsTOSActionDataDTO {
  /**
   * The revision of the T&C signed
   * @type {string}
   * @memberof UpdateSignRewardsTOSActionDataDTO
   */
  revision: string;
}
/**
 *
 * @export
 * @interface UpdateSubscriptionDTO
 */
export interface UpdateSubscriptionDTO {
  /**
   * The ID of the user to associate the subscription with
   * @type {string}
   * @memberof UpdateSubscriptionDTO
   */
  userId: string;
}

/**
 * HealthcheckApi - axios parameter creator
 * @export
 */
export const HealthcheckApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Get Subscriptions API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthcheckApi - functional programming interface
 * @export
 */
export const HealthcheckApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    HealthcheckApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Get Subscriptions API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthcheckApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthcheckApi - factory interface
 * @export
 */
export const HealthcheckApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthcheckApiFp(configuration);
  return {
    /**
     *
     * @summary Get Subscriptions API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthcheckApi - object-oriented interface
 * @export
 * @class HealthcheckApi
 * @extends {BaseAPI}
 */
export class HealthcheckApi extends BaseAPI {
  /**
   *
   * @summary Get Subscriptions API health
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthcheckApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthcheckApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SubscriptionsApi - axios parameter creator
 * @export
 */
export const SubscriptionsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Create a subscription
     * @summary Create a subscription
     * @param {CreatePodDriveRewardsSubscriptionDTO} createPodDriveRewardsSubscriptionDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerCreate: async (
      createPodDriveRewardsSubscriptionDTO: CreatePodDriveRewardsSubscriptionDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createPodDriveRewardsSubscriptionDTO' is not null or undefined
      assertParamExists(
        'subscriptionsControllerCreate',
        'createPodDriveRewardsSubscriptionDTO',
        createPodDriveRewardsSubscriptionDTO
      );
      const localVarPath = `/subscriptions`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createPodDriveRewardsSubscriptionDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Cancels and deletes a given subscription and it\'s corresponding actions and plan.
     * @summary Delete a subscription
     * @param {SubscriptionsControllerDeleteModeEnum} mode here be dragons
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerDelete: async (
      mode: SubscriptionsControllerDeleteModeEnum,
      subscriptionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'mode' is not null or undefined
      assertParamExists('subscriptionsControllerDelete', 'mode', mode);
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerDelete',
        'subscriptionId',
        subscriptionId
      );
      const localVarPath = `/subscriptions/{subscriptionId}`.replace(
        `{${'subscriptionId'}}`,
        encodeURIComponent(String(subscriptionId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (mode !== undefined) {
        localVarQueryParameter['mode'] = mode;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get an action by action ID within a subscription
     * @summary Get an action
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} actionId The ID of the action
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetByActionId: async (
      subscriptionId: string,
      actionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetByActionId',
        'subscriptionId',
        subscriptionId
      );
      // verify required parameter 'actionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetByActionId',
        'actionId',
        actionId
      );
      const localVarPath = `/subscriptions/{subscriptionId}/actions/{actionId}`
        .replace(
          `{${'subscriptionId'}}`,
          encodeURIComponent(String(subscriptionId))
        )
        .replace(`{${'actionId'}}`, encodeURIComponent(String(actionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a subscription by id
     * @summary Get a subscription
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetBySubscriptionId: async (
      subscriptionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetBySubscriptionId',
        'subscriptionId',
        subscriptionId
      );
      const localVarPath = `/subscriptions/{subscriptionId}`.replace(
        `{${'subscriptionId'}}`,
        encodeURIComponent(String(subscriptionId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get subscription direct debit details
     * @summary Get subscription direct debit details
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionDirectDebit: async (
      subscriptionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetSubscriptionDirectDebit',
        'subscriptionId',
        subscriptionId
      );
      const localVarPath =
        `/subscriptions/{subscriptionId}/direct-debit`.replace(
          `{${'subscriptionId'}}`,
          encodeURIComponent(String(subscriptionId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Downloads the PDF file of a given document
     * @summary Get subscription document
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} documentId The ID of the document
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionDocument: async (
      subscriptionId: string,
      documentId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetSubscriptionDocument',
        'subscriptionId',
        subscriptionId
      );
      // verify required parameter 'documentId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetSubscriptionDocument',
        'documentId',
        documentId
      );
      const localVarPath =
        `/subscriptions/{subscriptionId}/documents/{documentId}`
          .replace(
            `{${'subscriptionId'}}`,
            encodeURIComponent(String(subscriptionId))
          )
          .replace(`{${'documentId'}}`, encodeURIComponent(String(documentId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get documents related to a given subscription
     * @summary Get subscription documents
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionDocuments: async (
      subscriptionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetSubscriptionDocuments',
        'subscriptionId',
        subscriptionId
      );
      const localVarPath = `/subscriptions/{subscriptionId}/documents`.replace(
        `{${'subscriptionId'}}`,
        encodeURIComponent(String(subscriptionId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * List subscriptions by user ID or PPID
     * @summary List subscriptions
     * @param {string} [userId] The Firebase user ID to filter subscriptions
     * @param {string} [ppid] The Pod Point ID (ppid) to filter subscriptions
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerSearch: async (
      userId?: string,
      ppid?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/subscriptions`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (userId !== undefined) {
        localVarQueryParameter['userId'] = userId;
      }

      if (ppid !== undefined) {
        localVarQueryParameter['ppid'] = ppid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Transfer a subscription by id
     * @summary Transfer a subscription
     * @param {string} subscriptionId The ID of the subscription
     * @param {UpdateSubscriptionDTO} updateSubscriptionDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerTransferSubscription: async (
      subscriptionId: string,
      updateSubscriptionDTO: UpdateSubscriptionDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerTransferSubscription',
        'subscriptionId',
        subscriptionId
      );
      // verify required parameter 'updateSubscriptionDTO' is not null or undefined
      assertParamExists(
        'subscriptionsControllerTransferSubscription',
        'updateSubscriptionDTO',
        updateSubscriptionDTO
      );
      const localVarPath = `/subscriptions/{subscriptionId}`.replace(
        `{${'subscriptionId'}}`,
        encodeURIComponent(String(subscriptionId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateSubscriptionDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Resolve an action contextually based on the type of action
     * @summary Resolve an action
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} actionId The ID of the action
     * @param {SubscriptionsControllerUpdateActionByIdRequest} subscriptionsControllerUpdateActionByIdRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerUpdateActionById: async (
      subscriptionId: string,
      actionId: string,
      subscriptionsControllerUpdateActionByIdRequest: SubscriptionsControllerUpdateActionByIdRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerUpdateActionById',
        'subscriptionId',
        subscriptionId
      );
      // verify required parameter 'actionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerUpdateActionById',
        'actionId',
        actionId
      );
      // verify required parameter 'subscriptionsControllerUpdateActionByIdRequest' is not null or undefined
      assertParamExists(
        'subscriptionsControllerUpdateActionById',
        'subscriptionsControllerUpdateActionByIdRequest',
        subscriptionsControllerUpdateActionByIdRequest
      );
      const localVarPath = `/subscriptions/{subscriptionId}/actions/{actionId}`
        .replace(
          `{${'subscriptionId'}}`,
          encodeURIComponent(String(subscriptionId))
        )
        .replace(`{${'actionId'}}`, encodeURIComponent(String(actionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        subscriptionsControllerUpdateActionByIdRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SubscriptionsApi - functional programming interface
 * @export
 */
export const SubscriptionsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    SubscriptionsApiAxiosParamCreator(configuration);
  return {
    /**
     * Create a subscription
     * @summary Create a subscription
     * @param {CreatePodDriveRewardsSubscriptionDTO} createPodDriveRewardsSubscriptionDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerCreate(
      createPodDriveRewardsSubscriptionDTO: CreatePodDriveRewardsSubscriptionDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PersistedSubscriptionDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerCreate(
          createPodDriveRewardsSubscriptionDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SubscriptionsApi.subscriptionsControllerCreate']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Cancels and deletes a given subscription and it\'s corresponding actions and plan.
     * @summary Delete a subscription
     * @param {SubscriptionsControllerDeleteModeEnum} mode here be dragons
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerDelete(
      mode: SubscriptionsControllerDeleteModeEnum,
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerDelete(
          mode,
          subscriptionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SubscriptionsApi.subscriptionsControllerDelete']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get an action by action ID within a subscription
     * @summary Get an action
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} actionId The ID of the action
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetByActionId(
      subscriptionId: string,
      actionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionsControllerGetByActionId200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetByActionId(
          subscriptionId,
          actionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetByActionId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a subscription by id
     * @summary Get a subscription
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetBySubscriptionId(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PersistedSubscriptionDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetBySubscriptionId(
          subscriptionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetBySubscriptionId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get subscription direct debit details
     * @summary Get subscription direct debit details
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetSubscriptionDirectDebit(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionDirectDebitDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetSubscriptionDirectDebit(
          subscriptionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetSubscriptionDirectDebit'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Downloads the PDF file of a given document
     * @summary Get subscription document
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} documentId The ID of the document
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetSubscriptionDocument(
      subscriptionId: string,
      documentId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetSubscriptionDocument(
          subscriptionId,
          documentId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetSubscriptionDocument'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get documents related to a given subscription
     * @summary Get subscription documents
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetSubscriptionDocuments(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionDocumentsDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetSubscriptionDocuments(
          subscriptionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetSubscriptionDocuments'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * List subscriptions by user ID or PPID
     * @summary List subscriptions
     * @param {string} [userId] The Firebase user ID to filter subscriptions
     * @param {string} [ppid] The Pod Point ID (ppid) to filter subscriptions
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerSearch(
      userId?: string,
      ppid?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ListSubscriptionsDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerSearch(
          userId,
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SubscriptionsApi.subscriptionsControllerSearch']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Transfer a subscription by id
     * @summary Transfer a subscription
     * @param {string} subscriptionId The ID of the subscription
     * @param {UpdateSubscriptionDTO} updateSubscriptionDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerTransferSubscription(
      subscriptionId: string,
      updateSubscriptionDTO: UpdateSubscriptionDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PersistedSubscriptionDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerTransferSubscription(
          subscriptionId,
          updateSubscriptionDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerTransferSubscription'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Resolve an action contextually based on the type of action
     * @summary Resolve an action
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} actionId The ID of the action
     * @param {SubscriptionsControllerUpdateActionByIdRequest} subscriptionsControllerUpdateActionByIdRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerUpdateActionById(
      subscriptionId: string,
      actionId: string,
      subscriptionsControllerUpdateActionByIdRequest: SubscriptionsControllerUpdateActionByIdRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionsControllerUpdateActionById200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerUpdateActionById(
          subscriptionId,
          actionId,
          subscriptionsControllerUpdateActionByIdRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerUpdateActionById'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SubscriptionsApi - factory interface
 * @export
 */
export const SubscriptionsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SubscriptionsApiFp(configuration);
  return {
    /**
     * Create a subscription
     * @summary Create a subscription
     * @param {CreatePodDriveRewardsSubscriptionDTO} createPodDriveRewardsSubscriptionDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerCreate(
      createPodDriveRewardsSubscriptionDTO: CreatePodDriveRewardsSubscriptionDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PersistedSubscriptionDTO> {
      return localVarFp
        .subscriptionsControllerCreate(
          createPodDriveRewardsSubscriptionDTO,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Cancels and deletes a given subscription and it\'s corresponding actions and plan.
     * @summary Delete a subscription
     * @param {SubscriptionsControllerDeleteModeEnum} mode here be dragons
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerDelete(
      mode: SubscriptionsControllerDeleteModeEnum,
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .subscriptionsControllerDelete(mode, subscriptionId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get an action by action ID within a subscription
     * @summary Get an action
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} actionId The ID of the action
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetByActionId(
      subscriptionId: string,
      actionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionsControllerGetByActionId200Response> {
      return localVarFp
        .subscriptionsControllerGetByActionId(subscriptionId, actionId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a subscription by id
     * @summary Get a subscription
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetBySubscriptionId(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PersistedSubscriptionDTO> {
      return localVarFp
        .subscriptionsControllerGetBySubscriptionId(subscriptionId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get subscription direct debit details
     * @summary Get subscription direct debit details
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionDirectDebit(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionDirectDebitDTO> {
      return localVarFp
        .subscriptionsControllerGetSubscriptionDirectDebit(
          subscriptionId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Downloads the PDF file of a given document
     * @summary Get subscription document
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} documentId The ID of the document
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionDocument(
      subscriptionId: string,
      documentId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .subscriptionsControllerGetSubscriptionDocument(
          subscriptionId,
          documentId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get documents related to a given subscription
     * @summary Get subscription documents
     * @param {string} subscriptionId The ID of the subscription
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionDocuments(
      subscriptionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionDocumentsDTO> {
      return localVarFp
        .subscriptionsControllerGetSubscriptionDocuments(
          subscriptionId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * List subscriptions by user ID or PPID
     * @summary List subscriptions
     * @param {string} [userId] The Firebase user ID to filter subscriptions
     * @param {string} [ppid] The Pod Point ID (ppid) to filter subscriptions
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerSearch(
      userId?: string,
      ppid?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ListSubscriptionsDTO> {
      return localVarFp
        .subscriptionsControllerSearch(userId, ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Transfer a subscription by id
     * @summary Transfer a subscription
     * @param {string} subscriptionId The ID of the subscription
     * @param {UpdateSubscriptionDTO} updateSubscriptionDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerTransferSubscription(
      subscriptionId: string,
      updateSubscriptionDTO: UpdateSubscriptionDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PersistedSubscriptionDTO> {
      return localVarFp
        .subscriptionsControllerTransferSubscription(
          subscriptionId,
          updateSubscriptionDTO,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Resolve an action contextually based on the type of action
     * @summary Resolve an action
     * @param {string} subscriptionId The ID of the subscription
     * @param {string} actionId The ID of the action
     * @param {SubscriptionsControllerUpdateActionByIdRequest} subscriptionsControllerUpdateActionByIdRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerUpdateActionById(
      subscriptionId: string,
      actionId: string,
      subscriptionsControllerUpdateActionByIdRequest: SubscriptionsControllerUpdateActionByIdRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionsControllerUpdateActionById200Response> {
      return localVarFp
        .subscriptionsControllerUpdateActionById(
          subscriptionId,
          actionId,
          subscriptionsControllerUpdateActionByIdRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SubscriptionsApi - object-oriented interface
 * @export
 * @class SubscriptionsApi
 * @extends {BaseAPI}
 */
export class SubscriptionsApi extends BaseAPI {
  /**
   * Create a subscription
   * @summary Create a subscription
   * @param {CreatePodDriveRewardsSubscriptionDTO} createPodDriveRewardsSubscriptionDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerCreate(
    createPodDriveRewardsSubscriptionDTO: CreatePodDriveRewardsSubscriptionDTO,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerCreate(
        createPodDriveRewardsSubscriptionDTO,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Cancels and deletes a given subscription and it\'s corresponding actions and plan.
   * @summary Delete a subscription
   * @param {SubscriptionsControllerDeleteModeEnum} mode here be dragons
   * @param {string} subscriptionId The ID of the subscription
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerDelete(
    mode: SubscriptionsControllerDeleteModeEnum,
    subscriptionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerDelete(mode, subscriptionId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get an action by action ID within a subscription
   * @summary Get an action
   * @param {string} subscriptionId The ID of the subscription
   * @param {string} actionId The ID of the action
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetByActionId(
    subscriptionId: string,
    actionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetByActionId(subscriptionId, actionId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a subscription by id
   * @summary Get a subscription
   * @param {string} subscriptionId The ID of the subscription
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetBySubscriptionId(
    subscriptionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetBySubscriptionId(subscriptionId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get subscription direct debit details
   * @summary Get subscription direct debit details
   * @param {string} subscriptionId The ID of the subscription
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetSubscriptionDirectDebit(
    subscriptionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetSubscriptionDirectDebit(
        subscriptionId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Downloads the PDF file of a given document
   * @summary Get subscription document
   * @param {string} subscriptionId The ID of the subscription
   * @param {string} documentId The ID of the document
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetSubscriptionDocument(
    subscriptionId: string,
    documentId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetSubscriptionDocument(
        subscriptionId,
        documentId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get documents related to a given subscription
   * @summary Get subscription documents
   * @param {string} subscriptionId The ID of the subscription
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetSubscriptionDocuments(
    subscriptionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetSubscriptionDocuments(subscriptionId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * List subscriptions by user ID or PPID
   * @summary List subscriptions
   * @param {string} [userId] The Firebase user ID to filter subscriptions
   * @param {string} [ppid] The Pod Point ID (ppid) to filter subscriptions
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerSearch(
    userId?: string,
    ppid?: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerSearch(userId, ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Transfer a subscription by id
   * @summary Transfer a subscription
   * @param {string} subscriptionId The ID of the subscription
   * @param {UpdateSubscriptionDTO} updateSubscriptionDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerTransferSubscription(
    subscriptionId: string,
    updateSubscriptionDTO: UpdateSubscriptionDTO,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerTransferSubscription(
        subscriptionId,
        updateSubscriptionDTO,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Resolve an action contextually based on the type of action
   * @summary Resolve an action
   * @param {string} subscriptionId The ID of the subscription
   * @param {string} actionId The ID of the action
   * @param {SubscriptionsControllerUpdateActionByIdRequest} subscriptionsControllerUpdateActionByIdRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerUpdateActionById(
    subscriptionId: string,
    actionId: string,
    subscriptionsControllerUpdateActionByIdRequest: SubscriptionsControllerUpdateActionByIdRequest,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerUpdateActionById(
        subscriptionId,
        actionId,
        subscriptionsControllerUpdateActionByIdRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const SubscriptionsControllerDeleteModeEnum = {
  Debug: 'debug',
} as const;
export type SubscriptionsControllerDeleteModeEnum =
  (typeof SubscriptionsControllerDeleteModeEnum)[keyof typeof SubscriptionsControllerDeleteModeEnum];
