openapi: 3.0.0
paths:
  /health:
    get:
      operationId: HealthController_check
      parameters: []
      responses:
        '200':
          description: The Health Check is successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  info:
                    type: object
                    example: &a1
                      database: &a2
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example: {}
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example: *a1
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
        '503':
          description: The Health Check is not successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  info:
                    type: object
                    example: *a1
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example:
                      redis: &a3
                        status: down
                        message: Could not connect
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example:
                      database: *a2
                      redis: *a3
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
      summary: get Rewards API health
      tags:
        - Healthcheck
  /reward-wallets/{userId}:
    get:
      operationId: WalletController_read
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PersistedWalletHttpDto'
        '404':
          description: wallet not found
        '500':
          description: internal error
      tags: &a4
        - Reward Wallets
    put:
      description: For a given user, create or update a wallet
      operationId: WalletController_upsertWallet
      parameters:
        - name: userId
          required: true
          in: path
          description: The ID of the user the wallet belongs to
          schema:
            format: uuid
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertWalletDTO'
      responses:
        '200':
          description: The successfully created or updated wallet
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PersistedWalletHttpDto'
        '400':
          description:
            Returned when the request body is invalid or subscription does not
            exist
        '500':
          description: Returned when something unexpected occurs
      summary: Upsert Wallet
      tags: *a4
    patch:
      description: Transfer a wallet to a given user id
      operationId: WalletController_transferWallet
      parameters:
        - name: userId
          required: true
          in: path
          description: The ID of the user the wallet belongs to
          schema:
            format: uuid
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransferWalletDTO'
      responses:
        '200':
          description: The successfully updated wallet
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PersistedWalletHttpDto'
        '400':
          description: Returned when the request body is invalid
        '500':
          description: Returned when something unexpected occurs
      summary: Transfer Wallet
      tags: *a4
  /reward-wallets/{userId}/actions:
    post:
      description: Based on the request payload, perform an action on the given wallet
      operationId: WalletController_handleAction
      parameters:
        - name: userId
          required: true
          in: path
          description: The ID of the user the wallet belongs to
          schema:
            format: uuid
            type: string
        - name: x-idempotency-key
          required: false
          in: header
          description:
            The idempotency key to use with this operation.  One will be
            generated if not provided
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/WalletActionAccrualDTO'
                - $ref: '#/components/schemas/WalletActionPayoutDTO'
      responses:
        '201':
          description: The successfully processed acton
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountTransactionDTO'
        '400':
          description: Returned when the request body is invalid
        '404':
          description: ''
        '422':
          description: ''
        '500':
          description: Returned when something unexpected occurs
      summary: Perform Wallet Action
      tags: *a4
  /reward-wallets/{userId}/balance:
    get:
      operationId: WalletController_getWalletBalanceSummary
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Wallet balance summary
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletBalanceSummaryHttpDTO'
        '404':
          description: Wallet or account not found
        '500':
          description: Internal error
      tags: *a4
  /reward-wallets/{userId}/rewards:
    get:
      operationId: RewardsController_getRewardsDetails
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountDetailsHttpDto'
        '404':
          description: Wallet or account not found
        '500':
          description: internal error
      tags: &a5
        - Rewards
  /reward-wallets/{userId}/rewards/transactions:
    get:
      description: Retrieve rewards transactions
      operationId: RewardsController_getRewardsTransactions
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
        - name: lastKey
          required: false
          in: query
          description: The last key to paginate from
          schema:
            example: 06dd1ee0-355a-4636-9da3-15b5a7c251bd
            type: string
        - name: count
          required: false
          in: query
          description: The amount of records to return, 100 by default
          schema:
            default: 100
            type: number
      responses:
        '200':
          description: Returned on success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountTransactionsHttpDto'
        '404':
          description: Wallet or account not found for user
        '500':
          description: internal error
      summary: Retrieve rewards transactions
      tags: *a5
  /reward-wallets/{userId}/allowance:
    get:
      operationId: RewardsController_getAllowanceDetails
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountDetailsHttpDto'
        '404':
          description: Wallet or account not found
        '500':
          description: internal error
      tags: *a5
  /reward-wallets/{userId}/allowance/transactions:
    get:
      description: Retrieve allowance transactions
      operationId: RewardsController_getAllowanceTransactions
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
        - name: lastKey
          required: false
          in: query
          description: The last key to paginate from
          schema:
            example: 06dd1ee0-355a-4636-9da3-15b5a7c251bd
            type: string
        - name: count
          required: false
          in: query
          description: The amount of records to return, 100 by default
          schema:
            default: 100
            type: number
      responses:
        '200':
          description: Returned on success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountTransactionsHttpDto'
        '404':
          description: Wallet or account not found for user
        '500':
          description: internal error
      summary: Retrieve allowance transactions
      tags: *a5
info:
  title: Rewards API
  description: Rewards API service
  version: 1.0.0
  contact: {}
tags: []
servers: []
components:
  schemas:
    PersistedWalletHttpDto:
      type: object
      properties:
        id:
          type: string
          description: The id of the wallet
          example: 2accb786-b998-4d32-9cbf-c473b7402686
        type:
          type: string
          description: The type of the wallet
          example: POD_DRIVE
        subscriptionId:
          type: string
          description: The id of the subscription associated with the wallet
          example: 2accb786-b998-4d32-9cbf-c473b7402686
          nullable: true
      required:
        - id
        - type
        - subscriptionId
    UpsertWalletDTO:
      type: object
      properties:
        type:
          type: string
          description: The type of wallet to create
          enum:
            - POD_DRIVE
            - SYSTEM
        subscriptionId:
          type: string
          description: The ID of the subscription the wallet relates to
          format: uuid
      required:
        - type
        - subscriptionId
    TransferWalletDTO:
      type: object
      properties:
        userId:
          type: string
          description: The user ID to transfer the wallet to
          format: uuid
      required:
        - userId
    WalletActionAmountDTO:
      type: object
      properties:
        amount:
          type: number
          format: decimal
        currency:
          type: string
          enum:
            - MILES
      required:
        - amount
        - currency
    WalletActionAccrualDTO:
      type: object
      properties:
        action:
          type: string
          enum:
            - REWARD_ACCRUAL
        amount:
          $ref: '#/components/schemas/WalletActionAmountDTO'
      required:
        - action
        - amount
    WalletActionPayoutDTO:
      type: object
      properties:
        action:
          type: string
          enum:
            - REWARD_PAYOUT
        bankAccountId:
          type: string
          description: The ID of the bank account to pay into
      required:
        - action
        - bankAccountId
    AccountTransactionRewardsAccrualMetadata:
      type: object
      properties:
        chargeId:
          type: string
          description:
            Populated for REWARDS_ACCRUAL references, represents the ID of the
            accrual charge event
      required:
        - chargeId
    AccountTransactionRewardsPayoutMetadata:
      type: object
      properties:
        rewardsTransactionId:
          type: string
          description:
            Populated for REWARDS_PAYOUT references, represents the rewards
            transaction ID of the Payments API transaction
      required:
        - rewardsTransactionId
    AccountTransactionRewardsRefundMetadata:
      type: object
      properties:
        paymentTransactionId:
          type: string
          description:
            Populated for REWARDS_REFUND references, represents the ID of the
            Payments API transaction
      required:
        - paymentTransactionId
    AccountTransactionDTO:
      type: object
      properties:
        id:
          type: string
          description: A hash representing the transaction
        date:
          format: date-time
          type: string
          description: When the transaction took place
        status:
          type: string
          description: The status of the transaction
          enum:
            - NEW
            - PENDING
            - COMPLETED
          example: COMPLETED
        reference:
          type: string
          description: A reference for the transaction
          nullable: true
        currency:
          type: string
          description: The currency of the transaction
          enum:
            - MILES
        amount:
          type: number
          description: The value of the transaction
          format: decimal
        metadata:
          description: Metadata associated with the transaction
          nullable: true
          oneOf:
            - $ref: '#/components/schemas/AccountTransactionRewardsAccrualMetadata'
            - $ref: '#/components/schemas/AccountTransactionRewardsPayoutMetadata'
            - $ref: '#/components/schemas/AccountTransactionRewardsRefundMetadata'
      required:
        - id
        - date
        - status
        - reference
        - currency
        - amount
        - metadata
    WalletBalanceSummaryAllowanceHttpDTO:
      type: object
      properties:
        balanceMiles:
          type: number
          description: The miles remaining in the user's allowance
          example: 6532
        annualAllowanceMiles:
          type: number
          description: The total miles a user can claim within a year
          example: 7500
      required:
        - balanceMiles
        - annualAllowanceMiles
    WalletBalanceSummaryRewardsHttpDTO:
      type: object
      properties:
        balanceMiles:
          type: number
          description: How many miles can be withdrawn
          example: 1018
        balanceGbp:
          type: number
          description: The cash value of the withdrawable balance
          example: 23.71
      required:
        - balanceMiles
        - balanceGbp
    WalletBalanceSummaryHttpDTO:
      type: object
      properties:
        allowance:
          description: The user's reward wallet allowance
          allOf:
            - $ref: '#/components/schemas/WalletBalanceSummaryAllowanceHttpDTO'
        rewards:
          description: The rewards a user has
          allOf:
            - $ref: '#/components/schemas/WalletBalanceSummaryRewardsHttpDTO'
      required:
        - allowance
        - rewards
    BalanceHttpDto:
      type: object
      properties:
        amount:
          type: number
          description: The amount of the balance
          example: 100
        currency:
          type: string
          description: The currency of the balance
          example: MILES
      required:
        - amount
        - currency
    AccountDetailsHttpDto:
      type: object
      properties:
        id:
          type: string
          description: The id of the account
          example: 2accb786-b998-4d32-9cbf-c473b7402686
        type:
          type: string
          description: The type of the account
          enum:
            - ALLOWANCE
            - REWARDS
            - SYSTEM_MILES
          example: REWARDS
        balance:
          description: The balance of the account
          allOf:
            - $ref: '#/components/schemas/BalanceHttpDto'
      required:
        - id
        - type
        - balance
    AccountTransactionsMetaHttpDto:
      type: object
      properties:
        lastKey:
          type: string
          description: Key to continue paginating from, null if none
          nullable: true
          example: cee497cc-b729-407e-9322-16259cfec5a9
      required:
        - lastKey
    AccountTransactionsHttpDto:
      type: object
      properties:
        transactions:
          description: The transactions associated with the account
          type: array
          items:
            $ref: '#/components/schemas/AccountTransactionDTO'
        meta:
          description: Metadata associated with the response
          allOf:
            - $ref: '#/components/schemas/AccountTransactionsMetaHttpDto'
      required:
        - transactions
        - meta
