/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

// Map to store counters for each API endpoint
const apiCounters = new Map();

const next = (apiKey) => {
  let currentCount = apiCounters.get(apiKey) ?? 0;
  if (currentCount === Number.MAX_SAFE_INTEGER - 1) {
    currentCount = 0;
  }
  apiCounters.set(apiKey, currentCount + 1);
  return currentCount;
};

export const handlers = [
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [getHealthControllerCheck200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /health`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/reward-wallets/:userId`, async () => {
    const resultArray = [
      [getWalletControllerRead200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /reward-wallets/:userId`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/reward-wallets/:userId`, async () => {
    const resultArray = [
      [getWalletControllerUpsertWallet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`put /reward-wallets/:userId`) % resultArray.length]
    );
  }),
  http.patch(`${baseURL}/reward-wallets/:userId`, async () => {
    const resultArray = [
      [getWalletControllerTransferWallet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`patch /reward-wallets/:userId`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/reward-wallets/:userId/actions`, async () => {
    const resultArray = [
      [getWalletControllerHandleAction201Response(), { status: 201 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`post /reward-wallets/:userId/actions`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/reward-wallets/:userId/balance`, async () => {
    const resultArray = [
      [
        getWalletControllerGetWalletBalanceSummary200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /reward-wallets/:userId/balance`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/reward-wallets/:userId/rewards`, async () => {
    const resultArray = [
      [getRewardsControllerGetRewardsDetails200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /reward-wallets/:userId/rewards`) % resultArray.length
      ]
    );
  }),
  http.get(
    `${baseURL}/reward-wallets/:userId/rewards/transactions`,
    async () => {
      const resultArray = [
        [
          getRewardsControllerGetRewardsTransactions200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`get /reward-wallets/:userId/rewards/transactions`) %
            resultArray.length
        ]
      );
    }
  ),
  http.get(`${baseURL}/reward-wallets/:userId/allowance`, async () => {
    const resultArray = [
      [getRewardsControllerGetAllowanceDetails200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /reward-wallets/:userId/allowance`) % resultArray.length
      ]
    );
  }),
  http.get(
    `${baseURL}/reward-wallets/:userId/allowance/transactions`,
    async () => {
      const resultArray = [
        [
          getRewardsControllerGetAllowanceTransactions200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`get /reward-wallets/:userId/allowance/transactions`) %
            resultArray.length
        ]
      );
    }
  ),
];

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}

export function getWalletControllerRead200Response() {
  return {
    id: '2accb786-b998-4d32-9cbf-c473b7402686',
    type: 'POD_DRIVE',
    subscriptionId: '2accb786-b998-4d32-9cbf-c473b7402686',
  };
}

export function getWalletControllerUpsertWallet200Response() {
  return {
    id: '2accb786-b998-4d32-9cbf-c473b7402686',
    type: 'POD_DRIVE',
    subscriptionId: '2accb786-b998-4d32-9cbf-c473b7402686',
  };
}

export function getWalletControllerTransferWallet200Response() {
  return {
    id: '2accb786-b998-4d32-9cbf-c473b7402686',
    type: 'POD_DRIVE',
    subscriptionId: '2accb786-b998-4d32-9cbf-c473b7402686',
  };
}

export function getWalletControllerHandleAction201Response() {
  return {
    id: faker.string.uuid(),
    date: faker.date.past(),
    status: 'COMPLETED',
    reference: faker.lorem.words(),
    currency: faker.helpers.arrayElement(['MILES']),
    amount: faker.number.int(),
    metadata: faker.helpers.arrayElement([
      {
        chargeId: faker.string.uuid(),
      },
      {
        rewardsTransactionId: faker.string.uuid(),
      },
      {
        paymentTransactionId: faker.string.uuid(),
      },
    ]),
  };
}

export function getWalletControllerGetWalletBalanceSummary200Response() {
  return {
    allowance: {
      balanceMiles: 6532,
      annualAllowanceMiles: 7500,
    },
    rewards: {
      balanceMiles: 1018,
      balanceGbp: 23.71,
    },
  };
}

export function getRewardsControllerGetRewardsDetails200Response() {
  return {
    id: '2accb786-b998-4d32-9cbf-c473b7402686',
    type: 'REWARDS',
    balance: {
      amount: 100,
      currency: 'MILES',
    },
  };
}

export function getRewardsControllerGetRewardsTransactions200Response() {
  return {
    transactions: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      date: faker.date.past(),
      status: 'COMPLETED',
      reference: faker.lorem.words(),
      currency: faker.helpers.arrayElement(['MILES']),
      amount: faker.number.int(),
      metadata: faker.helpers.arrayElement([
        {
          chargeId: faker.string.uuid(),
        },
        {
          rewardsTransactionId: faker.string.uuid(),
        },
        {
          paymentTransactionId: faker.string.uuid(),
        },
      ]),
    })),
    meta: {
      lastKey: 'cee497cc-b729-407e-9322-16259cfec5a9',
    },
  };
}

export function getRewardsControllerGetAllowanceDetails200Response() {
  return {
    id: '2accb786-b998-4d32-9cbf-c473b7402686',
    type: 'REWARDS',
    balance: {
      amount: 100,
      currency: 'MILES',
    },
  };
}

export function getRewardsControllerGetAllowanceTransactions200Response() {
  return {
    transactions: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      date: faker.date.past(),
      status: 'COMPLETED',
      reference: faker.lorem.words(),
      currency: faker.helpers.arrayElement(['MILES']),
      amount: faker.number.int(),
      metadata: faker.helpers.arrayElement([
        {
          chargeId: faker.string.uuid(),
        },
        {
          rewardsTransactionId: faker.string.uuid(),
        },
        {
          paymentTransactionId: faker.string.uuid(),
        },
      ]),
    })),
    meta: {
      lastKey: 'cee497cc-b729-407e-9322-16259cfec5a9',
    },
  };
}
