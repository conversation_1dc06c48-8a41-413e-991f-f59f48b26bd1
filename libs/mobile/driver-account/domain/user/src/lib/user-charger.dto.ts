import { ApiProperty } from '@nestjs/swagger';

export class UserChargerDto {
  @ApiProperty({ type: String, example: 'PSL-456789' })
  ppid: string;

  @ApiProperty({ type: Number, example: 1 })
  unitId: number;

  @ApiProperty({ type: String, example: 'Etc/UTC' })
  timezone: string;

  @ApiProperty({ type: String, example: '2024-01-01T00:00:00.000Z' })
  linkedAt: string;

  @ApiProperty({
    required: false,
    type: [String],
  })
  otherLinkedUsers?: string[];
}
