/* tslint:disable */
/* eslint-disable */
/**
 * Driver Account API
 * Driver account API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface BalanceDto
 */
export interface BalanceDto {
  /**
   *
   * @type {string}
   * @memberof BalanceDto
   */
  currency: string;
  /**
   *
   * @type {number}
   * @memberof BalanceDto
   */
  amount: number;
}
/**
 *
 * @export
 * @interface ConsentDto
 */
export interface ConsentDto {
  /**
   * Marketing
   * @type {MarketingDto}
   * @memberof ConsentDto
   */
  marketing: MarketingDto;
}
/**
 *
 * @export
 * @interface CreateUserDto
 */
export interface CreateUserDto {
  /**
   * First name
   * @type {string}
   * @memberof CreateUserDto
   */
  first_name: string;
  /**
   * Last name
   * @type {string}
   * @memberof CreateUserDto
   */
  last_name: string;
  /**
   * Locale
   * @type {string}
   * @memberof CreateUserDto
   */
  locale: string;
  /**
   * Email
   * @type {string}
   * @memberof CreateUserDto
   */
  email: string;
  /**
   * password
   * @type {string}
   * @memberof CreateUserDto
   */
  password?: string;
  /**
   *
   * @type {ConsentDto}
   * @memberof CreateUserDto
   */
  consent?: ConsentDto;
  /**
   *
   * @type {PreferencesDto}
   * @memberof CreateUserDto
   */
  preferences?: PreferencesDto;
}
/**
 *
 * @export
 * @interface CreateUserResponseDto
 */
export interface CreateUserResponseDto {
  /**
   * GIP userId
   * @type {string}
   * @memberof CreateUserResponseDto
   */
  auth_id: string;
  /**
   *
   * @type {string}
   * @memberof CreateUserResponseDto
   */
  created_at: string;
  /**
   * pk of user
   * @type {string}
   * @memberof CreateUserResponseDto
   */
  id: string;
  /**
   * password reset link
   * @type {string}
   * @memberof CreateUserResponseDto
   */
  password_reset_link?: string;
}
/**
 *
 * @export
 * @interface ExtendedUserInfoResponseDto
 */
export interface ExtendedUserInfoResponseDto {
  /**
   * First name
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  first_name: string;
  /**
   * Last name
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  last_name: string;
  /**
   * Locale
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  locale: string;
  /**
   * Email
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  email: string;
  /**
   * auth id
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  uid: string;
  /**
   *
   * @type {PreferencesDto}
   * @memberof ExtendedUserInfoResponseDto
   */
  preferences?: PreferencesDto;
  /**
   *
   * @type {BalanceDto}
   * @memberof ExtendedUserInfoResponseDto
   */
  balance: BalanceDto;
  /**
   *
   * @type {RewardsDto}
   * @memberof ExtendedUserInfoResponseDto
   */
  rewards: RewardsDto | null;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  paymentProcessorId: string | null;
  /**
   *
   * @type {boolean}
   * @memberof ExtendedUserInfoResponseDto
   */
  emailVerified: boolean;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  lastSignInTimestamp: string | null;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  accountCreationTimestamp: string;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  status: ExtendedUserInfoResponseDtoStatusEnum;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  deletedAtTimestamp: string | null;
}

export const ExtendedUserInfoResponseDtoStatusEnum = {
  Active: 'active',
  Disabled: 'disabled',
} as const;

export type ExtendedUserInfoResponseDtoStatusEnum =
  (typeof ExtendedUserInfoResponseDtoStatusEnum)[keyof typeof ExtendedUserInfoResponseDtoStatusEnum];

/**
 *
 * @export
 * @interface Factor
 */
export interface Factor {
  /**
   * ID of the factor
   * @type {string}
   * @memberof Factor
   */
  id: string;
  /**
   * Phone number associated with this factor
   * @type {string}
   * @memberof Factor
   */
  phoneNumber: string;
  /**
   * Enrollment time of this factor
   * @type {string}
   * @memberof Factor
   */
  enrollmentTime: string;
}
/**
 *
 * @export
 * @interface FcmTokenDto
 */
export interface FcmTokenDto {
  /**
   * The notification token you want to save
   * @type {string}
   * @memberof FcmTokenDto
   */
  token: string;
  /**
   * The timestamp at which the notification token was created
   * @type {string}
   * @memberof FcmTokenDto
   */
  timestamp: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface MarketingDto
 */
export interface MarketingDto {
  /**
   *
   * @type {number}
   * @memberof MarketingDto
   */
  isConsentGiven: number;
  /**
   *
   * @type {string}
   * @memberof MarketingDto
   */
  type: string;
  /**
   *
   * @type {string}
   * @memberof MarketingDto
   */
  copy: string;
  /**
   *
   * @type {string}
   * @memberof MarketingDto
   */
  origin: string;
}
/**
 *
 * @export
 * @interface OAuthClientResponseDto
 */
export interface OAuthClientResponseDto {
  /**
   * The user friendly name of the client
   * @type {string}
   * @memberof OAuthClientResponseDto
   */
  name: string;
  /**
   * The website URL for the client\'s information about this oauth connection
   * @type {string}
   * @memberof OAuthClientResponseDto
   */
  websiteUrl: string;
  /**
   * The URL for the client\'s logo image
   * @type {string}
   * @memberof OAuthClientResponseDto
   */
  imageUrl: string;
}
/**
 *
 * @export
 * @interface OAuthConsentRequestDto
 */
export interface OAuthConsentRequestDto {
  /**
   * The client ID for the application requesting access
   * @type {string}
   * @memberof OAuthConsentRequestDto
   */
  clientId: string;
  /**
   * The redirect URI used in the authorize endpoint flow
   * @type {string}
   * @memberof OAuthConsentRequestDto
   */
  redirectUri: string;
  /**
   * Response type (must be \"code\")
   * @type {string}
   * @memberof OAuthConsentRequestDto
   */
  responseType: string;
  /**
   * PKCE code challenge method (only S256 is supported)
   * @type {string}
   * @memberof OAuthConsentRequestDto
   */
  codeChallengeMethod: string;
  /**
   * Code challenge generated by the client using the code verifier
   * @type {string}
   * @memberof OAuthConsentRequestDto
   */
  codeChallenge: string;
  /**
   * The scopes requested for access
   * @type {string}
   * @memberof OAuthConsentRequestDto
   */
  scope?: string;
  /**
   * Optional string to be repeated in the response to prevent CSRF attacks
   * @type {string}
   * @memberof OAuthConsentRequestDto
   */
  state?: string;
  /**
   * The user id of the user who is granting access to the client
   * @type {string}
   * @memberof OAuthConsentRequestDto
   */
  userId: string;
}
/**
 * @type OAuthControllerToken200Response
 * @export
 */
export type OAuthControllerToken200Response =
  | OAuthTokenAuthorizationCodeResponseDto
  | OAuthTokenClientCredentialsResponseDto
  | OAuthTokenRefreshTokenResponseDto;

/**
 * @type OAuthControllerTokenRequest
 * @export
 */
export type OAuthControllerTokenRequest =
  | OAuthTokenAuthorizationCodeRequestDto
  | OAuthTokenClientCredentialsRequestDto
  | OAuthTokenRefreshTokenRequestDto;

/**
 *
 * @export
 * @interface OAuthTokenAuthorizationCodeRequestDto
 */
export interface OAuthTokenAuthorizationCodeRequestDto {
  /**
   * The grant type for this token flow (must be \"authorization_code\")
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeRequestDto
   */
  grant_type: string;
  /**
   * The authorization code returned from the authorize endpoint flow
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeRequestDto
   */
  code: string;
  /**
   * The redirect URI used in the authorize endpoint flow
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeRequestDto
   */
  redirect_uri: string;
  /**
   * The client ID for the application requesting access
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeRequestDto
   */
  client_id: string;
  /**
   * The random key that aws used to generate the code challenge used in the authorize endpoint flow
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeRequestDto
   */
  code_verifier: string;
}
/**
 *
 * @export
 * @interface OAuthTokenAuthorizationCodeResponseDto
 */
export interface OAuthTokenAuthorizationCodeResponseDto {
  /**
   * The access token to be used as `${token_type} ${access_token}` in the Authorization header
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeResponseDto
   */
  access_token: string;
  /**
   * The type of token returned
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeResponseDto
   */
  token_type: string;
  /**
   * The number of seconds until the token expires (currently one hour)
   * @type {number}
   * @memberof OAuthTokenAuthorizationCodeResponseDto
   */
  expires_in: number;
  /**
   * The refresh token to be used to get a new access token when the current one expires. Valid for 30 days after authorization.
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeResponseDto
   */
  refresh_token: string;
  /**
   * The scopes allowed for access
   * @type {string}
   * @memberof OAuthTokenAuthorizationCodeResponseDto
   */
  scope?: string;
}
/**
 *
 * @export
 * @interface OAuthTokenClientCredentialsRequestDto
 */
export interface OAuthTokenClientCredentialsRequestDto {
  /**
   * The grant type for this token flow (must be \"client_credentials\")
   * @type {string}
   * @memberof OAuthTokenClientCredentialsRequestDto
   */
  grant_type: string;
  /**
   * The client ID for the application requesting access
   * @type {string}
   * @memberof OAuthTokenClientCredentialsRequestDto
   */
  client_id: string;
  /**
   * The client secret for the application requesting access
   * @type {string}
   * @memberof OAuthTokenClientCredentialsRequestDto
   */
  client_secret: string;
}
/**
 *
 * @export
 * @interface OAuthTokenClientCredentialsResponseDto
 */
export interface OAuthTokenClientCredentialsResponseDto {
  /**
   * The access token to be used as `${token_type} ${access_token}` in the Authorization header
   * @type {string}
   * @memberof OAuthTokenClientCredentialsResponseDto
   */
  access_token: string;
  /**
   * The type of token returned
   * @type {string}
   * @memberof OAuthTokenClientCredentialsResponseDto
   */
  token_type: string;
  /**
   * The number of seconds until the token expires (currently one hour)
   * @type {number}
   * @memberof OAuthTokenClientCredentialsResponseDto
   */
  expires_in: number;
  /**
   * The scopes allowed for access
   * @type {string}
   * @memberof OAuthTokenClientCredentialsResponseDto
   */
  scope?: string;
}
/**
 *
 * @export
 * @interface OAuthTokenRefreshTokenRequestDto
 */
export interface OAuthTokenRefreshTokenRequestDto {
  /**
   * The grant type for this token flow (must be \"refresh_token\")
   * @type {string}
   * @memberof OAuthTokenRefreshTokenRequestDto
   */
  grant_type: string;
  /**
   * The client ID for the application requesting access
   * @type {string}
   * @memberof OAuthTokenRefreshTokenRequestDto
   */
  client_id: string;
  /**
   * The refresh token to be used to generate a new access token
   * @type {string}
   * @memberof OAuthTokenRefreshTokenRequestDto
   */
  refresh_token: string;
}
/**
 *
 * @export
 * @interface OAuthTokenRefreshTokenResponseDto
 */
export interface OAuthTokenRefreshTokenResponseDto {
  /**
   * The access token to be used as `${token_type} ${access_token}` in the Authorization header
   * @type {string}
   * @memberof OAuthTokenRefreshTokenResponseDto
   */
  access_token: string;
  /**
   * The type of token returned
   * @type {string}
   * @memberof OAuthTokenRefreshTokenResponseDto
   */
  token_type: string;
  /**
   * The number of seconds until the token expires (currently one hour)
   * @type {number}
   * @memberof OAuthTokenRefreshTokenResponseDto
   */
  expires_in: number;
}
/**
 *
 * @export
 * @interface PreferencesDto
 */
export interface PreferencesDto {
  /**
   *
   * @type {string}
   * @memberof PreferencesDto
   */
  unitOfDistance: string;
}
/**
 *
 * @export
 * @interface RandomisePasswordRequest
 */
export interface RandomisePasswordRequest {
  /**
   * Email
   * @type {string}
   * @memberof RandomisePasswordRequest
   */
  email: string;
}
/**
 *
 * @export
 * @interface RemoveFactorRequest
 */
export interface RemoveFactorRequest {
  /**
   * The email of the user removing the factor
   * @type {string}
   * @memberof RemoveFactorRequest
   */
  email: string;
  /**
   * The region of the phone
   * @type {object}
   * @memberof RemoveFactorRequest
   */
  countryCode: object;
  /**
   * The phone number of the user removing the factor
   * @type {string}
   * @memberof RemoveFactorRequest
   */
  phoneNumber: string;
}
/**
 *
 * @export
 * @interface RewardsChargerDto
 */
export interface RewardsChargerDto {
  /**
   * The PPID of the charger
   * @type {string}
   * @memberof RewardsChargerDto
   */
  id: string;
  /**
   * The amount of rewardable miles for this charger
   * @type {number}
   * @memberof RewardsChargerDto
   */
  miles: number;
}
/**
 *
 * @export
 * @interface RewardsDto
 */
export interface RewardsDto {
  /**
   * The total amount of reward miles from individual chargers
   * @type {number}
   * @memberof RewardsDto
   */
  totalMiles: number;
  /**
   * The balance in the lowest unit of currency (pence for GBP)
   * @type {number}
   * @memberof RewardsDto
   */
  balance: number;
  /**
   * The currency represented by the balance
   * @type {string}
   * @memberof RewardsDto
   */
  currency: string;
  /**
   * The minimum amount of rewards miles required for payout
   * @type {number}
   * @memberof RewardsDto
   */
  payoutThreshold: number;
  /**
   * The chargers for which the user is eligible for the reward
   * @type {Array<RewardsChargerDto>}
   * @memberof RewardsDto
   */
  chargers: Array<RewardsChargerDto>;
}
/**
 *
 * @export
 * @interface SendEmailVerificationRequest
 */
export interface SendEmailVerificationRequest {
  /**
   * Email
   * @type {string}
   * @memberof SendEmailVerificationRequest
   */
  email: string;
  /**
   * Continue url
   * @type {string}
   * @memberof SendEmailVerificationRequest
   */
  email_verification_continue_url?: string;
}
/**
 *
 * @export
 * @interface SendPasswordResetRequest
 */
export interface SendPasswordResetRequest {
  /**
   * Email
   * @type {string}
   * @memberof SendPasswordResetRequest
   */
  email: string;
  /**
   * Continue url
   * @type {string}
   * @memberof SendPasswordResetRequest
   */
  reset_password_continue_url?: string;
}
/**
 *
 * @export
 * @interface SendRecoverFactorRequest
 */
export interface SendRecoverFactorRequest {
  /**
   * Email
   * @type {string}
   * @memberof SendRecoverFactorRequest
   */
  email: string;
  /**
   * Redirect URL after factor recovery
   * @type {string}
   * @memberof SendRecoverFactorRequest
   */
  recover_factor_continue_url?: string;
}
/**
 *
 * @export
 * @interface SendSignInWithEmailRequest
 */
export interface SendSignInWithEmailRequest {
  /**
   * Email
   * @type {string}
   * @memberof SendSignInWithEmailRequest
   */
  email: string;
  /**
   * Continue url
   * @type {string}
   * @memberof SendSignInWithEmailRequest
   */
  continue_url: string;
  /**
   * Origin service url, external service to authenticate the user
   * @type {string}
   * @memberof SendSignInWithEmailRequest
   */
  origin_service_url: string;
}
/**
 *
 * @export
 * @interface SendVerifyAndChangeEmailRequest
 */
export interface SendVerifyAndChangeEmailRequest {
  /**
   * Email
   * @type {string}
   * @memberof SendVerifyAndChangeEmailRequest
   */
  email: string;
  /**
   * New Email
   * @type {string}
   * @memberof SendVerifyAndChangeEmailRequest
   */
  newEmail: string;
}
/**
 *
 * @export
 * @interface TrackLoginRequest
 */
export interface TrackLoginRequest {
  /**
   *
   * @type {string}
   * @memberof TrackLoginRequest
   */
  ipAddress: string;
  /**
   *
   * @type {string}
   * @memberof TrackLoginRequest
   */
  userAgent: string;
  /**
   *
   * @type {string}
   * @memberof TrackLoginRequest
   */
  timestamp: string;
  /**
   *
   * @type {string}
   * @memberof TrackLoginRequest
   */
  authId?: string;
}
/**
 *
 * @export
 * @interface UpdateUserSuppressedStatusDto
 */
export interface UpdateUserSuppressedStatusDto {
  /**
   *
   * @type {number}
   * @memberof UpdateUserSuppressedStatusDto
   */
  status: UpdateUserSuppressedStatusDtoStatusEnum | null;
}

export const UpdateUserSuppressedStatusDtoStatusEnum = {} as const;

export type UpdateUserSuppressedStatusDtoStatusEnum =
  (typeof UpdateUserSuppressedStatusDtoStatusEnum)[keyof typeof UpdateUserSuppressedStatusDtoStatusEnum];

/**
 *
 * @export
 * @interface UserChargerDto
 */
export interface UserChargerDto {
  /**
   *
   * @type {string}
   * @memberof UserChargerDto
   */
  ppid: string;
  /**
   *
   * @type {number}
   * @memberof UserChargerDto
   */
  unitId: number;
  /**
   *
   * @type {string}
   * @memberof UserChargerDto
   */
  timezone: string;
  /**
   *
   * @type {string}
   * @memberof UserChargerDto
   */
  linkedAt: string;
  /**
   *
   * @type {Array<string>}
   * @memberof UserChargerDto
   */
  otherLinkedUsers?: Array<string>;
}
/**
 *
 * @export
 * @interface UserDetailsDto
 */
export interface UserDetailsDto {
  /**
   * First name
   * @type {string}
   * @memberof UserDetailsDto
   */
  first_name: string;
  /**
   * Last name
   * @type {string}
   * @memberof UserDetailsDto
   */
  last_name: string;
  /**
   * Locale
   * @type {string}
   * @memberof UserDetailsDto
   */
  locale: string;
}
/**
 *
 * @export
 * @interface UserEmailDto
 */
export interface UserEmailDto {
  /**
   * New email
   * @type {string}
   * @memberof UserEmailDto
   */
  newEmail: string;
  /**
   * Old email
   * @type {string}
   * @memberof UserEmailDto
   */
  oldEmail: string;
}
/**
 *
 * @export
 * @interface UserInfoResponseDto
 */
export interface UserInfoResponseDto {
  /**
   * First name
   * @type {string}
   * @memberof UserInfoResponseDto
   */
  first_name: string;
  /**
   * Last name
   * @type {string}
   * @memberof UserInfoResponseDto
   */
  last_name: string;
  /**
   * Locale
   * @type {string}
   * @memberof UserInfoResponseDto
   */
  locale: string;
  /**
   * Email
   * @type {string}
   * @memberof UserInfoResponseDto
   */
  email: string;
  /**
   * auth id
   * @type {string}
   * @memberof UserInfoResponseDto
   */
  uid: string;
  /**
   *
   * @type {PreferencesDto}
   * @memberof UserInfoResponseDto
   */
  preferences?: PreferencesDto;
}
/**
 *
 * @export
 * @interface UserSuppressedStatusDto
 */
export interface UserSuppressedStatusDto {
  /**
   *
   * @type {string}
   * @memberof UserSuppressedStatusDto
   */
  status: UserSuppressedStatusDtoStatusEnum | null;
}

export const UserSuppressedStatusDtoStatusEnum = {
  Bounce: 'BOUNCE',
  Complaint: 'COMPLAINT',
  Unknown: 'UNKNOWN',
} as const;

export type UserSuppressedStatusDtoStatusEnum =
  (typeof UserSuppressedStatusDtoStatusEnum)[keyof typeof UserSuppressedStatusDtoStatusEnum];

/**
 * AuthApi - axios parameter creator
 * @export
 */
export const AuthApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {SendEmailVerificationRequest} sendEmailVerificationRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    emailVerificationControllerSendEmailVerification: async (
      sendEmailVerificationRequest: SendEmailVerificationRequest,
      xAppName?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendEmailVerificationRequest' is not null or undefined
      assertParamExists(
        'emailVerificationControllerSendEmailVerification',
        'sendEmailVerificationRequest',
        sendEmailVerificationRequest
      );
      const localVarPath = `/v1/auth/email-verification`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendEmailVerificationRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {SendPasswordResetRequest} sendPasswordResetRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    passwordResetControllerSendPasswordReset: async (
      sendPasswordResetRequest: SendPasswordResetRequest,
      xAppName?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendPasswordResetRequest' is not null or undefined
      assertParamExists(
        'passwordResetControllerSendPasswordReset',
        'sendPasswordResetRequest',
        sendPasswordResetRequest
      );
      const localVarPath = `/v1/auth/password-reset`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendPasswordResetRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given email, notify them of their password being updated
     * @summary Sends a given email an alert to say that their password has updated
     * @param {string} xAppName The name of the requesting app
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    passwordResetControllerSendPasswordResetAlert: async (
      xAppName: string,
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'xAppName' is not null or undefined
      assertParamExists(
        'passwordResetControllerSendPasswordResetAlert',
        'xAppName',
        xAppName
      );
      // verify required parameter 'body' is not null or undefined
      assertParamExists(
        'passwordResetControllerSendPasswordResetAlert',
        'body',
        body
      );
      const localVarPath = `/v1/auth/password-reset-alert`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Secure an account by randomising the password
     * @summary randomise user password
     * @param {RandomisePasswordRequest} randomisePasswordRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    randomisePasswordControllerRandomisePassword: async (
      randomisePasswordRequest: RandomisePasswordRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'randomisePasswordRequest' is not null or undefined
      assertParamExists(
        'randomisePasswordControllerRandomisePassword',
        'randomisePasswordRequest',
        randomisePasswordRequest
      );
      const localVarPath = `/v1/auth/randomise-password`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        randomisePasswordRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends a recover factor email to the user\'s email address
     * @summary recover a user\'s factor
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    recoverFactorControllerSendRecoverFactor: async (
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      xAppName?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendRecoverFactorRequest' is not null or undefined
      assertParamExists(
        'recoverFactorControllerSendRecoverFactor',
        'sendRecoverFactorRequest',
        sendRecoverFactorRequest
      );
      const localVarPath = `/v1/auth/recover-factor`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendRecoverFactorRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Remove factor from an account
     * @summary remove factor
     * @param {RemoveFactorRequest} removeFactorRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    removeFactorControllerRemoveFactor: async (
      removeFactorRequest: RemoveFactorRequest,
      xAppName?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'removeFactorRequest' is not null or undefined
      assertParamExists(
        'removeFactorControllerRemoveFactor',
        'removeFactorRequest',
        removeFactorRequest
      );
      const localVarPath = `/v1/auth/factor`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        removeFactorRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Remove factor for a given authId and factorId
     * @summary remove factor by id
     * @param {string} authId
     * @param {string} factorId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    removeFactorControllerRemoveFactorById: async (
      authId: string,
      factorId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'authId' is not null or undefined
      assertParamExists(
        'removeFactorControllerRemoveFactorById',
        'authId',
        authId
      );
      // verify required parameter 'factorId' is not null or undefined
      assertParamExists(
        'removeFactorControllerRemoveFactorById',
        'factorId',
        factorId
      );
      const localVarPath = `/v1/auth/factor/{authId}/{factorId}`
        .replace(`{${'authId'}}`, encodeURIComponent(String(authId)))
        .replace(`{${'factorId'}}`, encodeURIComponent(String(factorId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve factors for a given authId
     * @summary get factors
     * @param {string} authId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    retrieveFactorControllerGetFactors: async (
      authId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'authId' is not null or undefined
      assertParamExists('retrieveFactorControllerGetFactors', 'authId', authId);
      const localVarPath = `/v1/auth/factor/{authId}`.replace(
        `{${'authId'}}`,
        encodeURIComponent(String(authId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends a sign in with email link to the user\'s email address
     * @summary generate a sign in with email, email for a user
     * @param {SendSignInWithEmailRequest} sendSignInWithEmailRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    signInWithEmailControllerSendMagicLoginLink: async (
      sendSignInWithEmailRequest: SendSignInWithEmailRequest,
      xAppName?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendSignInWithEmailRequest' is not null or undefined
      assertParamExists(
        'signInWithEmailControllerSendMagicLoginLink',
        'sendSignInWithEmailRequest',
        sendSignInWithEmailRequest
      );
      const localVarPath = `/v1/auth/sign-in-with-email`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendSignInWithEmailRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    telephoneCodesControllerGetTelephoneCodes: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/v1/auth/telephone-codes`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    verifyAndChangeEmailControllerUpdateEmail: async (
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      xAppName?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendVerifyAndChangeEmailRequest' is not null or undefined
      assertParamExists(
        'verifyAndChangeEmailControllerUpdateEmail',
        'sendVerifyAndChangeEmailRequest',
        sendVerifyAndChangeEmailRequest
      );
      const localVarPath = `/v1/auth/verify-and-change-email`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendVerifyAndChangeEmailRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AuthApi - functional programming interface
 * @export
 */
export const AuthApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = AuthApiAxiosParamCreator(configuration);
  return {
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {SendEmailVerificationRequest} sendEmailVerificationRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async emailVerificationControllerSendEmailVerification(
      sendEmailVerificationRequest: SendEmailVerificationRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.emailVerificationControllerSendEmailVerification(
          sendEmailVerificationRequest,
          xAppName,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.emailVerificationControllerSendEmailVerification'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {SendPasswordResetRequest} sendPasswordResetRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async passwordResetControllerSendPasswordReset(
      sendPasswordResetRequest: SendPasswordResetRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.passwordResetControllerSendPasswordReset(
          sendPasswordResetRequest,
          xAppName,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.passwordResetControllerSendPasswordReset'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given email, notify them of their password being updated
     * @summary Sends a given email an alert to say that their password has updated
     * @param {string} xAppName The name of the requesting app
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async passwordResetControllerSendPasswordResetAlert(
      xAppName: string,
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.passwordResetControllerSendPasswordResetAlert(
          xAppName,
          body,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.passwordResetControllerSendPasswordResetAlert'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Secure an account by randomising the password
     * @summary randomise user password
     * @param {RandomisePasswordRequest} randomisePasswordRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async randomisePasswordControllerRandomisePassword(
      randomisePasswordRequest: RandomisePasswordRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.randomisePasswordControllerRandomisePassword(
          randomisePasswordRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.randomisePasswordControllerRandomisePassword'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends a recover factor email to the user\'s email address
     * @summary recover a user\'s factor
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async recoverFactorControllerSendRecoverFactor(
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.recoverFactorControllerSendRecoverFactor(
          sendRecoverFactorRequest,
          xAppName,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.recoverFactorControllerSendRecoverFactor'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Remove factor from an account
     * @summary remove factor
     * @param {RemoveFactorRequest} removeFactorRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async removeFactorControllerRemoveFactor(
      removeFactorRequest: RemoveFactorRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.removeFactorControllerRemoveFactor(
          removeFactorRequest,
          xAppName,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AuthApi.removeFactorControllerRemoveFactor']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Remove factor for a given authId and factorId
     * @summary remove factor by id
     * @param {string} authId
     * @param {string} factorId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async removeFactorControllerRemoveFactorById(
      authId: string,
      factorId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.removeFactorControllerRemoveFactorById(
          authId,
          factorId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AuthApi.removeFactorControllerRemoveFactorById']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve factors for a given authId
     * @summary get factors
     * @param {string} authId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async retrieveFactorControllerGetFactors(
      authId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Factor>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.retrieveFactorControllerGetFactors(
          authId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AuthApi.retrieveFactorControllerGetFactors']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends a sign in with email link to the user\'s email address
     * @summary generate a sign in with email, email for a user
     * @param {SendSignInWithEmailRequest} sendSignInWithEmailRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async signInWithEmailControllerSendMagicLoginLink(
      sendSignInWithEmailRequest: SendSignInWithEmailRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.signInWithEmailControllerSendMagicLoginLink(
          sendSignInWithEmailRequest,
          xAppName,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.signInWithEmailControllerSendMagicLoginLink'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async telephoneCodesControllerGetTelephoneCodes(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.telephoneCodesControllerGetTelephoneCodes(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.telephoneCodesControllerGetTelephoneCodes'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async verifyAndChangeEmailControllerUpdateEmail(
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.verifyAndChangeEmailControllerUpdateEmail(
          sendVerifyAndChangeEmailRequest,
          xAppName,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.verifyAndChangeEmailControllerUpdateEmail'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AuthApi - factory interface
 * @export
 */
export const AuthApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AuthApiFp(configuration);
  return {
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {SendEmailVerificationRequest} sendEmailVerificationRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    emailVerificationControllerSendEmailVerification(
      sendEmailVerificationRequest: SendEmailVerificationRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .emailVerificationControllerSendEmailVerification(
          sendEmailVerificationRequest,
          xAppName,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {SendPasswordResetRequest} sendPasswordResetRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    passwordResetControllerSendPasswordReset(
      sendPasswordResetRequest: SendPasswordResetRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .passwordResetControllerSendPasswordReset(
          sendPasswordResetRequest,
          xAppName,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given email, notify them of their password being updated
     * @summary Sends a given email an alert to say that their password has updated
     * @param {string} xAppName The name of the requesting app
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    passwordResetControllerSendPasswordResetAlert(
      xAppName: string,
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .passwordResetControllerSendPasswordResetAlert(xAppName, body, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Secure an account by randomising the password
     * @summary randomise user password
     * @param {RandomisePasswordRequest} randomisePasswordRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    randomisePasswordControllerRandomisePassword(
      randomisePasswordRequest: RandomisePasswordRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .randomisePasswordControllerRandomisePassword(
          randomisePasswordRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends a recover factor email to the user\'s email address
     * @summary recover a user\'s factor
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    recoverFactorControllerSendRecoverFactor(
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .recoverFactorControllerSendRecoverFactor(
          sendRecoverFactorRequest,
          xAppName,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Remove factor from an account
     * @summary remove factor
     * @param {RemoveFactorRequest} removeFactorRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    removeFactorControllerRemoveFactor(
      removeFactorRequest: RemoveFactorRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .removeFactorControllerRemoveFactor(
          removeFactorRequest,
          xAppName,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Remove factor for a given authId and factorId
     * @summary remove factor by id
     * @param {string} authId
     * @param {string} factorId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    removeFactorControllerRemoveFactorById(
      authId: string,
      factorId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .removeFactorControllerRemoveFactorById(authId, factorId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve factors for a given authId
     * @summary get factors
     * @param {string} authId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    retrieveFactorControllerGetFactors(
      authId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Factor>> {
      return localVarFp
        .retrieveFactorControllerGetFactors(authId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends a sign in with email link to the user\'s email address
     * @summary generate a sign in with email, email for a user
     * @param {SendSignInWithEmailRequest} sendSignInWithEmailRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    signInWithEmailControllerSendMagicLoginLink(
      sendSignInWithEmailRequest: SendSignInWithEmailRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .signInWithEmailControllerSendMagicLoginLink(
          sendSignInWithEmailRequest,
          xAppName,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    telephoneCodesControllerGetTelephoneCodes(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .telephoneCodesControllerGetTelephoneCodes(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    verifyAndChangeEmailControllerUpdateEmail(
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .verifyAndChangeEmailControllerUpdateEmail(
          sendVerifyAndChangeEmailRequest,
          xAppName,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AuthApi - object-oriented interface
 * @export
 * @class AuthApi
 * @extends {BaseAPI}
 */
export class AuthApi extends BaseAPI {
  /**
   * Sends an email verification email to the user\'s email address
   * @summary verify a user\'s email address
   * @param {SendEmailVerificationRequest} sendEmailVerificationRequest
   * @param {string} [xAppName] The name of the requesting app
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public emailVerificationControllerSendEmailVerification(
    sendEmailVerificationRequest: SendEmailVerificationRequest,
    xAppName?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .emailVerificationControllerSendEmailVerification(
        sendEmailVerificationRequest,
        xAppName,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends a reset password email to the user\'s email address
   * @summary reset a user\'s password
   * @param {SendPasswordResetRequest} sendPasswordResetRequest
   * @param {string} [xAppName] The name of the requesting app
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public passwordResetControllerSendPasswordReset(
    sendPasswordResetRequest: SendPasswordResetRequest,
    xAppName?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .passwordResetControllerSendPasswordReset(
        sendPasswordResetRequest,
        xAppName,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given email, notify them of their password being updated
   * @summary Sends a given email an alert to say that their password has updated
   * @param {string} xAppName The name of the requesting app
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public passwordResetControllerSendPasswordResetAlert(
    xAppName: string,
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .passwordResetControllerSendPasswordResetAlert(xAppName, body, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Secure an account by randomising the password
   * @summary randomise user password
   * @param {RandomisePasswordRequest} randomisePasswordRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public randomisePasswordControllerRandomisePassword(
    randomisePasswordRequest: RandomisePasswordRequest,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .randomisePasswordControllerRandomisePassword(
        randomisePasswordRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends a recover factor email to the user\'s email address
   * @summary recover a user\'s factor
   * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
   * @param {string} [xAppName] The name of the requesting app
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public recoverFactorControllerSendRecoverFactor(
    sendRecoverFactorRequest: SendRecoverFactorRequest,
    xAppName?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .recoverFactorControllerSendRecoverFactor(
        sendRecoverFactorRequest,
        xAppName,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Remove factor from an account
   * @summary remove factor
   * @param {RemoveFactorRequest} removeFactorRequest
   * @param {string} [xAppName] The name of the requesting app
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public removeFactorControllerRemoveFactor(
    removeFactorRequest: RemoveFactorRequest,
    xAppName?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .removeFactorControllerRemoveFactor(
        removeFactorRequest,
        xAppName,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Remove factor for a given authId and factorId
   * @summary remove factor by id
   * @param {string} authId
   * @param {string} factorId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public removeFactorControllerRemoveFactorById(
    authId: string,
    factorId: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .removeFactorControllerRemoveFactorById(authId, factorId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve factors for a given authId
   * @summary get factors
   * @param {string} authId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public retrieveFactorControllerGetFactors(
    authId: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .retrieveFactorControllerGetFactors(authId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends a sign in with email link to the user\'s email address
   * @summary generate a sign in with email, email for a user
   * @param {SendSignInWithEmailRequest} sendSignInWithEmailRequest
   * @param {string} [xAppName] The name of the requesting app
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public signInWithEmailControllerSendMagicLoginLink(
    sendSignInWithEmailRequest: SendSignInWithEmailRequest,
    xAppName?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .signInWithEmailControllerSendMagicLoginLink(
        sendSignInWithEmailRequest,
        xAppName,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieves available telephone codes specified by the Firebase configuration
   * @summary retrieve available telephone codes
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public telephoneCodesControllerGetTelephoneCodes(
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .telephoneCodesControllerGetTelephoneCodes(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
   * @summary changes a user\'s email address
   * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
   * @param {string} [xAppName] The name of the requesting app
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public verifyAndChangeEmailControllerUpdateEmail(
    sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
    xAppName?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .verifyAndChangeEmailControllerUpdateEmail(
        sendVerifyAndChangeEmailRequest,
        xAppName,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ExampleApi - axios parameter creator
 * @export
 */
export const ExampleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    helloControllerGetData: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ExampleApi - functional programming interface
 * @export
 */
export const ExampleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ExampleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async helloControllerGetData(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.helloControllerGetData(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExampleApi.helloControllerGetData']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ExampleApi - factory interface
 * @export
 */
export const ExampleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ExampleApiFp(configuration);
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    helloControllerGetData(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .helloControllerGetData(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ExampleApi - object-oriented interface
 * @export
 * @class ExampleApi
 * @extends {BaseAPI}
 */
export class ExampleApi extends BaseAPI {
  /**
   *
   * @summary welcome message endpoint
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExampleApi
   */
  public helloControllerGetData(options?: RawAxiosRequestConfig) {
    return ExampleApiFp(this.configuration)
      .helloControllerGetData(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * HealthcheckApi - axios parameter creator
 * @export
 */
export const HealthcheckApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get driver account API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthcheckApi - functional programming interface
 * @export
 */
export const HealthcheckApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    HealthcheckApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get driver account API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthcheckApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthcheckApi - factory interface
 * @export
 */
export const HealthcheckApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthcheckApiFp(configuration);
  return {
    /**
     *
     * @summary get driver account API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthcheckApi - object-oriented interface
 * @export
 * @class HealthcheckApi
 * @extends {BaseAPI}
 */
export class HealthcheckApi extends BaseAPI {
  /**
   *
   * @summary get driver account API health
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthcheckApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthcheckApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * NotificationsApi - axios parameter creator
 * @export
 */
export const NotificationsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Deletes a notification token associated with a user
     * @summary delete notification token
     * @param {string} userId
     * @param {string} token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    notificationsControllerDeleteToken: async (
      userId: string,
      token: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('notificationsControllerDeleteToken', 'userId', userId);
      // verify required parameter 'token' is not null or undefined
      assertParamExists('notificationsControllerDeleteToken', 'token', token);
      const localVarPath = `/v1/users/{userId}/notifications/tokens/{token}`
        .replace(`{${'userId'}}`, encodeURIComponent(String(userId)))
        .replace(`{${'token'}}`, encodeURIComponent(String(token)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Gets notifications tokens stored against a given user
     * @summary
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    notificationsControllerGetTokens: async (
      userId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('notificationsControllerGetTokens', 'userId', userId);
      const localVarPath = `/v1/users/{userId}/notifications/tokens`.replace(
        `{${'userId'}}`,
        encodeURIComponent(String(userId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Stores a notification token against a given user
     * @summary store notification token
     * @param {string} userId
     * @param {FcmTokenDto} fcmTokenDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    notificationsControllerSaveToken: async (
      userId: string,
      fcmTokenDto: FcmTokenDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('notificationsControllerSaveToken', 'userId', userId);
      // verify required parameter 'fcmTokenDto' is not null or undefined
      assertParamExists(
        'notificationsControllerSaveToken',
        'fcmTokenDto',
        fcmTokenDto
      );
      const localVarPath = `/v1/users/{userId}/notifications/tokens`.replace(
        `{${'userId'}}`,
        encodeURIComponent(String(userId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        fcmTokenDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * NotificationsApi - functional programming interface
 * @export
 */
export const NotificationsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    NotificationsApiAxiosParamCreator(configuration);
  return {
    /**
     * Deletes a notification token associated with a user
     * @summary delete notification token
     * @param {string} userId
     * @param {string} token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async notificationsControllerDeleteToken(
      userId: string,
      token: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.notificationsControllerDeleteToken(
          userId,
          token,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'NotificationsApi.notificationsControllerDeleteToken'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Gets notifications tokens stored against a given user
     * @summary
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async notificationsControllerGetTokens(
      userId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<FcmTokenDto>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.notificationsControllerGetTokens(
          userId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'NotificationsApi.notificationsControllerGetTokens'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Stores a notification token against a given user
     * @summary store notification token
     * @param {string} userId
     * @param {FcmTokenDto} fcmTokenDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async notificationsControllerSaveToken(
      userId: string,
      fcmTokenDto: FcmTokenDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.notificationsControllerSaveToken(
          userId,
          fcmTokenDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'NotificationsApi.notificationsControllerSaveToken'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * NotificationsApi - factory interface
 * @export
 */
export const NotificationsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = NotificationsApiFp(configuration);
  return {
    /**
     * Deletes a notification token associated with a user
     * @summary delete notification token
     * @param {string} userId
     * @param {string} token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    notificationsControllerDeleteToken(
      userId: string,
      token: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .notificationsControllerDeleteToken(userId, token, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Gets notifications tokens stored against a given user
     * @summary
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    notificationsControllerGetTokens(
      userId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<FcmTokenDto>> {
      return localVarFp
        .notificationsControllerGetTokens(userId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Stores a notification token against a given user
     * @summary store notification token
     * @param {string} userId
     * @param {FcmTokenDto} fcmTokenDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    notificationsControllerSaveToken(
      userId: string,
      fcmTokenDto: FcmTokenDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .notificationsControllerSaveToken(userId, fcmTokenDto, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * NotificationsApi - object-oriented interface
 * @export
 * @class NotificationsApi
 * @extends {BaseAPI}
 */
export class NotificationsApi extends BaseAPI {
  /**
   * Deletes a notification token associated with a user
   * @summary delete notification token
   * @param {string} userId
   * @param {string} token
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof NotificationsApi
   */
  public notificationsControllerDeleteToken(
    userId: string,
    token: string,
    options?: RawAxiosRequestConfig
  ) {
    return NotificationsApiFp(this.configuration)
      .notificationsControllerDeleteToken(userId, token, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Gets notifications tokens stored against a given user
   * @summary
   * @param {string} userId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof NotificationsApi
   */
  public notificationsControllerGetTokens(
    userId: string,
    options?: RawAxiosRequestConfig
  ) {
    return NotificationsApiFp(this.configuration)
      .notificationsControllerGetTokens(userId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Stores a notification token against a given user
   * @summary store notification token
   * @param {string} userId
   * @param {FcmTokenDto} fcmTokenDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof NotificationsApi
   */
  public notificationsControllerSaveToken(
    userId: string,
    fcmTokenDto: FcmTokenDto,
    options?: RawAxiosRequestConfig
  ) {
    return NotificationsApiFp(this.configuration)
      .notificationsControllerSaveToken(userId, fcmTokenDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * OauthApi - axios parameter creator
 * @export
 */
export const OauthApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Triggers an OAuth 2.0 flow for users to authorize access to their charger The only supported flow is Authorization Code Flow with PKCE.
     * @summary OAuth 2.0 authorization endpoint
     * @param {string} responseType Response type (must be \&quot;code\&quot;)
     * @param {string} clientId Client ID for the application requesting access
     * @param {string} redirectUri URI to redirect the user to after authorization This must be provided and match one of the URIs registered against the client ID. On successful authorization this will be called with &#x60;code&#x60; (and &#x60;state&#x60; if provided) as query parameters. The &#x60;code&#x60; can then be exchanged for an access token using the &#x60;/token&#x60; endpoint. On error, the user will be redirected to this URI with an &#x60;error&#x60; (as defined in RFC 6749 *******) As well as an &#x60;error_description&#x60; to be displayed to the user (and &#x60;state&#x60; if provided).
     * @param {string} codeChallengeMethod PKCE code challenge method (only S256 is supported)
     * @param {string} codeChallenge Code challenge generated by the client using the code verifier
     * @param {string} [scope] The scope(s) requested for access Note that this is an OAuth endpoint only. Therefore openid scopes and /userinfo are not supported.
     * @param {string} [state] Optional string to be repeated in the response to prevent CSRF attacks
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    oAuthControllerAuthorize: async (
      responseType: string,
      clientId: string,
      redirectUri: string,
      codeChallengeMethod: string,
      codeChallenge: string,
      scope?: string,
      state?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'responseType' is not null or undefined
      assertParamExists(
        'oAuthControllerAuthorize',
        'responseType',
        responseType
      );
      // verify required parameter 'clientId' is not null or undefined
      assertParamExists('oAuthControllerAuthorize', 'clientId', clientId);
      // verify required parameter 'redirectUri' is not null or undefined
      assertParamExists('oAuthControllerAuthorize', 'redirectUri', redirectUri);
      // verify required parameter 'codeChallengeMethod' is not null or undefined
      assertParamExists(
        'oAuthControllerAuthorize',
        'codeChallengeMethod',
        codeChallengeMethod
      );
      // verify required parameter 'codeChallenge' is not null or undefined
      assertParamExists(
        'oAuthControllerAuthorize',
        'codeChallenge',
        codeChallenge
      );
      const localVarPath = `/v1/oauth/authorize`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (scope !== undefined) {
        localVarQueryParameter['scope'] = scope;
      }

      if (responseType !== undefined) {
        localVarQueryParameter['response_type'] = responseType;
      }

      if (clientId !== undefined) {
        localVarQueryParameter['client_id'] = clientId;
      }

      if (state !== undefined) {
        localVarQueryParameter['state'] = state;
      }

      if (redirectUri !== undefined) {
        localVarQueryParameter['redirect_uri'] = redirectUri;
      }

      if (codeChallengeMethod !== undefined) {
        localVarQueryParameter['code_challenge_method'] = codeChallengeMethod;
      }

      if (codeChallenge !== undefined) {
        localVarQueryParameter['code_challenge'] = codeChallenge;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * The internal stage of the OAuth 2.0 flow where the user, after signing-in, consents to the application accessing their account. (as limited by the scopes requested).
     * @summary Consent to an OAuth 2.0 application accessing the authorized account
     * @param {OAuthConsentRequestDto} oAuthConsentRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    oAuthControllerConsent: async (
      oAuthConsentRequestDto: OAuthConsentRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'oAuthConsentRequestDto' is not null or undefined
      assertParamExists(
        'oAuthControllerConsent',
        'oAuthConsentRequestDto',
        oAuthConsentRequestDto
      );
      const localVarPath = `/v1/oauth/consent`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        oAuthConsentRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get information on an OAuth 2.0 client, including name, image and website URL
     * @summary Get information on an OAuth 2.0 client
     * @param {string} clientId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    oAuthControllerGetOAuthClient: async (
      clientId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'clientId' is not null or undefined
      assertParamExists('oAuthControllerGetOAuthClient', 'clientId', clientId);
      const localVarPath = `/v1/oauth/client_info/{client_id}`.replace(
        `{${'client_id'}}`,
        encodeURIComponent(String(clientId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Exchanges an authorization/refresh code or client credentials for an access token. The supported grant types are `authorization_code`,`client_credentials` and `refresh_token`. Use of `authorization_code` requires PKCE extensions, and is used against charger specific endpoints. `refresh_token` can then be used to refresh an access token provided via the authorization code flow. `client_credentials` can only be used for non-charger specific endpoints (e.g. webhooks), and the resulting access token does not support refresh. All access tokens expire after 1 hour. Refresh tokens (where provided) expire as defined by the OAuth application.
     * @summary OAuth 2.0 token endpoint
     * @param {OAuthControllerTokenRequest} oAuthControllerTokenRequest OAuth 2.0 token exchange request
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    oAuthControllerToken: async (
      oAuthControllerTokenRequest: OAuthControllerTokenRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'oAuthControllerTokenRequest' is not null or undefined
      assertParamExists(
        'oAuthControllerToken',
        'oAuthControllerTokenRequest',
        oAuthControllerTokenRequest
      );
      const localVarPath = `/v1/oauth/token`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        oAuthControllerTokenRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * OauthApi - functional programming interface
 * @export
 */
export const OauthApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = OauthApiAxiosParamCreator(configuration);
  return {
    /**
     * Triggers an OAuth 2.0 flow for users to authorize access to their charger The only supported flow is Authorization Code Flow with PKCE.
     * @summary OAuth 2.0 authorization endpoint
     * @param {string} responseType Response type (must be \&quot;code\&quot;)
     * @param {string} clientId Client ID for the application requesting access
     * @param {string} redirectUri URI to redirect the user to after authorization This must be provided and match one of the URIs registered against the client ID. On successful authorization this will be called with &#x60;code&#x60; (and &#x60;state&#x60; if provided) as query parameters. The &#x60;code&#x60; can then be exchanged for an access token using the &#x60;/token&#x60; endpoint. On error, the user will be redirected to this URI with an &#x60;error&#x60; (as defined in RFC 6749 *******) As well as an &#x60;error_description&#x60; to be displayed to the user (and &#x60;state&#x60; if provided).
     * @param {string} codeChallengeMethod PKCE code challenge method (only S256 is supported)
     * @param {string} codeChallenge Code challenge generated by the client using the code verifier
     * @param {string} [scope] The scope(s) requested for access Note that this is an OAuth endpoint only. Therefore openid scopes and /userinfo are not supported.
     * @param {string} [state] Optional string to be repeated in the response to prevent CSRF attacks
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async oAuthControllerAuthorize(
      responseType: string,
      clientId: string,
      redirectUri: string,
      codeChallengeMethod: string,
      codeChallenge: string,
      scope?: string,
      state?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.oAuthControllerAuthorize(
          responseType,
          clientId,
          redirectUri,
          codeChallengeMethod,
          codeChallenge,
          scope,
          state,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['OauthApi.oAuthControllerAuthorize']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * The internal stage of the OAuth 2.0 flow where the user, after signing-in, consents to the application accessing their account. (as limited by the scopes requested).
     * @summary Consent to an OAuth 2.0 application accessing the authorized account
     * @param {OAuthConsentRequestDto} oAuthConsentRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async oAuthControllerConsent(
      oAuthConsentRequestDto: OAuthConsentRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.oAuthControllerConsent(
          oAuthConsentRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['OauthApi.oAuthControllerConsent']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get information on an OAuth 2.0 client, including name, image and website URL
     * @summary Get information on an OAuth 2.0 client
     * @param {string} clientId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async oAuthControllerGetOAuthClient(
      clientId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<OAuthClientResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.oAuthControllerGetOAuthClient(
          clientId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['OauthApi.oAuthControllerGetOAuthClient']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Exchanges an authorization/refresh code or client credentials for an access token. The supported grant types are `authorization_code`,`client_credentials` and `refresh_token`. Use of `authorization_code` requires PKCE extensions, and is used against charger specific endpoints. `refresh_token` can then be used to refresh an access token provided via the authorization code flow. `client_credentials` can only be used for non-charger specific endpoints (e.g. webhooks), and the resulting access token does not support refresh. All access tokens expire after 1 hour. Refresh tokens (where provided) expire as defined by the OAuth application.
     * @summary OAuth 2.0 token endpoint
     * @param {OAuthControllerTokenRequest} oAuthControllerTokenRequest OAuth 2.0 token exchange request
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async oAuthControllerToken(
      oAuthControllerTokenRequest: OAuthControllerTokenRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<OAuthControllerToken200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.oAuthControllerToken(
          oAuthControllerTokenRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['OauthApi.oAuthControllerToken']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * OauthApi - factory interface
 * @export
 */
export const OauthApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = OauthApiFp(configuration);
  return {
    /**
     * Triggers an OAuth 2.0 flow for users to authorize access to their charger The only supported flow is Authorization Code Flow with PKCE.
     * @summary OAuth 2.0 authorization endpoint
     * @param {string} responseType Response type (must be \&quot;code\&quot;)
     * @param {string} clientId Client ID for the application requesting access
     * @param {string} redirectUri URI to redirect the user to after authorization This must be provided and match one of the URIs registered against the client ID. On successful authorization this will be called with &#x60;code&#x60; (and &#x60;state&#x60; if provided) as query parameters. The &#x60;code&#x60; can then be exchanged for an access token using the &#x60;/token&#x60; endpoint. On error, the user will be redirected to this URI with an &#x60;error&#x60; (as defined in RFC 6749 *******) As well as an &#x60;error_description&#x60; to be displayed to the user (and &#x60;state&#x60; if provided).
     * @param {string} codeChallengeMethod PKCE code challenge method (only S256 is supported)
     * @param {string} codeChallenge Code challenge generated by the client using the code verifier
     * @param {string} [scope] The scope(s) requested for access Note that this is an OAuth endpoint only. Therefore openid scopes and /userinfo are not supported.
     * @param {string} [state] Optional string to be repeated in the response to prevent CSRF attacks
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    oAuthControllerAuthorize(
      responseType: string,
      clientId: string,
      redirectUri: string,
      codeChallengeMethod: string,
      codeChallenge: string,
      scope?: string,
      state?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .oAuthControllerAuthorize(
          responseType,
          clientId,
          redirectUri,
          codeChallengeMethod,
          codeChallenge,
          scope,
          state,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * The internal stage of the OAuth 2.0 flow where the user, after signing-in, consents to the application accessing their account. (as limited by the scopes requested).
     * @summary Consent to an OAuth 2.0 application accessing the authorized account
     * @param {OAuthConsentRequestDto} oAuthConsentRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    oAuthControllerConsent(
      oAuthConsentRequestDto: OAuthConsentRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .oAuthControllerConsent(oAuthConsentRequestDto, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get information on an OAuth 2.0 client, including name, image and website URL
     * @summary Get information on an OAuth 2.0 client
     * @param {string} clientId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    oAuthControllerGetOAuthClient(
      clientId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OAuthClientResponseDto> {
      return localVarFp
        .oAuthControllerGetOAuthClient(clientId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Exchanges an authorization/refresh code or client credentials for an access token. The supported grant types are `authorization_code`,`client_credentials` and `refresh_token`. Use of `authorization_code` requires PKCE extensions, and is used against charger specific endpoints. `refresh_token` can then be used to refresh an access token provided via the authorization code flow. `client_credentials` can only be used for non-charger specific endpoints (e.g. webhooks), and the resulting access token does not support refresh. All access tokens expire after 1 hour. Refresh tokens (where provided) expire as defined by the OAuth application.
     * @summary OAuth 2.0 token endpoint
     * @param {OAuthControllerTokenRequest} oAuthControllerTokenRequest OAuth 2.0 token exchange request
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    oAuthControllerToken(
      oAuthControllerTokenRequest: OAuthControllerTokenRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OAuthControllerToken200Response> {
      return localVarFp
        .oAuthControllerToken(oAuthControllerTokenRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * OauthApi - object-oriented interface
 * @export
 * @class OauthApi
 * @extends {BaseAPI}
 */
export class OauthApi extends BaseAPI {
  /**
   * Triggers an OAuth 2.0 flow for users to authorize access to their charger The only supported flow is Authorization Code Flow with PKCE.
   * @summary OAuth 2.0 authorization endpoint
   * @param {string} responseType Response type (must be \&quot;code\&quot;)
   * @param {string} clientId Client ID for the application requesting access
   * @param {string} redirectUri URI to redirect the user to after authorization This must be provided and match one of the URIs registered against the client ID. On successful authorization this will be called with &#x60;code&#x60; (and &#x60;state&#x60; if provided) as query parameters. The &#x60;code&#x60; can then be exchanged for an access token using the &#x60;/token&#x60; endpoint. On error, the user will be redirected to this URI with an &#x60;error&#x60; (as defined in RFC 6749 *******) As well as an &#x60;error_description&#x60; to be displayed to the user (and &#x60;state&#x60; if provided).
   * @param {string} codeChallengeMethod PKCE code challenge method (only S256 is supported)
   * @param {string} codeChallenge Code challenge generated by the client using the code verifier
   * @param {string} [scope] The scope(s) requested for access Note that this is an OAuth endpoint only. Therefore openid scopes and /userinfo are not supported.
   * @param {string} [state] Optional string to be repeated in the response to prevent CSRF attacks
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OauthApi
   */
  public oAuthControllerAuthorize(
    responseType: string,
    clientId: string,
    redirectUri: string,
    codeChallengeMethod: string,
    codeChallenge: string,
    scope?: string,
    state?: string,
    options?: RawAxiosRequestConfig
  ) {
    return OauthApiFp(this.configuration)
      .oAuthControllerAuthorize(
        responseType,
        clientId,
        redirectUri,
        codeChallengeMethod,
        codeChallenge,
        scope,
        state,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * The internal stage of the OAuth 2.0 flow where the user, after signing-in, consents to the application accessing their account. (as limited by the scopes requested).
   * @summary Consent to an OAuth 2.0 application accessing the authorized account
   * @param {OAuthConsentRequestDto} oAuthConsentRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OauthApi
   */
  public oAuthControllerConsent(
    oAuthConsentRequestDto: OAuthConsentRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return OauthApiFp(this.configuration)
      .oAuthControllerConsent(oAuthConsentRequestDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get information on an OAuth 2.0 client, including name, image and website URL
   * @summary Get information on an OAuth 2.0 client
   * @param {string} clientId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OauthApi
   */
  public oAuthControllerGetOAuthClient(
    clientId: string,
    options?: RawAxiosRequestConfig
  ) {
    return OauthApiFp(this.configuration)
      .oAuthControllerGetOAuthClient(clientId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Exchanges an authorization/refresh code or client credentials for an access token. The supported grant types are `authorization_code`,`client_credentials` and `refresh_token`. Use of `authorization_code` requires PKCE extensions, and is used against charger specific endpoints. `refresh_token` can then be used to refresh an access token provided via the authorization code flow. `client_credentials` can only be used for non-charger specific endpoints (e.g. webhooks), and the resulting access token does not support refresh. All access tokens expire after 1 hour. Refresh tokens (where provided) expire as defined by the OAuth application.
   * @summary OAuth 2.0 token endpoint
   * @param {OAuthControllerTokenRequest} oAuthControllerTokenRequest OAuth 2.0 token exchange request
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OauthApi
   */
  public oAuthControllerToken(
    oAuthControllerTokenRequest: OAuthControllerTokenRequest,
    options?: RawAxiosRequestConfig
  ) {
    return OauthApiFp(this.configuration)
      .oAuthControllerToken(oAuthControllerTokenRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * UsersApi - axios parameter creator
 * @export
 */
export const UsersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Creates a new user account
     * @summary create a new user
     * @param {string} xAppName
     * @param {CreateUserDto} createUserDto
     * @param {string} [resetPasswordContinueUrl] Reset password continue url that is optional
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerCreateUser: async (
      xAppName: string,
      createUserDto: CreateUserDto,
      resetPasswordContinueUrl?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'xAppName' is not null or undefined
      assertParamExists('userControllerCreateUser', 'xAppName', xAppName);
      // verify required parameter 'createUserDto' is not null or undefined
      assertParamExists(
        'userControllerCreateUser',
        'createUserDto',
        createUserDto
      );
      const localVarPath = `/v1/users`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (resetPasswordContinueUrl !== undefined) {
        localVarQueryParameter['reset_password_continue_url'] =
          resetPasswordContinueUrl;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createUserDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Enable a user profile for a given UID
     * @summary enable user profile by UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerEnable: async (
      uid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerEnable', 'uid', uid);
      const localVarPath = `/v1/users/enable/{uid}`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a user profile by a given filter. Accepts only email at the moment
     * @summary get user profile by given filters
     * @param {string} [ppid] Allows for searching by PPID
     * @param {string} [emailLike] Allows for fuzzy matching on the email field
     * @param {string} [email]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerGetByFilter: async (
      ppid?: string,
      emailLike?: string,
      email?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/v1/users`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (ppid !== undefined) {
        localVarQueryParameter['ppid'] = ppid;
      }

      if (emailLike !== undefined) {
        localVarQueryParameter['emailLike'] = emailLike;
      }

      if (email !== undefined) {
        localVarQueryParameter['email'] = email;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a user\'s chargers by their  UID
     * @summary get user\'s chargers by their UID
     * @param {string} uid
     * @param {boolean} [includeOtherLinkedUsers]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerGetChargers: async (
      uid: string,
      includeOtherLinkedUsers?: boolean,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerGetChargers', 'uid', uid);
      const localVarPath = `/v1/users/{uid}/chargers`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (includeOtherLinkedUsers !== undefined) {
        localVarQueryParameter['includeOtherLinkedUsers'] =
          includeOtherLinkedUsers;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * get a user\'s suppressed status by their UID
     * @summary get a user\'s suppressed status by their UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerGetSuppressedStatus: async (
      uid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerGetSuppressedStatus', 'uid', uid);
      const localVarPath = `/v1/users/{uid}/suppressedStatus`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a user profile for a given UID
     * @summary get user profile by UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerGetUser: async (
      uid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerGetUser', 'uid', uid);
      const localVarPath = `/v1/users/{uid}`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} uid
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerLinkCharger: async (
      uid: string,
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerLinkCharger', 'uid', uid);
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('userControllerLinkCharger', 'ppid', ppid);
      const localVarPath = `/v1/users/{uid}/chargers/{ppid}`
        .replace(`{${'uid'}}`, encodeURIComponent(String(uid)))
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * delete a user
     * @summary delete a user
     * @param {string} uid
     * @param {boolean} [force] Allows to disable users when they have outstanding balance
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerSoftDelete: async (
      uid: string,
      force?: boolean,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerSoftDelete', 'uid', uid);
      const localVarPath = `/v1/users/{uid}`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (force !== undefined) {
        localVarQueryParameter['force'] = force;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Generates an email of when and from which ip the user logged in
     * @summary Generates an email of when and from which ip the user logged in
     * @param {string} uid
     * @param {TrackLoginRequest} trackLoginRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerTrackLogin: async (
      uid: string,
      trackLoginRequest: TrackLoginRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerTrackLogin', 'uid', uid);
      // verify required parameter 'trackLoginRequest' is not null or undefined
      assertParamExists(
        'userControllerTrackLogin',
        'trackLoginRequest',
        trackLoginRequest
      );
      const localVarPath = `/v1/users/login/{uid}/alert`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        trackLoginRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Unlink a charger from a user
     * @summary unlink a charger from a user
     * @param {string} uid
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUnLinkCharger: async (
      uid: string,
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerUnLinkCharger', 'uid', uid);
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('userControllerUnLinkCharger', 'ppid', ppid);
      const localVarPath = `/v1/users/{uid}/chargers/{ppid}`
        .replace(`{${'uid'}}`, encodeURIComponent(String(uid)))
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update a user email for an existing email and new email
     * @summary update user email
     * @param {UserEmailDto} userEmailDto
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUpdateEmail: async (
      userEmailDto: UserEmailDto,
      xAppName?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userEmailDto' is not null or undefined
      assertParamExists(
        'userControllerUpdateEmail',
        'userEmailDto',
        userEmailDto
      );
      const localVarPath = `/v1/users/email/update`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        userEmailDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * remove a user from the SES suppression list
     * @summary remove a user from the SES suppression list
     * @param {string} uid
     * @param {UpdateUserSuppressedStatusDto} updateUserSuppressedStatusDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUpdateSuppressedStatus: async (
      uid: string,
      updateUserSuppressedStatusDto: UpdateUserSuppressedStatusDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerUpdateSuppressedStatus', 'uid', uid);
      // verify required parameter 'updateUserSuppressedStatusDto' is not null or undefined
      assertParamExists(
        'userControllerUpdateSuppressedStatus',
        'updateUserSuppressedStatusDto',
        updateUserSuppressedStatusDto
      );
      const localVarPath = `/v1/users/{uid}/suppressedStatus`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateUserSuppressedStatusDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update a user profile for a given UID
     * @summary update user profile by UID
     * @param {string} uid
     * @param {UserDetailsDto} userDetailsDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUpdateUser: async (
      uid: string,
      userDetailsDto: UserDetailsDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('userControllerUpdateUser', 'uid', uid);
      // verify required parameter 'userDetailsDto' is not null or undefined
      assertParamExists(
        'userControllerUpdateUser',
        'userDetailsDto',
        userDetailsDto
      );
      const localVarPath = `/v1/users/{uid}`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        userDetailsDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * UsersApi - functional programming interface
 * @export
 */
export const UsersApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = UsersApiAxiosParamCreator(configuration);
  return {
    /**
     * Creates a new user account
     * @summary create a new user
     * @param {string} xAppName
     * @param {CreateUserDto} createUserDto
     * @param {string} [resetPasswordContinueUrl] Reset password continue url that is optional
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerCreateUser(
      xAppName: string,
      createUserDto: CreateUserDto,
      resetPasswordContinueUrl?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateUserResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerCreateUser(
          xAppName,
          createUserDto,
          resetPasswordContinueUrl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerCreateUser']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Enable a user profile for a given UID
     * @summary enable user profile by UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerEnable(
      uid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<UserInfoResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerEnable(uid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerEnable']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a user profile by a given filter. Accepts only email at the moment
     * @summary get user profile by given filters
     * @param {string} [ppid] Allows for searching by PPID
     * @param {string} [emailLike] Allows for fuzzy matching on the email field
     * @param {string} [email]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerGetByFilter(
      ppid?: string,
      emailLike?: string,
      email?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<UserInfoResponseDto>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerGetByFilter(
          ppid,
          emailLike,
          email,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerGetByFilter']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a user\'s chargers by their  UID
     * @summary get user\'s chargers by their UID
     * @param {string} uid
     * @param {boolean} [includeOtherLinkedUsers]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerGetChargers(
      uid: string,
      includeOtherLinkedUsers?: boolean,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<UserChargerDto>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerGetChargers(
          uid,
          includeOtherLinkedUsers,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerGetChargers']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * get a user\'s suppressed status by their UID
     * @summary get a user\'s suppressed status by their UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerGetSuppressedStatus(
      uid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<UserSuppressedStatusDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerGetSuppressedStatus(
          uid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerGetSuppressedStatus']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a user profile for a given UID
     * @summary get user profile by UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerGetUser(
      uid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ExtendedUserInfoResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerGetUser(uid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerGetUser']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} uid
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerLinkCharger(
      uid: string,
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerLinkCharger(
          uid,
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerLinkCharger']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * delete a user
     * @summary delete a user
     * @param {string} uid
     * @param {boolean} [force] Allows to disable users when they have outstanding balance
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerSoftDelete(
      uid: string,
      force?: boolean,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerSoftDelete(
          uid,
          force,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerSoftDelete']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Generates an email of when and from which ip the user logged in
     * @summary Generates an email of when and from which ip the user logged in
     * @param {string} uid
     * @param {TrackLoginRequest} trackLoginRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerTrackLogin(
      uid: string,
      trackLoginRequest: TrackLoginRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerTrackLogin(
          uid,
          trackLoginRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerTrackLogin']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Unlink a charger from a user
     * @summary unlink a charger from a user
     * @param {string} uid
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerUnLinkCharger(
      uid: string,
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerUnLinkCharger(
          uid,
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerUnLinkCharger']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update a user email for an existing email and new email
     * @summary update user email
     * @param {UserEmailDto} userEmailDto
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerUpdateEmail(
      userEmailDto: UserEmailDto,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserEmailDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerUpdateEmail(
          userEmailDto,
          xAppName,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerUpdateEmail']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * remove a user from the SES suppression list
     * @summary remove a user from the SES suppression list
     * @param {string} uid
     * @param {UpdateUserSuppressedStatusDto} updateUserSuppressedStatusDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerUpdateSuppressedStatus(
      uid: string,
      updateUserSuppressedStatusDto: UpdateUserSuppressedStatusDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<UpdateUserSuppressedStatusDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerUpdateSuppressedStatus(
          uid,
          updateUserSuppressedStatusDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerUpdateSuppressedStatus']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update a user profile for a given UID
     * @summary update user profile by UID
     * @param {string} uid
     * @param {UserDetailsDto} userDetailsDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerUpdateUser(
      uid: string,
      userDetailsDto: UserDetailsDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<UserInfoResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerUpdateUser(
          uid,
          userDetailsDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.userControllerUpdateUser']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * UsersApi - factory interface
 * @export
 */
export const UsersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = UsersApiFp(configuration);
  return {
    /**
     * Creates a new user account
     * @summary create a new user
     * @param {string} xAppName
     * @param {CreateUserDto} createUserDto
     * @param {string} [resetPasswordContinueUrl] Reset password continue url that is optional
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerCreateUser(
      xAppName: string,
      createUserDto: CreateUserDto,
      resetPasswordContinueUrl?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateUserResponseDto> {
      return localVarFp
        .userControllerCreateUser(
          xAppName,
          createUserDto,
          resetPasswordContinueUrl,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Enable a user profile for a given UID
     * @summary enable user profile by UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerEnable(
      uid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UserInfoResponseDto> {
      return localVarFp
        .userControllerEnable(uid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a user profile by a given filter. Accepts only email at the moment
     * @summary get user profile by given filters
     * @param {string} [ppid] Allows for searching by PPID
     * @param {string} [emailLike] Allows for fuzzy matching on the email field
     * @param {string} [email]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerGetByFilter(
      ppid?: string,
      emailLike?: string,
      email?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<UserInfoResponseDto>> {
      return localVarFp
        .userControllerGetByFilter(ppid, emailLike, email, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a user\'s chargers by their  UID
     * @summary get user\'s chargers by their UID
     * @param {string} uid
     * @param {boolean} [includeOtherLinkedUsers]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerGetChargers(
      uid: string,
      includeOtherLinkedUsers?: boolean,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<UserChargerDto>> {
      return localVarFp
        .userControllerGetChargers(uid, includeOtherLinkedUsers, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * get a user\'s suppressed status by their UID
     * @summary get a user\'s suppressed status by their UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerGetSuppressedStatus(
      uid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UserSuppressedStatusDto> {
      return localVarFp
        .userControllerGetSuppressedStatus(uid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a user profile for a given UID
     * @summary get user profile by UID
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerGetUser(
      uid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ExtendedUserInfoResponseDto> {
      return localVarFp
        .userControllerGetUser(uid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} uid
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerLinkCharger(
      uid: string,
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .userControllerLinkCharger(uid, ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * delete a user
     * @summary delete a user
     * @param {string} uid
     * @param {boolean} [force] Allows to disable users when they have outstanding balance
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerSoftDelete(
      uid: string,
      force?: boolean,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .userControllerSoftDelete(uid, force, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Generates an email of when and from which ip the user logged in
     * @summary Generates an email of when and from which ip the user logged in
     * @param {string} uid
     * @param {TrackLoginRequest} trackLoginRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerTrackLogin(
      uid: string,
      trackLoginRequest: TrackLoginRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .userControllerTrackLogin(uid, trackLoginRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Unlink a charger from a user
     * @summary unlink a charger from a user
     * @param {string} uid
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUnLinkCharger(
      uid: string,
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .userControllerUnLinkCharger(uid, ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Update a user email for an existing email and new email
     * @summary update user email
     * @param {UserEmailDto} userEmailDto
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUpdateEmail(
      userEmailDto: UserEmailDto,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UserEmailDto> {
      return localVarFp
        .userControllerUpdateEmail(userEmailDto, xAppName, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * remove a user from the SES suppression list
     * @summary remove a user from the SES suppression list
     * @param {string} uid
     * @param {UpdateUserSuppressedStatusDto} updateUserSuppressedStatusDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUpdateSuppressedStatus(
      uid: string,
      updateUserSuppressedStatusDto: UpdateUserSuppressedStatusDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UpdateUserSuppressedStatusDto> {
      return localVarFp
        .userControllerUpdateSuppressedStatus(
          uid,
          updateUserSuppressedStatusDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update a user profile for a given UID
     * @summary update user profile by UID
     * @param {string} uid
     * @param {UserDetailsDto} userDetailsDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUpdateUser(
      uid: string,
      userDetailsDto: UserDetailsDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UserInfoResponseDto> {
      return localVarFp
        .userControllerUpdateUser(uid, userDetailsDto, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * UsersApi - object-oriented interface
 * @export
 * @class UsersApi
 * @extends {BaseAPI}
 */
export class UsersApi extends BaseAPI {
  /**
   * Creates a new user account
   * @summary create a new user
   * @param {string} xAppName
   * @param {CreateUserDto} createUserDto
   * @param {string} [resetPasswordContinueUrl] Reset password continue url that is optional
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerCreateUser(
    xAppName: string,
    createUserDto: CreateUserDto,
    resetPasswordContinueUrl?: string,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerCreateUser(
        xAppName,
        createUserDto,
        resetPasswordContinueUrl,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Enable a user profile for a given UID
   * @summary enable user profile by UID
   * @param {string} uid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerEnable(uid: string, options?: RawAxiosRequestConfig) {
    return UsersApiFp(this.configuration)
      .userControllerEnable(uid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a user profile by a given filter. Accepts only email at the moment
   * @summary get user profile by given filters
   * @param {string} [ppid] Allows for searching by PPID
   * @param {string} [emailLike] Allows for fuzzy matching on the email field
   * @param {string} [email]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerGetByFilter(
    ppid?: string,
    emailLike?: string,
    email?: string,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerGetByFilter(ppid, emailLike, email, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a user\'s chargers by their  UID
   * @summary get user\'s chargers by their UID
   * @param {string} uid
   * @param {boolean} [includeOtherLinkedUsers]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerGetChargers(
    uid: string,
    includeOtherLinkedUsers?: boolean,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerGetChargers(uid, includeOtherLinkedUsers, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * get a user\'s suppressed status by their UID
   * @summary get a user\'s suppressed status by their UID
   * @param {string} uid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerGetSuppressedStatus(
    uid: string,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerGetSuppressedStatus(uid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a user profile for a given UID
   * @summary get user profile by UID
   * @param {string} uid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerGetUser(uid: string, options?: RawAxiosRequestConfig) {
    return UsersApiFp(this.configuration)
      .userControllerGetUser(uid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} uid
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerLinkCharger(
    uid: string,
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerLinkCharger(uid, ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * delete a user
   * @summary delete a user
   * @param {string} uid
   * @param {boolean} [force] Allows to disable users when they have outstanding balance
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerSoftDelete(
    uid: string,
    force?: boolean,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerSoftDelete(uid, force, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Generates an email of when and from which ip the user logged in
   * @summary Generates an email of when and from which ip the user logged in
   * @param {string} uid
   * @param {TrackLoginRequest} trackLoginRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerTrackLogin(
    uid: string,
    trackLoginRequest: TrackLoginRequest,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerTrackLogin(uid, trackLoginRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Unlink a charger from a user
   * @summary unlink a charger from a user
   * @param {string} uid
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerUnLinkCharger(
    uid: string,
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerUnLinkCharger(uid, ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update a user email for an existing email and new email
   * @summary update user email
   * @param {UserEmailDto} userEmailDto
   * @param {string} [xAppName] The name of the requesting app
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerUpdateEmail(
    userEmailDto: UserEmailDto,
    xAppName?: string,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerUpdateEmail(userEmailDto, xAppName, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * remove a user from the SES suppression list
   * @summary remove a user from the SES suppression list
   * @param {string} uid
   * @param {UpdateUserSuppressedStatusDto} updateUserSuppressedStatusDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerUpdateSuppressedStatus(
    uid: string,
    updateUserSuppressedStatusDto: UpdateUserSuppressedStatusDto,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerUpdateSuppressedStatus(
        uid,
        updateUserSuppressedStatusDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update a user profile for a given UID
   * @summary update user profile by UID
   * @param {string} uid
   * @param {UserDetailsDto} userDetailsDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public userControllerUpdateUser(
    uid: string,
    userDetailsDto: UserDetailsDto,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .userControllerUpdateUser(uid, userDetailsDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * VersionApi - axios parameter creator
 * @export
 */
export const VersionApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/version`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * VersionApi - functional programming interface
 * @export
 */
export const VersionApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = VersionApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.versionControllerGetVersion(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VersionApi.versionControllerGetVersion']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * VersionApi - factory interface
 * @export
 */
export const VersionApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = VersionApiFp(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .versionControllerGetVersion(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * VersionApi - object-oriented interface
 * @export
 * @class VersionApi
 * @extends {BaseAPI}
 */
export class VersionApi extends BaseAPI {
  /**
   *
   * @summary get application version
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VersionApi
   */
  public versionControllerGetVersion(options?: RawAxiosRequestConfig) {
    return VersionApiFp(this.configuration)
      .versionControllerGetVersion(options)
      .then((request) => request(this.axios, this.basePath));
  }
}
