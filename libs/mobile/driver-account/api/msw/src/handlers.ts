/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

// Map to store counters for each API endpoint
const apiCounters = new Map();

const next = (apiKey) => {
  let currentCount = apiCounters.get(apiKey) ?? 0;
  if (currentCount === Number.MAX_SAFE_INTEGER - 1) {
    currentCount = 0;
  }
  apiCounters.set(apiKey, currentCount + 1);
  return currentCount;
};

export const handlers = [
  http.post(`${baseURL}/v1/auth/email-verification`, async () => {
    const resultArray = [[undefined, { status: 202 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /v1/auth/email-verification`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/v1/auth/password-reset`, async () => {
    const resultArray = [[undefined, { status: 202 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /v1/auth/password-reset`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/v1/auth/password-reset-alert`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /v1/auth/password-reset-alert`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/v1/auth/randomise-password`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /v1/auth/randomise-password`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/v1/auth/recover-factor`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /v1/auth/recover-factor`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/v1/auth/factor/:authId/:factorId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /v1/auth/factor/:authId/:factorId`) % resultArray.length
      ]
    );
  }),
  http.delete(`${baseURL}/v1/auth/factor`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`delete /v1/auth/factor`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/v1/auth/factor/:authId`, async () => {
    const resultArray = [
      [getRetrieveFactorControllerGetFactors200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /v1/auth/factor/:authId`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/v1/auth/sign-in-with-email`, async () => {
    const resultArray = [[undefined, { status: 202 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /v1/auth/sign-in-with-email`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/v1/auth/telephone-codes`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /v1/auth/telephone-codes`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/v1/auth/verify-and-change-email`, async () => {
    const resultArray = [[undefined, { status: 202 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /v1/auth/verify-and-change-email`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [getHealthControllerCheck200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /health`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/v1/oauth/authorize`, async () => {
    const resultArray = [[undefined, { status: 302 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /v1/oauth/authorize`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/v1/oauth/client_info/:clientId`, async () => {
    const resultArray = [
      [getOAuthControllerGetOAuthClient200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /v1/oauth/client_info/:clientId`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/v1/oauth/consent`, async () => {
    const resultArray = [[undefined, { status: 302 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /v1/oauth/consent`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/v1/oauth/token`, async () => {
    const resultArray = [
      [getOAuthControllerToken200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /v1/oauth/token`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/v1/users`, async () => {
    const resultArray = [
      [getUserControllerCreateUser200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /v1/users`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/v1/users`, async () => {
    const resultArray = [
      [getUserControllerGetByFilter200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /v1/users`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/v1/users/:uid`, async () => {
    const resultArray = [
      [getUserControllerGetUser200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /v1/users/:uid`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/v1/users/:uid`, async () => {
    const resultArray = [
      [getUserControllerUpdateUser200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`put /v1/users/:uid`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/v1/users/:uid`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`delete /v1/users/:uid`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/v1/users/enable/:uid`, async () => {
    const resultArray = [
      [getUserControllerEnable200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`put /v1/users/enable/:uid`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/v1/users/email/update`, async () => {
    const resultArray = [
      [getUserControllerUpdateEmail200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`put /v1/users/email/update`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/v1/users/:uid/chargers`, async () => {
    const resultArray = [
      [getUserControllerGetChargers200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /v1/users/:uid/chargers`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/v1/users/:uid/chargers/:ppid`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /v1/users/:uid/chargers/:ppid`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/v1/users/:uid/chargers/:ppid`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /v1/users/:uid/chargers/:ppid`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/v1/users/:uid/suppressedStatus`, async () => {
    const resultArray = [
      [getUserControllerGetSuppressedStatus200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /v1/users/:uid/suppressedStatus`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/v1/users/:uid/suppressedStatus`, async () => {
    const resultArray = [
      [getUserControllerUpdateSuppressedStatus200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`put /v1/users/:uid/suppressedStatus`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/v1/users/login/:uid/alert`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /v1/users/login/:uid/alert`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/version`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /version`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/v1/users/:userId/notifications/tokens`, async () => {
    const resultArray = [
      [getNotificationsControllerGetTokens200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /v1/users/:userId/notifications/tokens`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/v1/users/:userId/notifications/tokens`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /v1/users/:userId/notifications/tokens`) % resultArray.length
      ]
    );
  }),
  http.delete(
    `${baseURL}/v1/users/:userId/notifications/tokens/:token`,
    async () => {
      const resultArray = [[undefined, { status: 204 }]];

      return HttpResponse.json(
        ...resultArray[
          next(`delete /v1/users/:userId/notifications/tokens/:token`) %
            resultArray.length
        ]
      );
    }
  ),
];

export function getRetrieveFactorControllerGetFactors200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: '1f7c4f3c-2bb8-434d-a4d5-fc3415d2d4e2',
    phoneNumber: '+447755566677',
    enrollmentTime: 'Fri, 22 Sep 2017 01:49:58 GMT',
  }));
}

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}

export function getOAuthControllerGetOAuthClient200Response() {
  return {
    name: 'client_name',
    websiteUrl: 'https://example.com',
    imageUrl: 'https://example.com',
  };
}

export function getOAuthControllerToken200Response() {
  return faker.helpers.arrayElement([
    {
      access_token: '********************************',
      token_type: 'Bearer',
      expires_in: 3600,
      refresh_token: 'f5b5c6e9f2e4d3a8b7a9f3e5c6e9f2d',
      scope: 'data:read',
    },
    {
      access_token: '********************************',
      token_type: 'Bearer',
      expires_in: 3600,
      scope: 'data:read',
    },
    {
      access_token: '********************************',
      token_type: 'Bearer',
      expires_in: 3600,
    },
  ]);
}

export function getUserControllerCreateUser200Response() {
  return {
    auth_id: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
    created_at: '2023-10-02T12:27:19.000Z',
    id: '910004932',
    password_reset_link: faker.lorem.words(),
  };
}

export function getUserControllerGetByFilter200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    first_name: 'John',
    last_name: 'Doe',
    locale: 'en',
    email: '<EMAIL>',
    uid: 'a3450f38-217a-4d0c-8eec-b7d63c6fd2e0',
    preferences: {
      unitOfDistance: 'mi',
    },
  }));
}

export function getUserControllerGetUser200Response() {
  return {
    first_name: 'John',
    last_name: 'Doe',
    locale: 'en',
    email: '<EMAIL>',
    uid: 'a3450f38-217a-4d0c-8eec-b7d63c6fd2e0',
    preferences: {
      unitOfDistance: 'mi',
    },
    balance: {
      currency: 'GBP',
      amount: 500,
    },
    rewards: {
      totalMiles: 10.5,
      balance: 5000,
      currency: 'GBP',
      payoutThreshold: 150,
      chargers: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: 'PSL-12345',
        miles: 10.5,
      })),
    },
    paymentProcessorId: 'cus_hfJJeHsFdaV2H3s',
    emailVerified: faker.datatype.boolean(),
    lastSignInTimestamp: faker.helpers.arrayElement([
      null,
      '1995-07-16T13:46:06.000Z',
    ]),
    accountCreationTimestamp: '1995-07-16T13:46:06.000Z',
    status: faker.helpers.arrayElement(['active', 'disabled']),
    deletedAtTimestamp: '1995-07-16T13:46:06.000Z',
  };
}

export function getUserControllerUpdateUser200Response() {
  return {
    first_name: 'John',
    last_name: 'Doe',
    locale: 'en',
    email: '<EMAIL>',
    uid: 'a3450f38-217a-4d0c-8eec-b7d63c6fd2e0',
    preferences: {
      unitOfDistance: 'mi',
    },
  };
}

export function getUserControllerEnable200Response() {
  return {
    first_name: 'John',
    last_name: 'Doe',
    locale: 'en',
    email: '<EMAIL>',
    uid: 'a3450f38-217a-4d0c-8eec-b7d63c6fd2e0',
    preferences: {
      unitOfDistance: 'mi',
    },
  };
}

export function getUserControllerUpdateEmail200Response() {
  return {
    newEmail: '<EMAIL>',
    oldEmail: '<EMAIL>',
  };
}

export function getUserControllerGetChargers200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    ppid: 'PSL-456789',
    unitId: 1,
    timezone: 'Etc/UTC',
    linkedAt: '2024-01-01T00:00:00.000Z',
    otherLinkedUsers: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => faker.lorem.words()),
  }));
}

export function getUserControllerGetSuppressedStatus200Response() {
  return {
    status: faker.helpers.arrayElement(['BOUNCE', 'COMPLAINT', 'UNKNOWN']),
  };
}

export function getUserControllerUpdateSuppressedStatus200Response() {
  return {
    status: faker.helpers.arrayElement([null]),
  };
}

export function getNotificationsControllerGetTokens200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    token: 'd7g1h2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0l',
    timestamp: '2024-10-24T13:53:00.000Z',
  }));
}
