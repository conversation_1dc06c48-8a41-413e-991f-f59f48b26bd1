openapi: 3.0.0
paths:
  /v1/auth/email-verification:
    post:
      description: Sends an email verification email to the user's email address
      operationId: EmailVerificationController_sendEmailVerification
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendEmailVerificationRequest'
      responses:
        '202':
          description: ''
      summary: verify a user's email address
      tags:
        - Auth
  /v1/auth/password-reset:
    post:
      description: Sends a reset password email to the user's email address
      operationId: PasswordResetController_sendPasswordReset
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendPasswordResetRequest'
      responses:
        '202':
          description: ''
      summary: reset a user's password
      tags: &a1
        - Auth
  /v1/auth/password-reset-alert:
    post:
      description: For a given email, notify them of their password being updated
      operationId: PasswordResetController_sendPasswordResetAlert
      parameters:
        - name: x-app-name
          required: true
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserPasswordResetAlertDto'
      responses:
        '200':
          description: Successfully sent an alert
        '400':
          description: The provided payload is the incorrect format
        '500':
          description: Thrown when an unknown error occurs
      summary: Sends a given email an alert to say that their password has updated
      tags: *a1
  /v1/auth/randomise-password:
    post:
      description: Secure an account by randomising the password
      operationId: RandomisePasswordController_randomisePassword
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RandomisePasswordRequest'
      responses:
        '200':
          description: User password successfully randomised
      summary: randomise user password
      tags:
        - Auth
  /v1/auth/recover-factor:
    post:
      description: Sends a recover factor email to the user's email address
      operationId: RecoverFactorController_sendRecoverFactor
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendRecoverFactorRequest'
      responses:
        '200':
          description: ''
      summary: recover a user's factor
      tags:
        - Auth
  /v1/auth/factor/{authId}/{factorId}:
    delete:
      description: Remove factor for a given authId and factorId
      operationId: RemoveFactorController_removeFactorById
      parameters:
        - name: authId
          required: true
          in: path
          schema:
            type: string
        - name: factorId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Factor successfully deleted
        '404':
          description: Unable to find matching user
      summary: remove factor by id
      tags: &a2
        - Auth
  /v1/auth/factor:
    delete:
      description: Remove factor from an account
      operationId: RemoveFactorController_removeFactor
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RemoveFactorRequest'
      responses:
        '204':
          description: ''
      summary: remove factor
      tags: *a2
  /v1/auth/factor/{authId}:
    get:
      description: Retrieve factors for a given authId
      operationId: RetrieveFactorController_getFactors
      parameters:
        - name: authId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Factors successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Factor'
        '404':
          description: Unable to find matching user
      summary: get factors
      tags:
        - Auth
  /v1/auth/sign-in-with-email:
    post:
      description: Sends a sign in with email link to the user's email address
      operationId: SignInWithEmailController_sendMagicLoginLink
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendSignInWithEmailRequest'
      responses:
        '202':
          description: ''
      summary: generate a sign in with email, email for a user
      tags:
        - Auth
  /v1/auth/telephone-codes:
    get:
      description: Retrieves available telephone codes specified by the Firebase
        configuration
      operationId: TelephoneCodesController_getTelephoneCodes
      parameters: []
      responses:
        '200':
          description: Successfully retrieved country codes
        '404':
          description: Firebase configuration could not be found
      summary: retrieve available telephone codes
      tags:
        - Auth
  /v1/auth/verify-and-change-email:
    post:
      description: Sends a verification email to the user's new email address,
        updating the user's email address once the new email address has been
        verified
      operationId: VerifyAndChangeEmailController_updateEmail
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendVerifyAndChangeEmailRequest'
      responses:
        '202':
          description: ''
      summary: changes a user's email address
      tags:
        - Auth
  /health:
    get:
      operationId: HealthController_check
      parameters: []
      responses:
        '200':
          description: The Health Check is successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  info:
                    type: object
                    example: &a3
                      database: &a4
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example: {}
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example: *a3
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
        '503':
          description: The Health Check is not successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  info:
                    type: object
                    example: *a3
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example:
                      redis: &a5
                        status: down
                        message: Could not connect
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example:
                      database: *a4
                      redis: *a5
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
      summary: get driver account API health
      tags:
        - Healthcheck
  /:
    get:
      operationId: HelloController_getData
      parameters: []
      responses:
        '200':
          description: Welcome message returned
      summary: welcome message endpoint
      tags:
        - Example
  /v1/oauth/authorize:
    get:
      description: >-
        Triggers an OAuth 2.0 flow for users to authorize access to their
        charger

        The only supported flow is Authorization Code Flow with PKCE.
      operationId: OAuthController_authorize
      parameters:
        - name: scope
          required: false
          in: query
          description: |-
            The scope(s) requested for access
            Note that this is an OAuth endpoint only.
            Therefore openid scopes and /userinfo are not supported.
          schema:
            type: string
        - name: response_type
          required: true
          in: query
          description: Response type (must be "code")
          schema:
            example: code
            type: string
        - name: client_id
          required: true
          in: query
          description: Client ID for the application requesting access
          schema:
            type: string
        - name: state
          required: false
          in: query
          description:
            Optional string to be repeated in the response to prevent CSRF
            attacks
          schema:
            type: string
        - name: redirect_uri
          required: true
          in: query
          description: >-
            URI to redirect the user to after authorization

            This must be provided and match one of the URIs registered against
            the client ID.

            On successful authorization this will be called with `code` (and
            `state` if provided) as query parameters.

            The `code` can then be exchanged for an access token using the
            `/token` endpoint.

            On error, the user will be redirected to this URI with an `error`
            (as defined in RFC 6749 *******)

            As well as an `error_description` to be displayed to the user (and
            `state` if provided).
          schema:
            type: string
        - name: code_challenge_method
          required: true
          in: query
          description: PKCE code challenge method (only S256 is supported)
          schema:
            type: string
        - name: code_challenge
          required: true
          in: query
          description: Code challenge generated by the client using the code verifier
          schema:
            type: string
      responses:
        '302':
          description: See Location header for URI to redirect the user to for authorization
        '400':
          description: Invalid request (see error message for details)
      summary: OAuth 2.0 authorization endpoint
      tags: &a6
        - oauth
  /v1/oauth/client_info/{client_id}:
    get:
      description:
        Get information on an OAuth 2.0 client, including name, image and
        website URL
      operationId: OAuthController_getOAuthClient
      parameters:
        - name: client_id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Client information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthClientResponseDto'
        '401':
          description: Only an authenticated user can call this endpoint
        '404':
          description: Client not found
      summary: Get information on an OAuth 2.0 client
      tags: *a6
  /v1/oauth/consent:
    post:
      description: >-
        The internal stage of the OAuth 2.0 flow where the user, after
        signing-in,

        consents to the application accessing their account. (as limited by the
        scopes requested).
      operationId: OAuthController_consent
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OAuthConsentRequestDto'
      responses:
        '302':
          description: >-
            See Location header for URI to redirect the user to for
            authorization

            On successful authorization this will be called with `code` (and
            `state` if provided) as query parameters.

            The `code` can then be exchanged for an access token using the
            `/token` endpoint.

            On error, the user will be redirected to this URI with an `error`
            (as defined in RFC 6749 *******)

            As well as an `error_description` to be displayed to the user (and
            `state` if provided).
        '400':
          description: Invalid request (see error message for details)
        '401':
          description: Only an authenticated user can call this endpoint
      summary: Consent to an OAuth 2.0 application accessing the authorized account
      tags: *a6
  /v1/oauth/token:
    post:
      description: >-
        Exchanges an authorization/refresh code or client credentials for an
        access token.

        The supported grant types are `authorization_code`,`client_credentials`
        and `refresh_token`.

        Use of `authorization_code` requires PKCE extensions, and is used
        against charger specific endpoints.

        `refresh_token` can then be used to refresh an access token provided via
        the authorization code flow.

        `client_credentials` can only be used for non-charger specific endpoints
        (e.g. webhooks),

        and the resulting access token does not support refresh.

        All access tokens expire after 1 hour.

        Refresh tokens (where provided) expire as defined by the OAuth
        application.
      operationId: OAuthController_token
      parameters: []
      requestBody:
        required: true
        description: OAuth 2.0 token exchange request
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/OAuthTokenAuthorizationCodeRequestDto'
                - $ref: '#/components/schemas/OAuthTokenClientCredentialsRequestDto'
                - $ref: '#/components/schemas/OAuthTokenRefreshTokenRequestDto'
            examples:
              authorization_code:
                value:
                  grant_type: authorization_code
                  client_id: client_id
                  code: code
                  code_verifier: code_verifier
                  redirect_uri: redirect_uri
              client_credentials:
                value:
                  grant_type: client_credentials
                  client_id: client_id
                  client_secret: client_secret
              refresh_token:
                value:
                  grant_type: refresh_token
                  client_id: client_id
                  refresh_token: refresh_token
      responses:
        '200':
          description: OAuth 2.0 token exchange response
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/OAuthTokenAuthorizationCodeResponseDto'
                  - $ref: '#/components/schemas/OAuthTokenClientCredentialsResponseDto'
                  - $ref: '#/components/schemas/OAuthTokenRefreshTokenResponseDto'
              examples:
                authorization_code:
                  value:
                    access_token: access_token
                    token_type: Bearer
                    expires_in: 3600
                    refresh_token: refresh_token
                    scope: data:read
                client_credentials:
                  value:
                    access_token: access_token
                    token_type: Bearer
                    expires_in: 3600
                    scope: data:read
                refresh_token:
                  value:
                    access_token: access_token
                    token_type: Bearer
                    expires_in: 3600
        '400':
          description: Invalid request (see error message for details)
      summary: OAuth 2.0 token endpoint
      tags: *a6
  /v1/users:
    post:
      description: Creates a new user account
      operationId: UserController_createUser
      parameters:
        - name: reset_password_continue_url
          required: false
          in: query
          description: Reset password continue url that is optional
          schema:
            type: string
        - name: x-app-name
          required: true
          in: header
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserDto'
      responses:
        '200':
          description: Creates a new user account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserResponseDto'
      summary: create a new user
      tags: &a7
        - Users
    get:
      description: Get a user profile by a given filter. Accepts only email at the moment
      operationId: UserController_getByFilter
      parameters:
        - name: ppid
          required: false
          in: query
          description: Allows for searching by PPID
          schema:
            example: PSL-1234
            type: string
        - name: emailLike
          required: false
          in: query
          description: Allows for fuzzy matching on the email field
          schema:
            example: true
            type: string
        - name: email
          required: false
          in: query
          schema:
            example: <EMAIL>
            type: string
      responses:
        '200':
          description: Return matching users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserInfoResponseDto'
      summary: get user profile by given filters
      tags: *a7
  /v1/users/{uid}:
    get:
      description: Get a user profile for a given UID
      operationId: UserController_getUser
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Get a user profile for a given UID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtendedUserInfoResponseDto'
        '404':
          description: Thrown when user not found
      summary: get user profile by UID
      tags: *a7
    put:
      description: Update a user profile for a given UID
      operationId: UserController_updateUser
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserDetailsDto'
      responses:
        '200':
          description: Update a user profile for a given UID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInfoResponseDto'
        '404':
          description: Thrown when user not found
      summary: update user profile by UID
      tags: *a7
    delete:
      description: delete a user
      operationId: UserController_softDelete
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
        - name: force
          required: false
          in: query
          description: Allows to disable users when they have outstanding balance
          schema:
            example: true
            type: boolean
      responses:
        '200':
          description: delete a user
        '404':
          description: Thrown when user not found
      summary: delete a user
      tags: *a7
  /v1/users/enable/{uid}:
    put:
      description: Enable a user profile for a given UID
      operationId: UserController_enable
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Enable a user profile for a given UID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInfoResponseDto'
        '404':
          description: Thrown when user not found
      summary: enable user profile by UID
      tags: *a7
  /v1/users/email/update:
    put:
      description: Update a user email for an existing email and new email
      operationId: UserController_updateEmail
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserEmailDto'
      responses:
        '200':
          description: Update a user email for an existing email and new email
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserEmailDto'
        '404':
          description: Thrown when user not found
      summary: update user email
      tags: *a7
  /v1/users/{uid}/chargers:
    get:
      description: Get a user's chargers by their  UID
      operationId: UserController_getChargers
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
        - name: includeOtherLinkedUsers
          required: false
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Get a user's chargers by their UID
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserChargerDto'
        '404':
          description: Thrown when user not found
      summary: get user's chargers by their UID
      tags: *a7
  /v1/users/{uid}/chargers/{ppid}:
    delete:
      description: Unlink a charger from a user
      operationId: UserController_unLinkCharger
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
        - name: ppid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Unlink a charger from a user
        '404':
          description: Thrown when user not found
      summary: unlink a charger from a user
      tags: *a7
    post:
      operationId: UserController_linkCharger
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
        - name: ppid
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: linked a unit to a user successfully
        '403':
          description: unit/user cannot be linked
        '404':
          description: unit/user not found
        '422':
          description: Unable to link unit because of unit/user misconfiguration
        '500':
          description: unknown error
      tags: *a7
  /v1/users/{uid}/suppressedStatus:
    get:
      description: get a user's suppressed status by their UID
      operationId: UserController_getSuppressedStatus
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: get a user's suppressed status by their UID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSuppressedStatusDto'
        '404':
          description: Thrown when user not found
      summary: get a user's suppressed status by their UID
      tags: *a7
    put:
      description: remove a user from the SES suppression list
      operationId: UserController_updateSuppressedStatus
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserSuppressedStatusDto'
      responses:
        '200':
          description: remove a user's suppression status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateUserSuppressedStatusDto'
        '400':
          description: Thrown when a status other than null is supplied
        '404':
          description: Thrown when user not found
        '500':
          description: Thrown when an unknown error occurs
      summary: remove a user from the SES suppression list
      tags: *a7
  /v1/users/login/{uid}/alert:
    post:
      description: Generates an email of when and from which ip the user logged in
      operationId: UserController_trackLogin
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TrackLoginRequest'
      responses:
        '200':
          description: ''
      summary: Generates an email of when and from which ip the user logged in
      tags: *a7
  /version:
    get:
      operationId: VersionController_getVersion
      parameters: []
      responses:
        '200':
          description: application version
      summary: get application version
      tags:
        - Version
  /v1/users/{userId}/notifications/tokens:
    get:
      description: Gets notifications tokens stored against a given user
      operationId: NotificationsController_getTokens
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Successfully retrieved notifications tokens
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FcmTokenDto'
        '500':
          description: An unknown error occurred
      summary: ''
      tags: &a8
        - Notifications
    post:
      description: Stores a notification token against a given user
      operationId: NotificationsController_saveToken
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FcmTokenDto'
      responses:
        '201':
          description: The notification token was successfully stored
        '400':
          description: The request body payload is invalid
        '500':
          description: An unknown error occurred
      summary: store notification token
      tags: *a8
  /v1/users/{userId}/notifications/tokens/{token}:
    delete:
      description: Deletes a notification token associated with a user
      operationId: NotificationsController_deleteToken
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
        - name: token
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Token was successfully deleted
      summary: delete notification token
      tags: *a8
info:
  title: Driver Account API
  description: Driver account API service
  version: '1'
  contact: {}
tags:
  - name: Auth
    description: API for out of band administration of authenticated users
  - name: Users
    description: API for administration of user accounts
servers: []
components:
  securitySchemes:
    bearer:
      scheme: bearer
      bearerFormat: JWT
      type: http
  schemas:
    SendEmailVerificationRequest:
      type: object
      properties:
        email:
          type: string
          description: Email
        email_verification_continue_url:
          type: string
          description: Continue url
      required:
        - email
    SendPasswordResetRequest:
      type: object
      properties:
        email:
          type: string
          description: Email
        reset_password_continue_url:
          type: string
          description: Continue url
      required:
        - email
    UserPasswordResetAlertDto:
      type: object
      properties: {}
    RandomisePasswordRequest:
      type: object
      properties:
        email:
          type: string
          description: Email
      required:
        - email
    SendRecoverFactorRequest:
      type: object
      properties:
        email:
          type: string
          description: Email
        recover_factor_continue_url:
          type: string
          description: Redirect URL after factor recovery
      required:
        - email
    RemoveFactorRequest:
      type: object
      properties:
        email:
          type: string
          description: The email of the user removing the factor
          example: <EMAIL>
        countryCode:
          type: object
          description: The region of the phone
          example: en
        phoneNumber:
          type: string
          description: The phone number of the user removing the factor
          example: 1234567890
      required:
        - email
        - countryCode
        - phoneNumber
    Factor:
      type: object
      properties:
        id:
          type: string
          description: ID of the factor
          example: 1f7c4f3c-2bb8-434d-a4d5-fc3415d2d4e2
        phoneNumber:
          type: string
          description: Phone number associated with this factor
          example: '+447755566677'
        enrollmentTime:
          type: string
          description: Enrollment time of this factor
          example: Fri, 22 Sep 2017 01:49:58 GMT
      required:
        - id
        - phoneNumber
        - enrollmentTime
    SendSignInWithEmailRequest:
      type: object
      properties:
        email:
          type: string
          description: Email
        continue_url:
          type: string
          description: Continue url
        origin_service_url:
          type: string
          description: Origin service url, external service to authenticate the user
      required:
        - email
        - continue_url
        - origin_service_url
    SendVerifyAndChangeEmailRequest:
      type: object
      properties:
        email:
          type: string
          description: Email
        newEmail:
          type: string
          description: New Email
      required:
        - email
        - newEmail
    OAuthClientResponseDto:
      type: object
      properties:
        name:
          type: string
          description: The user friendly name of the client
          example: client_name
        websiteUrl:
          type: string
          description:
            The website URL for the client's information about this oauth
            connection
          format: uri
          example: https://example.com
        imageUrl:
          type: string
          description: The URL for the client's logo image
          format: uri
          example: https://example.com
      required:
        - name
        - websiteUrl
        - imageUrl
    OAuthConsentRequestDto:
      type: object
      properties:
        clientId:
          type: string
          description: The client ID for the application requesting access
          example: client_id
        redirectUri:
          type: string
          description: The redirect URI used in the authorize endpoint flow
          format: uri
          example: redirect_uri
        responseType:
          type: string
          description: Response type (must be "code")
          example: code
        codeChallengeMethod:
          type: string
          description: PKCE code challenge method (only S256 is supported)
          example: S256
        codeChallenge:
          type: string
          description: Code challenge generated by the client using the code verifier
          example: code_challenge
        scope:
          type: string
          description: The scopes requested for access
          example: data:read
        state:
          type: string
          description:
            Optional string to be repeated in the response to prevent CSRF
            attacks
        userId:
          type: string
          description: The user id of the user who is granting access to the client
          example: user-id
      required:
        - clientId
        - redirectUri
        - responseType
        - codeChallengeMethod
        - codeChallenge
        - userId
    OAuthTokenAuthorizationCodeResponseDto:
      type: object
      properties:
        access_token:
          type: string
          description:
            The access token to be used as `${token_type} ${access_token}` in
            the Authorization header
          example: ********************************
        token_type:
          type: string
          description: The type of token returned
          example: Bearer
        expires_in:
          type: number
          description: The number of seconds until the token expires (currently one hour)
          example: 3600
        refresh_token:
          type: string
          description: >-
            The refresh token to be used to get a new access token when the
            current one expires.

            Valid for 30 days after authorization.
          example: f5b5c6e9f2e4d3a8b7a9f3e5c6e9f2d
        scope:
          type: string
          description: The scopes allowed for access
          example: data:read
      required:
        - access_token
        - token_type
        - expires_in
        - refresh_token
    OAuthTokenClientCredentialsResponseDto:
      type: object
      properties:
        access_token:
          type: string
          description:
            The access token to be used as `${token_type} ${access_token}` in
            the Authorization header
          example: ********************************
        token_type:
          type: string
          description: The type of token returned
          example: Bearer
        expires_in:
          type: number
          description: The number of seconds until the token expires (currently one hour)
          example: 3600
        scope:
          type: string
          description: The scopes allowed for access
          example: data:read
      required:
        - access_token
        - token_type
        - expires_in
    OAuthTokenRefreshTokenResponseDto:
      type: object
      properties:
        access_token:
          type: string
          description:
            The access token to be used as `${token_type} ${access_token}` in
            the Authorization header
          example: ********************************
        token_type:
          type: string
          description: The type of token returned
          example: Bearer
        expires_in:
          type: number
          description: The number of seconds until the token expires (currently one hour)
          example: 3600
      required:
        - access_token
        - token_type
        - expires_in
    OAuthTokenAuthorizationCodeRequestDto:
      type: object
      properties:
        grant_type:
          type: string
          description: The grant type for this token flow (must be "authorization_code")
          example: authorization_code
        code:
          type: string
          description: The authorization code returned from the authorize endpoint flow
          example: code
        redirect_uri:
          type: string
          description: The redirect URI used in the authorize endpoint flow
          format: uri
          example: redirect_uri
        client_id:
          type: string
          description: The client ID for the application requesting access
          example: client_id
        code_verifier:
          type: string
          description:
            The random key that aws used to generate the code challenge used in
            the authorize endpoint flow
          example: code_verifier
      required:
        - grant_type
        - code
        - redirect_uri
        - client_id
        - code_verifier
    OAuthTokenClientCredentialsRequestDto:
      type: object
      properties:
        grant_type:
          type: string
          description: The grant type for this token flow (must be "client_credentials")
          example: client_credentials
        client_id:
          type: string
          description: The client ID for the application requesting access
          example: client_id
        client_secret:
          type: string
          description: The client secret for the application requesting access
          example: client_secret
      required:
        - grant_type
        - client_id
        - client_secret
    OAuthTokenRefreshTokenRequestDto:
      type: object
      properties:
        grant_type:
          type: string
          description: The grant type for this token flow (must be "refresh_token")
          example: refresh_token
        client_id:
          type: string
          description: The client ID for the application requesting access
          example: client_id
        refresh_token:
          type: string
          description: The refresh token to be used to generate a new access token
          example: code
      required:
        - grant_type
        - client_id
        - refresh_token
    MarketingDto:
      type: object
      properties:
        isConsentGiven:
          type: number
          example: 0
        type:
          type: string
          example: express
        copy:
          type: string
          example:
            I would like to receive updates about Pod Point products and services
            by email (and know that I can update my preferences from within any
            of the emails if I change my mind)
        origin:
          type: string
          example: opencharge-mobile-app
      required:
        - isConsentGiven
        - type
        - copy
        - origin
    ConsentDto:
      type: object
      properties:
        marketing:
          description: Marketing
          allOf:
            - $ref: '#/components/schemas/MarketingDto'
      required:
        - marketing
    PreferencesDto:
      type: object
      properties:
        unitOfDistance:
          type: string
          example: mi
      required:
        - unitOfDistance
    CreateUserDto:
      type: object
      properties:
        first_name:
          type: string
          description: First name
          example: John
        last_name:
          type: string
          description: Last name
          example: Doe
        locale:
          type: string
          description: Locale
          example: en
        email:
          type: string
          description: Email
          example: <EMAIL>
        password:
          type: string
          description: password
          example: password1234
        consent:
          $ref: '#/components/schemas/ConsentDto'
        preferences:
          $ref: '#/components/schemas/PreferencesDto'
      required:
        - first_name
        - last_name
        - locale
        - email
    CreateUserResponseDto:
      type: object
      properties:
        auth_id:
          type: string
          description: GIP userId
          example: fa95717e-cf98-4d2e-86cf-c12f580921f3
        created_at:
          type: string
          example: 2023-10-02 12:27:19
        id:
          type: string
          description: pk of user
          example: '910004932'
        password_reset_link:
          type: string
          description: password reset link
      required:
        - auth_id
        - created_at
        - id
    UserInfoResponseDto:
      type: object
      properties:
        first_name:
          type: string
          description: First name
          example: John
        last_name:
          type: string
          description: Last name
          example: Doe
        locale:
          type: string
          description: Locale
          example: en
        email:
          type: string
          description: Email
          example: <EMAIL>
        uid:
          type: string
          description: auth id
          example: a3450f38-217a-4d0c-8eec-b7d63c6fd2e0
        preferences:
          $ref: '#/components/schemas/PreferencesDto'
      required:
        - first_name
        - last_name
        - locale
        - email
        - uid
    BalanceDto:
      type: object
      properties:
        currency:
          type: string
          example: GBP
        amount:
          type: number
          example: 500
      required:
        - currency
        - amount
    RewardsChargerDto:
      type: object
      properties:
        id:
          type: string
          description: The PPID of the charger
          example: PSL-12345
        miles:
          type: number
          description: The amount of rewardable miles for this charger
          example: 10.5
      required:
        - id
        - miles
    RewardsDto:
      type: object
      properties:
        totalMiles:
          type: number
          description: The total amount of reward miles from individual chargers
          example: 10.5
        balance:
          type: number
          description: The balance in the lowest unit of currency (pence for GBP)
          example: 5000
        currency:
          type: string
          description: The currency represented by the balance
          example: GBP
        payoutThreshold:
          type: number
          description: The minimum amount of rewards miles required for payout
          example: 150
        chargers:
          description: The chargers for which the user is eligible for the reward
          type: array
          items:
            $ref: '#/components/schemas/RewardsChargerDto'
      required:
        - totalMiles
        - balance
        - currency
        - payoutThreshold
        - chargers
    ExtendedUserInfoResponseDto:
      type: object
      properties:
        first_name:
          type: string
          description: First name
          example: John
        last_name:
          type: string
          description: Last name
          example: Doe
        locale:
          type: string
          description: Locale
          example: en
        email:
          type: string
          description: Email
          example: <EMAIL>
        uid:
          type: string
          description: auth id
          example: a3450f38-217a-4d0c-8eec-b7d63c6fd2e0
        preferences:
          $ref: '#/components/schemas/PreferencesDto'
        balance:
          $ref: '#/components/schemas/BalanceDto'
        rewards:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/RewardsDto'
        paymentProcessorId:
          type: string
          nullable: true
          example: cus_hfJJeHsFdaV2H3s
        emailVerified:
          type: boolean
        lastSignInTimestamp:
          format: date-time
          oneOf:
            - type: 'null'
            - type: string
              example: 1995-07-16T13:46:06.0Z
        accountCreationTimestamp:
          type: string
          example: 1995-07-16T13:46:06.0Z
        status:
          type: string
          enum:
            - active
            - disabled
        deletedAtTimestamp:
          type: string
          example: 1995-07-16T13:46:06.0Z
          nullable: true
      required:
        - first_name
        - last_name
        - locale
        - email
        - uid
        - balance
        - rewards
        - paymentProcessorId
        - emailVerified
        - lastSignInTimestamp
        - accountCreationTimestamp
        - status
        - deletedAtTimestamp
    UserDetailsDto:
      type: object
      properties:
        first_name:
          type: string
          description: First name
          example: John
        last_name:
          type: string
          description: Last name
          example: Doe
        locale:
          type: string
          description: Locale
          example: en
      required:
        - first_name
        - last_name
        - locale
    UserEmailDto:
      type: object
      properties:
        newEmail:
          type: string
          description: New email
          example: <EMAIL>
        oldEmail:
          type: string
          description: Old email
          example: <EMAIL>
      required:
        - newEmail
        - oldEmail
    UserChargerDto:
      type: object
      properties:
        ppid:
          type: string
          example: PSL-456789
        unitId:
          type: number
          example: 1
        timezone:
          type: string
          example: Etc/UTC
        linkedAt:
          type: string
          example: 2024-01-01T00:00:00.000Z
        otherLinkedUsers:
          type: array
          items:
            type: string
      required:
        - ppid
        - unitId
        - timezone
        - linkedAt
    UserSuppressedStatusDto:
      type: object
      properties:
        status:
          type: string
          enum:
            - BOUNCE
            - COMPLAINT
            - UNKNOWN
          nullable: true
      required:
        - status
    UpdateUserSuppressedStatusDto:
      type: object
      properties:
        status:
          type: number
          example: null
          nullable: true
          enum:
            - null
      required:
        - status
    TrackLoginRequest:
      type: object
      properties:
        ipAddress:
          type: string
          example: 2a00:23ee:1320:109f:98e0:dcc6:b917:f126
        userAgent:
          type: string
          example:
            FirebaseAuth.iOS/8.15.0 com.podpoint.podpoint/3.26.0 iPhone/17.5.1
            hw/iPhone14_5,gzip(gfe),gzip(gfe)
        timestamp:
          type: string
          example: 2020-03-14 00:00:00
        authId:
          type: string
          example: 5357be96-1495-4951-8046-c2d59ba76c44
      required:
        - ipAddress
        - userAgent
        - timestamp
    FcmTokenDto:
      type: object
      properties:
        token:
          type: string
          description: The notification token you want to save
          example: d7g1h2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0l
        timestamp:
          format: date-time
          type: string
          description: The timestamp at which the notification token was created
          example: 2024-10-24T13:53:00.000Z
      required:
        - token
        - timestamp
