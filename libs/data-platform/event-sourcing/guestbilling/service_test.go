package guestbilling_test

import (
	"bytes"
	"context"
	"experience/libs/data-platform/event-sourcing/guestbilling"
	mockguestbilling "experience/libs/data-platform/event-sourcing/guestbilling/mock"
	billingapiclient "experience/libs/shared/go/external/billing-api-client/src"
	asynclogger "experience/libs/shared/go/logger/logger"
	"fmt"
	"io"
	"log"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"
)

func TestBill(t *testing.T) {
	test := []struct {
		name                          string
		chargeID                      uint32
		billingEventID                *int64
		getBillingDataFromChargeError error
		capturePaymentRequest         billingapiclient.CapturePaymentRequest
		expectedError                 error
		capturePaymentIntentResponse  *billingapiclient.CapturePaymentIntentResponse
		billingAPIHTTPResponse        *http.Response
		billingAPICalls               int
	}{
		{
			name:           "Test Guest Billing",
			chargeID:       1,
			billingEventID: ptr.To(int64(12345)),
			capturePaymentRequest: billingapiclient.CapturePaymentRequest{
				BillingEventId:  12345,
				AmountToCapture: 12345,
			},
			capturePaymentIntentResponse: billingapiclient.NewCapturePaymentIntentResponse("succeeded"),
			billingAPIHTTPResponse: &http.Response{
				StatusCode: 200,
				Body:       io.NopCloser(bytes.NewReader([]byte(`{"paymentIntentStatus": "succeeded"}`))),
			},
			billingAPICalls: 1,
		},
		{
			name:                          "Test Guest Billing with error on getting billing data",
			chargeID:                      2,
			billingEventID:                nil,
			getBillingDataFromChargeError: fmt.Errorf("failed to get billing event for charge ID 2"),
			expectedError:                 fmt.Errorf("error when getting billing data from charge: error getting billing data"),
			billingAPICalls:               0,
		},
	}
	for _, tt := range test {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockBillingRepository := mockguestbilling.NewMockRepository(ctrl)
			mockBillingRepository.EXPECT().GetBillingEventIDAssociatedToClaimedCharge(gomock.Any(), int64(tt.chargeID)).Return(tt.billingEventID, tt.getBillingDataFromChargeError).MinTimes(1)

			mockBillingAPIClient := mockguestbilling.NewMockBillingAPIClient(ctrl)

			mockBillingAPIRequest := billingapiclient.ApiPaymentControllerCaptureGuestPaymentRequest{
				ApiService: mockBillingAPIClient,
			}
			mockBillingAPIRequest = mockBillingAPIRequest.CapturePaymentRequest(billingapiclient.CapturePaymentRequest{
				BillingEventId:  float32(ptr.Deref(tt.billingEventID, 0)),
				AmountToCapture: tt.capturePaymentRequest.AmountToCapture,
			})

			mockBillingAPIClient.
				EXPECT().
				PaymentControllerCaptureGuestPayment(gomock.Any()).
				Return(mockBillingAPIRequest).
				Times(tt.billingAPICalls)
			mockBillingAPIClient.
				EXPECT().
				PaymentControllerCaptureGuestPaymentExecute(gomock.Eq(mockBillingAPIRequest)).
				Return(
					tt.capturePaymentIntentResponse,
					tt.billingAPIHTTPResponse,
					tt.expectedError,
				).
				Times(tt.billingAPICalls)

			service := guestbilling.NewService(asynclogger.NewTraceIDLogger(log.Default()), mockBillingAPIClient, mockBillingRepository)

			err := service.Bill(context.Background(), tt.chargeID, int32(tt.capturePaymentRequest.AmountToCapture))
			if tt.expectedError != nil {
				require.Error(t, tt.expectedError, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
