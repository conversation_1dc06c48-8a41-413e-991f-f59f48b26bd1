// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/event-sourcing/guestbilling/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/event-sourcing/guestbilling/mock/ports.go -source=libs/data-platform/event-sourcing/guestbilling/ports.go
//
// Package mock_guestbilling is a generated GoMock package.
package mock_guestbilling

import (
	context "context"
	billingapiclient "experience/libs/shared/go/external/billing-api-client/src"
	http "net/http"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetBillingEventIDAssociatedToClaimedCharge mocks base method.
func (m *MockRepository) GetBillingEventIDAssociatedToClaimedCharge(ctx context.Context, chargeID int64) (*int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBillingEventIDAssociatedToClaimedCharge", ctx, chargeID)
	ret0, _ := ret[0].(*int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBillingEventIDAssociatedToClaimedCharge indicates an expected call of GetBillingEventIDAssociatedToClaimedCharge.
func (mr *MockRepositoryMockRecorder) GetBillingEventIDAssociatedToClaimedCharge(ctx, chargeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBillingEventIDAssociatedToClaimedCharge", reflect.TypeOf((*MockRepository)(nil).GetBillingEventIDAssociatedToClaimedCharge), ctx, chargeID)
}

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// Bill mocks base method.
func (m *MockService) Bill(ctx context.Context, chargeID uint32, settlementAmount int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Bill", ctx, chargeID, settlementAmount)
	ret0, _ := ret[0].(error)
	return ret0
}

// Bill indicates an expected call of Bill.
func (mr *MockServiceMockRecorder) Bill(ctx, chargeID, settlementAmount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Bill", reflect.TypeOf((*MockService)(nil).Bill), ctx, chargeID, settlementAmount)
}

// MockBillingAPIClient is a mock of BillingAPIClient interface.
type MockBillingAPIClient struct {
	ctrl     *gomock.Controller
	recorder *MockBillingAPIClientMockRecorder
}

// MockBillingAPIClientMockRecorder is the mock recorder for MockBillingAPIClient.
type MockBillingAPIClientMockRecorder struct {
	mock *MockBillingAPIClient
}

// NewMockBillingAPIClient creates a new mock instance.
func NewMockBillingAPIClient(ctrl *gomock.Controller) *MockBillingAPIClient {
	mock := &MockBillingAPIClient{ctrl: ctrl}
	mock.recorder = &MockBillingAPIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBillingAPIClient) EXPECT() *MockBillingAPIClientMockRecorder {
	return m.recorder
}

// PaymentControllerCaptureGuestPayment mocks base method.
func (m *MockBillingAPIClient) PaymentControllerCaptureGuestPayment(ctx context.Context) billingapiclient.ApiPaymentControllerCaptureGuestPaymentRequest {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentControllerCaptureGuestPayment", ctx)
	ret0, _ := ret[0].(billingapiclient.ApiPaymentControllerCaptureGuestPaymentRequest)
	return ret0
}

// PaymentControllerCaptureGuestPayment indicates an expected call of PaymentControllerCaptureGuestPayment.
func (mr *MockBillingAPIClientMockRecorder) PaymentControllerCaptureGuestPayment(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentControllerCaptureGuestPayment", reflect.TypeOf((*MockBillingAPIClient)(nil).PaymentControllerCaptureGuestPayment), ctx)
}

// PaymentControllerCaptureGuestPaymentExecute mocks base method.
func (m *MockBillingAPIClient) PaymentControllerCaptureGuestPaymentExecute(r billingapiclient.ApiPaymentControllerCaptureGuestPaymentRequest) (*billingapiclient.CapturePaymentIntentResponse, *http.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentControllerCaptureGuestPaymentExecute", r)
	ret0, _ := ret[0].(*billingapiclient.CapturePaymentIntentResponse)
	ret1, _ := ret[1].(*http.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PaymentControllerCaptureGuestPaymentExecute indicates an expected call of PaymentControllerCaptureGuestPaymentExecute.
func (mr *MockBillingAPIClientMockRecorder) PaymentControllerCaptureGuestPaymentExecute(r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentControllerCaptureGuestPaymentExecute", reflect.TypeOf((*MockBillingAPIClient)(nil).PaymentControllerCaptureGuestPaymentExecute), r)
}

// PaymentControllerCreateGuestPaymentIntent mocks base method.
func (m *MockBillingAPIClient) PaymentControllerCreateGuestPaymentIntent(ctx context.Context) billingapiclient.ApiPaymentControllerCreateGuestPaymentIntentRequest {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentControllerCreateGuestPaymentIntent", ctx)
	ret0, _ := ret[0].(billingapiclient.ApiPaymentControllerCreateGuestPaymentIntentRequest)
	return ret0
}

// PaymentControllerCreateGuestPaymentIntent indicates an expected call of PaymentControllerCreateGuestPaymentIntent.
func (mr *MockBillingAPIClientMockRecorder) PaymentControllerCreateGuestPaymentIntent(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentControllerCreateGuestPaymentIntent", reflect.TypeOf((*MockBillingAPIClient)(nil).PaymentControllerCreateGuestPaymentIntent), ctx)
}

// PaymentControllerCreateGuestPaymentIntentExecute mocks base method.
func (m *MockBillingAPIClient) PaymentControllerCreateGuestPaymentIntentExecute(r billingapiclient.ApiPaymentControllerCreateGuestPaymentIntentRequest) (*billingapiclient.CreateGuestPaymentResponse, *http.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentControllerCreateGuestPaymentIntentExecute", r)
	ret0, _ := ret[0].(*billingapiclient.CreateGuestPaymentResponse)
	ret1, _ := ret[1].(*http.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PaymentControllerCreateGuestPaymentIntentExecute indicates an expected call of PaymentControllerCreateGuestPaymentIntentExecute.
func (mr *MockBillingAPIClientMockRecorder) PaymentControllerCreateGuestPaymentIntentExecute(r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentControllerCreateGuestPaymentIntentExecute", reflect.TypeOf((*MockBillingAPIClient)(nil).PaymentControllerCreateGuestPaymentIntentExecute), r)
}

// PaymentControllerCreateRegisteredUserPaymentIntent mocks base method.
func (m *MockBillingAPIClient) PaymentControllerCreateRegisteredUserPaymentIntent(ctx context.Context, uid string) billingapiclient.ApiPaymentControllerCreateRegisteredUserPaymentIntentRequest {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentControllerCreateRegisteredUserPaymentIntent", ctx, uid)
	ret0, _ := ret[0].(billingapiclient.ApiPaymentControllerCreateRegisteredUserPaymentIntentRequest)
	return ret0
}

// PaymentControllerCreateRegisteredUserPaymentIntent indicates an expected call of PaymentControllerCreateRegisteredUserPaymentIntent.
func (mr *MockBillingAPIClientMockRecorder) PaymentControllerCreateRegisteredUserPaymentIntent(ctx, uid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentControllerCreateRegisteredUserPaymentIntent", reflect.TypeOf((*MockBillingAPIClient)(nil).PaymentControllerCreateRegisteredUserPaymentIntent), ctx, uid)
}

// PaymentControllerCreateRegisteredUserPaymentIntentExecute mocks base method.
func (m *MockBillingAPIClient) PaymentControllerCreateRegisteredUserPaymentIntentExecute(r billingapiclient.ApiPaymentControllerCreateRegisteredUserPaymentIntentRequest) (*billingapiclient.CreateRegisteredUserPaymentResponse, *http.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentControllerCreateRegisteredUserPaymentIntentExecute", r)
	ret0, _ := ret[0].(*billingapiclient.CreateRegisteredUserPaymentResponse)
	ret1, _ := ret[1].(*http.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PaymentControllerCreateRegisteredUserPaymentIntentExecute indicates an expected call of PaymentControllerCreateRegisteredUserPaymentIntentExecute.
func (mr *MockBillingAPIClientMockRecorder) PaymentControllerCreateRegisteredUserPaymentIntentExecute(r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentControllerCreateRegisteredUserPaymentIntentExecute", reflect.TypeOf((*MockBillingAPIClient)(nil).PaymentControllerCreateRegisteredUserPaymentIntentExecute), r)
}

// PaymentControllerSetupIntent mocks base method.
func (m *MockBillingAPIClient) PaymentControllerSetupIntent(ctx context.Context, uid string) billingapiclient.ApiPaymentControllerSetupIntentRequest {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentControllerSetupIntent", ctx, uid)
	ret0, _ := ret[0].(billingapiclient.ApiPaymentControllerSetupIntentRequest)
	return ret0
}

// PaymentControllerSetupIntent indicates an expected call of PaymentControllerSetupIntent.
func (mr *MockBillingAPIClientMockRecorder) PaymentControllerSetupIntent(ctx, uid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentControllerSetupIntent", reflect.TypeOf((*MockBillingAPIClient)(nil).PaymentControllerSetupIntent), ctx, uid)
}

// PaymentControllerSetupIntentExecute mocks base method.
func (m *MockBillingAPIClient) PaymentControllerSetupIntentExecute(r billingapiclient.ApiPaymentControllerSetupIntentRequest) (*billingapiclient.CreateIntentResponse, *http.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentControllerSetupIntentExecute", r)
	ret0, _ := ret[0].(*billingapiclient.CreateIntentResponse)
	ret1, _ := ret[1].(*http.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// PaymentControllerSetupIntentExecute indicates an expected call of PaymentControllerSetupIntentExecute.
func (mr *MockBillingAPIClientMockRecorder) PaymentControllerSetupIntentExecute(r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentControllerSetupIntentExecute", reflect.TypeOf((*MockBillingAPIClient)(nil).PaymentControllerSetupIntentExecute), r)
}
