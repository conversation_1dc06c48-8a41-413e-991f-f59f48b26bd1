// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: billing.sql

package sqlc

import (
	"context"
	"database/sql"
)

const getBillingEventIDAssociatedToClaimedCharge = `-- name: GetBillingEventIDAssociatedToClaimedCharge :one
SELECT cl.billing_event_id
FROM podpoint.charges c
INNER JOIN podpoint.claimed_charges cl
  ON c.claimed_charge_id = cl.id
WHERE c.id = $1
`

func (q *Queries) GetBillingEventIDAssociatedToClaimedCharge(ctx context.Context, id int64) (sql.NullInt64, error) {
	row := q.db.QueryRowContext(ctx, getBillingEventIDAssociatedToClaimedCharge, id)
	var billing_event_id sql.NullInt64
	err := row.Scan(&billing_event_id)
	return billing_event_id, err
}
