package guestbilling_test

import (
	"database/sql"
	"experience/libs/data-platform/event-sourcing/guestbilling"
	guestbillinginfra "experience/libs/data-platform/event-sourcing/guestbilling/infra"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/setup"
	"experience/libs/data-platform/test/sqlc"
	"experience/libs/shared/go/db/postgres/test"
	asynclogger "experience/libs/shared/go/logger/logger"
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type RepositoryTestSuite struct {
	suite.Suite
	database  setup.PostgresTestDB
	queries   *sqlc.Queries
	underTest guestbilling.Repository
	db        *sql.DB
}

func TestRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}
	suite.Run(t, new(RepositoryTestSuite))
}

func (s *RepositoryTestSuite) SetupSuite() {
	s.database = setup.NewPostgresTestDB(s.T(), "file://../../../golang-migrate/migration", test.WithUser("xdp_async_processor"))
	s.queries = sqlc.New(s.database.DBHandleSqlx)
	s.underTest = guestbillinginfra.NewRepository(asynclogger.NewTraceIDLogger(log.Default()), s.database.ReadWriteDB.ReadDB)
	s.db = s.database.DBHandle
}

func (s *RepositoryTestSuite) TearDownSuite() {
	require.NoError(s.T(), s.database.TestDB.Container.Terminate(s.T().Context()))
}

func (s *RepositoryTestSuite) TearDownTest() {
	fixtures.CleanupAllTestData(s.T().Context(), s.T(), s.queries, nil, nil, nil)
}

func (s *RepositoryTestSuite) TestGetBillingEventIDAssociatedToCharge() {
	ctx := s.T().Context()

	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	claimedChargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	charge, err := s.queries.CreateCharge(ctx, sqlc.CreateChargeParams{
		ID:               int64(chargeID),
		LocationID:       sql.NullInt64{},
		UnitID:           0,
		Door:             0,
		EnergyCost:       sql.NullInt32{},
		BillingEventID:   sql.NullInt64{Int64: 12345, Valid: true},
		StartsAt:         sql.NullTime{},
		EndsAt:           sql.NullTime{},
		KwhUsed:          "0",
		Duration:         sql.NullInt64{},
		IsClosed:         0,
		GroupID:          sql.NullInt64{},
		ClaimedChargeID:  sql.NullInt64{Int64: int64(claimedChargeID), Valid: true},
		BillingAccountID: sql.NullInt64{},
		DeletedAt:        sql.NullTime{},
	})
	require.NoError(s.T(), err)

	_, err = s.queries.CreateClaimedCharge(ctx, sqlc.CreateClaimedChargeParams{
		ID:               int64(claimedChargeID),
		PodLocationID:    0,
		PodDoorID:        0,
		UserID:           sql.NullInt64{},
		BillingEventID:   sql.NullInt64{Int64: 12345, Valid: true},
		CreatedAt:        time.Time{},
		UpdatedAt:        time.Time{},
		DeletedAt:        sql.NullTime{},
		ClaimedBy:        sql.NullInt64{},
		AuthoriserID:     0,
		BillingAccountID: sql.NullInt64{},
	})
	require.NoError(s.T(), err)

	billingEventID, err := s.underTest.GetBillingEventIDAssociatedToClaimedCharge(ctx, charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), *billingEventID, charge.BillingEventID.Int64)
}

func (s *RepositoryTestSuite) TestGetBillingEventIDAssociatedToCharge_NoBillingEventID() {
	ctx := s.T().Context()

	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	claimedChargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	charge, err := s.queries.CreateCharge(ctx, sqlc.CreateChargeParams{
		ID:               int64(chargeID),
		LocationID:       sql.NullInt64{},
		UnitID:           0,
		Door:             0,
		EnergyCost:       sql.NullInt32{},
		BillingEventID:   sql.NullInt64{Valid: false},
		StartsAt:         sql.NullTime{},
		EndsAt:           sql.NullTime{},
		KwhUsed:          "0",
		Duration:         sql.NullInt64{},
		IsClosed:         0,
		GroupID:          sql.NullInt64{},
		ClaimedChargeID:  sql.NullInt64{Int64: int64(claimedChargeID), Valid: true},
		BillingAccountID: sql.NullInt64{},
		DeletedAt:        sql.NullTime{},
	})
	require.NoError(s.T(), err)
	_, err = s.queries.CreateClaimedCharge(ctx, sqlc.CreateClaimedChargeParams{
		ID:               int64(claimedChargeID),
		PodLocationID:    0,
		PodDoorID:        0,
		UserID:           sql.NullInt64{},
		BillingEventID:   sql.NullInt64{Valid: false},
		CreatedAt:        time.Time{},
		UpdatedAt:        time.Time{},
		DeletedAt:        sql.NullTime{},
		ClaimedBy:        sql.NullInt64{},
		AuthoriserID:     0,
		BillingAccountID: sql.NullInt64{},
	})
	require.NoError(s.T(), err)

	billingEventID, err := s.underTest.GetBillingEventIDAssociatedToClaimedCharge(ctx, charge.ID)
	require.Error(s.T(), err)
	require.ErrorContains(s.T(), err, "no billing event found for charge ID")
	require.Nil(s.T(), billingEventID)
}
