package guestbilling

import (
	"context"
	eventsubscriptions "experience/libs/data-platform/event-sourcing/event-subscriptions"
	"experience/libs/data-platform/event-sourcing/guestbilling"
	"experience/libs/data-platform/event-sourcing/guestbilling/infra/sqlc"
	"fmt"
)

type repository struct {
	queriesRead *sqlc.Queries
	logger      eventsubscriptions.ContextLogger
}

func NewRepository(logger eventsubscriptions.ContextLogger, db sqlc.DBTX) guestbilling.Repository {
	return &repository{
		logger:      logger,
		queriesRead: sqlc.New(db),
	}
}

func (r *repository) GetBillingEventIDAssociatedToClaimedCharge(ctx context.Context, chargeID int64) (*int64, error) {
	billingEventID, err := r.queriesRead.GetBillingEventIDAssociatedToClaimedCharge(ctx, chargeID)
	if err != nil {
		r.logger.Printf(ctx, fmt.Sprintf("failed to get billing event for charge ID %d: %v", chargeID, err))
		return nil, err
	}

	if !billingEventID.Valid {
		r.logger.Printf(ctx, fmt.Sprintf("no billing event found for charge ID %d", chargeID))
		return nil, fmt.Errorf("no billing event found for charge ID %d", chargeID)
	}

	return &billingEventID.Int64, nil
}
