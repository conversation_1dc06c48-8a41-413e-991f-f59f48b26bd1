package guestbilling

import (
	"context"
	"net/http"

	billingapiclient "experience/libs/shared/go/external/billing-api-client/src"
)

type Repository interface {
	GetBillingEventIDAssociatedToClaimedCharge(ctx context.Context, chargeID int64) (*int64, error)
}

type Service interface {
	Bill(ctx context.Context, chargeID uint32, settlementAmount int32) error
}

type BillingAPIClient interface {
	PaymentControllerCaptureGuestPayment(ctx context.Context) billingapiclient.ApiPaymentControllerCaptureGuestPaymentRequest
	PaymentControllerCaptureGuestPaymentExecute(r billingapiclient.ApiPaymentControllerCaptureGuestPaymentRequest) (*billingapiclient.CapturePaymentIntentResponse, *http.Response, error)
	PaymentControllerCreateGuestPaymentIntent(ctx context.Context) billingapiclient.ApiPaymentControllerCreateGuestPaymentIntentRequest
	PaymentControllerCreateGuestPaymentIntentExecute(r billingapiclient.ApiPaymentControllerCreateGuestPaymentIntentRequest) (*billingapiclient.CreateGuestPaymentResponse, *http.Response, error)
	PaymentControllerCreateRegisteredUserPaymentIntent(ctx context.Context, uid string) billingapiclient.ApiPaymentControllerCreateRegisteredUserPaymentIntentRequest
	PaymentControllerCreateRegisteredUserPaymentIntentExecute(r billingapiclient.ApiPaymentControllerCreateRegisteredUserPaymentIntentRequest) (*billingapiclient.CreateRegisteredUserPaymentResponse, *http.Response, error)
	PaymentControllerSetupIntent(ctx context.Context, uid string) billingapiclient.ApiPaymentControllerSetupIntentRequest
	PaymentControllerSetupIntentExecute(r billingapiclient.ApiPaymentControllerSetupIntentRequest) (*billingapiclient.CreateIntentResponse, *http.Response, error)
}
