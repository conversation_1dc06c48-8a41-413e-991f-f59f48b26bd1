package guestbilling

import (
	"context"
	eventsubscriptions "experience/libs/data-platform/event-sourcing/event-subscriptions"
	billingapiclient "experience/libs/shared/go/external/billing-api-client/src"
	"experience/libs/shared/go/numbers"
	"fmt"
	"io"
)

type guestBillingSvc struct {
	logger           eventsubscriptions.ContextLogger
	billingAPIClient BillingAPIClient
	repository       Repository
}

func NewService(logger eventsubscriptions.ContextLogger, billingAPIClient BillingAPIClient, repository Repository) Service {
	return &guestBillingSvc{
		logger:           logger,
		billingAPIClient: billingAPIClient,
		repository:       repository,
	}
}

func (s *guestBillingSvc) Bill(ctx context.Context, chargeID uint32, settlementAmount int32) error {
	convertedChargeID, err := numbers.Convert[uint32, int64](chargeID)
	if err != nil {
		return err
	}

	billingEventID, err := s.repository.GetBillingEventIDAssociatedToClaimedCharge(ctx, convertedChargeID)
	if err != nil {
		return fmt.Errorf("error when getting billing event from charge: %w", err)
	}

	requestBody := billingapiclient.CapturePaymentRequest{
		BillingEventId:  float32(*billingEventID),
		AmountToCapture: float32(settlementAmount),
	}

	request := s.billingAPIClient.PaymentControllerCaptureGuestPayment(ctx)
	_, response, err := request.CapturePaymentRequest(requestBody).Execute() //nolint:bodyclose // body is closed in the defer statement
	if err != nil {
		return err
	}
	if response != nil {
		defer func(body io.Closer) {
			closeErr := body.Close()
			if closeErr != nil {
				s.logger.Printf(ctx, fmt.Sprintf("error closing response body: %v", closeErr))
			}
		}(response.Body)
	}
	return nil
}
