// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/cost-calculation/energy-cost/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/cost-calculation/energy-cost/mock/ports.go -source=libs/data-platform/cost-calculation/energy-cost/ports.go
//
// Package mock_energycost is a generated GoMock package.
package mock_energycost

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockCalculator is a mock of Calculator interface.
type MockCalculator struct {
	ctrl     *gomock.Controller
	recorder *MockCalculatorMockRecorder
}

// MockCalculatorMockRecorder is the mock recorder for MockCalculator.
type MockCalculatorMockRecorder struct {
	mock *MockCalculator
}

// NewMockCalculator creates a new mock instance.
func NewMockCalculator(ctrl *gomock.Controller) *MockCalculator {
	mock := &MockCalculator{ctrl: ctrl}
	mock.recorder = &MockCalculatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCalculator) EXPECT() *MockCalculatorMockRecorder {
	return m.recorder
}

// EnergyCost mocks base method.
func (m *MockCalculator) EnergyCost(arg0 *energycost.Address, arg1 *costcalculation.Charge) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnergyCost", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// EnergyCost indicates an expected call of EnergyCost.
func (mr *MockCalculatorMockRecorder) EnergyCost(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnergyCost", reflect.TypeOf((*MockCalculator)(nil).EnergyCost), arg0, arg1)
}
