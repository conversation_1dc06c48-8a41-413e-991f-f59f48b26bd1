package energycost_test

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	"experience/libs/data-platform/cost-calculation/energy-cost/random"
	"experience/libs/shared/go/numbers"
	"experience/libs/shared/go/service"
	"fmt"
	"math"
	"testing"
	"time"

	"github.com/zlasd/tzloc"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func Test_energyCostCalculator_GetEnergyCost(t *testing.T) {
	zeroCost := uint32(0)

	europeLondonLocation, _ := time.LoadLocation(tzloc.Europe_London)

	energy := gofakeit.Float64Range(0.1, 500.0)
	rate := int64(gofakeit.IntRange(10000, math.MaxUint32))
	rateUint32, err := numbers.Convert[int64, uint32](rate)
	require.NoError(t, err)
	fixedTime := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)

	fixedCharge := &costcalculation.Charge{
		Energy: 5.0,
		DateRange: service.DateRange{
			From: ptr.To(fixedTime),
			To:   ptr.To(fixedTime.Add(1 * time.Hour))},
	}

	daylightSavingTimeCharge := &costcalculation.Charge{
		Energy: 5.0,
		DateRange: service.DateRange{
			From: ptr.To(time.Date(2023, 7, 1, 1, 0, 0, 0, time.UTC)),
			To:   ptr.To(time.Date(2023, 7, 1, 2, 0, 0, 0, time.UTC)),
		},
	}

	chargeIntersectsPreviousDay := &costcalculation.Charge{
		Energy: 23.7,
		DateRange: service.DateRange{
			From: ptr.To(fixedTime.Add(-time.Hour * 2)),
			To:   ptr.To(fixedTime.Add(time.Hour * 22)),
		},
	}

	chargeIntersectsNextDay := &costcalculation.Charge{
		Energy: 3.0,
		DateRange: service.DateRange{
			From: ptr.To(fixedTime.Add(time.Hour * 22)),
			To:   ptr.To(fixedTime.Add(time.Hour * 33)),
		},
	}

	chargeCrossingTwoTariffs := &costcalculation.Charge{
		Energy: 5.0,
		DateRange: service.DateRange{
			From: ptr.To(fixedTime.Add(time.Minute * 30)),
			To:   ptr.To(fixedTime.Add(time.Minute * 90)),
		},
	}

	longRunningCharge := &costcalculation.Charge{
		Energy: 100.0,
		DateRange: service.DateRange{
			From: ptr.To(fixedTime),
			To:   ptr.To(fixedTime.Add(time.Hour * 24)),
		},
	}

	fixedChargeWithIntervals := &costcalculation.Charge{
		Energy: 5.0,
		DateRange: service.DateRange{
			From: ptr.To(time.Date(2023, 11, 1, 0, 0, 0, 0, time.UTC)),
			To:   ptr.To(time.Date(2023, 11, 1, 4, 0, 0, 0, time.UTC)),
		},
		Intervals: []*costcalculation.ChargeInterval{
			{
				Energy:    3.5,
				EndedAt:   ptr.To(time.Date(2023, 11, 1, 2, 0, 0, 0, time.UTC)),
				StartedAt: ptr.To(time.Date(2023, 11, 1, 1, 0, 0, 0, time.UTC)),
			},
			{
				Energy:    5.0,
				EndedAt:   ptr.To(time.Date(2023, 11, 1, 4, 0, 0, 0, time.UTC)),
				StartedAt: ptr.To(time.Date(2023, 11, 1, 3, 0, 0, 0, time.UTC)),
			},
		},
	}

	fixedChargeWithIntervalEndinginConnectedEV := &costcalculation.Charge{
		Energy: 6.0,
		DateRange: service.DateRange{
			From: ptr.To(time.Date(2023, 11, 1, 0, 0, 0, 0, time.UTC)),
			To:   ptr.To(time.Date(2023, 11, 1, 4, 0, 0, 0, time.UTC)),
		},
		Intervals: []*costcalculation.ChargeInterval{
			{
				Energy:    3.5,
				EndedAt:   ptr.To(time.Date(2023, 11, 1, 2, 0, 0, 0, time.UTC)),
				StartedAt: ptr.To(time.Date(2023, 11, 1, 1, 0, 0, 0, time.UTC)),
			},
			{
				Energy:    5.0,
				EndedAt:   ptr.To(time.Date(2023, 11, 1, 4, 0, 0, 0, time.UTC)),
				StartedAt: ptr.To(time.Date(2023, 11, 1, 3, 0, 0, 0, time.UTC)),
			},
		},
	}

	validCharge := &costcalculation.Charge{
		Energy: energy,
		DateRange: service.DateRange{
			From: ptr.To(fixedTime),
			To:   ptr.To(fixedTime.Add(time.Second * time.Duration(gofakeit.IntRange(10, 100000))))},
	}

	ukAddressWithNoTariffTiers := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{},
		},
	}

	nonUKAddressWithNoTariffTiers := &energycost.Address{
		Country: random.CountryNotIncluding("GB"),
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{},
		},
	}

	ukAddressWithSingleTariffTier := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate: rate,
				},
			},
		},
	}

	ukAddressWithMultpleTariffTiers := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      320000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
				},
				{
					Rate:      640000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*7).Hour(), fixedTime.Minute(), fixedTime.Second()))),
				},
			},
			Timezone: time.UTC,
		},
	}

	ukAddressWithMultpleTariffTiersWhereSameDaysHasMultipleRates := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      320000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					Days:      []time.Weekday{time.Sunday},
				},
				{
					Rate:      640000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*7).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					Days:      []time.Weekday{time.Sunday},
				},
			},
			Timezone: time.UTC,
		},
	}

	ukAddressWithFixedTariffTiers2 := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      71600, // 7.16p
					BeginTime: ptr.To(tariffTierTime(t, "00:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "02:30:00")),
				},
				{
					Rate:      235600, // 23.56p
					BeginTime: ptr.To(tariffTierTime(t, "02:30:00")),
					EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
				},
			},
			Timezone: time.UTC,
		},
	}

	ukAddressWithFixedTariffTiers3 := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      71600, // 7.16p
					BeginTime: ptr.To(tariffTierTime(t, "00:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "01:15:00")),
				},
				{
					Rate:      235600, // 23.56p
					BeginTime: ptr.To(tariffTierTime(t, "01:15:00")),
					EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
				},
			},
			Timezone: time.UTC,
		},
	}

	ukAddressWithMultipleTariffTiers := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      320000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
				},
				{
					Rate:      640000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*5).Hour(), fixedTime.Minute(), fixedTime.Second()))),
				},
				{
					Rate:      1280000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*5).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*10).Hour(), fixedTime.Minute(), fixedTime.Second()))),
				},
				{
					Rate:      2560000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*10).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*13).Hour(), fixedTime.Minute(), fixedTime.Second()))),
				},
				{
					Rate:      5120000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*13).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*23).Hour(), fixedTime.Minute(), fixedTime.Second()))),
				},
				{
					Rate:      10240000,
					BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*23).Hour(), fixedTime.Minute(), fixedTime.Second()))),
					EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*24).Hour(), fixedTime.Minute(), fixedTime.Second()))),
				},
			},
			Timezone: time.UTC,
		},
	}

	tests := []struct {
		name    string
		want    uint32
		charge  *costcalculation.Charge
		address *energycost.Address
	}{
		{
			name:   "cost is 0 when address is nil",
			want:   zeroCost,
			charge: validCharge,
		},
		{
			name:    "cost is 0 when no tariff for address",
			want:    zeroCost,
			address: &energycost.Address{},
		},
		{
			name:    "cost is 0 when no tariff tier for tariff and non-UK address",
			want:    zeroCost,
			address: nonUKAddressWithNoTariffTiers,
		},
		{
			name:    "want tariff of 14p/kWh applied when no tariff tier for tariff and UK address",
			address: ukAddressWithNoTariffTiers,
			charge:  validCharge,
			want:    energyCostUsingDefaultTariff(energy),
		},
		{
			name: "cost is 0 when charge ends at is nil",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Time{}),
					To:   ptr.To(time.Time{}),
				},
			},
			address: ukAddressWithNoTariffTiers,
			want:    zeroCost,
		},
		{
			name: "cost is 0 when charge duration is less than 10 seconds",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(fixedTime),
					To:   ptr.To(fixedTime.Add(time.Second * time.Duration(gofakeit.IntRange(0, 9)))),
				},
			},
			address: ukAddressWithNoTariffTiers,
			want:    zeroCost,
		},
		{
			name:    "tariff rate applied to a charge with a single tariff tier",
			charge:  validCharge,
			address: ukAddressWithSingleTariffTier,
			want:    energyCostUsingTariffTierRate(energy, rateUint32),
		},
		{
			name:    "calculate charge cost against charge with multiple tariff tiers with fixed energy and rate",
			charge:  fixedCharge,
			address: ukAddressWithMultpleTariffTiers,
			want:    uint32(160),
		},
		{
			name:    "calculate charge cost against charge occurring over two tariff tiers",
			charge:  chargeCrossingTwoTariffs,
			address: ukAddressWithMultpleTariffTiers,
			want:    uint32(240),
		},
		{
			name:    "calculate charge cost against charge occurring over multiple tariff tiers",
			charge:  longRunningCharge,
			address: ukAddressWithMultipleTariffTiers,
			want:    uint32(32667),
		},
		{
			name:    "calculate charge cost against charge with multiple tariff tiers and multiple charging intervals, no split over tariff boundaries",
			charge:  fixedChargeWithIntervals,
			address: ukAddressWithFixedTariffTiers2,
			want:    uint32(60),
		},
		{
			name:    "calculate charge cost against charge with multiple tariff tiers and multiple charging intervals, no split over tariff boundaries, use charge energy when final interval is not charging",
			charge:  fixedChargeWithIntervalEndinginConnectedEV,
			address: ukAddressWithFixedTariffTiers2,
			want:    uint32(84),
		},
		{
			name:    "calculate charge cost against charge with multiple tariff tiers and multiple charging intervals, one interval is split across tariffs",
			charge:  fixedChargeWithIntervals,
			address: ukAddressWithFixedTariffTiers3,
			want:    uint32(103),
		},
		{
			name:   "calculate cost for fixed charge with no intervals when tariff first intersects on previous day",
			charge: chargeIntersectsPreviousDay,
			address: &energycost.Address{
				Tariff: energycost.Tariff{
					Tiers: []energycost.TariffTier{
						{
							Rate:      101600, // 10.16p
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(-1*time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
						},
						{
							Rate:      235600, // 23.56p
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(23*time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
						},
					},
					Timezone: time.UTC,
				},
				Country: "GB",
			},
			want: uint32(532),
		},
		{
			name:   "calculate cost for fixed charge with no intervals when tariff intersects on next day",
			charge: chargeIntersectsNextDay,
			address: &energycost.Address{
				Tariff: energycost.Tariff{
					Tiers: []energycost.TariffTier{
						{
							Rate:      100000, // 10p
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*8).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*9).Hour(), fixedTime.Minute(), fixedTime.Second()))),
						},
						{
							Rate:      22300, // 2.23p
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*1).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*3).Hour(), fixedTime.Minute(), fixedTime.Second()))),
						},
					},
					Timezone: time.UTC,
				},
				Country: "GB",
			},
			want: uint32(4),
		},
		{
			name:   "tariff tier in UTC are applied against charge date times",
			charge: daylightSavingTimeCharge,
			address: &energycost.Address{
				Tariff: energycost.Tariff{
					Tiers: []energycost.TariffTier{
						{
							Rate:      100000, // 10p
							BeginTime: ptr.To(tariffTierTime(t, "01:00:00")),
							EndTime:   ptr.To(tariffTierTime(t, "02:00:00")),
						},
						{
							Rate:      22300, // 2.23p
							BeginTime: ptr.To(tariffTierTime(t, "02:00:00")),
							EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
						},
					},
					Timezone: europeLondonLocation,
				},
				Country: "GB",
			},
			want: uint32(11),
		},
		{
			name:    "calculate charge cost against charge occurring over two tariffs where on the same day",
			charge:  chargeCrossingTwoTariffs,
			address: ukAddressWithMultpleTariffTiersWhereSameDaysHasMultipleRates,
			want:    uint32(240),
		},
		{
			name:   "calculate charge cost against charge occurring over two tariffs",
			charge: chargeIntersectsNextDay,
			address: &energycost.Address{
				Tariff: energycost.Tariff{
					Tiers: []energycost.TariffTier{
						{
							Rate:      100000, // 10p
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*8).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*9).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							Days:      []time.Weekday{time.Monday},
						},
						{
							Rate:      22300, // 2.23p
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*1).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour*3).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							Days:      []time.Weekday{time.Monday},
						},
					},
					Timezone: time.UTC,
				},
				Country: "GB",
			},
			want: uint32(4),
		},
		{
			name:   "calculate charge cost against charge occurring over two tariffs",
			charge: chargeIntersectsPreviousDay,
			address: &energycost.Address{
				Tariff: energycost.Tariff{
					Tiers: []energycost.TariffTier{
						{
							Rate:      101600, // 10.16p
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(-1*time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							Days:      []time.Weekday{time.Sunday},
						},
						{
							Rate:      235600, // 23.56p
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(23*time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							Days:      []time.Weekday{time.Sunday},
						},
						{
							Rate:      1001600, // Do not apply this rate - it's not a Monday
							BeginTime: ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(-1*time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							EndTime:   ptr.To(tariffTierTime(t, fmt.Sprintf("%02d:%02d:%02d", fixedTime.Add(time.Hour).Hour(), fixedTime.Minute(), fixedTime.Second()))),
							Days:      []time.Weekday{time.Monday},
						},
					},
					Timezone: time.UTC,
				},
				Country: "GB",
			},
			want: uint32(532),
		},
		{
			name:   "calculate charge cost against charge with multiple tariff tiers and multiple charging intervals, no split over tariff boundaries, where day for tariff tier is specified",
			charge: fixedChargeWithIntervals, // this charge is on a Wednesday
			address: &energycost.Address{
				Country: "GB",
				Tariff: energycost.Tariff{
					Tiers: []energycost.TariffTier{
						{
							Rate:      71600, // 7.16p
							BeginTime: ptr.To(tariffTierTime(t, "00:00:00")),
							EndTime:   ptr.To(tariffTierTime(t, "02:30:00")),
							Days:      []time.Weekday{time.Wednesday},
						},
						{
							Rate:      235600, // 23.56p
							BeginTime: ptr.To(tariffTierTime(t, "02:30:00")),
							EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
							Days:      []time.Weekday{time.Wednesday},
						},
						{
							Rate:      5000000, // do not apply this rate - it's not a Wednesday
							BeginTime: ptr.To(tariffTierTime(t, "02:30:00")),
							EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
							Days:      []time.Weekday{time.Thursday},
						},
					},
					Timezone: time.UTC,
				},
			},
			want: uint32(60),
		},
		{
			name:   "calculate charge cost against charge with multiple tariff tiers and multiple charging intervals, one interval is split across tariffs, where day is specified",
			charge: fixedChargeWithIntervals,
			address: &energycost.Address{
				Country: "GB",
				Tariff: energycost.Tariff{
					Tiers: []energycost.TariffTier{
						{
							Rate:      71600, // 7.16p
							BeginTime: ptr.To(tariffTierTime(t, "00:00:00")),
							EndTime:   ptr.To(tariffTierTime(t, "01:15:00")),
							Days:      []time.Weekday{time.Wednesday},
						},
						{
							Rate:      235600, // 23.56p
							BeginTime: ptr.To(tariffTierTime(t, "01:15:00")),
							EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
							Days:      []time.Weekday{time.Wednesday},
						},
						{
							Rate:      535600, // do not apply this rate - it's not a Wednesday
							BeginTime: ptr.To(tariffTierTime(t, "01:15:00")),
							EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
							Days:      []time.Weekday{time.Thursday},
						},
					},
					Timezone: time.UTC,
				},
			},
			want: uint32(103),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			underTest := energycost.NewEnergyCostCalculator()
			got := underTest.EnergyCost(tt.address, tt.charge)
			require.Equal(t, int(tt.want), int(got))
		})
	}
}

func Test_energyCostCalculatorDays_TariffTierSingleTariff(t *testing.T) {
	chargeMonday := &costcalculation.Charge{
		Energy: 10.0,
		DateRange: service.DateRange{
			// this is a Monday
			From: ptr.To(time.Date(2024, 6, 10, 8, 0, 0, 0, time.UTC)),
			To:   ptr.To(time.Date(2024, 6, 10, 9, 0, 0, 0, time.UTC)),
		},
	}

	tariffAllDays := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      100000, // 10p
					BeginTime: ptr.To(tariffTierTime(t, "08:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "10:00:00")),
					Days:      []time.Weekday{time.Monday, time.Tuesday, time.Wednesday, time.Thursday, time.Friday, time.Saturday, time.Sunday},
				},
			},
		},
	}
	underTest := energycost.NewEnergyCostCalculator()
	gotMonday := underTest.EnergyCost(tariffAllDays, chargeMonday)

	require.Equal(t, uint32(100), gotMonday)
}

func Test_energyCostCalculator_TariffTierDays_OneDayHasDifferentRate(t *testing.T) {
	chargeMonday := &costcalculation.Charge{
		Energy: 10.0,
		DateRange: service.DateRange{
			// this is a Monday
			From: ptr.To(time.Date(2024, 6, 10, 8, 0, 0, 0, time.UTC)),
			To:   ptr.To(time.Date(2024, 6, 10, 9, 0, 0, 0, time.UTC)),
		},
	}

	tariffAllDays := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      150000, // 15p
					BeginTime: ptr.To(tariffTierTime(t, "08:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "10:00:00")),
					Days:      []time.Weekday{time.Monday},
				},
				{
					Rate:      100000, // 10p
					BeginTime: ptr.To(tariffTierTime(t, "08:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "10:00:00")),
					Days:      []time.Weekday{time.Tuesday, time.Wednesday, time.Thursday, time.Friday, time.Saturday, time.Sunday},
				},
			},
			Timezone: time.UTC,
		},
	}
	underTest := energycost.NewEnergyCostCalculator()
	gotMonday := underTest.EnergyCost(tariffAllDays, chargeMonday)

	require.Equal(t, uint32(150), gotMonday)
}

func Test_energyCostCalculator_TariffTierDays_OneDayHasDifferentRateAcrossChargeIntervals(t *testing.T) {
	charge := &costcalculation.Charge{
		Energy: 10.0,
		DateRange: service.DateRange{
			// this is a Monday
			From: ptr.To(time.Date(2024, 6, 10, 21, 0, 0, 0, time.UTC)),
			// this is a Tuesday
			To: ptr.To(time.Date(2024, 6, 11, 3, 0, 0, 0, time.UTC)),
		},
		// some of the charge is in Monday and some in Tuesday
		Intervals: []*costcalculation.ChargeInterval{
			{
				Energy:    5.0,
				StartedAt: ptr.To(time.Date(2024, 6, 10, 22, 0, 0, 0, time.UTC)),
				EndedAt:   ptr.To(time.Date(2024, 6, 10, 23, 0, 0, 0, time.UTC)),
			},
			{
				Energy:    10.0,
				StartedAt: ptr.To(time.Date(2024, 6, 11, 2, 0, 0, 0, time.UTC)),
				EndedAt:   ptr.To(time.Date(2024, 6, 11, 3, 0, 0, 0, time.UTC)),
			},
		},
	}

	tariff := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      150000, // 15p
					BeginTime: ptr.To(tariffTierTime(t, "00:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "23:59:59")),
					Days:      []time.Weekday{time.Monday},
				},
				{
					Rate:      100000, // 10p
					BeginTime: ptr.To(tariffTierTime(t, "00:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "23:59:59")),
					Days:      []time.Weekday{time.Tuesday, time.Wednesday, time.Thursday, time.Friday, time.Saturday, time.Sunday},
				},
			},
			Timezone: time.UTC,
		},
	}
	underTest := energycost.NewEnergyCostCalculator()
	cost := underTest.EnergyCost(tariff, charge)

	require.Equal(t, uint32(125), cost)
}

func Test_energyCostCalculator_TariffTierDays_ChargeIntervalsAreApplicableOnTwoTariffsOnTheSameDay(t *testing.T) {
	charge := &costcalculation.Charge{
		Energy: 10.0,
		DateRange: service.DateRange{
			// this is a Monday
			From: ptr.To(time.Date(2024, 6, 10, 21, 0, 0, 0, time.UTC)),
			// this is a Tuesday
			To: ptr.To(time.Date(2024, 6, 11, 3, 0, 0, 0, time.UTC)),
		},
		// some of the charge is in Monday and some in Tuesday
		Intervals: []*costcalculation.ChargeInterval{
			{
				Energy:    5.0,
				StartedAt: ptr.To(time.Date(2024, 6, 10, 22, 0, 0, 0, time.UTC)),
				EndedAt:   ptr.To(time.Date(2024, 6, 10, 23, 0, 0, 0, time.UTC)),
			},
			{
				Energy:    10.0,
				StartedAt: ptr.To(time.Date(2024, 6, 11, 2, 0, 0, 0, time.UTC)),
				EndedAt:   ptr.To(time.Date(2024, 6, 11, 3, 0, 0, 0, time.UTC)),
			},
		},
	}

	tariff := &energycost.Address{
		Country: "GB",
		Tariff: energycost.Tariff{
			Tiers: []energycost.TariffTier{
				{
					Rate:      150000, // 15p
					BeginTime: ptr.To(tariffTierTime(t, "00:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
					Days:      []time.Weekday{time.Monday, time.Tuesday},
				},
				{
					Rate:      100000, // 10p
					BeginTime: ptr.To(tariffTierTime(t, "00:00:00")),
					EndTime:   ptr.To(tariffTierTime(t, "00:00:00")),
					Days:      []time.Weekday{time.Wednesday, time.Thursday, time.Friday, time.Saturday, time.Sunday},
				},
			},
			Timezone: time.UTC,
		},
	}
	underTest := energycost.NewEnergyCostCalculator()
	cost := underTest.EnergyCost(tariff, charge)

	require.Equal(t, uint32(150), cost)
}

func energyCostUsingDefaultTariff(energy float64) uint32 {
	return uint32(math.Round(14.0 * energy))
}

func energyCostUsingTariffTierRate(energy float64, rate uint32) uint32 {
	return uint32(math.Round(float64(rate) * energy / 10000))
}

func tariffTierTime(t *testing.T, tStr string) time.Time {
	t.Helper()

	t1, err := time.Parse(time.TimeOnly, tStr)
	require.NoError(t, err)
	return t1
}
