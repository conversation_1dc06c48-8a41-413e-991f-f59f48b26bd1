package energycost

import (
	"experience/libs/shared/go/service"
	"time"
)

func NewTariff(tiers []TariffTier, timezone *time.Location) *Tariff {
	return &Tariff{
		Tiers:    tiers,
		Timezone: timezone,
	}
}

// Address a location with an associated tariff and country code
type Address struct {
	Tariff  Tariff
	Country string
}

// Tariff a collection of tariff tiers applicable to a charger and its location
type Tariff struct {
	Tiers    []TariffTier
	Timezone *time.Location
}

// TariffTier a single tier of a tariff, defining the rate and applicable time range
type TariffTier struct {
	// Rate upon which the tier is calculated
	Rate int64
	// DateRange defines the period during which this tier is applicable
	BeginTime *time.Time
	// EndTime defines the end of the period for this tier; if nil, it is considered ongoing
	EndTime *time.Time
	// The days of the week when this tier is applicable
	Days []time.Weekday
}

// TariffTierRange a subset of TariffTier that includes a rate and a date range - used to represent where charges cross many tariff tiers
// Ranges is generated from TariffTiers and are summed up to calculate the total cost of a charge
type TariffTierRange struct {
	Rate      int64
	DateRange service.DateRange
	Days      []time.Weekday
}
