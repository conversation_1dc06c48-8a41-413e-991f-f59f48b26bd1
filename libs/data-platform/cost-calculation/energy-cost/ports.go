package energycost

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"fmt"
)

type ServerError struct {
	context  string
	ppid     string
	evseID   string
	chargeID string
	error    error
}

func (e *ServerError) Error() string {
	return fmt.Sprintf("%s by ppid: %s, evseID: %s, chargeID: %s, error: %s", e.context, e.ppid, e.evseID, e.chargeID, e.error)
}

type ClientError struct {
	context  string
	ppid     string
	evseID   string
	chargeID string
	error    error
}

func (e *ClientError) Error() string {
	return fmt.Sprintf("%s by ppid: %s, evseID: %s, chargeID: %s, error: %s", e.context, e.ppid, e.evseID, e.chargeID, e.error)
}

type Calculator interface {
	EnergyCost(*Address, *costcalculation.Charge) uint32
}
