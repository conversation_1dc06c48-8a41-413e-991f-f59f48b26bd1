package energycost

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	energymetricsclient "experience/libs/shared/go/external/energy-metrics-client/src"
	"fmt"
	"time"
)

type EnergyMetricsAPIToChargeIntervalAdapter func(*energymetricsclient.Metric) (*costcalculation.ChargeInterval, error)

func ToChargeInterval(payload *energymetricsclient.Metric) (*costcalculation.ChargeInterval, error) {
	if payload == nil {
		return nil, fmt.Errorf("no payload")
	}

	if payload.Timestamp.IsZero() {
		return nil, fmt.Errorf("time is not set")
	}

	energy := payload.Value.Get()
	if energy == nil {
		return nil, fmt.Errorf("energy is not set")
	}

	startedAt := payload.Timestamp
	endedAt := startedAt.Add(30 * time.Minute) // half-hour window

	return &costcalculation.ChargeInterval{
		Energy:    float64(*energy),
		StartedAt: &startedAt,
		EndedAt:   &endedAt,
	}, nil
}
