package random

import (
	"github.com/biter777/countries"
	"github.com/brianvoe/gofakeit/v7"
)

func CountryNotIncluding(country string) string {
	countryCodes := countries.All()
	var index int
	for i, code := range countryCodes {
		if country == code.Alpha2() {
			index = i
			break
		}
	}

	countryCodes = append(countryCodes[:index], countryCodes[index+1:]...)
	random := gofakeit.IntRange(0, len(countryCodes)-1)
	return countryCodes[random].Alpha2()
}
