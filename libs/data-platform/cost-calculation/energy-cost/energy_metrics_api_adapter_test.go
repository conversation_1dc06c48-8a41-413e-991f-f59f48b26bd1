package energycost_test

import (
	"errors"
	costcalculation "experience/libs/data-platform/cost-calculation"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	energymetricsclient "experience/libs/shared/go/external/energy-metrics-client/src"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestToChargeInterval(t *testing.T) {
	fixedStartedAt := time.Date(2025, time.July, 25, 13, 33, 45, 0, time.UTC)
	endedAt := fixedStartedAt.Add(30 * time.Minute)
	energy := float32(10.5)

	tests := []struct {
		name  string
		input *energymetricsclient.Metric
		want  *costcalculation.ChargeInterval
		err   error
	}{
		{
			name:  "error when no payload",
			input: nil,
			err:   errors.New("no payload"),
		},
		{
			name:  "error when time is not set",
			input: &energymetricsclient.Metric{},
			err:   errors.New("time is not set"),
		},
		{
			name: "error when time is zero",
			input: &energymetricsclient.Metric{
				Timestamp: time.Time{},
				Value:     *energymetricsclient.NewNullableFloat32(&energy),
			},
			err: errors.New("time is not set"),
		},
		{
			name: "error when energy is not set",
			input: &energymetricsclient.Metric{
				Timestamp: fixedStartedAt,
				Value:     *energymetricsclient.NewNullableFloat32(nil),
			},
			err: errors.New("energy is not set"),
		},
		{
			name: "successfully return charge interval",
			input: &energymetricsclient.Metric{
				Timestamp: fixedStartedAt,
				Value:     *energymetricsclient.NewNullableFloat32(&energy),
			},
			want: &costcalculation.ChargeInterval{
				Energy:    10.5,
				StartedAt: &fixedStartedAt,
				EndedAt:   &endedAt,
			},
			err: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := energycost.ToChargeInterval(tt.input)
			require.Equal(t, tt.err, err)
			require.Equal(t, tt.want, got)
		})
	}
}
