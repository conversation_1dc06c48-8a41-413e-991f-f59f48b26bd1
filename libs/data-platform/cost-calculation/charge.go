package costcalculation

import (
	"experience/libs/shared/go/service"
	"sort"
	"time"
)

type Charge struct {
	Energy    float64
	DateRange service.DateRange
	Intervals []*ChargeInterval
	Location  *time.Location
}

// ChargeIntervals filter for intervals where the charger was offering energy.
func (c *Charge) ChargeIntervals() []*ChargeInterval {
	chargeIntervals := make([]*ChargeInterval, 0)
	if c.Intervals == nil {
		return chargeIntervals
	}

	for _, interval := range c.Intervals {
		if interval.StartedAt == nil {
			continue
		}
		if interval.EndedAt == nil {
			interval.EndedAt = c.DateRange.To
		}
		duration := interval.EndedAt.Sub(*interval.StartedAt).Seconds()

		// Exclude non-charging intervals or any charging intervals with no energy/duration
		if interval.Energy == 0 || duration == 0 {
			continue
		}

		chargeIntervals = append(chargeIntervals, interval)
	}

	if len(chargeIntervals) > 0 {
		sort.Slice(chargeIntervals, func(i, j int) bool {
			return chargeIntervals[i].StartedAt.Before(*chargeIntervals[j].StartedAt)
		})
	}
	return chargeIntervals
}

type ChargeInterval struct {
	Energy    float64
	EndedAt   *time.Time
	StartedAt *time.Time
}

func (cs *ChargeInterval) DateRange() (*service.DateRange, error) {
	return service.NewDateRange(cs.StartedAt, cs.EndedAt)
}
