package calculators

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/data-platform/cost-calculation/revenue"
	"experience/libs/shared/go/service"
	"testing"
	"time"

	"github.com/jinzhu/now"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"
)

func Test_energyRangeRevenueCostCalculator_Revenue(t *testing.T) {
	tests := []struct {
		name       string
		tierRanges []revenue.TierRange
		charge     *costcalculation.Charge
		want       float64
	}{
		{
			name: "returns energy * rate where charge is in one tier range (no intervals)",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 420000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 14, 0, 0, 0, time.UTC)),
				},
			},
			want: 420000 * 15,
		},
		{
			name: "returns energy * rate where charge is in multiple tier range (no intervals)",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 420000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate: 100000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 18, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 14, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 16, 0, 0, 0, time.UTC)),
				},
			},
			want: 420000*0.5*15 + 100000*0.5*15,
		},
		{
			name: "returns energy * rate where charge is in one tier range (empty intervals)",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 420000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 14, 0, 0, 0, time.UTC)),
				},
				Intervals: []*costcalculation.ChargeInterval{},
			},
			want: 420000 * 15,
		},
		{
			name: "returns energy * rate where the only charge interval is wholly in one tier range",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 430000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 12.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 16, 0, 0, 0, time.UTC)),
				},
				Intervals: []*costcalculation.ChargeInterval{
					{
						Energy:    12.0,
						EndedAt:   ptr.To(time.Date(2024, 5, 25, 14, 0, 0, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					},
				},
			},
			want: 430000 * 12,
		},
		{
			name: "returns cumulative revenue across two tier ranges where the only charge interval is split unevenly across the two tier ranges",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 430000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate: 700000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 18, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 12.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 16, 0, 0, 0, time.UTC)),
				},
				Intervals: []*costcalculation.ChargeInterval{
					{
						Energy:    12.0,
						EndedAt:   ptr.To(time.Date(2024, 5, 25, 15, 30, 0, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					},
				},
			},
			want: 5808000, // 430000*12*2/2.5 + 700000*12*0.5/2.5 => 2 hours in tier range 1, 0.5 hrs in tier range 2
		},
		{
			name: "returns the rate * energy delivered in this tier when it extends past tier start/end (no intervals)",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 430000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 12.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 16, 0, 0, 0, time.UTC)),
				},
			},
			want: 3440000, // 430000 * (2.0 / 3.0) * 12
		},
		{
			name: "returns the rate * energy delivered in this tier when it extends past tier start/end (with one interval applicable to tier range)",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 430000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 29.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 22, 0, 0, 0, time.UTC)),
				},
				Intervals: []*costcalculation.ChargeInterval{
					{
						// This interval starts within tier range, and ends after it
						Energy:    12.0,
						EndedAt:   ptr.To(time.Date(2024, 5, 25, 15, 30, 0, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					},
					{
						// This interval is outside the tier range
						Energy:    29.0,
						EndedAt:   ptr.To(time.Date(2024, 5, 25, 22, 0, 0, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 5, 25, 16, 0, 0, 0, time.UTC)),
					},
				},
			},
			want: 4128000, // 430000 * (2 / 2.5) * 12
		},
		{
			name: "revenue calc doesn't double count intervals covered by previous tier ranges",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate:      360000,
						BeginDay:  2,
						BeginTime: ptr.To(now.MustParse("1:00:00")),
						EndDay:    2,
						EndTime:   ptr.To(now.MustParse("1:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 6, 18, 1, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 6, 25, 1, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate:      360000,
						BeginDay:  2,
						BeginTime: ptr.To(now.MustParse("1:00:00")),
						EndDay:    2,
						EndTime:   ptr.To(now.MustParse("1:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 6, 25, 1, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 7, 2, 1, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 1.3,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 6, 24, 17, 5, 10, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 6, 26, 17, 9, 51, 0, time.UTC)),
				},
				Intervals: []*costcalculation.ChargeInterval{
					{
						Energy:    1.2,
						EndedAt:   ptr.To(time.Date(2024, 6, 24, 17, 55, 53, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 6, 24, 17, 5, 10, 0, time.UTC)),
					},
					{
						Energy:    1.3,
						EndedAt:   ptr.To(time.Date(2024, 6, 26, 13, 18, 28, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 6, 26, 13, 11, 8, 0, time.UTC)),
					},
				},
			},
			want: 468000, // 360000 * 1.2 + 360000 * 0.1
		},
		{
			name: "returns the rate * energy delivered in this tier when it extends past tier start/end (with multiple intervals applicable to tier range)",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 430000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 27, 12, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 100.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 27, 22, 0, 0, 0, time.UTC)),
				},
				Intervals: []*costcalculation.ChargeInterval{
					{
						// Fully within tier range
						Energy:    12.0,
						EndedAt:   ptr.To(time.Date(2024, 5, 25, 15, 30, 0, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					},
					{
						// Fully within tier range
						Energy:    29.0,
						EndedAt:   ptr.To(time.Date(2024, 5, 25, 22, 0, 0, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 5, 25, 16, 0, 0, 0, time.UTC)),
					},
					{
						// Starts within tier range, finishes outside (26hrs within, 10 hours outside)
						Energy:    100.0,
						EndedAt:   ptr.To(time.Date(2024, 5, 27, 22, 0, 0, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 5, 26, 10, 0, 0, 0, time.UTC)),
					},
				},
			},
			want: 34519444.44444445, // 430000*29 + 430000*(26.0/36.0)*71 = 34519444.44. Inconsequential floating point error here
		},
		{
			name: "calculation avoids floating point error on energy fraction when charge interval entirely within tier range",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 250000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 6, 23, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 6, 30, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 6.1,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 6, 26, 6, 39, 56, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 6, 26, 11, 44, 34, 0, time.UTC)),
				},
				Intervals: []*costcalculation.ChargeInterval{
					{
						Energy:    6.1,
						EndedAt:   ptr.To(time.Date(2024, 6, 26, 7, 34, 13, 0, time.UTC)),
						StartedAt: ptr.To(time.Date(2024, 6, 26, 6, 39, 56, 0, time.UTC)),
					},
				},
			},
			want: 1525000,
		},
		{
			name: "calculation avoids floating point error on energy fraction when charge interval entirely within tier range",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 250000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 6, 23, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 6, 30, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 6.1,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 6, 26, 6, 39, 56, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 6, 26, 7, 34, 13, 0, time.UTC)),
				},
			},
			want: 1525000,
		},
		{
			name: "when charge and tier range don't overlap, return 0",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 420000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 6, 1, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 1, 14, 0, 0, 0, time.UTC)),
				},
			},
			want: 0,
		},
		{
			name: "when range tier is nil returns zero",
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 6, 1, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 1, 14, 0, 0, 0, time.UTC)),
				},
			},
			want: 0,
		},
		{
			name: "when charge is nil returns zero",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 430000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			calc := EnergyRevenue
			got := calc(tt.tierRanges, tt.charge)
			require.Equal(t, tt.want, got)
		})
	}
}
