package calculators

import (
	"context"
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/data-platform/cost-calculation/revenue"
	mock_revenue2 "experience/libs/data-platform/event-sourcing/domain/events/charges/revenue/mock"
	"experience/libs/shared/go/service"
	shared_test "experience/libs/shared/go/test"
	"testing"
	"time"

	"go.uber.org/mock/gomock"

	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func Test_durationRangeRevenueCostCalculator_Revenue(t *testing.T) {
	tests := []struct {
		name             string
		tierRanges       []revenue.TierRange
		charge           *costcalculation.Charge
		mockRepoResponse *revenue.Tier
		want             float64
	}{
		{
			name: "returns duration * rate where charge is in one tier range and there is only one duration sub-tier with 0 delay",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate:        2000000, // £2 per hour
						StartSecond: 0,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 14, 0, 0, 0, time.UTC)),
				},
			},
			want: 2000000,
		},
		{
			name: "returns 0 when total charge duration is shorter than sub-tier delay",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate:        2000000, // £2 per hour
						StartSecond: 7200,    // 2 hours (longer than the charge length)
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{ // 1 hour
					From: ptr.To(time.Date(2024, 5, 25, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 14, 0, 0, 0, time.UTC)),
				},
			},
			want: 0,
		},
		{
			name: "continuous ranges with no start delay",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate:        1000000, // £1 per hour
						StartSecond: 0,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 18, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate:        1000000, // £1 per hour
						StartSecond: 0,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{ // 24 hrs
					From: ptr.To(time.Date(2024, 5, 24, 12, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
				},
			},
			want: 12*1000000 + 12*1000000,
		},
		{
			name: "continuous ranges with start delay",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate:        1000000, // £1 per hour
						StartSecond: 3600,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 18, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate:        1000000, // £1 per hour
						StartSecond: 3600,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{ // 24 hrs
					From: ptr.To(time.Date(2024, 5, 24, 12, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
				},
			},
			want: 11*1000000 + 12*1000000, // Because they're continuous, we don't wait for the delay on the 2nd range
		},
		{
			name: "returns value for non-continuous tier ranges with sub tiers",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate:        10000000, // £5 per hour
						StartSecond: 7200,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 18, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 19, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate:        10000000, // £5 per hour
						StartSecond: 7200,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 20, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 21, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{ // 3 days
					From: ptr.To(time.Date(2024, 5, 18, 12, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 21, 12, 0, 0, 0, time.UTC)),
				},
			},
			want: 10*10000000 + 22*10000000, // Ignores the first 2 hours of each
		},
		{
			name: "when charge and tier range don't overlap, return 0",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 2000000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 6, 1, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 1, 14, 0, 0, 0, time.UTC)),
				},
			},
			want: 0,
		},
		{
			name: "when range tier is nil returns zero",
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 6, 1, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 1, 14, 0, 0, 0, time.UTC)),
				},
			},
			want: 0,
		},
		{
			name: "when charge is nil returns zero",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 2000000,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			want: 0,
		},
		{
			name: "returns duration * rate where duration is capped by another subtier being found",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate:        2000000, // £2 per hour
						StartSecond: 0,
						UserType:    revenue.UserTypeDriver,
						BeginDay:    0,
						EndDay:      0,
						BeginTime:   ptr.To(shared_test.MustParse(time.TimeOnly, "12:00:00")),
						EndTime:     ptr.To(shared_test.MustParse(time.TimeOnly, "15:00:00")),
					},
					DateRange: service.DateRange{ // 3 hours
						From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
					},
				},
			},
			mockRepoResponse: &revenue.Tier{
				Rate:        10000000,
				TierType:    revenue.TypeDuration,
				UserType:    revenue.UserTypeDriver,
				BeginDay:    0,
				EndDay:      0,
				BeginTime:   ptr.To(shared_test.MustParse(time.TimeOnly, "12:00:00")),
				EndTime:     ptr.To(shared_test.MustParse(time.TimeOnly, "15:00:00")),
				StartSecond: 7200,
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{ // 3 hours
					From: ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 15, 0, 0, 0, time.UTC)),
				},
			},
			want: 2 * 2000000, // Capped at 2 hours
		},
		{
			name: "continuous ranges where next subtier cap only applies once",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate:        1000000, // £1 per hour
						StartSecond: 0,
						TierType:    revenue.TypeDuration,
						UserType:    revenue.UserTypeDriver,
						BeginDay:    0,
						EndDay:      0,
						BeginTime:   ptr.To(shared_test.MustParse(time.TimeOnly, "00:00:00")),
						EndTime:     ptr.To(shared_test.MustParse(time.TimeOnly, "00:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 18, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate:        1000000, // £1 per hour
						StartSecond: 0,
						TierType:    revenue.TypeDuration,
						UserType:    revenue.UserTypeDriver,
						BeginDay:    0,
						EndDay:      0,
						BeginTime:   ptr.To(shared_test.MustParse(time.TimeOnly, "00:00:00")),
						EndTime:     ptr.To(shared_test.MustParse(time.TimeOnly, "00:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			mockRepoResponse: &revenue.Tier{
				Rate:        50000000,
				TierType:    revenue.TypeDuration,
				UserType:    revenue.UserTypeDriver,
				BeginDay:    0,
				EndDay:      0,
				BeginTime:   ptr.To(shared_test.MustParse(time.TimeOnly, "00:00:00")),
				EndTime:     ptr.To(shared_test.MustParse(time.TimeOnly, "00:00:00")),
				StartSecond: 7200,
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{ // 24 hrs
					From: ptr.To(time.Date(2024, 5, 24, 12, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
				},
			},
			want: 2 * 1000000, // Continuous, so merged and then capped by next subtier
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockRepository := mock_revenue2.NewMockRepository(ctrl)

			mockRepository.EXPECT().
				GetNextDurationSubTier(gomock.AssignableToTypeOf(context.Background()), gomock.AssignableToTypeOf(&revenue.TierRange{})).
				Return(tt.mockRepoResponse, nil).
				AnyTimes()

			calculator := NewDurationRangeCalculator(mockRepository)
			got := calculator.Calculate(tt.tierRanges, tt.charge)
			require.Equal(t, tt.want, got)
		})
	}
}
