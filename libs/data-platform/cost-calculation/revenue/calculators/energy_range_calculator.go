package calculators

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/data-platform/cost-calculation/revenue"
)

func EnergyRevenue(tierRanges []revenue.TierRange, charge *costcalculation.Charge) (calculatedRevenue float64) {
	if tierRanges == nil || charge == nil {
		return 0
	}

	intervals := charge.ChargeIntervals()

	for _, tierRange := range tierRanges {
		if !charge.DateRange.Intersects(tierRange.DateRange) {
			continue
		}

		if len(intervals) > 0 {
			calculatedRevenue += tierRangeRevenueForChargeWithIntervals(&tierRange, charge, intervals)
		} else {
			calculatedRevenue += tierRangeRevenueForChargeWithoutIntervals(&tierRange, charge)
		}
	}

	return calculatedRevenue
}

func tierRangeRevenueForChargeWithoutIntervals(tierRange *revenue.TierRange, charge *costcalculation.Charge) (tierRangeRevenue float64) {
	chargeDurationWithinTierRange := charge.DateRange.IntersectionTime(tierRange.DateRange)
	chargeDuration := float64(charge.DateRange.DurationSeconds())

	energyDeliveredInTierRange := charge.Energy * chargeDurationWithinTierRange / chargeDuration

	// For cases where the charge is entirely in a tier range, avoid floating point error caused by fraction calculation
	if chargeDurationWithinTierRange == chargeDuration {
		energyDeliveredInTierRange = charge.Energy
	}

	tierRangeRevenue = energyDeliveredInTierRange * float64(tierRange.Rate)
	return tierRangeRevenue
}

func tierRangeRevenueForChargeWithIntervals(tierRange *revenue.TierRange, charge *costcalculation.Charge, intervals []*costcalculation.ChargeInterval) (tierRangeRevenue float64) {
	var cumulativeEnergy float64
	for i, interval := range intervals {
		if interval == nil {
			continue
		}

		energyUsed := interval.Energy
		// If it's the last charging interval, the interval energy running total should match the total charge energy
		if i == len(intervals)-1 {
			energyUsed = charge.Energy
		}

		// Work out the energy delivered just in this interval
		energyDelta := energyUsed - cumulativeEnergy
		cumulativeEnergy = energyUsed

		intervalRange, err := interval.DateRange()
		// Charging interval start/end time is invalid, or this tier range does not apply to this interval so skip
		if err != nil || !intervalRange.Intersects(tierRange.DateRange) {
			continue
		}

		// Out of the interval's delivered energy, calculate the portion that
		// should be attributed to being inside the tier range
		durationWithinTierRange := intervalRange.IntersectionTime(tierRange.DateRange)
		chargeIntervalDuration := float64(intervalRange.DurationSeconds())
		energyDeliveredInTier := energyDelta * durationWithinTierRange / chargeIntervalDuration

		// For cases where the interval is entirely in a tier range, avoid floating point error caused by fraction calculation
		if durationWithinTierRange == chargeIntervalDuration {
			energyDeliveredInTier = energyDelta
		}

		tierRangeRevenue += energyDeliveredInTier * float64(tierRange.Rate)
	}
	return tierRangeRevenue
}
