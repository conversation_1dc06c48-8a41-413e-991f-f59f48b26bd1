package calculators

import (
	"context"
	costcalculation "experience/libs/data-platform/cost-calculation"
	revenuecalc "experience/libs/data-platform/cost-calculation/revenue"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/revenue"
	"experience/libs/shared/go/service"
	"fmt"
)

const secondsPerHour = 3600

func NewDurationRangeCalculator(repository revenue.Repository) revenuecalc.TierRangeCalculator {
	return &rangeDurationCalculator{repository: repository}
}

type rangeDurationCalculator struct {
	repository revenue.Repository
}

func (c *rangeDurationCalculator) Calculate(tierRanges []revenuecalc.TierRange, charge *costcalculation.Charge) (calculatedRevenue float64) {
	if tierRanges == nil || charge == nil {
		return 0
	}

	if rangesAreContinuous(tierRanges) {
		tierRanges = convertToSingleTierRange(tierRanges)
	}

	for _, tierRange := range tierRanges {
		// Charge is shorter than this tier's start delay, so it won't ever apply, nor will any other ranges for this sub-tier
		if tierRange.StartSecond > charge.DateRange.DurationSeconds() {
			return 0
		}

		if !tierRange.DateRange.Intersects(charge.DateRange) {
			continue
		}

		rangeIntersectionTime := tierRange.DateRange.IntersectionTime(charge.DateRange)

		// See if there is another sub-tier for this time period that has a later delayed start
		// If so, cap the intersection at start_second of the next sub-tier
		nextSubTier, err := c.repository.GetNextDurationSubTier(context.Background(), &tierRange)
		if err != nil {
			fmt.Printf("RevenueCalculation - retrieving next duration sub-tier (day %v %v - day %v %v with start_second %v): %v", tierRange.BeginDay, tierRange.BeginTime, tierRange.EndDay, tierRange.EndTime, tierRange.StartSecond, err)
		}
		if nextSubTier != nil && rangeIntersectionTime > float64(nextSubTier.StartSecond) {
			rangeIntersectionTime = float64(nextSubTier.StartSecond)
		}

		// If this current range has a delayed start, then subtract that from the intersection time
		// Unless we'd end up negative
		if tierRange.StartSecond > 0 && rangeIntersectionTime > float64(tierRange.StartSecond) {
			rangeIntersectionTime -= float64(tierRange.StartSecond)
		}
		calculatedRevenue += float64(tierRange.Rate) * rangeIntersectionTime / secondsPerHour
	}

	return calculatedRevenue
}

// rangesAreContinuous works out if multiple ranges of the same tier should be combined
func rangesAreContinuous(tierRanges []revenuecalc.TierRange) bool {
	if len(tierRanges) < 2 {
		return false
	}

	continuous := true
	for i := range tierRanges {
		if i != len(tierRanges)-1 {
			rangeEnd := tierRanges[i].DateRange.To
			nextRangeStart := tierRanges[i+1].DateRange.From
			if nextRangeStart.Sub(*rangeEnd) != 0 {
				continuous = false
			}
		}
	}
	return continuous
}

func convertToSingleTierRange(tierRanges []revenuecalc.TierRange) []revenuecalc.TierRange {
	return []revenuecalc.TierRange{
		{
			Tier: tierRanges[0].Tier,
			DateRange: service.DateRange{
				From: tierRanges[0].DateRange.From,
				To:   tierRanges[len(tierRanges)-1].DateRange.To,
			},
		},
	}
}
