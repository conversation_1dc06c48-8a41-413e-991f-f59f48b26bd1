package calculators

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/data-platform/cost-calculation/revenue"
	"experience/libs/shared/go/service"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"
)

func Test_fixedRangeRevenueCostCalculator_Revenue(t *testing.T) {
	tests := []struct {
		name       string
		tierRanges []revenue.TierRange
		charge     *costcalculation.Charge
		want       float64
	}{
		{
			name: "returns the range tier rate",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 42,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 1, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 2, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 1, 12, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 1, 13, 0, 0, 0, time.UTC)),
				},
			},
			want: 42,
		},
		{
			name: "returns cumulative range tier rates",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 42,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 1, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 1, 12, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate: 22,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 1, 12, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 2, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 1, 11, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 1, 13, 0, 0, 0, time.UTC)),
				},
			},
			want: 64,
		},
		{
			name: "ignores superfluous tierRanges",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 500,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 1, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 2, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: revenue.Tier{
						Rate: 500,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 2, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 3, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 2, 0, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 2, 5, 0, 0, 0, time.UTC)),
				},
			},
			want: 500,
		},
		{
			name: "when range tier is nil returns zero",
			charge: &costcalculation.Charge{
				Energy: 15.0,
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 2, 0, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 2, 5, 0, 0, 0, time.UTC)),
				},
			},
			want: 0,
		},
		{
			name: "when charge is nil returns zero",
			tierRanges: []revenue.TierRange{
				{
					Tier: revenue.Tier{
						Rate: 42,
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 1, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 2, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			want: 0,
		},
	}
	for i := range tests {
		t.Run(tests[i].name, func(t *testing.T) {
			calc := FixedRevenue
			got := calc(tests[i].tierRanges, tests[i].charge)
			require.Equal(t, tests[i].want, got)
		})
	}
}
