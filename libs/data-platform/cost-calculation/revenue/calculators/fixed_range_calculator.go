package calculators

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/data-platform/cost-calculation/revenue"
)

func FixedRevenue(tierRanges []revenue.TierRange, charge *costcalculation.Charge) (rate float64) {
	if tierRanges == nil || charge == nil {
		return 0
	}

	for _, tierRange := range tierRanges {
		if !tierRange.DateRange.Intersects(charge.DateRange) {
			continue
		}
		rate += float64(tierRange.Rate)
	}
	return rate
}
