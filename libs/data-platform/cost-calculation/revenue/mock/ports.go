// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/cost-calculation/revenue/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/cost-calculation/revenue/mock/ports.go -source=libs/data-platform/cost-calculation/revenue/ports.go
//
// Package mock_revenue is a generated GoMock package.
package mock_revenue

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	revenue "experience/libs/data-platform/cost-calculation/revenue"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockCalculator is a mock of Calculator interface.
type MockCalculator struct {
	ctrl     *gomock.Controller
	recorder *MockCalculatorMockRecorder
}

// MockCalculatorMockRecorder is the mock recorder for MockCalculator.
type MockCalculatorMockRecorder struct {
	mock *MockCalculator
}

// NewMockCalculator creates a new mock instance.
func NewMockCalculator(ctrl *gomock.Controller) *MockCalculator {
	mock := &MockCalculator{ctrl: ctrl}
	mock.recorder = &MockCalculatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCalculator) EXPECT() *MockCalculatorMockRecorder {
	return m.recorder
}

// Revenue mocks base method.
func (m *MockCalculator) Revenue(arg0 revenue.Profile, arg1 *costcalculation.Charge) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Revenue", arg0, arg1)
	ret0, _ := ret[0].(int)
	return ret0
}

// Revenue indicates an expected call of Revenue.
func (mr *MockCalculatorMockRecorder) Revenue(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Revenue", reflect.TypeOf((*MockCalculator)(nil).Revenue), arg0, arg1)
}

// MockTierRangeCalculator is a mock of TierRangeCalculator interface.
type MockTierRangeCalculator struct {
	ctrl     *gomock.Controller
	recorder *MockTierRangeCalculatorMockRecorder
}

// MockTierRangeCalculatorMockRecorder is the mock recorder for MockTierRangeCalculator.
type MockTierRangeCalculatorMockRecorder struct {
	mock *MockTierRangeCalculator
}

// NewMockTierRangeCalculator creates a new mock instance.
func NewMockTierRangeCalculator(ctrl *gomock.Controller) *MockTierRangeCalculator {
	mock := &MockTierRangeCalculator{ctrl: ctrl}
	mock.recorder = &MockTierRangeCalculatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTierRangeCalculator) EXPECT() *MockTierRangeCalculatorMockRecorder {
	return m.recorder
}

// Calculate mocks base method.
func (m *MockTierRangeCalculator) Calculate(arg0 []revenue.TierRange, arg1 *costcalculation.Charge) float64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Calculate", arg0, arg1)
	ret0, _ := ret[0].(float64)
	return ret0
}

// Calculate indicates an expected call of Calculate.
func (mr *MockTierRangeCalculatorMockRecorder) Calculate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Calculate", reflect.TypeOf((*MockTierRangeCalculator)(nil).Calculate), arg0, arg1)
}
