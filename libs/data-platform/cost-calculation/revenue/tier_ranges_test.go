package revenue

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/shared/go/service"
	"testing"
	"time"

	"github.com/jinzhu/now"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"
)

/* There can be four types of tier ranges:
*
* 1: Those that start before but end within the charge
* 2: Those that start and end inside the charge
* 3: Those that start inside but end outside the charge
* 4: Those that start before and end after the charge
*
*                 C
*      |---------------------|
*
*       1                 3
*  |----------|      |----------|
*                2
*          |-----------|
*                4
*   |-----------------------------|
*
* However in theory a single tier could define multiple ranges that covered this charge if it repeated within the charge time frame.
* So we are going to start at the beginning of the charge, first check if there is a "type 1" or "type 4" tier range and if so move back to the beginning of that.
* Then walk forward finding tier start days within the charge time frame which will identify other ranges.
 */
func TestGetTierRanges(t *testing.T) {
	europeLondon, _ := time.LoadLocation("Europe/London")

	tests := []struct {
		name   string
		charge *costcalculation.Charge
		tier   *Tier
		want   []TierRange
	}{
		{
			name: "(Type 1) Tier starts before charge; tier ends during charge. Returns correct tier range.",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 19, 13, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 19, 14, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  6,
				BeginTime: ptr.To(now.MustParse("10:00:00")),
				EndDay:    0,
				EndTime:   ptr.To(now.MustParse("13:30:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  6,
						BeginTime: ptr.To(now.MustParse("10:00:00")),
						EndDay:    0,
						EndTime:   ptr.To(now.MustParse("13:30:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 18, 9, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 19, 12, 30, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "(Type 2) Tier starts during charge; tier ends during charge. Returns correct tier range.",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 19, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 19, 14, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  0,
				BeginTime: ptr.To(now.MustParse("11:00:00")),
				EndDay:    0,
				EndTime:   ptr.To(now.MustParse("13:00:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  0,
						BeginTime: ptr.To(now.MustParse("11:00:00")),
						EndDay:    0,
						EndTime:   ptr.To(now.MustParse("13:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 19, 10, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 19, 12, 0, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "(Type 3) Tier starts during charge; tier ends after charge. Returns correct tier range.",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 19, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 19, 14, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  0,
				BeginTime: ptr.To(now.MustParse("11:00:00")),
				EndDay:    1,
				EndTime:   ptr.To(now.MustParse("09:00:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  0,
						BeginTime: ptr.To(now.MustParse("11:00:00")),
						EndDay:    1,
						EndTime:   ptr.To(now.MustParse("09:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 19, 10, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 20, 8, 0, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "(Type 4) Tier starts before charge; tier ends after charge. Returns correct tier range.",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 19, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 19, 14, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  0,
				BeginTime: ptr.To(now.MustParse("00:00:00")),
				EndDay:    4,
				EndTime:   ptr.To(now.MustParse("00:00:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  0,
						BeginTime: ptr.To(now.MustParse("00:00:00")),
						EndDay:    4,
						EndTime:   ptr.To(now.MustParse("00:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 18, 23, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 22, 23, 0, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "(Multiple Type 2) Tier starts during charge; tier ends during charge. Returns correct tier ranges.",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 19, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 6, 2, 14, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  0,
				BeginTime: ptr.To(now.MustParse("11:00:00")),
				EndDay:    0,
				EndTime:   ptr.To(now.MustParse("13:00:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  0,
						BeginTime: ptr.To(now.MustParse("11:00:00")),
						EndDay:    0,
						EndTime:   ptr.To(now.MustParse("13:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 19, 10, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 19, 12, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: Tier{
						BeginDay:  0,
						BeginTime: ptr.To(now.MustParse("11:00:00")),
						EndDay:    0,
						EndTime:   ptr.To(now.MustParse("13:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 26, 10, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 26, 12, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: Tier{
						BeginDay:  0,
						BeginTime: ptr.To(now.MustParse("11:00:00")),
						EndDay:    0,
						EndTime:   ptr.To(now.MustParse("13:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 6, 2, 10, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 6, 2, 12, 0, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "(Multiple - Type 1, Type 2 and Type 3) Tier range starts before charge end during; tier range starts and ends during charge, and another starts during and ends after. Returns correct tier ranges.",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 10, 10, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 24, 12, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  4,
				BeginTime: ptr.To(now.MustParse("09:00:00")),
				EndDay:    6,
				EndTime:   ptr.To(now.MustParse("13:00:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  4,
						BeginTime: ptr.To(now.MustParse("09:00:00")),
						EndDay:    6,
						EndTime:   ptr.To(now.MustParse("13:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 9, 8, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 11, 12, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: Tier{
						BeginDay:  4,
						BeginTime: ptr.To(now.MustParse("09:00:00")),
						EndDay:    6,
						EndTime:   ptr.To(now.MustParse("13:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 16, 8, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 18, 12, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: Tier{
						BeginDay:  4,
						BeginTime: ptr.To(now.MustParse("09:00:00")),
						EndDay:    6,
						EndTime:   ptr.To(now.MustParse("13:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 23, 8, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 12, 0, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "Continuous Tier, charge starts before and ends after the tier breakpoint. Returns correct tier ranges.",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 25, 23, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 26, 1, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  0,
				BeginTime: ptr.To(now.MustParse("00:00:00")),
				EndDay:    0,
				EndTime:   ptr.To(now.MustParse("00:00:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  0,
						BeginTime: ptr.To(now.MustParse("00:00:00")),
						EndDay:    0,
						EndTime:   ptr.To(now.MustParse("00:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 18, 23, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 25, 23, 0, 0, 0, time.UTC)),
					},
				},
				{
					Tier: Tier{
						BeginDay:  0,
						BeginTime: ptr.To(now.MustParse("00:00:00")),
						EndDay:    0,
						EndTime:   ptr.To(now.MustParse("00:00:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 25, 23, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 6, 1, 23, 0, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "Timezone tier ranges account for BST",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 19, 23, 1, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 20, 4, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  1,
				BeginTime: ptr.To(now.MustParse("00:00:00")),
				EndDay:    1,
				EndTime:   ptr.To(now.MustParse("07:30:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  1,
						BeginTime: ptr.To(now.MustParse("00:00:00")),
						EndDay:    1,
						EndTime:   ptr.To(now.MustParse("07:30:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2024, 5, 19, 23, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2024, 5, 20, 6, 30, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "Timezone tier ranges account for being outside of BST",
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2023, 12, 24, 23, 1, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2023, 12, 25, 4, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			tier: &Tier{
				BeginDay:  1,
				BeginTime: ptr.To(now.MustParse("00:00:00")),
				EndDay:    1,
				EndTime:   ptr.To(now.MustParse("07:30:00")),
			},
			want: []TierRange{
				{
					Tier: Tier{
						BeginDay:  1,
						BeginTime: ptr.To(now.MustParse("00:00:00")),
						EndDay:    1,
						EndTime:   ptr.To(now.MustParse("07:30:00")),
					},
					DateRange: service.DateRange{
						From: ptr.To(time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC)),
						To:   ptr.To(time.Date(2023, 12, 25, 7, 30, 0, 0, time.UTC)),
					},
				},
			},
		},
		{
			name: "a tier which has no applicable tier ranges",
			tier: &Tier{
				Rate:      4,
				TierType:  TypeFixed,
				UserType:  UserTypePublic,
				BeginDay:  0,
				BeginTime: ptr.To(now.MustParse("00:00:00")),
				EndDay:    0,
				EndTime:   ptr.To(now.MustParse("23:59:59")),
			},
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 18, 5, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 18, 22, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			want: nil,
		},
		{
			name: "nil tier gives nil ranges",
			tier: nil,
			charge: &costcalculation.Charge{
				DateRange: service.DateRange{
					From: ptr.To(time.Date(2024, 5, 18, 5, 0, 0, 0, time.UTC)),
					To:   ptr.To(time.Date(2024, 5, 18, 22, 0, 0, 0, time.UTC)),
				},
				Location: europeLondon,
			},
			want: nil,
		},
		{
			name: "nil charge gives nil ranges",
			tier: &Tier{
				Rate:      4,
				TierType:  TypeFixed,
				UserType:  UserTypePublic,
				BeginDay:  0,
				BeginTime: ptr.To(now.MustParse("00:00:00")),
				EndDay:    0,
				EndTime:   ptr.To(now.MustParse("23:59:59")),
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetTierRanges(tt.charge, tt.tier)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestNextWeekDay(t *testing.T) {
	tests := []struct {
		name string
		ts   time.Time
		day  time.Weekday
		want time.Time
	}{
		{
			name: "given Fri 24 May next Saturday is 2025-05-25",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Saturday,
			want: time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May next Sunday is 2024-05-26",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Sunday,
			want: time.Date(2024, 5, 26, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May next Monday is 2024-05-27",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Monday,
			want: time.Date(2024, 5, 27, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May next Tuesday is 2024-05-28",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Tuesday,
			want: time.Date(2024, 5, 28, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May next Wednesday is 2024-05-29",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Wednesday,
			want: time.Date(2024, 5, 29, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May next Thursday is 2024-05-30",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Thursday,
			want: time.Date(2024, 5, 30, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May next Friday is 2024-05-31",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Friday,
			want: time.Date(2024, 5, 31, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Sat 25 May next Saturday is 2024-06-01",
			ts:   time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC),
			day:  time.Saturday,
			want: time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := nextWeekDay(tt.ts, tt.day)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestPreviousWeekDay(t *testing.T) {
	tests := []struct {
		name string
		ts   time.Time
		day  time.Weekday
		want time.Time
	}{
		{
			name: "given Fri 24 May previous Thursday is 2025-05-23",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Thursday,
			want: time.Date(2024, 5, 23, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May previous Wednesday is 2025-05-22",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Wednesday,
			want: time.Date(2024, 5, 22, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May previous Tuesday is 2025-05-21",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Tuesday,
			want: time.Date(2024, 5, 21, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May previous Monday is 2025-05-20",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Monday,
			want: time.Date(2024, 5, 20, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May previous Sunday is 2025-05-19",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Sunday,
			want: time.Date(2024, 5, 19, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May previous Saturday is 2025-05-18",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Saturday,
			want: time.Date(2024, 5, 18, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Fri 24 May previous Friday is 2025-05-17",
			ts:   time.Date(2024, 5, 24, 0, 0, 0, 0, time.UTC),
			day:  time.Friday,
			want: time.Date(2024, 5, 17, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "given Wednesday 1 May previous Monday is 2025-04-29",
			ts:   time.Date(2024, 5, 1, 0, 0, 0, 0, time.UTC),
			day:  time.Monday,
			want: time.Date(2024, 4, 29, 0, 0, 0, 0, time.UTC),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := previousWeekDay(tt.ts, tt.day)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestNextWeekDayAtTime(t *testing.T) {
	tests := []struct {
		name string
		ts   time.Time
		at   time.Time
		day  time.Weekday
		want time.Time
	}{
		{
			name: "given Sat 25 May next Saturday at 09:15:05 is 2024-06-01 09:15:05",
			ts:   time.Date(2024, 5, 25, 0, 0, 0, 0, time.UTC),
			at:   time.Date(0, 0, 0, 9, 15, 5, 0, time.UTC),
			day:  time.Saturday,
			want: time.Date(2024, 6, 1, 9, 15, 5, 0, time.UTC),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := nextWeekDayAtTime(tt.ts, tt.at, tt.day)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestPreviousWeekDayAtTime(t *testing.T) {
	tests := []struct {
		name string
		ts   time.Time
		at   time.Time
		day  time.Weekday
		want time.Time
	}{
		{
			name: "given Wednesday 1 May previous Monday at 22:20:10 is 2025-04-29 22:20:10",
			ts:   time.Date(2024, 5, 1, 0, 0, 0, 0, time.UTC),
			at:   time.Date(0, 0, 0, 22, 20, 10, 0, time.UTC),
			day:  time.Monday,
			want: time.Date(2024, 4, 29, 22, 20, 10, 0, time.UTC),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := previousWeekDayAtTime(tt.ts, tt.at, tt.day)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestConvertDayAndTimeToDateUTC(t *testing.T) {
	europeLondon, _ := time.LoadLocation("Europe/London")
	tests := []struct {
		name        string
		tierWeekday *time.Weekday
		tierTime    *time.Time
		fallback    time.Time
		want        time.Time
	}{
		{
			name:        "a saturday at 6:30",
			tierWeekday: ptr.To(time.Saturday),
			tierTime:    ptr.To(time.Date(0, 0, 0, 6, 30, 0, 0, time.UTC)),
			fallback:    time.Date(2024, 5, 24, 10, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 5, 25, 5, 30, 0, 0, time.UTC),
		},
		{
			name:        "a cold winter saturday at 6:30 (UTC)",
			tierWeekday: ptr.To(time.Saturday),
			tierTime:    ptr.To(time.Date(0, 0, 0, 6, 30, 0, 0, time.UTC)),
			fallback:    time.Date(2024, 1, 6, 10, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 1, 13, 6, 30, 0, 0, time.UTC),
		},
		{
			name:        "a monday at 20:30",
			tierWeekday: ptr.To(time.Monday),
			tierTime:    ptr.To(time.Date(0, 0, 0, 20, 30, 0, 0, time.UTC)),
			fallback:    time.Date(2024, 5, 24, 10, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 5, 27, 19, 30, 0, 0, time.UTC),
		},
		{
			name:        "a friday at 22:00",
			tierWeekday: ptr.To(time.Friday),
			tierTime:    ptr.To(time.Date(0, 0, 0, 22, 0, 0, 0, time.UTC)),
			fallback:    time.Date(2024, 5, 24, 10, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 5, 31, 21, 0, 0, 0, time.UTC),
		},
		{
			name:        "uses fallback time",
			tierWeekday: ptr.To(time.Thursday),
			tierTime:    nil,
			fallback:    time.Date(2024, 5, 24, 22, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 5, 30, 22, 0, 0, 0, time.UTC),
		},
		{
			name:        "uses fallback day",
			tierWeekday: nil,
			tierTime:    ptr.To(time.Date(0, 0, 0, 22, 0, 0, 0, time.UTC)),
			fallback:    time.Date(2024, 5, 24, 22, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 5, 24, 21, 0, 0, 0, time.UTC),
		},
		{
			name:        "uses fallback day in winter (UTC)",
			tierWeekday: nil,
			tierTime:    ptr.To(time.Date(0, 0, 0, 22, 0, 0, 0, time.UTC)),
			fallback:    time.Date(2024, 1, 24, 22, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 1, 24, 22, 0, 0, 0, time.UTC),
		},
		{
			name:        "uses fallback time and day",
			tierWeekday: nil,
			tierTime:    nil,
			fallback:    time.Date(2024, 5, 24, 22, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 5, 24, 22, 0, 0, 0, time.UTC),
		},
		{
			name:        "uses fallback time and day in winter (UTC) no effect on time",
			tierWeekday: nil,
			tierTime:    nil,
			fallback:    time.Date(2024, 1, 24, 22, 0, 0, 0, time.UTC),
			want:        time.Date(2024, 1, 24, 22, 0, 0, 0, time.UTC),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertDayAndTimeToDateUTC(tt.tierWeekday, tt.tierTime, tt.fallback, europeLondon)
			require.Equal(t, tt.want, got)
		})
	}
}
