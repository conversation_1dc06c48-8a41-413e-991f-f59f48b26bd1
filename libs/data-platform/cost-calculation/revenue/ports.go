package revenue

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
)

type Calculator interface {
	Revenue(Profile, *costcalculation.Charge) int
}

type TierRangeCalculator interface {
	Calculate([]TierRange, *costcalculation.Charge) float64
}

type RangeCalculatorAdapter func([]TierRange, *costcalculation.Charge) float64

func (r RangeCalculatorAdapter) Calculate(tierRanges []TierRange, charge *costcalculation.Charge) float64 {
	return r(tierRanges, charge)
}
