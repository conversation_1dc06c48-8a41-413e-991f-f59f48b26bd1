package revenue

import (
	"experience/libs/shared/go/service"
	"time"
)

type TierType string
type UserType string

const (
	TypeFixed    TierType = "fixed"
	TypeDuration TierType = "duration"
	TypeEnergy   TierType = "energy"

	UserTypePublic UserType = "public"
	UserTypeMember UserType = "member"
	UserTypeDriver UserType = "driver"
)

// Tier is the Revenue Profile Tier as is defined for a location
type Tier struct {
	RevenueProfileID int64
	Rate             int64
	TierType         TierType
	UserType         UserType
	BeginDay         int64
	BeginTime        *time.Time
	EndDay           int64
	EndTime          *time.Time
	StartSecond      int64
}

func (t *Tier) BeginWeekday() time.Weekday {
	return time.Weekday(t.BeginDay)
}

func (t *Tier) EndWeekday() time.Weekday {
	return time.Weekday(t.EndDay)
}

// TierRange represents a real DateTime range over which the Tier applies to this Charge
type TierRange struct {
	Tier
	DateRange service.DateRange
}

type Profile struct {
	Tiers []Tier
}

type Locale struct {
	Currency string
	Location *time.Location
}
