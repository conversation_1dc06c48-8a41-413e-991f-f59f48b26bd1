package revenue

import (
	cost_calculation "experience/libs/data-platform/cost-calculation"
	"math"
)

const revenueRatePrecision = 10000

func NewCostCalculator(fixedCalculator, energyCalculator, durationCalculator TierRangeCalculator) Calculator {
	return &revenueCostCalculator{fixedCalculator, energyCalculator, durationCalculator}
}

type revenueCostCalculator struct {
	fixedCalculator, energyCalculator, durationCalculator TierRangeCalculator
}

func (r revenueCostCalculator) Revenue(profile Profile, charge *cost_calculation.Charge) int {
	if charge == nil || charge.DateRange.To == nil || len(profile.Tiers) == 0 {
		return 0
	}

	if charge.DateRange.DurationSeconds() < 10 {
		return 0
	}

	var revenuePrecise float64

	for _, tier := range profile.Tiers {
		tierRanges := GetTierRanges(charge, &tier)
		if len(tierRanges) == 0 {
			continue
		}

		switch tier.TierType {
		case TypeFixed:
			revenuePrecise += r.fixedCalculator.Calculate(tierRanges, charge)
		case TypeEnergy:
			revenuePrecise += r.energyCalculator.Calculate(tierRanges, charge)
		case TypeDuration:
			revenuePrecise += r.durationCalculator.Calculate(tierRanges, charge)
		}
	}
	// Rates are stored in x10000p, so divide and round to nearest penny.
	// We do this at the end to avoid rounding errors adding up
	return int(math.Round(revenuePrecise / float64(revenueRatePrecision)))
}
