package revenue

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/shared/go/service"
	"time"

	"k8s.io/utils/ptr"
)

// GetTierRanges is responsible for working out the time ranges a given revenue tier rate applies within a charge cycle
func GetTierRanges(charge *costcalculation.Charge, tier *Tier) []TierRange {
	if charge == nil || tier == nil {
		return nil
	}

	// We need to first convert the tier start/end time and day into a date object and adjust it to UTC
	tierBeginTime := convertDayAndTimeToDateUTC(ptr.To(tier.BeginWeekday()), tier.BeginTime, *charge.DateRange.From, charge.Location)
	tierBeginDay := tierBeginTime.Weekday()

	tierEndTime := convertDayAndTimeToDateUTC(ptr.To(tier.EndWeekday()), tier.EndTime, *charge.DateRange.To, charge.Location)
	tierEndDay := tierEndTime.Weekday()

	// To start with we will assume the tier starts and ends on the charge start date then start looking for tiers from the start date of the charge
	nextTierStart := time.Date(charge.DateRange.From.Year(), charge.DateRange.From.Month(), charge.DateRange.From.Day(),
		tierBeginTime.Hour(), tierBeginTime.Minute(), tierBeginTime.Second(), tierBeginTime.Nanosecond(), tierBeginTime.Location())
	nextTierEnd := time.Date(charge.DateRange.From.Year(), charge.DateRange.From.Month(), charge.DateRange.From.Day(),
		tierEndTime.Hour(), tierEndTime.Minute(), tierEndTime.Second(), tierEndTime.Nanosecond(), tierEndTime.Location())

	// If the tier's begin day is not the same as the charge start day we need to look forward to find the next one
	if tierBeginDay != nextTierStart.Weekday() {
		nextTierStart = nextWeekDayAtTime(nextTierStart, tierBeginTime, tierBeginDay)
	}

	// We will also look forward to find the next end day from the charge start
	if tierEndDay != charge.DateRange.From.Weekday() {
		nextTierEnd = nextWeekDayAtTime(*charge.DateRange.From, tierEndTime, tierEndDay)
	}

	// If the nearest tier end day is before the nearest tier start day we have found a "type 1" range which starts before the charge start
	// So we need to actually look back from the charge start to find the first tier start day
	if nextTierStart.Equal(nextTierEnd) || nextTierStart.After(nextTierEnd) {
		nextTierStart = previousWeekDayAtTime(nextTierEnd, tierBeginTime, tierBeginDay)
	}

	var tierRanges []TierRange

	// Then we are going to loop through tier ranges until we find one with a start date that is greater than the end date of the charge
	for nextTierStart.Before(*charge.DateRange.To) {
		// If the current end day is not the same as the start day or the start and end are the same look for the next end day
		if tierEndDay != nextTierStart.Weekday() || nextTierStart.Equal(nextTierEnd) {
			nextTierEnd = nextWeekDayAtTime(nextTierStart, tierEndTime, tierEndDay)
		}
		if nextTierStart.After(nextTierEnd) {
			nextTierEnd = nextWeekDayAtTime(nextTierEnd, tierEndTime, tierEndDay)
		}
		tierRanges = append(tierRanges, TierRange{
			Tier: *tier,
			DateRange: service.DateRange{
				From: ptr.To(time.Date(nextTierStart.Year(), nextTierStart.Month(), nextTierStart.Day(), nextTierStart.Hour(), nextTierStart.Minute(), nextTierStart.Second(), nextTierStart.Nanosecond(), nextTierStart.Location())),
				To:   ptr.To(time.Date(nextTierEnd.Year(), nextTierEnd.Month(), nextTierEnd.Day(), nextTierEnd.Hour(), nextTierEnd.Minute(), nextTierEnd.Second(), nextTierEnd.Nanosecond(), nextTierEnd.Location())),
			},
		})

		// Move on to the next tier start day
		nextTierStart = nextWeekDayAtTime(nextTierStart, tierBeginTime, tierBeginDay)
	}

	return tierRanges
}

// convertDayAndTimeToDateUTC Converts a given day of the week and time to a UTC formatted date object
// defaults to the charge time values.
func convertDayAndTimeToDateUTC(tierWeekday *time.Weekday, tierTime *time.Time, chargeTime time.Time, location *time.Location) time.Time {
	tierRangeDate := chargeTime
	// If we have a time use it
	if tierTime != nil {
		tierRangeDate = time.Date(chargeTime.Year(), chargeTime.Month(), chargeTime.Day(), tierTime.Hour(), tierTime.Minute(), tierTime.Second(), tierTime.Nanosecond(), location)
	}

	// If we have a day of the week use it
	if tierWeekday != nil {
		tierRangeDate = nextWeekDay(tierRangeDate, *tierWeekday)
	}

	// Convert to UTC
	return tierRangeDate.UTC()
}

// nextWeekDayAtTime returns the Time at as of the next given weekday from t
func nextWeekDayAtTime(t, at time.Time, day time.Weekday) time.Time {
	nwd := nextWeekDay(t, day)
	return time.Date(nwd.Year(), nwd.Month(), nwd.Day(), at.Hour(), at.Minute(), at.Second(), at.Nanosecond(), at.Location())
}

// previousWeekDayAtTime returns the Time at as of the previous given weekday from t
func previousWeekDayAtTime(t, at time.Time, day time.Weekday) time.Time {
	pwd := previousWeekDay(t, day)
	return time.Date(pwd.Year(), pwd.Month(), pwd.Day(), at.Hour(), at.Minute(), at.Second(), at.Nanosecond(), at.Location())
}

// nextWeekDay returns the Time t as of the next given Weekday
// As per Carbon->next(Weekday) behaviour when t.Weekday() == day then returns t a full week forward
// The time component returned is the one from t - only the date is changed
func nextWeekDay(t time.Time, day time.Weekday) time.Time {
	return onWeekday(t, day, func(i time.Weekday) time.Weekday {
		if i <= 0 {
			i += 7
		}
		return i
	})
}

// previousWeekDay returns the Time t as of the previous given Weekday
// As per Carbon->previous(Weekday) behaviour when t.Weekday() == day then returns t a full week back
// The time component returned is the one from t - only the date is changed
func previousWeekDay(t time.Time, day time.Weekday) time.Time {
	return onWeekday(t, day, func(i time.Weekday) time.Weekday {
		if i >= 0 {
			i -= 7
		}
		return i
	})
}

func onWeekday(t time.Time, day time.Weekday, direction func(weekday time.Weekday) time.Weekday) time.Time {
	return t.AddDate(0, 0, int(direction(day-t.Weekday())))
}

/*
	Notes to reader

This could be written to be more readable if we accept a little repetition
e.g.

func nextWeekDay(t time.Time, day time.Weekday) time.Time {
	diff := day - t.Weekday()
	if diff <= 0 {
		diff += 7
	}
	return t.AddDate(0, 0, int(diff))
}
func previousWeekDay(t time.Time, day time.Weekday) time.Time {
	diff := day - t.Weekday()
	if diff >= 0 {
		diff -= 7
	}
	return t.AddDate(0, 0, int(diff))
}
*/
