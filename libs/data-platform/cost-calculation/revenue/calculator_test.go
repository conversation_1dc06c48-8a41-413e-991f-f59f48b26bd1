package revenue_test

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/data-platform/cost-calculation/revenue"
	"experience/libs/shared/go/service"
	"testing"
	"time"

	"github.com/jinzhu/now"
	"k8s.io/utils/ptr"
)

func Test_revenueCostCalculator_Revenue(t *testing.T) {
	tests := []struct {
		name         string
		profileTypes []revenue.TierType
		charge       costcalculation.Charge
		fixedCost    float64
		durationCost float64
		energyCost   float64
		want         int
	}{
		{
			name:         "returns result of fixed calculator",
			profileTypes: []revenue.TierType{revenue.TypeFixed},
			charge:       aValidCharge(t),
			fixedCost:    10000,
			want:         1,
		},
		{
			name:         "passes multiple fixed revenue tiers to calculator",
			profileTypes: []revenue.TierType{revenue.TypeFixed},
			charge:       aChargeOverTwoWeeks(t),
			fixedCost:    10000,
			want:         2,
		},
		{
			name:         "returns result of energy calculator",
			profileTypes: []revenue.TierType{revenue.TypeEnergy},
			charge:       aValidCharge(t),
			energyCost:   10000,
			want:         1,
		},
		{
			name:         "returns result of duration calculator",
			profileTypes: []revenue.TierType{revenue.TypeDuration},
			charge:       aValidCharge(t),
			durationCost: 10000,
			want:         1,
		},
		{
			name:         "sums result of all relevant calculators",
			profileTypes: []revenue.TierType{revenue.TypeFixed, revenue.TypeEnergy, revenue.TypeDuration},
			charge:       aValidCharge(t),
			durationCost: 10000,
			fixedCost:    10000,
			energyCost:   10000,
			want:         3,
		},
		{
			name: "no cost if there is no charge ends at date",
			charge: costcalculation.Charge{
				DateRange: service.DateRange{To: nil},
			},
			want: 0,
		},
		{
			name:         "no cost if there is are no profile tiers",
			profileTypes: []revenue.TierType{},
			charge:       aValidCharge(t),
			want:         0,
		},
		{
			name:         "no cost if the charge has duration of less than 10 seconds",
			profileTypes: []revenue.TierType{revenue.TypeFixed},
			fixedCost:    10000,
			charge:       aChargeWithNoDuration(t),
			want:         0,
		},
		{
			name:         "no cost if there are no tier range overlaps",
			profileTypes: []revenue.TierType{revenue.TypeEnergy},
			charge: costcalculation.Charge{DateRange: service.DateRange{
				To:   ptr.To(time.Date(2024, 5, 25, 5, 0, 0, 0, time.UTC)),
				From: ptr.To(time.Date(2024, 5, 26, 5, 0, 0, 0, time.UTC)),
			}},
			energyCost: 10000,
			want:       0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := revenue.NewCostCalculator(
				revenue.RangeCalculatorAdapter(func(ranges []revenue.TierRange, _ *costcalculation.Charge) float64 {
					return float64(len(ranges)) * tt.fixedCost
				}),
				revenue.RangeCalculatorAdapter(func(ranges []revenue.TierRange, _ *costcalculation.Charge) float64 {
					return float64(len(ranges)) * tt.energyCost
				}),
				revenue.RangeCalculatorAdapter(func(ranges []revenue.TierRange, _ *costcalculation.Charge) float64 {
					return float64(len(ranges)) * tt.durationCost
				}),
			)

			profile := aProfile(tt.profileTypes)
			chargeCopy := tt.charge
			got := r.Revenue(profile, &chargeCopy)
			if got != tt.want {
				t.Errorf("Revenue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func aValidCharge(t *testing.T) costcalculation.Charge {
	t.Helper()
	from := time.Date(2024, 5, 24, 5, 0, 0, 0, time.UTC)
	to := time.Date(2024, 5, 24, 15, 0, 0, 0, time.UTC)
	return costcalculation.Charge{DateRange: service.DateRange{
		To:   &to,
		From: &from,
	},
		Location: defaultLocation(),
	}
}

func aChargeOverTwoWeeks(t *testing.T) costcalculation.Charge {
	t.Helper()
	from := time.Date(2024, 5, 24, 5, 0, 0, 0, time.UTC)
	to := time.Date(2024, 5, 31, 15, 0, 0, 0, time.UTC)
	return costcalculation.Charge{DateRange: service.DateRange{
		To:   &to,
		From: &from,
	},
		Location: defaultLocation(),
	}
}

func aChargeWithNoDuration(t *testing.T) costcalculation.Charge {
	t.Helper()
	n := time.Now()

	return costcalculation.Charge{
		DateRange: service.DateRange{
			To:   &n,
			From: &n,
		},
		Location: defaultLocation(),
	}
}

func aProfile(profileTypes []revenue.TierType) revenue.Profile {
	tiers := make([]revenue.Tier, len(profileTypes))
	for i, profileType := range profileTypes {
		tiers[i] = revenue.Tier{
			TierType:  profileType,
			BeginDay:  5,
			BeginTime: ptr.To(now.MustParse("00:00:00")),
			EndDay:    5,
			EndTime:   ptr.To(now.MustParse("23:59:59")),
		}
	}
	return revenue.Profile{Tiers: tiers}
}

func defaultLocation() *time.Location {
	def, _ := time.LoadLocation("Europe/London")
	return def
}
