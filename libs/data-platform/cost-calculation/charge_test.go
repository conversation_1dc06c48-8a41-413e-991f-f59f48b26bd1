package costcalculation

import (
	"experience/libs/shared/go/service"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestCharge_ChargeInterval_DateRange_Duration(t *testing.T) {
	charge := ChargeInterval{
		Energy:    23.2,
		EndedAt:   ptr.To(time.Date(2023, 11, 12, 14, 21, 57, 0, time.UTC)),
		StartedAt: ptr.To(time.Date(2023, 11, 12, 14, 21, 15, 0, time.UTC)),
	}
	dateRange, err := charge.DateRange()
	require.NoError(t, err)
	expectedDateRange := &service.DateRange{
		From: ptr.To(time.Date(2023, 11, 12, 14, 21, 15, 0, time.UTC)),
		To:   ptr.To(time.Date(2023, 11, 12, 14, 21, 57, 0, time.UTC)),
	}
	require.Equal(t, expectedDateRange, dateRange)
	require.Equal(t, int64(42), dateRange.DurationSeconds())
}

func TestCharge_ChargingIntervals_Ordered(t *testing.T) {
	chargeInterval1 := ChargeInterval{
		Energy:    23.2,
		EndedAt:   ptr.To(time.Date(2023, 11, 2, 12, 13, 14, 0, time.UTC)),
		StartedAt: ptr.To(time.Date(2023, 11, 2, 10, 1, 11, 0, time.UTC)),
	}
	chargeInterval2 := ChargeInterval{
		Energy:    28.2,
		EndedAt:   ptr.To(time.Date(2023, 11, 2, 13, 1, 14, 0, time.UTC)),
		StartedAt: ptr.To(time.Date(2023, 11, 2, 12, 13, 14, 0, time.UTC)),
	}

	now := time.Now()
	charge := Charge{
		Energy: gofakeit.Float64(),
		DateRange: service.DateRange{
			From: ptr.To(now),
			To:   ptr.To(now.Add(time.Second * time.Duration(gofakeit.IntRange(10, 500)))),
		},
		Intervals: []*ChargeInterval{
			&chargeInterval2,
			&chargeInterval1,
			{
				Energy:    23.2,
				EndedAt:   ptr.To(time.Date(2023, 11, 2, 12, 13, 14, 0, time.UTC)),
				StartedAt: nil,
			},
			{
				Energy:    23.2,
				EndedAt:   ptr.To(time.Date(2023, 11, 2, 12, 13, 14, 0, time.UTC)),
				StartedAt: ptr.To(time.Date(2023, 11, 2, 12, 13, 14, 0, time.UTC)),
			},
			{
				Energy:    0.0,
				EndedAt:   ptr.To(time.Date(2023, 11, 2, 12, 13, 14, 0, time.UTC)),
				StartedAt: ptr.To(time.Date(2023, 11, 2, 10, 1, 11, 0, time.UTC)),
			},
		},
	}

	require.Equal(t, []*ChargeInterval{&chargeInterval1, &chargeInterval2}, charge.ChargeIntervals())
}
