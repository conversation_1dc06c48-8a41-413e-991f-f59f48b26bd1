{"name": "data-platform-cost-calculation", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/data-platform/cost-calculation", "implicitDependencies": ["shared-go-service"], "projectType": "library", "tags": ["data-platform"], "targets": {"test": {"executor": "@nx-go/nx-go:test", "options": {"race": true}}, "generate-sources": {"executor": "nx:run-commands", "options": {"commands": ["mockgen -destination libs/data-platform/cost-calculation/energy-cost/mock/ports.go -source=libs/data-platform/cost-calculation/energy-cost/ports.go", "mockgen -destination libs/data-platform/cost-calculation/revenue/mock/ports.go -source=libs/data-platform/cost-calculation/revenue/ports.go"], "parallel": false}}}}