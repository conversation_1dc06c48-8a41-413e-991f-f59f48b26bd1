package podadmin_test

import (
	"context"
	"database/sql"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	repository "experience/libs/data-platform/charge-authorisation/pkg/infra/podadmin"
	sqlcpodadmin "experience/libs/data-platform/test/sqlc-podadmin"
	"experience/libs/shared/go/db/mysql"
	"experience/libs/shared/go/numbers"
	"log"
	"os"
	"testing"

	"github.com/google/uuid"
	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type PodadminTestSuite struct {
	suite.Suite
	testDB    *mysql.Database
	queries   *sqlcpodadmin.Queries
	underTest chargeauthorisation.PodadminRepository
}

func TestPodadminRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping PodadminRepositoryTestSuite integration test")
	}
	suite.Run(t, new(PodadminTestSuite))
}

func (s *PodadminTestSuite) SetupSuite() {
	var err error
	podadminDB := s.setupMySQLContainer(s.T())
	s.underTest, err = repository.NewRepository(podadminDB, log.New(os.Stderr, "[podadmin_integration] ", log.Ltime))
	require.NoError(s.T(), err)
}

func (s *PodadminTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating mysql container: %s", err)
	}
}

func (s *PodadminTestSuite) TestInsertClaimedCharge() {
	claimedCharge := &chargeauthorisation.ClaimedCharge{
		PodLocationID:    uint32(1),
		PodDoorID:        uint32(1),
		AuthoriserID:     int32(1),
		BillingAccountID: ptr.To(int32(1)),
		BillingEventID:   ptr.To(int32(1)),
	}

	claimedChargeID, err := s.underTest.InsertClaimedCharge(s.T().Context(), claimedCharge)
	require.NoError(s.T(), err)

	claimedChargeIDUint32, err := numbers.Convert[int64, uint32](claimedChargeID)
	require.NoError(s.T(), err)

	claimedChargeFromDB, err := s.queries.GetPodadminClaimedCharge(s.T().Context(), claimedChargeIDUint32)
	require.NoError(s.T(), err)

	require.Equal(s.T(), claimedCharge.PodLocationID, claimedChargeFromDB.PodLocationID)
	require.Equal(s.T(), claimedCharge.PodDoorID, claimedChargeFromDB.PodDoorID)
	require.Equal(s.T(), claimedCharge.AuthoriserID, claimedChargeFromDB.AuthoriserID)
	require.Equal(s.T(), *claimedCharge.BillingAccountID, claimedChargeFromDB.BillingAccountID.Int32)
	require.Equal(s.T(), *claimedCharge.BillingEventID, claimedChargeFromDB.BillingEventID.Int32)
}

func (s *PodadminTestSuite) TestInsertClaimedChargeWithNoBillingAccount() {
	claimedCharge := &chargeauthorisation.ClaimedCharge{
		PodLocationID:  uint32(1),
		PodDoorID:      uint32(1),
		AuthoriserID:   int32(1),
		BillingEventID: ptr.To(int32(1)),
	}

	_, err := s.underTest.InsertClaimedCharge(s.T().Context(), claimedCharge)
	require.NoError(s.T(), err)

	var claimedChargeID int64
	claimedChargeID, err = s.queries.GetLastInsertId(s.T().Context())
	require.NoError(s.T(), err)

	claimedChargeIDUint32, err := numbers.Convert[int64, uint32](claimedChargeID)
	require.NoError(s.T(), err)

	claimedChargeFromDB, err := s.queries.GetPodadminClaimedCharge(s.T().Context(), claimedChargeIDUint32)
	require.NoError(s.T(), err)

	require.Equal(s.T(), claimedCharge.PodLocationID, claimedChargeFromDB.PodLocationID)
	require.Equal(s.T(), claimedCharge.PodDoorID, claimedChargeFromDB.PodDoorID)
	require.Equal(s.T(), claimedCharge.AuthoriserID, claimedChargeFromDB.AuthoriserID)
	require.False(s.T(), claimedChargeFromDB.BillingAccountID.Valid)
	require.Equal(s.T(), *claimedCharge.BillingEventID, claimedChargeFromDB.BillingEventID.Int32)
}

func (s *PodadminTestSuite) TestInsertClaimedChargeWithNoBillingEvent() {
	claimedCharge := &chargeauthorisation.ClaimedCharge{
		PodLocationID:    uint32(1),
		PodDoorID:        uint32(1),
		AuthoriserID:     int32(1),
		BillingAccountID: ptr.To(int32(1)),
	}

	_, err := s.underTest.InsertClaimedCharge(s.T().Context(), claimedCharge)
	require.NoError(s.T(), err)

	var claimedChargeID int64
	claimedChargeID, err = s.queries.GetLastInsertId(s.T().Context())
	require.NoError(s.T(), err)

	claimedChargeIDUint32, err := numbers.Convert[int64, uint32](claimedChargeID)
	require.NoError(s.T(), err)

	claimedChargeFromDB, err := s.queries.GetPodadminClaimedCharge(s.T().Context(), claimedChargeIDUint32)
	require.NoError(s.T(), err)

	require.Equal(s.T(), claimedCharge.PodLocationID, claimedChargeFromDB.PodLocationID)
	require.Equal(s.T(), claimedCharge.PodDoorID, claimedChargeFromDB.PodDoorID)
	require.Equal(s.T(), claimedCharge.AuthoriserID, claimedChargeFromDB.AuthoriserID)
	require.Equal(s.T(), *claimedCharge.BillingAccountID, claimedChargeFromDB.BillingAccountID.Int32)
	require.False(s.T(), claimedChargeFromDB.BillingEventID.Valid)
}

func (s *PodadminTestSuite) setupMySQLContainer(t *testing.T) *sql.DB {
	t.Helper()
	t = s.T()
	ctx := s.T().Context()

	s.testDB = mysql.NewPodadmin(t)

	podadminDBRoot, err := mysql.NewPasswordDB(s.testDB.GetRootPasswordConfig(ctx), true, false)
	require.NoError(t, err)

	mysql.SeedPodadmin(podadminDBRoot, []string{
		"../../../../../../libs/shared/test/db/podadmin/migrations/_init.sql",
		"../../../../../../libs/shared/go/db/mysql/stub-data/inserts.sql",
	})

	podadminDB, err := mysql.NewPasswordDB(s.testDB.GetPasswordConfig(ctx), true, false)
	require.NoError(t, err)

	s.queries = sqlcpodadmin.New(podadminDB)

	createTestData(ctx, t, s.queries)

	return podadminDB
}

func createTestData(ctx context.Context, t *testing.T, queries *sqlcpodadmin.Queries) {
	t.Helper()

	err := queries.CreatePodadminAddressType(ctx, 1)
	require.NoError(t, err)

	err = queries.CreatePodadminAddress(ctx, sqlcpodadmin.CreatePodadminAddressParams{
		ID:           1,
		BusinessName: "test address",
		TypeID:       1,
	})
	require.NoError(t, err)

	err = queries.CreatePodadminLocation(ctx, sqlcpodadmin.CreatePodadminLocationParams{
		ID:        1,
		Uuid:      uuid.NewString(),
		AddressID: 1,
	})
	require.NoError(t, err)

	err = queries.CreatePodadminBillingAccount(ctx, sqlcpodadmin.CreatePodadminBillingAccountParams{
		ID:  1,
		Uid: uuid.NewString(),
	})
	require.NoError(t, err)

	err = queries.CreatePodadminDoor(ctx, sqlcpodadmin.CreatePodadminDoorParams{
		ID:   1,
		Name: "A",
	})
	require.NoError(t, err)

	err = queries.CreatePodadminAuthoriser(ctx, sqlcpodadmin.CreatePodadminAuthoriserParams{
		ID:  1,
		Uid: uuid.NewString(),
	})
	require.NoError(t, err)

	err = queries.CreatePodadminBillingEvent(ctx, sqlcpodadmin.CreatePodadminBillingEventParams{
		ID: 1,
	})
	require.NoError(t, err)
}
