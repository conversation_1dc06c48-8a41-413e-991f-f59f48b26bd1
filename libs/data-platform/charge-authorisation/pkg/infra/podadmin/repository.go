package podadmin

import (
	"context"
	"database/sql"
	"errors"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	sqlc "experience/libs/data-platform/charge-authorisation/pkg/infra/podadmin-sqlc"
	"log"
)

type repository struct {
	logger       *log.Logger
	db           *sql.DB
	queriesWrite *sqlc.Queries
}

func NewRepository(db *sql.DB, l *log.Logger) (chargeauthorisation.PodadminRepository, error) {
	return &repository{
		logger:       l,
		queriesWrite: sqlc.New(db),
		db:           db,
	}, nil
}

func (r *repository) InsertClaimedCharge(ctx context.Context, claimedCharge *chargeauthorisation.ClaimedCharge) (int64, error) {
	tx, err := r.db.Begin()
	defer func(tx *sql.Tx) {
		rollbackErr := tx.Rollback()
		if rollbackErr != nil && !errors.Is(rollbackErr, sql.ErrTxDone) {
			r.logger.Printf("error rolling back transaction: %v", rollbackErr)
		}
	}(tx)
	if err != nil {
		return 0, err
	}
	withTx := r.queriesWrite.WithTx(tx)
	err = withTx.InsertClaimedCharge(ctx, sqlc.InsertClaimedChargeParams{
		PodLocationID:    claimedCharge.PodLocationID,
		PodDoorID:        claimedCharge.PodDoorID,
		AuthoriserID:     claimedCharge.AuthoriserID,
		BillingAccountID: toNullInt32(claimedCharge.BillingAccountID),
		BillingEventID:   toNullInt32(claimedCharge.BillingEventID),
	})
	if err != nil {
		return 0, err
	}

	id, err := withTx.LastInsertID(ctx)
	if err != nil {
		return 0, err
	}

	err = tx.Commit()
	if err != nil {
		return 0, err
	}

	return id, nil
}

func toNullInt32(field *int32) sql.NullInt32 {
	if field == nil {
		return sql.NullInt32{
			Int32: 0,
			Valid: false,
		}
	}
	return sql.NullInt32{
		Int32: *field,
		Valid: true,
	}
}
