// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: claimed_charge.sql

package sqlcpodadmin

import (
	"context"
	"database/sql"
)

const insertClaimedCharge = `-- name: InsertClaimedCharge :exec
INSERT INTO claimed_charges (pod_location_id, pod_door_id, created_at, updated_at, authoriser_id, billing_account_id, billing_event_id)
VALUES (?, ?, NOW(), NOW(), ?, ?, ?)
`

type InsertClaimedChargeParams struct {
	PodLocationID    uint32        `json:"pod_location_id"`
	PodDoorID        uint32        `json:"pod_door_id"`
	AuthoriserID     int32         `json:"authoriser_id"`
	BillingAccountID sql.NullInt32 `json:"billing_account_id"`
	BillingEventID   sql.NullInt32 `json:"billing_event_id"`
}

func (q *Queries) InsertClaimedCharge(ctx context.Context, arg InsertClaimedChargeParams) error {
	_, err := q.db.ExecContext(ctx, insertClaimedCharge,
		arg.PodLocationID,
		arg.PodDoorID,
		arg.AuthoriserID,
		arg.BillingAccountID,
		arg.BillingEventID,
	)
	return err
}

const lastInsertID = `-- name: LastInsertID :one
SELECT LAST_INSERT_ID() AS id
`

func (q *Queries) LastInsertID(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, lastInsertID)
	var id int64
	err := row.Scan(&id)
	return id, err
}
