{"name": "data-platform-charge-authorisation", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/data-platform/charge-authorisation", "tags": ["data-platform"], "targets": {"test": {"executor": "@nx-go/nx-go:test", "options": {"race": true}}, "generate-sources": {"executor": "nx:run-commands", "options": {"commands": ["go run libs/shared/go/sqlc/generate.go -config libs/data-platform/charge-authorisation/sqlc.yaml", "go run libs/shared/go/sqlc/generate.go -config libs/data-platform/charge-authorisation/pkg/infra/sqlc.yaml", "mockgen -destination libs/data-platform/charge-authorisation/mock/ports.go -source=libs/data-platform/charge-authorisation/ports.go"], "parallel": false}}}}