package chargeauthorisation_test

import (
	"bytes"
	"context"
	"errors"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	mock_chargeauthorisation "experience/libs/data-platform/charge-authorisation/mock"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"log"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"
)

type validateClaim struct {
	isValid       bool
	claimedCharge *chargeauthorisation.ClaimedCharge
	err           error
}

func TestDecoratorValidateRFIDClaim(t *testing.T) {
	validUUID := uuid.New()
	tests := []struct {
		name                        string
		expectedGetGroupForRFID     *chargeauthorisation.ClaimedCharge
		expectedGetGroupForUnitPpid *chargeauthorisation.ClaimedCharge
		expectedValidateRFIDClaim   validateClaim
		claimID                     int64
		expectedError               error
		expectedPodadminError       error
		expectedReturnClaimedCharge *chargeauthorisation.ClaimedCharge
		expectedLogMessage          string
	}{
		{
			name:    "return no error when valid rfid claim",
			claimID: 1,
			expectedGetGroupForRFID: &chargeauthorisation.ClaimedCharge{
				GroupUUID: &validUUID,
			},
			expectedGetGroupForUnitPpid: &chargeauthorisation.ClaimedCharge{
				GroupUUID: &validUUID,
			},
			expectedValidateRFIDClaim: validateClaim{
				isValid: true,
				claimedCharge: &chargeauthorisation.ClaimedCharge{
					GroupUUID: &validUUID,
				},
			},
			expectedReturnClaimedCharge: &chargeauthorisation.ClaimedCharge{
				GroupUUID: &validUUID,
				ID:        utils.AuthorisedEventUUIDFromNumericID(1),
			},
		},
		{
			name: "return error when invalid rfid claim",
			expectedGetGroupForRFID: &chargeauthorisation.ClaimedCharge{
				GroupUUID: ptr.To(uuid.New()),
			},
			expectedGetGroupForUnitPpid: &chargeauthorisation.ClaimedCharge{
				GroupUUID: ptr.To(uuid.New()),
			},
			expectedValidateRFIDClaim: validateClaim{
				isValid: false,
				err:     nil,
			},
			expectedLogMessage: "not inserting claimed charge as invalid RFID claim",
		},
		{
			name: "return error when cannot insert claimed charge",
			expectedGetGroupForRFID: &chargeauthorisation.ClaimedCharge{
				GroupUUID: &validUUID,
			},
			expectedGetGroupForUnitPpid: &chargeauthorisation.ClaimedCharge{
				GroupUUID: &validUUID,
			},
			expectedValidateRFIDClaim: validateClaim{
				isValid: true,
				claimedCharge: &chargeauthorisation.ClaimedCharge{
					GroupUUID: &validUUID,
				},
			},
			expectedPodadminError: errors.New("my test error"),
			expectedLogMessage:    "failed to insert claimed charge: my test error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockRepository := mock_chargeauthorisation.NewMockRepository(ctrl)
			mockService := mock_chargeauthorisation.NewMockService(ctrl)
			mockPodadminRepository := mock_chargeauthorisation.NewMockPodadminRepository(ctrl)

			var buff bytes.Buffer
			mockLogger := log.New(&buff, "[commands_test]", log.Lmsgprefix)

			decoratedService := chargeauthorisation.NewPodadminClaimedChargeDecorator(mockLogger, mockService, mockPodadminRepository)

			mockRepository.EXPECT().GetGroupForRFID(gomock.Any(), gomock.Any()).AnyTimes().Return(tt.expectedGetGroupForRFID, nil)
			mockRepository.EXPECT().GetGroupForUnitPpid(gomock.Any(), gomock.Any()).AnyTimes().Return(tt.expectedGetGroupForUnitPpid, nil)

			mockService.EXPECT().ValidateRfidClaim(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.expectedValidateRFIDClaim.isValid, tt.expectedValidateRFIDClaim.claimedCharge, tt.expectedValidateRFIDClaim.err)
			mockPodadminRepository.EXPECT().InsertClaimedCharge(gomock.Any(), gomock.Any()).AnyTimes().Return(tt.claimID, tt.expectedPodadminError)

			_, claimedCharge, err := decoratedService.ValidateRfidClaim(context.Background(), "test-rfid-tag", "test-charger-id", 1)

			assert.Equal(t, tt.expectedReturnClaimedCharge, claimedCharge)

			logMessages := buff.String()
			if tt.expectedLogMessage != "" {
				require.Contains(t, logMessages, tt.expectedLogMessage)
			} else {
				require.Empty(t, buff.String())
			}

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
				return
			}
			if tt.expectedPodadminError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedPodadminError, err)
				return
			}
			assert.NoError(t, err)
		})
	}
}

func TestDecoratorValidateOCPIClaim(t *testing.T) {
	tests := []struct {
		name                  string
		validateClaimResponse validateClaim
		expectedClaim         *chargeauthorisation.ClaimedCharge
		expectedAuthorised    bool
		expectedLog           string
		expectedError         error
		insertClaimErr        error
		claimID               int64
	}{
		{
			name: "return no error when valid ocpi claim",
			validateClaimResponse: validateClaim{
				isValid: true,
				claimedCharge: &chargeauthorisation.ClaimedCharge{
					AuthoriserID:  int32(0),
					PodLocationID: uint32(0),
					PodDoorID:     uint32(1),
				},
			},
			expectedClaim: &chargeauthorisation.ClaimedCharge{
				AuthoriserID:  int32(0),
				PodLocationID: uint32(0),
				PodDoorID:     uint32(1),
				ID:            utils.AuthorisedEventUUIDFromNumericID(1),
			},
			expectedAuthorised: true,
			claimID:            1,
		},
		{
			name: "return error when validating ocpi claim",
			validateClaimResponse: validateClaim{
				err: errors.New("my test error"),
			},
			expectedError: errors.New("my test error"),
			expectedLog:   "failed to validate OCPI claim: my test error",
		},
		{
			name: "return error when cannot insert claimed charge",
			validateClaimResponse: validateClaim{
				isValid:       true,
				claimedCharge: &chargeauthorisation.ClaimedCharge{},
			},
			insertClaimErr: errors.New("insertion error"),
			expectedError:  errors.New("insertion error"),
			expectedLog:    "failed to insert claimed charge: insertion error",
		},
		{
			name: "return empty claimed charge when invalid ocpi claim",
			validateClaimResponse: validateClaim{
				isValid: false,
			},
			expectedAuthorised: false,
			expectedLog:        "not inserting claimed charge as invalid OCPI claim for charger: test-charger-id",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockService := mock_chargeauthorisation.NewMockService(ctrl)
			mockPodadminRepository := mock_chargeauthorisation.NewMockPodadminRepository(ctrl)

			var buff bytes.Buffer
			mockLogger := log.New(&buff, fmt.Sprintf("[%s]", t.Name()), log.Lmsgprefix)

			decoratedService := chargeauthorisation.NewPodadminClaimedChargeDecorator(mockLogger, mockService, mockPodadminRepository)

			mockService.EXPECT().ValidateOcpiClaim(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.validateClaimResponse.isValid, tt.validateClaimResponse.claimedCharge, tt.validateClaimResponse.err)
			mockPodadminRepository.EXPECT().InsertClaimedCharge(gomock.Any(), gomock.Any()).AnyTimes().Return(tt.claimID, tt.insertClaimErr)

			actualAuthorised, actualClaim, actualError := decoratedService.ValidateOcpiClaim(context.Background(), "test-ocpi-tag", "test-charger-id", 1)

			require.Equal(t, tt.expectedAuthorised, actualAuthorised)
			require.Equal(t, tt.expectedClaim, actualClaim)
			require.Equal(t, tt.expectedError, actualError)

			require.Contains(t, buff.String(), tt.expectedLog)
		})
	}
}

func TestDecoratorValidateGuestClaim(t *testing.T) {
	tests := []struct {
		name                  string
		validateClaimResponse validateClaim
		expectedClaim         *chargeauthorisation.ClaimedCharge
		expectedAuthorised    bool
		expectedLog           string
		expectedError         error
		insertClaimErr        error
		claimID               int64
	}{
		{
			name: "return no error when valid guest claim",
			validateClaimResponse: validateClaim{
				isValid: true,
				claimedCharge: &chargeauthorisation.ClaimedCharge{
					BillingEventID: ptr.To(int32(1)),
					PodLocationID:  uint32(1),
					AuthoriserID:   1,
				},
			},
			expectedClaim: &chargeauthorisation.ClaimedCharge{
				ID:             utils.AuthorisedEventUUIDFromNumericID(1),
				BillingEventID: ptr.To(int32(1)),
				PodLocationID:  uint32(1),
				AuthoriserID:   1,
			},
			expectedAuthorised: true,
			claimID:            1,
		},
		{
			name: "return error when validating guest claim",
			validateClaimResponse: validateClaim{
				isValid: false,
				err:     errors.New("my test error"),
			},
			expectedError: errors.New("my test error"),
			expectedLog:   "failed to validate Guest claim: my test error",
		},
		{
			name: "return error when cannot insert claimed charge",
			validateClaimResponse: validateClaim{
				isValid:       true,
				claimedCharge: &chargeauthorisation.ClaimedCharge{},
			},
			insertClaimErr: errors.New("insertion error"),
			expectedError:  errors.New("insertion error"),
			expectedLog:    "failed to insert claimed charge: insertion error",
		},
		{
			name:               "return empty claimed charge when invalid guest claim",
			expectedAuthorised: false,
			expectedLog:        "not inserting claimed charge as invalid Guest claim",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockService := mock_chargeauthorisation.NewMockService(ctrl)
			mockPodadminRepository := mock_chargeauthorisation.NewMockPodadminRepository(ctrl)

			var buff bytes.Buffer
			mockLogger := log.New(&buff, fmt.Sprintf("[%s]", t.Name()), log.Lmsgprefix)

			decoratedService := chargeauthorisation.NewPodadminClaimedChargeDecorator(mockLogger, mockService, mockPodadminRepository)

			mockService.EXPECT().ValidateGuestClaim(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.validateClaimResponse.isValid, tt.validateClaimResponse.claimedCharge, tt.validateClaimResponse.err)
			mockPodadminRepository.EXPECT().InsertClaimedCharge(gomock.Any(), gomock.Any()).AnyTimes().Return(tt.claimID, tt.insertClaimErr)

			actualAuthorised, actualClaim, actualError := decoratedService.ValidateGuestClaim(context.Background(), "test-guest-token", "test-charger-id", 1)

			require.Equal(t, tt.expectedAuthorised, actualAuthorised)
			require.Equal(t, tt.expectedClaim, actualClaim)
			require.Equal(t, tt.expectedError, actualError)

			require.Contains(t, buff.String(), tt.expectedLog)
		})
	}
}
