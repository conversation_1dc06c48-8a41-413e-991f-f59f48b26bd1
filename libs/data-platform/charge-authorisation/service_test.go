package chargeauthorisation_test

import (
	"context"
	"encoding/json"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	mock_chargeauthorisation "experience/libs/data-platform/charge-authorisation/mock"
	transactionsapiclient "experience/libs/shared/go/external/transactions-api-client/src"
	"log"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"
)

func Test_service_ValidateRfidClaim(t *testing.T) {
	id := uuid.New()

	tests := []struct {
		name              string
		want              bool
		mockGroupForRfid  *chargeauthorisation.ClaimedCharge
		mockGroupForPpid  *chargeauthorisation.ClaimedCharge
		mockRfidErr       error
		mockPpidErr       error
		wantErr           error
		wantClaimedCharge *chargeauthorisation.ClaimedCharge
	}{
		{
			name:              "valid rfid claim",
			mockGroupForRfid:  &chargeauthorisation.ClaimedCharge{GroupUUID: &id, PodDoorID: 1},
			mockGroupForPpid:  &chargeauthorisation.ClaimedCharge{GroupUUID: &id, PodDoorID: 1},
			want:              true,
			wantClaimedCharge: &chargeauthorisation.ClaimedCharge{GroupUUID: &id, PodDoorID: 1},
		},
		{
			name:              "charger and rfid token belong to different groups",
			mockGroupForRfid:  &chargeauthorisation.ClaimedCharge{GroupUUID: ptr.To(uuid.New())},
			mockGroupForPpid:  &chargeauthorisation.ClaimedCharge{GroupUUID: ptr.To(uuid.New())},
			want:              false,
			wantClaimedCharge: &chargeauthorisation.ClaimedCharge{PodDoorID: 1},
		},
		{
			name:             "authoriser not found",
			mockGroupForRfid: nil,
			want:             false,
		},
		{
			name:             "rfid not assigned to group",
			mockGroupForRfid: &chargeauthorisation.ClaimedCharge{GroupUUID: nil},
			want:             false,
		},
		{
			name:             "error getting group by ppid",
			mockGroupForRfid: &chargeauthorisation.ClaimedCharge{GroupUUID: &id},
			mockPpidErr:      chargeauthorisation.ErrChargerNotFound,
			want:             false,
			wantErr:          chargeauthorisation.ErrChargerNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockRepository := mock_chargeauthorisation.NewMockRepository(ctrl)
			s := chargeauthorisation.NewService(log.Default(), mockRepository, nil)

			mockRepository.EXPECT().GetGroupForRFID(gomock.Any(), gomock.Any()).AnyTimes().Return(tt.mockGroupForRfid, tt.mockRfidErr)
			mockRepository.EXPECT().GetGroupForUnitPpid(gomock.Any(), gomock.Any()).AnyTimes().Return(tt.mockGroupForPpid, tt.mockPpidErr)

			got, claimedCharge, gotErr := s.ValidateRfidClaim(context.Background(), "test-rfid-tag", "test-charge-id", 1)
			require.Equal(t, tt.want, got)
			if tt.wantClaimedCharge == nil {
				require.Equal(t, &chargeauthorisation.ClaimedCharge{}, claimedCharge)
			} else {
				require.Equal(t, tt.wantClaimedCharge, claimedCharge)
			}
			require.Equal(t, tt.wantErr, gotErr)
		})
	}
}

func Test_service_ValidOcpiClaim(t *testing.T) {
	tests := []struct {
		name                  string
		want                  bool
		mockAuthoriserForOcpi *chargeauthorisation.ClaimedCharge
		mockOcpiErr           error
		wantErr               error
		wantClaimedCharge     *chargeauthorisation.ClaimedCharge
		chargerID             string
		door                  struct {
			doorID   uint32
			doorName string
		}
		transactionsAPIStatusCode int
	}{
		{
			name:                  "valid ocpi claim - transaction created",
			mockAuthoriserForOcpi: &chargeauthorisation.ClaimedCharge{AuthoriserID: 1, PodDoorID: 1},
			want:                  true,
			chargerID:             "PG-123",
			door: struct {
				doorID   uint32
				doorName string
			}{
				1,
				"A",
			},
			transactionsAPIStatusCode: http.StatusCreated,
		},
		{
			name:                  "valid ocpi claim - transaction OK",
			mockAuthoriserForOcpi: &chargeauthorisation.ClaimedCharge{AuthoriserID: 1, PodDoorID: 1},
			want:                  true,
			chargerID:             "PG-123",
			door: struct {
				doorID   uint32
				doorName string
			}{
				1,
				"A",
			},
			transactionsAPIStatusCode: http.StatusOK,
		},
		{
			name:                  "valid ocpi claim - transaction gone",
			mockAuthoriserForOcpi: &chargeauthorisation.ClaimedCharge{AuthoriserID: 1, PodDoorID: 1},
			want:                  true,
			chargerID:             "PG-123",
			door: struct {
				doorID   uint32
				doorName string
			}{
				1,
				"A",
			},
			transactionsAPIStatusCode: http.StatusGone,
		},
		{
			name:                  "transaction not started",
			mockAuthoriserForOcpi: &chargeauthorisation.ClaimedCharge{AuthoriserID: 1, PodDoorID: 1},
			want:                  false,
			wantErr:               chargeauthorisation.ErrTransactionNotStarted,
			chargerID:             "PG-123",
			door: struct {
				doorID   uint32
				doorName string
			}{
				1,
				"A",
			},
			transactionsAPIStatusCode: http.StatusBadGateway,
		},
		{
			name:                  "authoriser not found",
			mockAuthoriserForOcpi: &chargeauthorisation.ClaimedCharge{},
			mockOcpiErr:           chargeauthorisation.ErrAuthoriserNotFound,
			want:                  false,
			wantErr:               chargeauthorisation.ErrAuthoriserNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
				require.Equal(t, http.MethodPost, req.Method)
				require.Equal(t, "/transactions/start", req.RequestURI)
				var requestBody transactionsapiclient.HandlersStartTransactionRequestBody
				err := json.NewDecoder(req.Body).Decode(&requestBody)
				require.NoError(t, err)
				require.Equal(t, strconv.FormatInt(int64(tt.mockAuthoriserForOcpi.AuthoriserID), 10), *requestBody.AuthoriserId)
				require.Equal(t, tt.chargerID, *requestBody.ChargingStationId)
				require.Equal(t, tt.door.doorName, *requestBody.Door)
				require.Equal(t, "xdp-api", *requestBody.ClientRef)
				res.Header().Set("Content-Type", "application/json")
				res.WriteHeader(tt.transactionsAPIStatusCode)
			}))
			ctrl := gomock.NewController(t)
			mockRepository := mock_chargeauthorisation.NewMockRepository(ctrl)
			testTransactionsAPI, _ := transactionsapiclient.NewTransactionsAPIClient(true, testServer.URL)
			s := chargeauthorisation.NewService(log.Default(), mockRepository, testTransactionsAPI)

			mockRepository.EXPECT().GetAuthoriserForOCPIToken(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.mockAuthoriserForOcpi, tt.mockOcpiErr)

			got, claimedCharge, gotErr := s.ValidateOcpiClaim(context.Background(), "test-ocpi-tag", tt.chargerID, tt.door.doorID)
			require.Equal(t, tt.want, got)
			if tt.wantClaimedCharge == nil {
				require.Equal(t, &chargeauthorisation.ClaimedCharge{AuthoriserID: tt.mockAuthoriserForOcpi.AuthoriserID, PodDoorID: tt.mockAuthoriserForOcpi.PodDoorID}, claimedCharge)
			} else {
				require.Equal(t, tt.wantClaimedCharge, claimedCharge)
			}
			require.Equal(t, tt.wantErr, gotErr)
		})
	}
}

func Test_service_ValidateGuestClaim(t *testing.T) {
	tests := []struct {
		name                      string
		guestToken                string
		want                      bool
		wantClaimedCharge         chargeauthorisation.ClaimedCharge
		wantErr                   error
		wantBillingEventErr       error
		wantLocationError         error
		guestAuthoriserErr        error
		chargerID                 string
		transactionsAPIStatusCode int
		door                      struct {
			doorID   uint32
			doorName string
		}
	}{
		{
			name:       "Valid guest claim- transaction created",
			want:       true,
			guestToken: "123",
			wantClaimedCharge: chargeauthorisation.ClaimedCharge{
				PodLocationID:  uint32(123),
				BillingEventID: ptr.To(int32(123)),
				AuthoriserID:   123,
				PodDoorID:      1,
			},
			chargerID: "PG-1234",
			door: struct {
				doorID   uint32
				doorName string
			}{
				1,
				"A",
			},
			transactionsAPIStatusCode: http.StatusCreated,
		},
		{
			name:       "Valid guest claim- transaction OK",
			want:       true,
			guestToken: "123",
			wantClaimedCharge: chargeauthorisation.ClaimedCharge{
				PodLocationID:  uint32(123),
				BillingEventID: ptr.To(int32(123)),
				AuthoriserID:   123,
				PodDoorID:      1,
			},
			chargerID: "PG-1234",
			door: struct {
				doorID   uint32
				doorName string
			}{
				1,
				"A",
			},
			transactionsAPIStatusCode: http.StatusOK,
		},
		{
			name:       "Valid guest claim- transaction gone",
			want:       true,
			guestToken: "123",
			wantClaimedCharge: chargeauthorisation.ClaimedCharge{
				PodLocationID:  uint32(123),
				BillingEventID: ptr.To(int32(123)),
				AuthoriserID:   123,
				PodDoorID:      1,
			},
			chargerID: "PG-1234",
			door: struct {
				doorID   uint32
				doorName string
			}{
				1,
				"A",
			},
			transactionsAPIStatusCode: http.StatusGone,
		},
		{
			name:       "transaction not started",
			want:       false,
			guestToken: "123",
			wantClaimedCharge: chargeauthorisation.ClaimedCharge{
				PodLocationID:  uint32(123),
				BillingEventID: ptr.To(int32(123)),
				AuthoriserID:   123,
				PodDoorID:      1,
			},
			wantErr:   chargeauthorisation.ErrTransactionNotStarted,
			chargerID: "PG-1234",
			door: struct {
				doorID   uint32
				doorName string
			}{
				1,
				"A",
			},
			transactionsAPIStatusCode: http.StatusBadGateway,
		},
		{
			name:                "Invalid guest claim - guest token not parsable to int",
			want:                false,
			guestToken:          "invalid-token",
			wantBillingEventErr: chargeauthorisation.ErrAuthoriserNotFound,
			wantErr:             chargeauthorisation.ErrAuthoriserNotFound,
		},
		{
			name:              "Invalid guest claim - pod location not found",
			want:              false,
			guestToken:        "123",
			wantLocationError: chargeauthorisation.ErrChargerNotFound,
			wantErr:           chargeauthorisation.ErrChargerNotFound,
		},
		{
			name:              "Invalid guest claim - location not public",
			want:              false,
			guestToken:        "123",
			wantLocationError: chargeauthorisation.ErrLocationNotPublic,
			wantErr:           nil,
		},
		{
			name:                "Invalid guest claim - billing event not found",
			want:                false,
			guestToken:          "123",
			wantBillingEventErr: chargeauthorisation.ErrAuthoriserNotFound,
			wantErr:             chargeauthorisation.ErrAuthoriserNotFound,
		},
		{
			name:               "Invalid guest claim - guest authoriser not found",
			want:               false,
			guestToken:         "123",
			guestAuthoriserErr: chargeauthorisation.ErrAuthoriserNotFound,
			wantErr:            chargeauthorisation.ErrAuthoriserNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
				require.Equal(t, http.MethodPost, req.Method)
				require.Equal(t, "/transactions/start", req.RequestURI)
				var requestBody transactionsapiclient.HandlersStartTransactionRequestBody
				err := json.NewDecoder(req.Body).Decode(&requestBody)
				require.NoError(t, err)
				require.Equal(t, strconv.FormatInt(int64(tt.wantClaimedCharge.AuthoriserID), 10), *requestBody.AuthoriserId)
				require.Equal(t, tt.chargerID, *requestBody.ChargingStationId)
				require.Equal(t, tt.door.doorName, *requestBody.Door)
				require.Equal(t, "xdp-api", *requestBody.ClientRef)
				res.Header().Set("Content-Type", "application/json")
				res.WriteHeader(tt.transactionsAPIStatusCode)
			}))
			ctrl := gomock.NewController(t)
			mockRepository := mock_chargeauthorisation.NewMockRepository(ctrl)
			testTransactionsAPI, _ := transactionsapiclient.NewTransactionsAPIClient(true, testServer.URL)
			s := chargeauthorisation.NewService(log.Default(), mockRepository, testTransactionsAPI)

			wantClaimedCharge := &chargeauthorisation.ClaimedCharge{}
			if tt.wantClaimedCharge != (chargeauthorisation.ClaimedCharge{}) {
				wantClaimedCharge = &tt.wantClaimedCharge
			}

			mockRepository.EXPECT().GetLocationForUnitPpid(gomock.Any(), gomock.Any()).AnyTimes().Return(wantClaimedCharge, tt.wantLocationError)
			mockRepository.EXPECT().GetBillingEventByID(gomock.Any(), gomock.Any()).AnyTimes().Return(wantClaimedCharge, tt.wantBillingEventErr)

			if tt.wantLocationError == nil && tt.wantBillingEventErr == nil {
				mockRepository.EXPECT().GetGuestAuthoriserID(gomock.Any()).AnyTimes().Return(int64(tt.wantClaimedCharge.AuthoriserID), tt.guestAuthoriserErr)
			}

			got, claimedCharge, gotErr := s.ValidateGuestClaim(context.Background(), tt.guestToken, tt.chargerID, tt.door.doorID)
			require.Equal(t, tt.want, got)
			require.Equal(t, &tt.wantClaimedCharge, claimedCharge)
			require.Equal(t, tt.wantErr, gotErr)
		})
	}
}
