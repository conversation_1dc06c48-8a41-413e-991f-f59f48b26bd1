// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: authorisers.sql

package sqlc

import (
	"context"
	"database/sql"
)

const getAuthoriserIDForOCPIToken = `-- name: GetAuthoriserIDForOCPIToken :one
SELECT a.id
FROM podpoint.authorisers a
WHERE a.uid = $1
  AND a.type = 'ocpi'
  AND a.deleted_at IS NULL
`

func (q *Queries) GetAuthoriserIDForOCPIToken(ctx context.Context, uid string) (int64, error) {
	row := q.db.QueryRowContext(ctx, getAuthoriserIDForOCPIToken, uid)
	var id int64
	err := row.Scan(&id)
	return id, err
}

const getBillingEventByID = `-- name: GetBillingEventByID :one
SELECT id
FROM podpoint.billing_events
WHERE id = $1
  AND deleted_at IS NULL
`

func (q *Queries) GetBillingEventByID(ctx context.Context, id int64) (int64, error) {
	row := q.db.QueryRowContext(ctx, getBillingEventByID, id)
	err := row.Scan(&id)
	return id, err
}

const getGroupForRfid = `-- name: GetGroupForRfid :many
SELECT a.id authoriser_id, a.group_uid, gba.billing_account_id
FROM podpoint.authorisers a
LEFT JOIN podpoint.groups g
  ON a.group_uid = g.uid
LEFT JOIN podpoint.group_billing_accounts gba
  ON gba.group_id = g.id AND gba.deleted_at IS NULL
WHERE type = 'rfid'
  AND a.uid = $1
`

type GetGroupForRfidRow struct {
	AuthoriserID     int64          `json:"authoriser_id"`
	GroupUid         sql.NullString `json:"group_uid"`
	BillingAccountID sql.NullInt64  `json:"billing_account_id"`
}

func (q *Queries) GetGroupForRfid(ctx context.Context, uid string) ([]GetGroupForRfidRow, error) {
	rows, err := q.db.QueryContext(ctx, getGroupForRfid, uid)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetGroupForRfidRow
	for rows.Next() {
		var i GetGroupForRfidRow
		if err := rows.Scan(&i.AuthoriserID, &i.GroupUid, &i.BillingAccountID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getGroupIdByPpid = `-- name: GetGroupIdByPpid :one
SELECT g.uid, pl.id pod_location_id
FROM podpoint.pod_units pu
INNER JOIN podpoint.pod_locations pl
  ON pu.id = pl.unit_id
       AND pl.deleted_at IS NULL
INNER JOIN podpoint.pod_addresses pa
  ON pl.address_id = pa.id
       AND pa.deleted_at IS NULL
INNER JOIN podpoint.groups g
  ON g.id = pa.group_id
       AND g.deleted_at IS NULL
WHERE pu.ppid = $1 AND pu.deleted_at IS NULL
`

type GetGroupIdByPpidRow struct {
	Uid           string `json:"uid"`
	PodLocationID int64  `json:"pod_location_id"`
}

func (q *Queries) GetGroupIdByPpid(ctx context.Context, ppid string) (GetGroupIdByPpidRow, error) {
	row := q.db.QueryRowContext(ctx, getGroupIdByPpid, ppid)
	var i GetGroupIdByPpidRow
	err := row.Scan(&i.Uid, &i.PodLocationID)
	return i, err
}

const getGuestAuthoriserID = `-- name: GetGuestAuthoriserID :one
SELECT id
FROM podpoint.authorisers
WHERE type = 'guest'
  AND deleted_at IS NULL
`

func (q *Queries) GetGuestAuthoriserID(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, getGuestAuthoriserID)
	var id int64
	err := row.Scan(&id)
	return id, err
}

const getLocationForPPID = `-- name: GetLocationForPPID :one
SELECT pl.id, pl.is_public
FROM podpoint.pod_units pu
       INNER JOIN podpoint.pod_locations pl
                  ON pu.id = pl.unit_id
                    AND pl.deleted_at IS NULL
WHERE pu.ppid = $1 AND pu.deleted_at IS NULL
`

type GetLocationForPPIDRow struct {
	ID       int64 `json:"id"`
	IsPublic int16 `json:"is_public"`
}

func (q *Queries) GetLocationForPPID(ctx context.Context, ppid string) (GetLocationForPPIDRow, error) {
	row := q.db.QueryRowContext(ctx, getLocationForPPID, ppid)
	var i GetLocationForPPIDRow
	err := row.Scan(&i.ID, &i.IsPublic)
	return i, err
}

const getLocationIDForPPID = `-- name: GetLocationIDForPPID :one
SELECT pl.id pod_location_id
FROM podpoint.pod_units pu
INNER JOIN podpoint.pod_locations pl
          ON pu.id = pl.unit_id
            AND pl.deleted_at IS NULL
WHERE pu.ppid = $1 AND pu.deleted_at IS NULL
`

func (q *Queries) GetLocationIDForPPID(ctx context.Context, ppid string) (int64, error) {
	row := q.db.QueryRowContext(ctx, getLocationIDForPPID, ppid)
	var pod_location_id int64
	err := row.Scan(&pod_location_id)
	return pod_location_id, err
}
