package api

import (
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	errormapper "experience/libs/shared/go/http"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestHandleError(t *testing.T) {
	tests := []struct {
		name     string
		toHandle error
		want     error
	}{
		{
			name:     "charge expensed maps to ChargeExpensedGoaError",
			toHandle: chargeauthorisation.ErrAuthoriserNotFound,
			want:     AuthoriserNotFoundGoaError,
		},
		{
			name:     "charge not found maps to ChargeNotFoundGoaError",
			toHandle: chargeauthorisation.ErrChargerNotFound,
			want:     ChargerNotFoundGoaError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := errormapper.NewErrorMapper(ErrorMap).MapError(tt.toHandle)
			require.Error(t, got)
			require.Equal(t, tt.want.Error(), got.Error())
		})
	}
}
