package api

import (
	"context"
	contract "experience/libs/data-platform/api/contract/gen/charge_authorisation"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	"experience/libs/shared/go/http"
	"log"
	"strconv"

	"k8s.io/utils/ptr"
)

const (
	AuthorisationMethodRfid  contract.AuthorisationMethod = "rfid"
	AuthorisationMethodOcpi  contract.AuthorisationMethod = "ocpi"
	AuthorisationMethodGuest contract.AuthorisationMethod = "guest"
)

type serviceFunction func(context.Context, string, string, uint32) (bool, *chargeauthorisation.ClaimedCharge, error)

var doorMap = map[string]uint32{
	"A": 1,
	"B": 2,
	"C": 3,
}

type chargeAuthSvc struct {
	logger  *log.Logger
	service chargeauthorisation.Service
	mapper  http.ErrorMapper
}

func NewChargeAuthorisation(logger *log.Logger, service chargeauthorisation.Service, mapper http.ErrorMapper) contract.Service {
	return &chargeAuthSvc{logger, service, mapper}
}

func (s chargeAuthSvc) AuthoriseCharge(ctx context.Context, payload *contract.AuthoriseChargePayload) (res *contract.ChargeAuthorisationResponse, err error) {
	serviceFunctionRouter := map[contract.AuthorisationMethod]serviceFunction{
		AuthorisationMethodRfid:  s.service.ValidateRfidClaim,
		AuthorisationMethodOcpi:  s.service.ValidateOcpiClaim,
		AuthorisationMethodGuest: s.service.ValidateGuestClaim,
	}
	ppid := payload.ChargerID
	authToken := payload.Token
	door := payload.Door

	if payload.AuthorisationMethod == AuthorisationMethodGuest {
		_, parseErr := strconv.ParseInt(payload.Token, 10, 64)
		if parseErr != nil {
			return nil, s.mapper.MapError(chargeauthorisation.ErrAuthoriserNotFound)
		}
	}

	authorised, claimedCharge, err := serviceFunctionRouter[payload.AuthorisationMethod](ctx, authToken, ppid, doorMap[door])
	if err != nil {
		return nil, s.mapper.MapError(err)
	}

	response := contract.ChargeAuthorisationResponse{
		Authorised: authorised,
	}
	response.Meta = (*contract.Meta)(http.PopulateMetaParams(*payload))

	if authorised {
		response.ID = ptr.To(claimedCharge.ID.String())
	}
	return &response, nil
}
