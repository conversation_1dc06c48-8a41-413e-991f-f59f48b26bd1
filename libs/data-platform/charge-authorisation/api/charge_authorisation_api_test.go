package api

import (
	"encoding/json"
	chargeauthorisation "experience/libs/data-platform/api/contract/gen/charge_authorisation"
	"experience/libs/data-platform/api/contract/gen/http/charge_authorisation/server"
	chargeauthorisation2 "experience/libs/data-platform/charge-authorisation"
	mock_chargeauthorisation "experience/libs/data-platform/charge-authorisation/mock"
	errormapper "experience/libs/shared/go/http"
	"io"
	"log"
	"net/http"
	"testing"

	"github.com/google/uuid"

	"github.com/ikawaha/goahttpcheck"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func Test_chargeAuthorisation(t *testing.T) {
	chargeAuthRfidURL := "/charge-authorisations/rfid"
	chargeAuthOcpiURL := "/charge-authorisations/ocpi"
	chargeAuthGuestURL := "/charge-authorisations/guest"

	tests := []struct {
		name                   string
		path                   string
		requestBody            chargeauthorisation.AuthoriseChargePayload
		wantStatus             int
		wantAuthorised         bool
		mockResponse           func() (bool, *chargeauthorisation2.ClaimedCharge, error)
		rfidFunctionCallCount  int
		ocpiFunctionCallCount  int
		guestFunctionCallCount int
	}{
		{
			name: "status OK: rfid authorised",
			path: chargeAuthRfidURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "A4BIU31S0F",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:            http.StatusOK,
			wantAuthorised:        true,
			rfidFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return true, &chargeauthorisation2.ClaimedCharge{}, nil
			},
		},
		{
			name: "status OK: rfid not authorised, but not an error",
			path: chargeAuthRfidURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "A4BIU31S0F",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:            http.StatusOK,
			wantAuthorised:        false,
			rfidFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, nil, nil
			},
		},
		{
			name: "status OK: rfid not authorised because rfid token not found, but not an error",
			path: chargeAuthRfidURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "A4BIU31S0F",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:            http.StatusOK,
			wantAuthorised:        false,
			rfidFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, nil, nil
			},
		},
		{
			name: "status 404: rfid not authorised because charger not found",
			path: chargeAuthRfidURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "B4BIU31S0F",
				ChargerID: "PSL-1234",
				Door:      "A",
			},
			wantStatus:            http.StatusNotFound,
			wantAuthorised:        false,
			rfidFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, nil, chargeauthorisation2.ErrChargerNotFound
			},
		},
		{
			name: "bad request: rfid missing door",
			path: chargeAuthRfidURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "B4BIU31S0F",
				ChargerID: "PSL-1234",
			},
			wantStatus: http.StatusBadRequest,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return true, &chargeauthorisation2.ClaimedCharge{}, nil
			},
		},
		{
			name: "bad request: rfid invalid enum door value",
			path: chargeAuthRfidURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "B4BIU31S0F",
				ChargerID: "PSL-1234",
				Door:      "D",
			},
			wantStatus: http.StatusBadRequest,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return true, &chargeauthorisation2.ClaimedCharge{}, nil
			},
		},
		{
			name: "status OK: ocpi authorised",
			path: chargeAuthOcpiURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "A4BIU31S0F",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:            http.StatusOK,
			wantAuthorised:        true,
			ocpiFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return true, &chargeauthorisation2.ClaimedCharge{
					ID: uuid.New(),
				}, nil
			},
		},
		{
			name: "status 500: ocpi transaction not started",
			path: chargeAuthOcpiURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "A4BIU31S0F",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:            http.StatusInternalServerError,
			wantAuthorised:        false,
			ocpiFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, nil, chargeauthorisation2.ErrTransactionNotStarted
			},
		},
		{
			name: "status 404: ocpi authoriser not found",
			path: chargeAuthOcpiURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "A4BIU31S0F",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:            http.StatusNotFound,
			wantAuthorised:        false,
			ocpiFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, nil, chargeauthorisation2.ErrAuthoriserNotFound
			},
		},
		{
			name: "status OK: guest authorised",
			path: chargeAuthGuestURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "123456",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:             http.StatusOK,
			wantAuthorised:         true,
			guestFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return true, &chargeauthorisation2.ClaimedCharge{}, nil
			},
		},
		{
			name: "status OK: guest not authorised",
			path: chargeAuthGuestURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "123456",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:             http.StatusOK,
			wantAuthorised:         false,
			guestFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, &chargeauthorisation2.ClaimedCharge{}, nil
			},
		},
		{
			name: "status not found: billing event not found",
			path: chargeAuthGuestURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "123",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:             http.StatusNotFound,
			wantAuthorised:         false,
			guestFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, nil, chargeauthorisation2.ErrAuthoriserNotFound
			},
		},
		{
			name: "status OK: location not public",
			path: chargeAuthGuestURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "123",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:             http.StatusOK,
			wantAuthorised:         false,
			guestFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, &chargeauthorisation2.ClaimedCharge{}, nil
			},
		},
		{
			name: "status not found: charger not found",
			path: chargeAuthGuestURL,
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "123",
				ChargerID: "PSL-2468",
				Door:      "A",
			},
			wantStatus:             http.StatusNotFound,
			wantAuthorised:         false,
			guestFunctionCallCount: 1,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return false, nil, chargeauthorisation2.ErrChargerNotFound
			},
		},
		{
			name: "bad request: invalid enum authorisation method",
			path: "/charge-authorisations/invalid",
			requestBody: chargeauthorisation.AuthoriseChargePayload{
				Token:     "B4BIU31S0F",
				ChargerID: "PSL-1234",
				Door:      "A",
			},
			wantStatus: http.StatusBadRequest,
			mockResponse: func() (bool, *chargeauthorisation2.ClaimedCharge, error) {
				return true, &chargeauthorisation2.ClaimedCharge{}, nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var checker = goahttpcheck.New()
			mockService := mock_chargeauthorisation.NewMockService(gomock.NewController(t))
			checker.Mount(
				server.NewAuthoriseChargeHandler,
				server.MountAuthoriseChargeHandler,
				chargeauthorisation.NewAuthoriseChargeEndpoint(NewChargeAuthorisation(log.Default(), mockService, errormapper.NewErrorMapper(ErrorMap))))
			mockService.EXPECT().ValidateRfidClaim(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockResponse()).Times(tt.rfidFunctionCallCount)
			mockService.EXPECT().ValidateOcpiClaim(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockResponse()).Times(tt.ocpiFunctionCallCount)
			mockService.EXPECT().ValidateGuestClaim(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockResponse()).Times(tt.guestFunctionCallCount)
			requestJSON, err := json.Marshal(tt.requestBody)
			require.NoError(t, err)

			checker.Test(t, http.MethodPost, tt.path).WithBody(requestJSON).Check().HasStatus(tt.wantStatus).Cb(func(response *http.Response) {
				b, _ := io.ReadAll(response.Body)
				err := response.Body.Close()
				if err != nil {
					t.Fatalf("unexpected error, %v", err)
				}

				if response.StatusCode == http.StatusOK {
					var actual chargeauthorisation.ChargeAuthorisationResponse
					if err = json.Unmarshal(b, &actual); err != nil {
						t.Fatalf("unexpected error, %v", err)
					}
					assert.Equal(t, tt.wantAuthorised, actual.Authorised)

					// Should only get an id in response if authorised was true
					if tt.wantAuthorised {
						gotUUID, err := uuid.Parse(*actual.ID)
						require.NoError(t, err)
						require.IsType(t, uuid.UUID{}, gotUUID)
					} else {
						require.Nil(t, actual.ID)
					}
				}
			})
		})
	}
}
