package api

import (
	contract "experience/libs/data-platform/api/contract/gen/charge_authorisation"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	"net/http"
)

var AuthoriserNotFoundGoaError = &contract.AuthoriserNotFound{
	Reason: "authoriser_not_found",
	Status: http.StatusNotFound,
}

var ChargerNotFoundGoaError = &contract.ChargerPpidNotFound{
	Reason: "charger_not_found",
	Status: http.StatusNotFound,
}

var TransactionNotStartedGoaError = &contract.TransactionNotStarted{
	Reason: "transaction_not_started",
	Status: http.StatusInternalServerError,
}

var ErrorMap = map[error]error{
	chargeauthorisation.ErrAuthoriserNotFound:    AuthoriserNotFoundGoaError,
	chargeauthorisation.ErrChargerNotFound:       ChargerNotFoundGoaError,
	chargeauthorisation.ErrTransactionNotStarted: TransactionNotStartedGoaError,
}
