package chargeauthorisation

import (
	"context"
	"errors"
	transactionsapiclient "experience/libs/shared/go/external/transactions-api-client/src"
	"experience/libs/shared/go/numbers"
	"io"
	"log"
	"net/http"
	"strconv"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
)

var doorMap = map[uint32]string{
	1: "A",
	2: "B",
	3: "C",
}

type service struct {
	repository      Repository
	logger          *log.Logger
	transactionsAPI transactionsapiclient.DefaultAPI
}

func NewService(logger *log.Logger, r Repository, transactionsAPI transactionsapiclient.DefaultAPI) Service {
	return &service{
		repository:      r,
		logger:          logger,
		transactionsAPI: transactionsAPI,
	}
}

func (s *service) ValidateOcpiClaim(ctx context.Context, ocpiToken, ppid string, doorID uint32) (bool, *ClaimedCharge, error) {
	authoriser, err := s.repository.GetAuthoriserForOCPIToken(ctx, ocpiToken, ppid)
	if err != nil {
		s.logger.Printf("failed to get authoriser ID for ocpi %s", ocpiToken)
		return false, &ClaimedCharge{}, err
	}

	claimedCharge := &ClaimedCharge{
		PodDoorID:     doorID,
		AuthoriserID:  authoriser.AuthoriserID,
		PodLocationID: authoriser.PodLocationID,
	}

	err = s.StartTransaction(ctx, authoriser.AuthoriserID, ppid, doorID)
	if err != nil {
		s.logger.Printf("failed to start transaction for ocpi %s", ocpiToken)
		return false, claimedCharge, ErrTransactionNotStarted
	}
	return true, claimedCharge, nil
}

func (s *service) StartTransaction(ctx context.Context, authoriserID int32, ppid string, doorID uint32) error {
	requestBody := transactionsapiclient.HandlersStartTransactionRequestBody{
		AuthoriserId:      ptr.To(strconv.FormatInt(int64(authoriserID), 10)),
		ChargingStationId: &ppid,
		ClientRef:         ptr.To("xdp-api"),
		Door:              ptr.To(doorMap[doorID]),
	}

	request := s.transactionsAPI.TransactionsStartPost(ctx)
	_, response, err := request.HandlersStartTransactionRequestBody(requestBody).Execute() //nolint:bodyclose // body is closed in the defer statement
	if err != nil && response.StatusCode != http.StatusGone {
		return err
	}
	if response != nil {
		defer func(body io.Closer) {
			closeErr := body.Close()
			if closeErr != nil {
				s.logger.Printf("error closing response body: %v", closeErr)
			}
		}(response.Body)
	}
	return nil
}

func (s *service) ValidateRfidClaim(ctx context.Context, rfidToken, chargerID string, doorID uint32) (bool, *ClaimedCharge, error) {
	rfidGroup, err := s.repository.GetGroupForRFID(ctx, rfidToken)
	if err != nil {
		s.logger.Printf("failed to get group for rfid %s", rfidToken)
		return false, &ClaimedCharge{}, err
	}

	if rfidGroup == nil {
		s.logger.Printf("no group found for rfid %s", rfidToken)
		return false, &ClaimedCharge{}, nil
	}

	chargerGroup, err := s.repository.GetGroupForUnitPpid(ctx, chargerID)
	if err != nil {
		s.logger.Printf("failed to get group for unit ppid %s", chargerID)
		return false, &ClaimedCharge{}, err
	}

	if chargerGroup == nil {
		s.logger.Printf("no group found for unit ppid %s", chargerID)
		return false, &ClaimedCharge{}, nil
	}

	groupsAreEqual := s.compareGroupUUIDs(rfidGroup.GroupUUID, chargerGroup.GroupUUID)
	s.logger.Printf("group uuids are equal: %t, rfid group uuid: %s, charger group uuid: %s", groupsAreEqual, rfidGroup.GroupUUID, chargerGroup.GroupUUID)

	var groupUUID *uuid.UUID
	if groupsAreEqual {
		groupUUID = rfidGroup.GroupUUID
	}

	claimedCharge := &ClaimedCharge{
		GroupUUID:        groupUUID,
		PodLocationID:    chargerGroup.PodLocationID,
		PodDoorID:        doorID,
		AuthoriserID:     rfidGroup.AuthoriserID,
		BillingAccountID: rfidGroup.BillingAccountID,
	}

	return groupsAreEqual, claimedCharge, nil
}

func (s *service) ValidateGuestClaim(ctx context.Context, guestToken, chargerID string, doorID uint32) (bool, *ClaimedCharge, error) {
	billingEventID, err := strconv.ParseInt(guestToken, 10, 64)
	if err != nil {
		return false, &ClaimedCharge{}, ErrAuthoriserNotFound
	}

	podLocation, err := s.repository.GetLocationForUnitPpid(ctx, chargerID)
	if err != nil {
		if errors.Is(err, ErrLocationNotPublic) {
			s.logger.Printf("location for unit ppid %s is not public", chargerID)
			return false, &ClaimedCharge{}, nil
		}
		return false, &ClaimedCharge{}, err
	}

	billingEvent, err := s.repository.GetBillingEventByID(ctx, billingEventID)
	if err != nil {
		s.logger.Printf("failed to get billing event by ID %d", billingEventID)
		return false, &ClaimedCharge{}, err
	}

	authoriserID, err := s.repository.GetGuestAuthoriserID(ctx)
	if err != nil {
		s.logger.Printf("failed to get guest authoriser ID")
		return false, &ClaimedCharge{}, err
	}

	convertedAuthoriserID, err := numbers.Convert[int64, int32](authoriserID)
	if err != nil {
		s.logger.Printf("failed to convert guest authoriser ID %d", authoriserID)
		return false, &ClaimedCharge{}, err
	}

	claimedCharge := &ClaimedCharge{
		PodLocationID:  podLocation.PodLocationID,
		BillingEventID: billingEvent.BillingEventID,
		AuthoriserID:   convertedAuthoriserID,
		PodDoorID:      doorID,
	}

	err = s.StartTransaction(ctx, convertedAuthoriserID, chargerID, doorID)
	if err != nil {
		s.logger.Printf("failed to start transaction for guest token %s", guestToken)
		return false, claimedCharge, ErrTransactionNotStarted
	}

	return true, claimedCharge, nil
}

func (s *service) compareGroupUUIDs(rfidGroupUUID, chargerGroupUUID *uuid.UUID) bool {
	return *rfidGroupUUID == *chargerGroupUUID
}
