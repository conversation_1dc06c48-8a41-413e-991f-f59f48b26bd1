package cmd

import (
	"database/sql"
	contract "experience/libs/data-platform/api/contract/gen/charge_authorisation"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	"experience/libs/data-platform/charge-authorisation/api"
	"experience/libs/data-platform/charge-authorisation/pkg/infra/podadmin"
	"experience/libs/shared/go/db/postgres"
	transactionsapiclient "experience/libs/shared/go/external/transactions-api-client/src"
	httphelpers "experience/libs/shared/go/http"
	"log"
	"time"

	"github.com/patrickmn/go-cache"
)

func NewService(logger *log.Logger, db *postgres.ReadWriteDB, podadminDb *sql.DB, transactionsAPI transactionsapiclient.DefaultAPI) contract.Service {
	repo := chargeauthorisation.NewRepository(logger, db, cache.New(cache.NoExpiration, 24*time.Hour))
	service := chargeauthorisation.NewService(logger, repo, transactionsAPI)
	podadminRepository, err := podadmin.NewRepository(podadminDb, logger)
	if err != nil {
		logger.Fatalf("unable to initialise podadmin repository: %v\n", err)
	}

	serviceWithPodadminClaimedCharge := chargeauthorisation.NewPodadminClaimedChargeDecorator(logger, service, podadminRepository)

	return api.NewChargeAuthorisation(logger, serviceWithPodadminClaimedCharge, httphelpers.NewErrorMapper(api.ErrorMap))
}
