-- name: GetGroupForRfid :many
SELECT a.id authoriser_id, a.group_uid, gba.billing_account_id
FROM podpoint.authorisers a
LEFT JOIN podpoint.groups g
  ON a.group_uid = g.uid
LEFT JOIN podpoint.group_billing_accounts gba
  ON gba.group_id = g.id AND gba.deleted_at IS NULL
WHERE type = 'rfid'
  AND a.uid = $1;

-- name: GetGroupIdByPpid :one
SELECT g.uid, pl.id pod_location_id
FROM podpoint.pod_units pu
INNER JOIN podpoint.pod_locations pl
  ON pu.id = pl.unit_id
       AND pl.deleted_at IS NULL
INNER JOIN podpoint.pod_addresses pa
  ON pl.address_id = pa.id
       AND pa.deleted_at IS NULL
INNER JOIN podpoint.groups g
  ON g.id = pa.group_id
       AND g.deleted_at IS NULL
WHERE pu.ppid = $1 AND pu.deleted_at IS NULL;

-- name: GetAuthoriserIDForOCPIToken :one
SELECT a.id
FROM podpoint.authorisers a
WHERE a.uid = $1
  AND a.type = 'ocpi'
  AND a.deleted_at IS NULL;

-- name: GetLocationIDForPPID :one
SELECT pl.id pod_location_id
FROM podpoint.pod_units pu
INNER JOIN podpoint.pod_locations pl
          ON pu.id = pl.unit_id
            AND pl.deleted_at IS NULL
WHERE pu.ppid = $1 AND pu.deleted_at IS NULL;

-- name: GetLocationForPPID :one
SELECT pl.id, pl.is_public
FROM podpoint.pod_units pu
       INNER JOIN podpoint.pod_locations pl
                  ON pu.id = pl.unit_id
                    AND pl.deleted_at IS NULL
WHERE pu.ppid = $1 AND pu.deleted_at IS NULL;

-- name: GetBillingEventByID :one
SELECT id
FROM podpoint.billing_events
WHERE id = $1
  AND deleted_at IS NULL;

-- name: GetGuestAuthoriserID :one
SELECT id
FROM podpoint.authorisers
WHERE type = 'guest'
  AND deleted_at IS NULL;
