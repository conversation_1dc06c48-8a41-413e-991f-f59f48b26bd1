package chargeauthorisation

import (
	"context"
	"database/sql"
	"errors"
	"experience/libs/data-platform/charge-authorisation/sqlc"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/numbers"
	"fmt"
	"log"

	"github.com/patrickmn/go-cache"

	"github.com/google/uuid"
	"k8s.io/utils/ptr"
)

const GuestAuthoriserType = "guest"

type repository struct {
	queriesRead          *sqlc.Queries
	queriesWrite         *sqlc.Queries
	logger               *log.Logger
	guestAuthoriserCache Cache
}

func NewRepository(logger *log.Logger, db *postgres.ReadWriteDB, guestAuthoriserCache Cache) Repository {
	return &repository{
		logger:               logger,
		queriesRead:          sqlc.New(db.ReadDB),
		queriesWrite:         sqlc.New(db.WriteDB),
		guestAuthoriserCache: guestAuthoriserCache,
	}
}

func (r *repository) GetGroupForRFID(ctx context.Context, rfidTag string) (*ClaimedCharge, error) {
	group, err := r.queriesRead.GetGroupForRfid(ctx, rfidTag)
	if err != nil {
		r.logger.Printf("failed to get group for rfid %s", rfidTag)
		return nil, err
	}

	if len(group) == 0 {
		r.logger.Printf("no group found for rfid %s", rfidTag)
		return nil, nil
	}

	if len(group) != 1 {
		r.logger.Printf("got %d results for rfid %s. Expected 1", len(group), rfidTag)
		return nil, fmt.Errorf("got %d results for rfid %s. Expected 1", len(group), rfidTag)
	}

	groupData := group[0]
	if !groupData.GroupUid.Valid {
		r.logger.Printf("rfid %s has no valid group uuid", rfidTag)
		return nil, nil
	}

	groupUUID, err := uuid.Parse(groupData.GroupUid.String)
	if err != nil {
		r.logger.Printf("failed to parse group uuid %s", groupData.GroupUid.String)
		return nil, err
	}

	authoriserID, err := numbers.Convert[int64, int32](groupData.AuthoriserID)
	if err != nil {
		return nil, fmt.Errorf("error converting groupID: %w", err)
	}

	billingAccountID, err := toBillingAccountID(groupData.BillingAccountID)
	if err != nil {
		return nil, fmt.Errorf("error converting billing account id: %w", err)
	}

	return &ClaimedCharge{
		GroupUUID:        &groupUUID,
		AuthoriserID:     authoriserID,
		BillingAccountID: billingAccountID,
	}, nil
}

func (r *repository) GetGroupForUnitPpid(ctx context.Context, chargerID string) (*ClaimedCharge, error) {
	unit, err := r.queriesRead.GetGroupIdByPpid(ctx, chargerID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			r.logger.Printf("no group found for unit ppid %s", chargerID)
			return nil, ErrChargerNotFound
		}
		r.logger.Printf("failed to get group for unit ppid %s, %v", chargerID, err)
		return nil, err
	}
	groupUUID, err := uuid.Parse(unit.Uid)
	if err != nil {
		r.logger.Printf("failed to parse group uuid %s", unit.Uid)
		return nil, err
	}

	locationID, err := numbers.Convert[int64, uint32](unit.PodLocationID)
	if err != nil {
		return nil, fmt.Errorf("error converting locationID: %w", err)
	}

	return &ClaimedCharge{
		GroupUUID:     &groupUUID,
		PodLocationID: locationID,
	}, nil
}

func (r *repository) GetAuthoriserForOCPIToken(ctx context.Context, ocpiToken, ppid string) (*ClaimedCharge, error) {
	authoriser, err := r.queriesRead.GetAuthoriserIDForOCPIToken(ctx, ocpiToken)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			r.logger.Printf("no active authoriser found for ocpi token %s", ocpiToken)
			return nil, ErrAuthoriserNotFound
		}
		r.logger.Printf("failed to get authoriser for ocpi token %s, %v", ocpiToken, err)
		return nil, err
	}

	authoriserID, err := numbers.Convert[int64, int32](authoriser)
	if err != nil {
		return nil, fmt.Errorf("error converting authoriserID: %w", err)
	}

	locationIDForPPID, err := r.queriesRead.GetLocationIDForPPID(ctx, ppid)
	if err != nil {
		return nil, ErrChargerNotFound
	}

	podLocationID, err := numbers.Convert[int64, uint32](locationIDForPPID)
	if err != nil {
		return nil, fmt.Errorf("error converting locationID: %w", err)
	}

	return &ClaimedCharge{
		AuthoriserID:  authoriserID,
		PodLocationID: podLocationID,
	}, nil
}

func (r *repository) GetLocationForUnitPpid(ctx context.Context, chargerID string) (*ClaimedCharge, error) {
	location, err := r.queriesRead.GetLocationForPPID(ctx, chargerID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			r.logger.Printf("no location found for unit ppid %s", chargerID)
			return nil, ErrChargerNotFound
		}
		r.logger.Printf("failed to get location for unit ppid %s, %v", chargerID, err)
		return nil, err
	}
	if location.IsPublic == 0 {
		return nil, ErrLocationNotPublic
	}

	locationID, err := numbers.Convert[int64, uint32](location.ID)
	if err != nil {
		return nil, fmt.Errorf("error converting locationID: %w", err)
	}

	return &ClaimedCharge{
		PodLocationID: locationID,
	}, nil
}

func (r *repository) GetBillingEventByID(ctx context.Context, billingEventID int64) (*ClaimedCharge, error) {
	billingEvent, err := r.queriesRead.GetBillingEventByID(ctx, billingEventID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrAuthoriserNotFound
		}
		r.logger.Printf("failed to get billing event for id %d, %v", billingEventID, err)
		return nil, err
	}

	convertedBillingEventID, conversionErr := numbers.Convert[int64, int32](billingEvent)
	if conversionErr != nil {
		r.logger.Printf("failed to convert billing event id %d, %v", billingEvent, conversionErr)
	}

	return &ClaimedCharge{
		BillingEventID: &convertedBillingEventID,
	}, nil
}

func (r *repository) GetGuestAuthoriserID(ctx context.Context) (int64, error) {
	if cachedGuestAuthoriserID, isCached := r.guestAuthoriserCache.Get(GuestAuthoriserType); isCached {
		cachedEntry, _ := cachedGuestAuthoriserID.(int64)
		return cachedEntry, nil
	}
	authoriserID, err := r.queriesRead.GetGuestAuthoriserID(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			r.logger.Printf("no guest authoriser found")
			return 0, ErrAuthoriserNotFound
		}
		r.logger.Printf("failed to get guest authoriser id, %v", err)
		return 0, err
	}
	r.guestAuthoriserCache.Set(GuestAuthoriserType, authoriserID, cache.NoExpiration)
	return authoriserID, nil
}

func toBillingAccountID(billingAccountID sql.NullInt64) (*int32, error) {
	if !billingAccountID.Valid {
		return nil, nil
	}
	convert, err := numbers.Convert[int64, int32](billingAccountID.Int64)
	return ptr.To(convert), err
}
