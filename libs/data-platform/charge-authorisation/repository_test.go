package chargeauthorisation_test

import (
	"database/sql"
	chargeauthorisation "experience/libs/data-platform/charge-authorisation"
	mock_chargeauthorisation "experience/libs/data-platform/charge-authorisation/mock"
	"experience/libs/data-platform/test/fixtures"
	cfg "experience/libs/shared/go/db"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/db/postgres/test"
	"experience/libs/shared/go/numbers"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"
	"go.uber.org/mock/gomock"

	"k8s.io/utils/ptr"

	"experience/libs/data-platform/test/sqlc"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type ChargeAuthRepositoryTestSuite struct {
	suite.Suite
	testDB                   *test.Database
	underTest                chargeauthorisation.Repository
	dbh                      *sql.DB
	queries                  *sqlc.Queries
	mockGuestAuthoriserCache *mock_chargeauthorisation.MockCache
}

func TestChargeAuthRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping ChargersRepositoryTestSuite integration test")
	}
	suite.Run(t, new(ChargeAuthRepositoryTestSuite))
}

func (s *ChargeAuthRepositoryTestSuite) SetupSuite() {
	var err error

	s.testDB = test.NewDatabase(s.T())
	passwordConfig := s.testDB.PasswordConfig(s.T())

	s.dbh, err = postgres.NewPasswordDB(passwordConfig, true)
	require.NoError(s.T(), err)

	_, err = migrate.MigrateUp("file://../golang-migrate/migration", s.testDB.PasswordConfig(s.T()), nil, true)
	require.NoError(s.T(), err)

	config := s.testDB.IamConfig(s.T())

	rwDB := postgres.NewReadWriteDB(&cfg.ServiceDatasource{
		ReadConfig:  cfg.ReadConfig{IAMConfig: config},
		WriteConfig: cfg.WriteConfig{IAMConfig: config},
	}, true)

	s.mockGuestAuthoriserCache = mock_chargeauthorisation.NewMockCache(gomock.NewController(s.T()))
	s.underTest = chargeauthorisation.NewRepository(log.Default(), rwDB, s.mockGuestAuthoriserCache)
	s.queries = sqlc.New(s.dbh)
}

func (s *ChargeAuthRepositoryTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *ChargeAuthRepositoryTestSuite) TearDownTest() {
	s.queries.DeleteUnitsInIDRange(s.T().Context(), sqlc.DeleteUnitsInIDRangeParams{
		ID:   fixtures.IDRangeLowerBound,
		ID_2: fixtures.IDRangeUpperBound,
	})

	s.queries.DeleteAuthoriser(s.T().Context(), 1)
}

func (s *ChargeAuthRepositoryTestSuite) TestGetGroupForRFID() {
	rfidTags, associatedGroups, authoriserIDs := s.seedForRFIDGroup()
	tests := []struct {
		name     string
		rfidTag  string
		expected func() (*chargeauthorisation.ClaimedCharge, error)
	}{
		{
			name:    "Returns group for registered rfid",
			rfidTag: rfidTags[0],
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return &chargeauthorisation.ClaimedCharge{
					GroupUUID:    associatedGroups[0],
					AuthoriserID: authoriserIDs[0],
				}, nil
			},
		},
		{
			name:    "Returns group for registered rfid lowercase",
			rfidTag: strings.ToLower(rfidTags[0]),
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return &chargeauthorisation.ClaimedCharge{
					GroupUUID:    associatedGroups[0],
					AuthoriserID: authoriserIDs[0],
				}, nil
			},
		},
		{
			name:    "Returns group for registered rfid uppercase",
			rfidTag: strings.ToUpper(rfidTags[0]),
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return &chargeauthorisation.ClaimedCharge{
					GroupUUID:    associatedGroups[0],
					AuthoriserID: authoriserIDs[0],
				}, nil
			},
		},
		{
			name:    "Returns nil for tag with no group",
			rfidTag: rfidTags[1],
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, nil
			},
		},
		{
			name:    "Returns nil for non rfid authoriser",
			rfidTag: rfidTags[2],
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, nil
			},
		},
		{
			name:    "Returns nil for non-existent tag",
			rfidTag: "NOTaT4G",
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, nil
			},
		},
		{
			name:    "Returns more than one result for tag",
			rfidTag: rfidTags[3],
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, fmt.Errorf("got 2 results for rfid %s. Expected 1", rfidTags[3])
			},
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			ctx := s.T().Context()
			expected, expectedErr := tt.expected()
			got, err := s.underTest.GetGroupForRFID(ctx, tt.rfidTag)

			require.Equal(t, expected, got)
			if expectedErr != nil {
				require.ErrorContains(t, err, expectedErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func (s *ChargeAuthRepositoryTestSuite) TestGetGroupIdByUnitPpid() {
	group, unit, location := s.seedForGroupForPpid()
	locationID, err := numbers.Convert[int64, uint32](location.ID)
	require.NoError(s.T(), err)
	tests := []struct {
		name     string
		ppid     string
		expected func() (*chargeauthorisation.ClaimedCharge, error)
	}{
		{
			name: "Returns group for unit ppid",
			ppid: unit.Ppid,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				uid := uuid.MustParse(group.Uid)
				return &chargeauthorisation.ClaimedCharge{
					GroupUUID:     &uid,
					PodLocationID: locationID,
				}, nil
			},
		},
		{
			name: "Returns error for no group found",
			ppid: "fake",
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, chargeauthorisation.ErrChargerNotFound
			},
		},
	}
	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			expected, expectedErr := tt.expected()
			got, err := s.underTest.GetGroupForUnitPpid(s.T().Context(), tt.ppid)
			require.Equal(t, expected, got)
			require.ErrorIs(t, err, expectedErr)
		})
	}
}

func (s *ChargeAuthRepositoryTestSuite) TestGetAuthoriserForOCPIToken() {
	activeAuthoriser, inactiveAuthoriser, unit, location := s.seedForOCPI()
	activeAuthoriserID, err := numbers.Convert[int64, int32](activeAuthoriser.ID)
	require.NoError(s.T(), err)

	locationID, err := numbers.Convert[int64, uint32](location.ID)
	require.NoError(s.T(), err)

	tests := []struct {
		name     string
		ocpiTag  string
		expected func() (*chargeauthorisation.ClaimedCharge, error)
		ppid     string
	}{
		{
			name:    "Return authoriser ID for OCPI tag",
			ocpiTag: activeAuthoriser.Uid,
			ppid:    unit.Ppid,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return &chargeauthorisation.ClaimedCharge{
					AuthoriserID:  activeAuthoriserID,
					PodLocationID: locationID,
				}, nil
			},
		},
		{
			name:    "Return authoriser ID for OCPI tag lowercase",
			ocpiTag: strings.ToLower(activeAuthoriser.Uid),
			ppid:    unit.Ppid,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return &chargeauthorisation.ClaimedCharge{
					AuthoriserID:  activeAuthoriserID,
					PodLocationID: locationID,
				}, nil
			},
		},
		{
			name:    "Return authoriser ID for OCPI tag uppercase",
			ocpiTag: strings.ToUpper(activeAuthoriser.Uid),
			ppid:    unit.Ppid,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return &chargeauthorisation.ClaimedCharge{
					AuthoriserID:  activeAuthoriserID,
					PodLocationID: locationID,
				}, nil
			},
		},
		{
			name:    "Returns AuthoriserNotFound err when no active authoriser found",
			ocpiTag: inactiveAuthoriser.Uid,
			ppid:    unit.Ppid,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, chargeauthorisation.ErrAuthoriserNotFound
			},
		},
		{
			name:    "Returns AuthoriserNotFound err when no authoriser found",
			ocpiTag: "no-authoriser",
			ppid:    unit.Ppid,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, chargeauthorisation.ErrAuthoriserNotFound
			},
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			expected, expectedErr := tt.expected()
			got, err := s.underTest.GetAuthoriserForOCPIToken(s.T().Context(), tt.ocpiTag, tt.ppid)
			require.Equal(t, expected, got)
			require.ErrorIs(t, err, expectedErr)
		})
	}
}

func (s *ChargeAuthRepositoryTestSuite) TestGetLocationByUnitPpid() {
	unit, location, _ := s.seedForGuest(false)
	locationID, err := numbers.Convert[int64, uint32](location.ID)
	require.NoError(s.T(), err)
	tests := []struct {
		name     string
		ppid     string
		expected func() (*chargeauthorisation.ClaimedCharge, error)
	}{
		{
			name: "Returns location for unit ppid",
			ppid: unit.Ppid,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return &chargeauthorisation.ClaimedCharge{
					PodLocationID: locationID,
				}, nil
			},
		},
		{
			name: "Returns error for no location found",
			ppid: "fake",
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, chargeauthorisation.ErrChargerNotFound
			},
		},
	}
	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			expected, expectedErr := tt.expected()
			got, err := s.underTest.GetLocationForUnitPpid(s.T().Context(), tt.ppid)
			require.Equal(t, expected, got)
			require.ErrorIs(t, err, expectedErr)
		})
	}
}

func (s *ChargeAuthRepositoryTestSuite) TestGetLocationByUnitPpidNotPublic() {
	unit, _, _ := s.seedForGuest(true)
	tests := []struct {
		name     string
		ppid     string
		expected func() (*chargeauthorisation.ClaimedCharge, error)
	}{
		{
			name: "Returns error for location not public",
			ppid: unit.Ppid,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, chargeauthorisation.ErrLocationNotPublic
			},
		},
	}
	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			expected, expectedErr := tt.expected()
			got, err := s.underTest.GetLocationForUnitPpid(s.T().Context(), tt.ppid)
			require.Equal(t, expected, got)
			require.ErrorIs(t, err, expectedErr)
		})
	}
}

func (s *ChargeAuthRepositoryTestSuite) TestGetBillingEventByID() {
	_, _, billingEvent := s.seedForGuest(false)
	billingEventIDInt32, err := numbers.Convert[int64, int32](billingEvent.ID)
	require.NoError(s.T(), err)

	tests := []struct {
		name           string
		billingEventID int64
		expected       func() (*chargeauthorisation.ClaimedCharge, error)
	}{
		{
			name:           "Returns billing event based on billing event ID",
			billingEventID: billingEvent.ID,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return &chargeauthorisation.ClaimedCharge{BillingEventID: &billingEventIDInt32}, nil
			},
		},
		{
			name:           "Returns authoriserNotFound when billing event doesn't exist",
			billingEventID: 123,
			expected: func() (*chargeauthorisation.ClaimedCharge, error) {
				return nil, chargeauthorisation.ErrAuthoriserNotFound
			},
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			expected, err := tt.expected()
			got, expectedErr := s.underTest.GetBillingEventByID(s.T().Context(), tt.billingEventID)
			require.Equal(t, expected, got)
			require.ErrorIs(t, err, expectedErr)
		})
	}
}

func (s *ChargeAuthRepositoryTestSuite) TestGetGuestAuthoriserID_ReturnsGuestAuthoriserID() {
	_, err := s.queries.CreateAuthoriser(s.T().Context(), fixtures.CreateAuthoriserParams(1, "guest", "guest", nil))
	require.NoError(s.T(), err)

	// authoriser not cached - fetch from database and set in cache
	s.mockGuestAuthoriserCache.EXPECT().Get(gomock.Eq("guest")).Return(int64(0), false).Times(1)
	s.mockGuestAuthoriserCache.EXPECT().Set(gomock.Eq("guest"), gomock.Eq(int64(1)), gomock.Eq(cache.NoExpiration)).Times(1)

	got, err := s.underTest.GetGuestAuthoriserID(s.T().Context())
	require.EqualValues(s.T(), 1, got)
	require.NoError(s.T(), err)
}

func (s *ChargeAuthRepositoryTestSuite) TestGetGuestAuthoriserID_ReturnsCachedGuestAuthoriserID() {
	// authoriser in cache
	s.mockGuestAuthoriserCache.EXPECT().Get(gomock.Eq("guest")).Return(int64(1), true).Times(1)
	s.mockGuestAuthoriserCache.EXPECT().Set(gomock.Any(), gomock.Any(), gomock.Any()).Times(0)

	got, err := s.underTest.GetGuestAuthoriserID(s.T().Context())
	require.EqualValues(s.T(), 1, got)
	require.NoError(s.T(), err)
}

func (s *ChargeAuthRepositoryTestSuite) TestGetGuestAuthoriserID_ReturnsErrorWhenNoGuestAuthoriserFound() {
	// authoriser in neither cache nor database
	s.mockGuestAuthoriserCache.EXPECT().Get(gomock.Eq("guest")).Return(int64(0), false).Times(1)

	got, err := s.underTest.GetGuestAuthoriserID(s.T().Context())
	require.EqualValues(s.T(), 0, got)
	require.ErrorIs(s.T(), err, chargeauthorisation.ErrAuthoriserNotFound)
}

func (s *ChargeAuthRepositoryTestSuite) seedForRFIDGroup() (rfidTags []string, groups []*uuid.UUID, authorisers []int32) {
	groupUUID := uuid.NewString()
	_, err := s.queries.CreateOrganisation(s.T().Context(), fixtures.CreateOrganisationParams(0, groupUUID, "Dunder Mifflin"))
	require.NoError(s.T(), err)
	_, err = s.queries.CreateOrganisation(s.T().Context(), fixtures.CreateOrganisationParams(0, groupUUID, "Acme Corp"))
	require.NoError(s.T(), err)

	authoriser1, err := s.queries.CreateAuthoriser(s.T().Context(), fixtures.CreateAuthoriserParams(0, "046G6A5A8E4981", "rfid", ptr.To(uuid.NewString())))
	require.NoError(s.T(), err)
	authoriser2, err := s.queries.CreateAuthoriser(s.T().Context(), fixtures.CreateAuthoriserParams(0, "146G6A5A8E4981", "rfid", nil))
	require.NoError(s.T(), err)
	authoriser3, err := s.queries.CreateAuthoriser(s.T().Context(), fixtures.CreateAuthoriserParams(0, "246G6A5A8E4981", "user", ptr.To(uuid.NewString())))
	require.NoError(s.T(), err)
	authoriser4, err := s.queries.CreateAuthoriser(s.T().Context(), fixtures.CreateAuthoriserParams(0, "346G6A5A8E4981", "rfid", ptr.To(groupUUID)))
	require.NoError(s.T(), err)

	authoriser1ID, err := numbers.Convert[int64, int32](authoriser1.ID)
	require.NoError(s.T(), err)
	authoriser2ID, err := numbers.Convert[int64, int32](authoriser2.ID)
	require.NoError(s.T(), err)
	authoriser3ID, err := numbers.Convert[int64, int32](authoriser3.ID)
	require.NoError(s.T(), err)
	authoriser4ID, err := numbers.Convert[int64, int32](authoriser4.ID)
	require.NoError(s.T(), err)

	authorisers = []int32{authoriser1ID, authoriser2ID, authoriser3ID, authoriser4ID}
	rfidTags = []string{authoriser1.Uid, authoriser2.Uid, authoriser3.Uid, authoriser4.Uid}
	groups = []*uuid.UUID{ptr.To(uuid.MustParse(authoriser1.GroupUid.String)), nil, ptr.To(uuid.MustParse(authoriser3.GroupUid.String)), ptr.To(uuid.MustParse(groupUUID))}

	return
}

func (s *ChargeAuthRepositoryTestSuite) seedForGuest(isHome bool) (sqlc.PodpointPodUnit, sqlc.PodpointPodLocation, sqlc.PodpointBillingEvent) {
	unit, _, location, _ := fixtures.PrepareChargeData(s.T().Context(), s.T(), s.queries, isHome, true)
	billingEvent, err := s.queries.CreateBillingEvent(s.T().Context(), fixtures.CreateBillingEventParams(0, 0))
	require.NoError(s.T(), err)

	return unit, location, billingEvent
}

func (s *ChargeAuthRepositoryTestSuite) seedForGroupForPpid() (sqlc.PodpointGroup, sqlc.PodpointPodUnit, sqlc.PodpointPodLocation) {
	group, err := s.queries.CreateOrganisation(s.T().Context(), fixtures.CreateOrganisationParams(0, uuid.NewString(), "Dunder Mifflin"))
	require.NoError(s.T(), err)

	unit, _, location, _ := fixtures.PrepareChargeData(s.T().Context(), s.T(), s.queries, false, false, group.ID)
	return group, unit, location
}

func (s *ChargeAuthRepositoryTestSuite) seedForOCPI() (activeAuthoriser, inactiveAuthoriser sqlc.PodpointAuthoriser, unit sqlc.PodpointPodUnit, location sqlc.PodpointPodLocation) {
	activeAuthoriser, err := s.queries.CreateAuthoriser(s.T().Context(), fixtures.CreateAuthoriserParams(0, "ocpi-test-uid", "ocpi", nil))
	require.NoError(s.T(), err)

	inactiveParams := sqlc.CreateAuthoriserParams{
		ID:        1,
		Uid:       "inactive-ocpi-test-uid",
		Type:      "ocpi",
		DeletedAt: sql.NullTime{Time: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), Valid: true},
	}
	inactiveAuthoriser, err = s.queries.CreateAuthoriser(s.T().Context(), inactiveParams)
	require.NoError(s.T(), err)

	unit, _, location, _ = fixtures.PrepareChargeData(s.T().Context(), s.T(), s.queries, false, false)

	return activeAuthoriser, inactiveAuthoriser, unit, location
}
