package chargeauthorisation

import (
	"context"
	"experience/libs/shared/go/numbers"
	"experience/libs/shared/go/service/utils"
	"log"
)

type decorator struct {
	logger             *log.Logger
	service            Service
	podadminRepository PodadminRepository
}

func NewPodadminClaimedChargeDecorator(logger *log.Logger, service Service, podAdminRepository PodadminRepository) Service {
	return &decorator{
		logger:             logger,
		service:            service,
		podadminRepository: podAdminRepository,
	}
}

func (d *decorator) ValidateOcpiClaim(ctx context.Context, ocpiToken, chargerID string, doorID uint32) (bool, *ClaimedCharge, error) {
	isValid, claimedCharge, err := d.service.ValidateOcpiClaim(ctx, ocpiToken, chargerID, doorID)
	if err != nil {
		d.logger.Printf("failed to validate OCPI claim: %v", err)
		return false, nil, err
	}

	if !isValid {
		d.logger.Printf("not inserting claimed charge as invalid OCPI claim for charger: %s", chargerID)
		return false, nil, nil
	}
	claimID, err := d.podadminRepository.InsertClaimedCharge(ctx, claimedCharge)
	if err != nil {
		d.logger.Printf("failed to insert claimed charge: %v", err)
		return false, nil, err
	}

	claimedChargeIDint, err := numbers.Convert[int64, int](claimID)
	if err != nil {
		return false, nil, err
	}

	claimedCharge.ID = utils.AuthorisedEventUUIDFromNumericID(claimedChargeIDint)

	return true, claimedCharge, nil
}

func (d *decorator) ValidateRfidClaim(ctx context.Context, rfidToken, chargerID string, doorID uint32) (bool, *ClaimedCharge, error) {
	isValid, claimedCharge, err := d.service.ValidateRfidClaim(ctx, rfidToken, chargerID, doorID)
	if err != nil {
		d.logger.Printf("failed to validate RFID claim: %v", err)
		return false, nil, err
	}

	if !isValid {
		d.logger.Printf("not inserting claimed charge as invalid RFID claim")
		return false, nil, nil
	}
	claimID, err := d.podadminRepository.InsertClaimedCharge(ctx, claimedCharge)
	if err != nil {
		d.logger.Printf("failed to insert claimed charge: %v", err)
		return false, nil, err
	}

	claimedChargeIDint, err := numbers.Convert[int64, int](claimID)
	if err != nil {
		return false, nil, err
	}

	claimedCharge.ID = utils.AuthorisedEventUUIDFromNumericID(claimedChargeIDint)

	return true, claimedCharge, nil
}

func (d *decorator) ValidateGuestClaim(ctx context.Context, guestToken, chargerID string, doorID uint32) (bool, *ClaimedCharge, error) {
	isValid, claimedCharge, err := d.service.ValidateGuestClaim(ctx, guestToken, chargerID, doorID)
	if err != nil {
		d.logger.Printf("failed to validate Guest claim: %v", err)
		return false, nil, err
	}

	if !isValid {
		d.logger.Printf("not inserting claimed charge as invalid Guest claim")
		return false, nil, nil
	}

	claimID, err := d.podadminRepository.InsertClaimedCharge(ctx, claimedCharge)
	if err != nil {
		d.logger.Printf("failed to insert claimed charge: %v", err)
		return false, nil, err
	}

	claimedChargeIDint, err := numbers.Convert[int64, int](claimID)
	if err != nil {
		return false, nil, err
	}

	claimedCharge.ID = utils.AuthorisedEventUUIDFromNumericID(claimedChargeIDint)

	return true, claimedCharge, nil
}
