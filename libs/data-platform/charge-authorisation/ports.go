package chargeauthorisation

import (
	"context"
	"time"

	"github.com/google/uuid"
)

type ClaimedCharge struct {
	ID               uuid.UUID
	GroupUUID        *uuid.UUID
	PodLocationID    uint32
	PodDoorID        uint32
	AuthoriserID     int32
	BillingAccountID *int32
	BillingEventID   *int32
}

type Repository interface {
	GetGroupForRFID(ctx context.Context, rfidToken string) (*ClaimedCharge, error)
	GetGroupForUnitPpid(ctx context.Context, chargerID string) (*ClaimedCharge, error)
	GetAuthoriserForOCPIToken(ctx context.Context, ocpiToken string, ppid string) (*ClaimedCharge, error)
	GetLocationForUnitPpid(ctx context.Context, chargerID string) (*ClaimedCharge, error)
	GetBillingEventByID(ctx context.Context, billingEventID int64) (*ClaimedCharge, error)
	GetGuestAuthoriserID(ctx context.Context) (int64, error)
}

type Service interface {
	ValidateRfidClaim(ctx context.Context, rfidToken, chargerID string, doorID uint32) (bool, *ClaimedCharge, error)
	ValidateOcpiClaim(ctx context.Context, ocpiToken, chargerID string, doorID uint32) (bool, *ClaimedCharge, error)
	ValidateGuestClaim(ctx context.Context, guestToken, chargerID string, doorID uint32) (bool, *ClaimedCharge, error)
}

type PodadminRepository interface {
	InsertClaimedCharge(ctx context.Context, claimedCharge *ClaimedCharge) (int64, error)
}

type Cache interface {
	Get(string) (any, bool)
	Set(string, any, time.Duration)
}
