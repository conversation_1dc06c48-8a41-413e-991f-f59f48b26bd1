-- name: DeleteCharges :exec
DELETE
FROM podpoint.charges
WHERE id = ANY ($1::int[]);

-- name: DeleteChargesInIDRange :exec
DELETE
FROM podpoint.charges
WHERE id BETWEEN $1 AND $2;

-- name: CloseCharge :exec
UPDATE podpoint.charges
SET is_closed = 1
WHERE id = $1;

-- name: CreateUnit :one
INSERT INTO podpoint.pod_units (id, ppid, name, model_id, status_id, relay_weld_flag, deleted_at)
VALUES ($1, $2, $3, $4, 1, 0, $5)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteUnit :exec
DELETE
FROM podpoint.pod_units
WHERE id = $1;

-- name: DeleteUnitsInIDRange :exec
DELETE
FROM podpoint.pod_units
WHERE id BETWEEN $1 AND $2;

-- name: CreateAddress :one
INSERT INTO podpoint.pod_addresses (id, business_name, line_1, line_2,
                                    postal_town, postcode, country, description, type_id, deleted_at, group_id,
                                    tariff_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 2, $9, $10, $11)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteAddress :exec
DELETE
FROM podpoint.pod_addresses
WHERE id = $1;

-- name: DeleteAddressesInIDRange :exec
DELETE
FROM podpoint.pod_addresses
WHERE id BETWEEN $1 AND $2;

-- name: CreateLocation :one
INSERT INTO podpoint.pod_locations (id, uuid, address_id, longitude, latitude, geohash, description, payg_enabled,
                                    contactless_enabled, midmeter_enabled, is_public, is_home, is_ev_zone, unit_id,
                                    deleted_at, revenue_profile_id, timezone)
VALUES ($1, $2, $3, $4, $5, $6, ' ', $7, 0, 0, $8, $9, 0, $10, $11, $12, $13)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteLocation :exec
DELETE
FROM podpoint.pod_locations
WHERE id = $1;

-- name: DeleteLocationsInIDRange :exec
DELETE
FROM podpoint.pod_locations
WHERE id BETWEEN $1 AND $2;

-- name: CreateCharge :one
INSERT INTO podpoint.charges (id, location_id, unit_id, door, energy_cost, billing_event_id, starts_at, ends_at,
                              kwh_used, duration,
                              is_closed, group_id, claimed_charge_id, billing_account_id, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteCharge :exec
DELETE
FROM podpoint.charges
WHERE id = $1;


-- name: CreateBillingAccount :one
INSERT INTO podpoint.billing_accounts (id, user_id, uid, balance,
                                       currency,
                                       business_name,
                                       line_1,
                                       line_2,
                                       postal_town,
                                       postcode,
                                       country,
                                       phone,
                                       mobile,
                                       payment_processor_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteBillingAccountsInIDRange :exec
DELETE
FROM podpoint.billing_accounts
WHERE id BETWEEN $1 AND $2;

-- name: CreateBillingEvent :one
INSERT INTO podpoint.billing_events (id, account_id, presentment_amount, presentment_currency, exchange_rate,
                                     settlement_amount, settlement_currency, amount, description, refunded_amount,
                                     user_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, ' ', 0, $9)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteBillingEvent :exec
DELETE
FROM podpoint.billing_events
WHERE id = $1;

-- name: DeleteBillingEventsInIDRange :exec
DELETE
FROM podpoint.billing_events
WHERE id BETWEEN $1 AND $2;

-- name: CreateOrganisation :one
INSERT INTO podpoint.groups (id, uid, type_id, name, contact_name, phone, business_name, line_1, line_2, postal_town,
                             postcode, country, fee_percentage, created_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteOrganisation :exec
DELETE
FROM podpoint.groups
WHERE id = $1;

-- name: DeleteGroupsInIDRange :exec
DELETE
FROM podpoint.groups
WHERE id BETWEEN $1 AND $2;

-- name: CreateUser :one
INSERT INTO podpoint.users (id, auth_id, group_id, role_id, email, salutation, first_name, last_name, password,
                            created_at, deleted_at, is_emailed_usage_data)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 0)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteUser :exec
DELETE
FROM podpoint.users
WHERE id = $1;

-- name: DeleteUsersInIDRange :exec
DELETE
FROM podpoint.users
WHERE id BETWEEN $1 AND $2;

-- name: CreateAuthoriser :one
INSERT INTO podpoint.authorisers (id, uid, type, deleted_at, group_uid)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteAuthorisersInIDRange :exec
DELETE
FROM podpoint.authorisers
WHERE id BETWEEN $1 AND $2;

-- name: DeleteAuthoriser :exec
DELETE
FROM podpoint.authorisers
WHERE id = $1;

-- name: CreateMember :one
INSERT INTO podpoint.members (id, email, group_id, first_name, last_name)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteMembersInIDRange :exec
DELETE
FROM podpoint.members
WHERE id BETWEEN $1 AND $2;

-- name: CreateEvDriver :one
INSERT INTO podpoint.ev_drivers (id, email, group_id, first_name, last_name)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteEvDriversInIDRange :exec
DELETE
FROM podpoint.ev_drivers
WHERE id BETWEEN $1 AND $2;

-- name: CreateRevenueProfile :one
INSERT INTO podpoint.revenue_profiles (id, group_id, name, currency, created_at, updated_at, deleted_at)
VALUES ($1, $2, $3, $4, NOW(), NOW(), $5)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteRevenueProfilesInIDRange :exec
DELETE
FROM podpoint.revenue_profiles
WHERE id BETWEEN $1 AND $2;

-- name: CreateRevenueProfileTiersForAllUserTypes :many
INSERT INTO podpoint.revenue_profile_tiers (id, revenue_profile_id, type, user_type, begin_time, begin_day, end_time,
                                            end_day, start_second, rate, created_at, updated_at, deleted_at)
VALUES ($1, $3, 'energy', 'drivers', '00:00:00', 1, '00:00:00', 1, 0, 100000, NOW(), NOW(), $4),
       ($2, $3, 'energy', 'members', '00:00:00', 1, '00:00:00', 1, 0, 100000, NOW(), NOW(), $4),
       ($3, $3, 'energy', 'public', '00:00:00', 1, '00:00:00', 1, 0, 100000, NOW(), NOW(), $4)
ON CONFLICT DO NOTHING
RETURNING id, revenue_profile_id, type, user_type, CAST(begin_time AS time) AS begin_time, begin_day, CAST(end_time AS time) AS end_time,
  end_day, start_second, rate, created_at, updated_at, deleted_at;

-- name: CreateRevenueProfileTier :one
INSERT INTO podpoint.revenue_profile_tiers (id, revenue_profile_id, type, user_type, begin_time, begin_day, end_time,
                                            end_day, start_second, rate, created_at, updated_at, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW(), $11)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteRevenueProfileTiersInIDRange :exec
DELETE
FROM podpoint.revenue_profile_tiers
WHERE id BETWEEN $1 AND $2;

-- name: CreatePodModel :one
INSERT INTO podpoint.pod_models (id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp,
                                 supports_contactless, supports_simultaneous_dc_charging, image_url, created_at,
                                 updated_at, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeletePodModelsInIDRange :exec
DELETE
FROM podpoint.pod_models
WHERE id BETWEEN $1 AND $2;

-- name: CreateEvDriverDomain :one
INSERT INTO podpoint.ev_driver_domains (id, domain_name, group_id, created_at, updated_at, deleted_at)
VALUES ($1, $2, $3, NOW(), NOW(), $4)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteEvDriverDomainsInIDRange :exec
DELETE
FROM podpoint.ev_driver_domains
WHERE id BETWEEN $1 AND $2;

-- name: CreateGroupBillingAccount :one
INSERT INTO podpoint.group_billing_accounts (id, group_id, billing_account_id, created_at, updated_at, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteClaimedChargesInIDRange :exec
DELETE
FROM podpoint.claimed_charges
WHERE id BETWEEN $1 AND $2;

-- name: DeleteGroupBillingAccountsInIDRange :exec
DELETE
FROM podpoint.group_billing_accounts
WHERE id BETWEEN $1 AND $2;

-- name: CreateTariff :one
INSERT INTO podpoint.tariffs (id, currency, name)
VALUES ($1, $2, $3)
RETURNING *;

-- name: DeleteTariffsInIDRange :exec
DELETE
FROM podpoint.tariffs
WHERE id BETWEEN $1 AND $2;

-- name: CreateTariffTier :one
INSERT INTO podpoint.tariff_tiers (id, tariff_id, begin_time, end_time, rate, begin_day, end_day)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING *;

-- name: DeleteTariffTiersInIDRange :exec
DELETE
FROM podpoint.tariff_tiers
WHERE id BETWEEN $1 AND $2;

-- name: UpdateTariffTierRatesForLocationID :exec
UPDATE podpoint.tariff_tiers
SET rate = $1
WHERE id IN (SELECT tt.id
             FROM podpoint.tariff_tiers tt
                    INNER JOIN podpoint.tariffs t ON t.id = tt.tariff_id
                    INNER JOIN podpoint.pod_addresses pa ON t.id = pa.tariff_id
                    INNER JOIN podpoint.pod_locations pl ON pl.address_id = pa.id
             WHERE pl.id = $2);

-- name: DeleteTariff :exec
DELETE FROM podpoint.tariffs WHERE id = $1;

-- name: DeleteTariffTier :exec
DELETE FROM podpoint.tariff_tiers WHERE id = $1;

-- name: DeleteGroupBillingAccount :exec
DELETE FROM podpoint.group_billing_accounts WHERE id = $1;

-- name: DeleteBillingAccount :exec
DELETE FROM podpoint.billing_accounts WHERE id = $1;

-- name: DeleteGroup :exec
DELETE FROM podpoint.groups WHERE id = $1;

-- name: RetrieveCharge :one
SELECT * FROM podpoint.charges WHERE id = $1;

-- name: RetrieveBillingAccount :one
SELECT * FROM podpoint.billing_accounts WHERE id = $1;

-- name: RetrieveGuestAuthoriser :one
SELECT id FROM podpoint.authorisers WHERE type = 'guest' AND deleted_at IS NULL;

-- name: CreateClaimedCharge :one
INSERT INTO podpoint.claimed_charges
(id, pod_location_id, pod_door_id, user_id, billing_event_id, created_at, updated_at, deleted_at, claimed_by, authoriser_id, billing_account_id)
VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
ON CONFLICT DO NOTHING
RETURNING *;
