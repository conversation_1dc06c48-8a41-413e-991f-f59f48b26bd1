// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: podpoint.sql

package sqlc

import (
	"context"
	"database/sql"
	"time"

	"github.com/lib/pq"
)

const closeCharge = `-- name: CloseCharge :exec
UPDATE podpoint.charges
SET is_closed = 1
WHERE id = $1
`

func (q *Queries) CloseCharge(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, closeCharge, id)
	return err
}

const createAddress = `-- name: CreateAddress :one
INSERT INTO podpoint.pod_addresses (id, business_name, line_1, line_2,
                                    postal_town, postcode, country, description, type_id, deleted_at, group_id,
                                    tariff_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 2, $9, $10, $11)
ON CONFLICT DO NOTHING
RETURNING id, group_id, contact_name, email, telephone, business_name, line_1, line_2, postal_town, postcode, country, description, type_id, tariff_id, cost_per_kwh, created_at, updated_at, deleted_at
`

type CreateAddressParams struct {
	ID           int64         `json:"id"`
	BusinessName string        `json:"business_name"`
	Line1        string        `json:"line_1"`
	Line2        string        `json:"line_2"`
	PostalTown   string        `json:"postal_town"`
	Postcode     string        `json:"postcode"`
	Country      string        `json:"country"`
	Description  string        `json:"description"`
	DeletedAt    sql.NullTime  `json:"deleted_at"`
	GroupID      sql.NullInt64 `json:"group_id"`
	TariffID     sql.NullInt64 `json:"tariff_id"`
}

func (q *Queries) CreateAddress(ctx context.Context, arg CreateAddressParams) (PodpointPodAddress, error) {
	row := q.db.QueryRowContext(ctx, createAddress,
		arg.ID,
		arg.BusinessName,
		arg.Line1,
		arg.Line2,
		arg.PostalTown,
		arg.Postcode,
		arg.Country,
		arg.Description,
		arg.DeletedAt,
		arg.GroupID,
		arg.TariffID,
	)
	var i PodpointPodAddress
	err := row.Scan(
		&i.ID,
		&i.GroupID,
		&i.ContactName,
		&i.Email,
		&i.Telephone,
		&i.BusinessName,
		&i.Line1,
		&i.Line2,
		&i.PostalTown,
		&i.Postcode,
		&i.Country,
		&i.Description,
		&i.TypeID,
		&i.TariffID,
		&i.CostPerKwh,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createAuthoriser = `-- name: CreateAuthoriser :one
INSERT INTO podpoint.authorisers (id, uid, type, deleted_at, group_uid)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT DO NOTHING
RETURNING id, uid, type, deleted_at, group_uid
`

type CreateAuthoriserParams struct {
	ID        int64          `json:"id"`
	Uid       string         `json:"uid"`
	Type      string         `json:"type"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
	GroupUid  sql.NullString `json:"group_uid"`
}

func (q *Queries) CreateAuthoriser(ctx context.Context, arg CreateAuthoriserParams) (PodpointAuthoriser, error) {
	row := q.db.QueryRowContext(ctx, createAuthoriser,
		arg.ID,
		arg.Uid,
		arg.Type,
		arg.DeletedAt,
		arg.GroupUid,
	)
	var i PodpointAuthoriser
	err := row.Scan(
		&i.ID,
		&i.Uid,
		&i.Type,
		&i.DeletedAt,
		&i.GroupUid,
	)
	return i, err
}

const createBillingAccount = `-- name: CreateBillingAccount :one
INSERT INTO podpoint.billing_accounts (id, user_id, uid, balance,
                                       currency,
                                       business_name,
                                       line_1,
                                       line_2,
                                       postal_town,
                                       postcode,
                                       country,
                                       phone,
                                       mobile,
                                       payment_processor_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
ON CONFLICT DO NOTHING
RETURNING id, user_id, uid, balance, currency, business_name, line_1, line_2, postal_town, postcode, country, phone, mobile, payment_processor_id, created_at, updated_at, deleted_at
`

type CreateBillingAccountParams struct {
	ID                 int64          `json:"id"`
	UserID             sql.NullInt64  `json:"user_id"`
	Uid                string         `json:"uid"`
	Balance            int32          `json:"balance"`
	Currency           string         `json:"currency"`
	BusinessName       string         `json:"business_name"`
	Line1              string         `json:"line_1"`
	Line2              string         `json:"line_2"`
	PostalTown         string         `json:"postal_town"`
	Postcode           string         `json:"postcode"`
	Country            string         `json:"country"`
	Phone              sql.NullString `json:"phone"`
	Mobile             sql.NullString `json:"mobile"`
	PaymentProcessorID sql.NullString `json:"payment_processor_id"`
}

func (q *Queries) CreateBillingAccount(ctx context.Context, arg CreateBillingAccountParams) (PodpointBillingAccount, error) {
	row := q.db.QueryRowContext(ctx, createBillingAccount,
		arg.ID,
		arg.UserID,
		arg.Uid,
		arg.Balance,
		arg.Currency,
		arg.BusinessName,
		arg.Line1,
		arg.Line2,
		arg.PostalTown,
		arg.Postcode,
		arg.Country,
		arg.Phone,
		arg.Mobile,
		arg.PaymentProcessorID,
	)
	var i PodpointBillingAccount
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Uid,
		&i.Balance,
		&i.Currency,
		&i.BusinessName,
		&i.Line1,
		&i.Line2,
		&i.PostalTown,
		&i.Postcode,
		&i.Country,
		&i.Phone,
		&i.Mobile,
		&i.PaymentProcessorID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createBillingEvent = `-- name: CreateBillingEvent :one
INSERT INTO podpoint.billing_events (id, account_id, presentment_amount, presentment_currency, exchange_rate,
                                     settlement_amount, settlement_currency, amount, description, refunded_amount,
                                     user_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, ' ', 0, $9)
ON CONFLICT DO NOTHING
RETURNING id, account_id, presentment_amount, presentment_currency, exchange_rate, settlement_amount, settlement_currency, amount, transaction_provider, transaction_id, description, refunded_amount, created_at, processed_at, updated_at, deleted_at, user_id
`

type CreateBillingEventParams struct {
	ID                  int64         `json:"id"`
	AccountID           sql.NullInt64 `json:"account_id"`
	PresentmentAmount   int32         `json:"presentment_amount"`
	PresentmentCurrency string        `json:"presentment_currency"`
	ExchangeRate        string        `json:"exchange_rate"`
	SettlementAmount    int32         `json:"settlement_amount"`
	SettlementCurrency  string        `json:"settlement_currency"`
	Amount              sql.NullInt32 `json:"amount"`
	UserID              sql.NullInt64 `json:"user_id"`
}

func (q *Queries) CreateBillingEvent(ctx context.Context, arg CreateBillingEventParams) (PodpointBillingEvent, error) {
	row := q.db.QueryRowContext(ctx, createBillingEvent,
		arg.ID,
		arg.AccountID,
		arg.PresentmentAmount,
		arg.PresentmentCurrency,
		arg.ExchangeRate,
		arg.SettlementAmount,
		arg.SettlementCurrency,
		arg.Amount,
		arg.UserID,
	)
	var i PodpointBillingEvent
	err := row.Scan(
		&i.ID,
		&i.AccountID,
		&i.PresentmentAmount,
		&i.PresentmentCurrency,
		&i.ExchangeRate,
		&i.SettlementAmount,
		&i.SettlementCurrency,
		&i.Amount,
		&i.TransactionProvider,
		&i.TransactionID,
		&i.Description,
		&i.RefundedAmount,
		&i.CreatedAt,
		&i.ProcessedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.UserID,
	)
	return i, err
}

const createCharge = `-- name: CreateCharge :one
INSERT INTO podpoint.charges (id, location_id, unit_id, door, energy_cost, billing_event_id, starts_at, ends_at,
                              kwh_used, duration,
                              is_closed, group_id, claimed_charge_id, billing_account_id, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
ON CONFLICT DO NOTHING
RETURNING id, location_id, unit_id, job_id, door, energy_cost, tag_id, billing_account_id, billing_event_id, group_id, claimed_charge_id, charge_cycle_id, starts_at, ends_at, start_event_processed_at, end_event_processed_at, kwh_used, duration, is_closed, created_at, updated_at, deleted_at, generation_kwh_used
`

type CreateChargeParams struct {
	ID               int64         `json:"id"`
	LocationID       sql.NullInt64 `json:"location_id"`
	UnitID           int64         `json:"unit_id"`
	Door             int64         `json:"door"`
	EnergyCost       sql.NullInt32 `json:"energy_cost"`
	BillingEventID   sql.NullInt64 `json:"billing_event_id"`
	StartsAt         sql.NullTime  `json:"starts_at"`
	EndsAt           sql.NullTime  `json:"ends_at"`
	KwhUsed          string        `json:"kwh_used"`
	Duration         sql.NullInt64 `json:"duration"`
	IsClosed         int16         `json:"is_closed"`
	GroupID          sql.NullInt64 `json:"group_id"`
	ClaimedChargeID  sql.NullInt64 `json:"claimed_charge_id"`
	BillingAccountID sql.NullInt64 `json:"billing_account_id"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
}

func (q *Queries) CreateCharge(ctx context.Context, arg CreateChargeParams) (PodpointCharge, error) {
	row := q.db.QueryRowContext(ctx, createCharge,
		arg.ID,
		arg.LocationID,
		arg.UnitID,
		arg.Door,
		arg.EnergyCost,
		arg.BillingEventID,
		arg.StartsAt,
		arg.EndsAt,
		arg.KwhUsed,
		arg.Duration,
		arg.IsClosed,
		arg.GroupID,
		arg.ClaimedChargeID,
		arg.BillingAccountID,
		arg.DeletedAt,
	)
	var i PodpointCharge
	err := row.Scan(
		&i.ID,
		&i.LocationID,
		&i.UnitID,
		&i.JobID,
		&i.Door,
		&i.EnergyCost,
		&i.TagID,
		&i.BillingAccountID,
		&i.BillingEventID,
		&i.GroupID,
		&i.ClaimedChargeID,
		&i.ChargeCycleID,
		&i.StartsAt,
		&i.EndsAt,
		&i.StartEventProcessedAt,
		&i.EndEventProcessedAt,
		&i.KwhUsed,
		&i.Duration,
		&i.IsClosed,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.GenerationKwhUsed,
	)
	return i, err
}

const createClaimedCharge = `-- name: CreateClaimedCharge :one
INSERT INTO podpoint.claimed_charges
(id, pod_location_id, pod_door_id, user_id, billing_event_id, created_at, updated_at, deleted_at, claimed_by, authoriser_id, billing_account_id)
VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
ON CONFLICT DO NOTHING
RETURNING id, pod_location_id, pod_door_id, user_id, billing_event_id, created_at, updated_at, deleted_at, claimed_by, authoriser_id, billing_account_id
`

type CreateClaimedChargeParams struct {
	ID               int64         `json:"id"`
	PodLocationID    int64         `json:"pod_location_id"`
	PodDoorID        int64         `json:"pod_door_id"`
	UserID           sql.NullInt64 `json:"user_id"`
	BillingEventID   sql.NullInt64 `json:"billing_event_id"`
	CreatedAt        time.Time     `json:"created_at"`
	UpdatedAt        time.Time     `json:"updated_at"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
	ClaimedBy        sql.NullInt64 `json:"claimed_by"`
	AuthoriserID     int64         `json:"authoriser_id"`
	BillingAccountID sql.NullInt64 `json:"billing_account_id"`
}

func (q *Queries) CreateClaimedCharge(ctx context.Context, arg CreateClaimedChargeParams) (PodpointClaimedCharge, error) {
	row := q.db.QueryRowContext(ctx, createClaimedCharge,
		arg.ID,
		arg.PodLocationID,
		arg.PodDoorID,
		arg.UserID,
		arg.BillingEventID,
		arg.CreatedAt,
		arg.UpdatedAt,
		arg.DeletedAt,
		arg.ClaimedBy,
		arg.AuthoriserID,
		arg.BillingAccountID,
	)
	var i PodpointClaimedCharge
	err := row.Scan(
		&i.ID,
		&i.PodLocationID,
		&i.PodDoorID,
		&i.UserID,
		&i.BillingEventID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.ClaimedBy,
		&i.AuthoriserID,
		&i.BillingAccountID,
	)
	return i, err
}

const createEvDriver = `-- name: CreateEvDriver :one
INSERT INTO podpoint.ev_drivers (id, email, group_id, first_name, last_name)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT DO NOTHING
RETURNING id, email, first_name, last_name, group_id, created_at, updated_at, deleted_at
`

type CreateEvDriverParams struct {
	ID        int64  `json:"id"`
	Email     string `json:"email"`
	GroupID   int64  `json:"group_id"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

func (q *Queries) CreateEvDriver(ctx context.Context, arg CreateEvDriverParams) (PodpointEvDriver, error) {
	row := q.db.QueryRowContext(ctx, createEvDriver,
		arg.ID,
		arg.Email,
		arg.GroupID,
		arg.FirstName,
		arg.LastName,
	)
	var i PodpointEvDriver
	err := row.Scan(
		&i.ID,
		&i.Email,
		&i.FirstName,
		&i.LastName,
		&i.GroupID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createEvDriverDomain = `-- name: CreateEvDriverDomain :one
INSERT INTO podpoint.ev_driver_domains (id, domain_name, group_id, created_at, updated_at, deleted_at)
VALUES ($1, $2, $3, NOW(), NOW(), $4)
ON CONFLICT DO NOTHING
RETURNING id, domain_name, group_id, created_at, updated_at, deleted_at
`

type CreateEvDriverDomainParams struct {
	ID         int64        `json:"id"`
	DomainName string       `json:"domain_name"`
	GroupID    int64        `json:"group_id"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

func (q *Queries) CreateEvDriverDomain(ctx context.Context, arg CreateEvDriverDomainParams) (PodpointEvDriverDomain, error) {
	row := q.db.QueryRowContext(ctx, createEvDriverDomain,
		arg.ID,
		arg.DomainName,
		arg.GroupID,
		arg.DeletedAt,
	)
	var i PodpointEvDriverDomain
	err := row.Scan(
		&i.ID,
		&i.DomainName,
		&i.GroupID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createGroupBillingAccount = `-- name: CreateGroupBillingAccount :one
INSERT INTO podpoint.group_billing_accounts (id, group_id, billing_account_id, created_at, updated_at, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6)
ON CONFLICT DO NOTHING
RETURNING id, group_id, billing_account_id, created_at, updated_at, deleted_at
`

type CreateGroupBillingAccountParams struct {
	ID               int64        `json:"id"`
	GroupID          int64        `json:"group_id"`
	BillingAccountID int64        `json:"billing_account_id"`
	CreatedAt        sql.NullTime `json:"created_at"`
	UpdatedAt        sql.NullTime `json:"updated_at"`
	DeletedAt        sql.NullTime `json:"deleted_at"`
}

func (q *Queries) CreateGroupBillingAccount(ctx context.Context, arg CreateGroupBillingAccountParams) (PodpointGroupBillingAccount, error) {
	row := q.db.QueryRowContext(ctx, createGroupBillingAccount,
		arg.ID,
		arg.GroupID,
		arg.BillingAccountID,
		arg.CreatedAt,
		arg.UpdatedAt,
		arg.DeletedAt,
	)
	var i PodpointGroupBillingAccount
	err := row.Scan(
		&i.ID,
		&i.GroupID,
		&i.BillingAccountID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createLocation = `-- name: CreateLocation :one
INSERT INTO podpoint.pod_locations (id, uuid, address_id, longitude, latitude, geohash, description, payg_enabled,
                                    contactless_enabled, midmeter_enabled, is_public, is_home, is_ev_zone, unit_id,
                                    deleted_at, revenue_profile_id, timezone)
VALUES ($1, $2, $3, $4, $5, $6, ' ', $7, 0, 0, $8, $9, 0, $10, $11, $12, $13)
ON CONFLICT DO NOTHING
RETURNING id, address_id, revenue_profile_id, advert_id, longitude, latitude, geohash, name, description, payg_enabled, contactless_enabled, midmeter_enabled, is_public, is_home, is_ev_zone, unit_id, created_at, updated_at, deleted_at, timezone, uuid
`

type CreateLocationParams struct {
	ID               int64          `json:"id"`
	Uuid             string         `json:"uuid"`
	AddressID        int64          `json:"address_id"`
	Longitude        string         `json:"longitude"`
	Latitude         string         `json:"latitude"`
	Geohash          sql.NullInt64  `json:"geohash"`
	PaygEnabled      int16          `json:"payg_enabled"`
	IsPublic         int16          `json:"is_public"`
	IsHome           int16          `json:"is_home"`
	UnitID           sql.NullInt64  `json:"unit_id"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
	RevenueProfileID sql.NullInt64  `json:"revenue_profile_id"`
	Timezone         sql.NullString `json:"timezone"`
}

func (q *Queries) CreateLocation(ctx context.Context, arg CreateLocationParams) (PodpointPodLocation, error) {
	row := q.db.QueryRowContext(ctx, createLocation,
		arg.ID,
		arg.Uuid,
		arg.AddressID,
		arg.Longitude,
		arg.Latitude,
		arg.Geohash,
		arg.PaygEnabled,
		arg.IsPublic,
		arg.IsHome,
		arg.UnitID,
		arg.DeletedAt,
		arg.RevenueProfileID,
		arg.Timezone,
	)
	var i PodpointPodLocation
	err := row.Scan(
		&i.ID,
		&i.AddressID,
		&i.RevenueProfileID,
		&i.AdvertID,
		&i.Longitude,
		&i.Latitude,
		&i.Geohash,
		&i.Name,
		&i.Description,
		&i.PaygEnabled,
		&i.ContactlessEnabled,
		&i.MidmeterEnabled,
		&i.IsPublic,
		&i.IsHome,
		&i.IsEvZone,
		&i.UnitID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Timezone,
		&i.Uuid,
	)
	return i, err
}

const createMember = `-- name: CreateMember :one
INSERT INTO podpoint.members (id, email, group_id, first_name, last_name)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT DO NOTHING
RETURNING id, email, first_name, last_name, group_id, created_at, updated_at, deleted_at
`

type CreateMemberParams struct {
	ID        int64  `json:"id"`
	Email     string `json:"email"`
	GroupID   int64  `json:"group_id"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

func (q *Queries) CreateMember(ctx context.Context, arg CreateMemberParams) (PodpointMember, error) {
	row := q.db.QueryRowContext(ctx, createMember,
		arg.ID,
		arg.Email,
		arg.GroupID,
		arg.FirstName,
		arg.LastName,
	)
	var i PodpointMember
	err := row.Scan(
		&i.ID,
		&i.Email,
		&i.FirstName,
		&i.LastName,
		&i.GroupID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createOrganisation = `-- name: CreateOrganisation :one
INSERT INTO podpoint.groups (id, uid, type_id, name, contact_name, phone, business_name, line_1, line_2, postal_town,
                             postcode, country, fee_percentage, created_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
ON CONFLICT DO NOTHING
RETURNING id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at
`

type CreateOrganisationParams struct {
	ID            int64        `json:"id"`
	Uid           string       `json:"uid"`
	TypeID        int64        `json:"type_id"`
	Name          string       `json:"name"`
	ContactName   string       `json:"contact_name"`
	Phone         string       `json:"phone"`
	BusinessName  string       `json:"business_name"`
	Line1         string       `json:"line_1"`
	Line2         string       `json:"line_2"`
	PostalTown    string       `json:"postal_town"`
	Postcode      string       `json:"postcode"`
	Country       string       `json:"country"`
	FeePercentage string       `json:"fee_percentage"`
	CreatedAt     sql.NullTime `json:"created_at"`
}

func (q *Queries) CreateOrganisation(ctx context.Context, arg CreateOrganisationParams) (PodpointGroup, error) {
	row := q.db.QueryRowContext(ctx, createOrganisation,
		arg.ID,
		arg.Uid,
		arg.TypeID,
		arg.Name,
		arg.ContactName,
		arg.Phone,
		arg.BusinessName,
		arg.Line1,
		arg.Line2,
		arg.PostalTown,
		arg.Postcode,
		arg.Country,
		arg.FeePercentage,
		arg.CreatedAt,
	)
	var i PodpointGroup
	err := row.Scan(
		&i.ID,
		&i.Uid,
		&i.TypeID,
		&i.OwnerUserID,
		&i.Name,
		&i.ContactName,
		&i.Phone,
		&i.BusinessName,
		&i.Line1,
		&i.Line2,
		&i.PostalTown,
		&i.Postcode,
		&i.Country,
		&i.FeePercentage,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createPodModel = `-- name: CreatePodModel :one
INSERT INTO podpoint.pod_models (id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp,
                                 supports_contactless, supports_simultaneous_dc_charging, image_url, created_at,
                                 updated_at, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
ON CONFLICT DO NOTHING
RETURNING id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at
`

type CreatePodModelParams struct {
	ID                             int64          `json:"id"`
	VendorID                       int64          `json:"vendor_id"`
	RangeID                        sql.NullInt64  `json:"range_id"`
	PcbConfigurationID             sql.NullInt64  `json:"pcb_configuration_id"`
	Name                           string         `json:"name"`
	SupportsPayg                   int16          `json:"supports_payg"`
	SupportsOcpp                   int16          `json:"supports_ocpp"`
	SupportsContactless            int16          `json:"supports_contactless"`
	SupportsSimultaneousDcCharging int16          `json:"supports_simultaneous_dc_charging"`
	ImageUrl                       sql.NullString `json:"image_url"`
	CreatedAt                      sql.NullTime   `json:"created_at"`
	UpdatedAt                      sql.NullTime   `json:"updated_at"`
	DeletedAt                      sql.NullTime   `json:"deleted_at"`
}

func (q *Queries) CreatePodModel(ctx context.Context, arg CreatePodModelParams) (PodpointPodModel, error) {
	row := q.db.QueryRowContext(ctx, createPodModel,
		arg.ID,
		arg.VendorID,
		arg.RangeID,
		arg.PcbConfigurationID,
		arg.Name,
		arg.SupportsPayg,
		arg.SupportsOcpp,
		arg.SupportsContactless,
		arg.SupportsSimultaneousDcCharging,
		arg.ImageUrl,
		arg.CreatedAt,
		arg.UpdatedAt,
		arg.DeletedAt,
	)
	var i PodpointPodModel
	err := row.Scan(
		&i.ID,
		&i.VendorID,
		&i.RangeID,
		&i.PcbConfigurationID,
		&i.Name,
		&i.SupportsPayg,
		&i.SupportsOcpp,
		&i.SupportsContactless,
		&i.SupportsSimultaneousDcCharging,
		&i.ImageUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createRevenueProfile = `-- name: CreateRevenueProfile :one
INSERT INTO podpoint.revenue_profiles (id, group_id, name, currency, created_at, updated_at, deleted_at)
VALUES ($1, $2, $3, $4, NOW(), NOW(), $5)
ON CONFLICT DO NOTHING
RETURNING id, group_id, name, currency, created_at, updated_at, deleted_at
`

type CreateRevenueProfileParams struct {
	ID        int64        `json:"id"`
	GroupID   int64        `json:"group_id"`
	Name      string       `json:"name"`
	Currency  string       `json:"currency"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

func (q *Queries) CreateRevenueProfile(ctx context.Context, arg CreateRevenueProfileParams) (PodpointRevenueProfile, error) {
	row := q.db.QueryRowContext(ctx, createRevenueProfile,
		arg.ID,
		arg.GroupID,
		arg.Name,
		arg.Currency,
		arg.DeletedAt,
	)
	var i PodpointRevenueProfile
	err := row.Scan(
		&i.ID,
		&i.GroupID,
		&i.Name,
		&i.Currency,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createRevenueProfileTier = `-- name: CreateRevenueProfileTier :one
INSERT INTO podpoint.revenue_profile_tiers (id, revenue_profile_id, type, user_type, begin_time, begin_day, end_time,
                                            end_day, start_second, rate, created_at, updated_at, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW(), $11)
ON CONFLICT DO NOTHING
RETURNING id, revenue_profile_id, type, user_type, begin_time, begin_day, end_time, end_day, start_second, rate, created_at, updated_at, deleted_at
`

type CreateRevenueProfileTierParams struct {
	ID               int64          `json:"id"`
	RevenueProfileID int64          `json:"revenue_profile_id"`
	Type             Term           `json:"type"`
	UserType         UserType       `json:"user_type"`
	BeginTime        sql.NullString `json:"begin_time"`
	BeginDay         sql.NullInt64  `json:"begin_day"`
	EndTime          sql.NullString `json:"end_time"`
	EndDay           sql.NullInt64  `json:"end_day"`
	StartSecond      int64          `json:"start_second"`
	Rate             int64          `json:"rate"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
}

func (q *Queries) CreateRevenueProfileTier(ctx context.Context, arg CreateRevenueProfileTierParams) (PodpointRevenueProfileTier, error) {
	row := q.db.QueryRowContext(ctx, createRevenueProfileTier,
		arg.ID,
		arg.RevenueProfileID,
		arg.Type,
		arg.UserType,
		arg.BeginTime,
		arg.BeginDay,
		arg.EndTime,
		arg.EndDay,
		arg.StartSecond,
		arg.Rate,
		arg.DeletedAt,
	)
	var i PodpointRevenueProfileTier
	err := row.Scan(
		&i.ID,
		&i.RevenueProfileID,
		&i.Type,
		&i.UserType,
		&i.BeginTime,
		&i.BeginDay,
		&i.EndTime,
		&i.EndDay,
		&i.StartSecond,
		&i.Rate,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createRevenueProfileTiersForAllUserTypes = `-- name: CreateRevenueProfileTiersForAllUserTypes :many
INSERT INTO podpoint.revenue_profile_tiers (id, revenue_profile_id, type, user_type, begin_time, begin_day, end_time,
                                            end_day, start_second, rate, created_at, updated_at, deleted_at)
VALUES ($1, $3, 'energy', 'drivers', '00:00:00', 1, '00:00:00', 1, 0, 100000, NOW(), NOW(), $4),
       ($2, $3, 'energy', 'members', '00:00:00', 1, '00:00:00', 1, 0, 100000, NOW(), NOW(), $4),
       ($3, $3, 'energy', 'public', '00:00:00', 1, '00:00:00', 1, 0, 100000, NOW(), NOW(), $4)
ON CONFLICT DO NOTHING
RETURNING id, revenue_profile_id, type, user_type, CAST(begin_time AS time) AS begin_time, begin_day, CAST(end_time AS time) AS end_time,
  end_day, start_second, rate, created_at, updated_at, deleted_at
`

type CreateRevenueProfileTiersForAllUserTypesParams struct {
	ID               int64        `json:"id"`
	ID_2             int64        `json:"id_2"`
	RevenueProfileID int64        `json:"revenue_profile_id"`
	DeletedAt        sql.NullTime `json:"deleted_at"`
}

type CreateRevenueProfileTiersForAllUserTypesRow struct {
	ID               int64         `json:"id"`
	RevenueProfileID int64         `json:"revenue_profile_id"`
	Type             Term          `json:"type"`
	UserType         UserType      `json:"user_type"`
	BeginTime        time.Time     `json:"begin_time"`
	BeginDay         sql.NullInt64 `json:"begin_day"`
	EndTime          time.Time     `json:"end_time"`
	EndDay           sql.NullInt64 `json:"end_day"`
	StartSecond      int64         `json:"start_second"`
	Rate             int64         `json:"rate"`
	CreatedAt        sql.NullTime  `json:"created_at"`
	UpdatedAt        sql.NullTime  `json:"updated_at"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
}

func (q *Queries) CreateRevenueProfileTiersForAllUserTypes(ctx context.Context, arg CreateRevenueProfileTiersForAllUserTypesParams) ([]CreateRevenueProfileTiersForAllUserTypesRow, error) {
	rows, err := q.db.QueryContext(ctx, createRevenueProfileTiersForAllUserTypes,
		arg.ID,
		arg.ID_2,
		arg.RevenueProfileID,
		arg.DeletedAt,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CreateRevenueProfileTiersForAllUserTypesRow
	for rows.Next() {
		var i CreateRevenueProfileTiersForAllUserTypesRow
		if err := rows.Scan(
			&i.ID,
			&i.RevenueProfileID,
			&i.Type,
			&i.UserType,
			&i.BeginTime,
			&i.BeginDay,
			&i.EndTime,
			&i.EndDay,
			&i.StartSecond,
			&i.Rate,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const createTariff = `-- name: CreateTariff :one
INSERT INTO podpoint.tariffs (id, currency, name)
VALUES ($1, $2, $3)
RETURNING id, currency, name, user_id, energy_supplier_id, created_at, updated_at, deleted_at
`

type CreateTariffParams struct {
	ID       int64  `json:"id"`
	Currency string `json:"currency"`
	Name     string `json:"name"`
}

func (q *Queries) CreateTariff(ctx context.Context, arg CreateTariffParams) (PodpointTariff, error) {
	row := q.db.QueryRowContext(ctx, createTariff, arg.ID, arg.Currency, arg.Name)
	var i PodpointTariff
	err := row.Scan(
		&i.ID,
		&i.Currency,
		&i.Name,
		&i.UserID,
		&i.EnergySupplierID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createTariffTier = `-- name: CreateTariffTier :one
INSERT INTO podpoint.tariff_tiers (id, tariff_id, begin_time, end_time, rate, begin_day, end_day)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING id, rate, begin_time, begin_day, end_time, end_day, tariff_id, created_at, updated_at, deleted_at
`

type CreateTariffTierParams struct {
	ID        int64          `json:"id"`
	TariffID  int64          `json:"tariff_id"`
	BeginTime sql.NullString `json:"begin_time"`
	EndTime   sql.NullString `json:"end_time"`
	Rate      int64          `json:"rate"`
	BeginDay  sql.NullInt32  `json:"begin_day"`
	EndDay    sql.NullInt32  `json:"end_day"`
}

func (q *Queries) CreateTariffTier(ctx context.Context, arg CreateTariffTierParams) (PodpointTariffTier, error) {
	row := q.db.QueryRowContext(ctx, createTariffTier,
		arg.ID,
		arg.TariffID,
		arg.BeginTime,
		arg.EndTime,
		arg.Rate,
		arg.BeginDay,
		arg.EndDay,
	)
	var i PodpointTariffTier
	err := row.Scan(
		&i.ID,
		&i.Rate,
		&i.BeginTime,
		&i.BeginDay,
		&i.EndTime,
		&i.EndDay,
		&i.TariffID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createUnit = `-- name: CreateUnit :one
INSERT INTO podpoint.pod_units (id, ppid, name, model_id, status_id, relay_weld_flag, deleted_at)
VALUES ($1, $2, $3, $4, 1, 0, $5)
ON CONFLICT DO NOTHING
RETURNING id, ppid, name, model_id, status_id, config_id, ocpp_endpoint_id, installation_id, date_commissioned, last_contact, relay_weld_flag, created_at, updated_at, deleted_at
`

type CreateUnitParams struct {
	ID        int64          `json:"id"`
	Ppid      string         `json:"ppid"`
	Name      sql.NullString `json:"name"`
	ModelID   int64          `json:"model_id"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

func (q *Queries) CreateUnit(ctx context.Context, arg CreateUnitParams) (PodpointPodUnit, error) {
	row := q.db.QueryRowContext(ctx, createUnit,
		arg.ID,
		arg.Ppid,
		arg.Name,
		arg.ModelID,
		arg.DeletedAt,
	)
	var i PodpointPodUnit
	err := row.Scan(
		&i.ID,
		&i.Ppid,
		&i.Name,
		&i.ModelID,
		&i.StatusID,
		&i.ConfigID,
		&i.OcppEndpointID,
		&i.InstallationID,
		&i.DateCommissioned,
		&i.LastContact,
		&i.RelayWeldFlag,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createUser = `-- name: CreateUser :one
INSERT INTO podpoint.users (id, auth_id, group_id, role_id, email, salutation, first_name, last_name, password,
                            created_at, deleted_at, is_emailed_usage_data)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 0)
ON CONFLICT DO NOTHING
RETURNING id, auth_id, group_id, role_id, email, salutation, first_name, last_name, password, remember_token, verify_token, last_login, activated_at, last_notification_seen, created_at, updated_at, deleted_at, is_emailed_usage_data
`

type CreateUserParams struct {
	ID         int64          `json:"id"`
	AuthID     string         `json:"auth_id"`
	GroupID    sql.NullInt64  `json:"group_id"`
	RoleID     int64          `json:"role_id"`
	Email      string         `json:"email"`
	Salutation sql.NullString `json:"salutation"`
	FirstName  string         `json:"first_name"`
	LastName   string         `json:"last_name"`
	Password   sql.NullString `json:"password"`
	CreatedAt  sql.NullTime   `json:"created_at"`
	DeletedAt  sql.NullTime   `json:"deleted_at"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (PodpointUser, error) {
	row := q.db.QueryRowContext(ctx, createUser,
		arg.ID,
		arg.AuthID,
		arg.GroupID,
		arg.RoleID,
		arg.Email,
		arg.Salutation,
		arg.FirstName,
		arg.LastName,
		arg.Password,
		arg.CreatedAt,
		arg.DeletedAt,
	)
	var i PodpointUser
	err := row.Scan(
		&i.ID,
		&i.AuthID,
		&i.GroupID,
		&i.RoleID,
		&i.Email,
		&i.Salutation,
		&i.FirstName,
		&i.LastName,
		&i.Password,
		&i.RememberToken,
		&i.VerifyToken,
		&i.LastLogin,
		&i.ActivatedAt,
		&i.LastNotificationSeen,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsEmailedUsageData,
	)
	return i, err
}

const deleteAddress = `-- name: DeleteAddress :exec
DELETE
FROM podpoint.pod_addresses
WHERE id = $1
`

func (q *Queries) DeleteAddress(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteAddress, id)
	return err
}

const deleteAddressesInIDRange = `-- name: DeleteAddressesInIDRange :exec
DELETE
FROM podpoint.pod_addresses
WHERE id BETWEEN $1 AND $2
`

type DeleteAddressesInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteAddressesInIDRange(ctx context.Context, arg DeleteAddressesInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteAddressesInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteAuthoriser = `-- name: DeleteAuthoriser :exec
DELETE
FROM podpoint.authorisers
WHERE id = $1
`

func (q *Queries) DeleteAuthoriser(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteAuthoriser, id)
	return err
}

const deleteAuthorisersInIDRange = `-- name: DeleteAuthorisersInIDRange :exec
DELETE
FROM podpoint.authorisers
WHERE id BETWEEN $1 AND $2
`

type DeleteAuthorisersInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteAuthorisersInIDRange(ctx context.Context, arg DeleteAuthorisersInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteAuthorisersInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteBillingAccount = `-- name: DeleteBillingAccount :exec
DELETE FROM podpoint.billing_accounts WHERE id = $1
`

func (q *Queries) DeleteBillingAccount(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteBillingAccount, id)
	return err
}

const deleteBillingAccountsInIDRange = `-- name: DeleteBillingAccountsInIDRange :exec
DELETE
FROM podpoint.billing_accounts
WHERE id BETWEEN $1 AND $2
`

type DeleteBillingAccountsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteBillingAccountsInIDRange(ctx context.Context, arg DeleteBillingAccountsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteBillingAccountsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteBillingEvent = `-- name: DeleteBillingEvent :exec
DELETE
FROM podpoint.billing_events
WHERE id = $1
`

func (q *Queries) DeleteBillingEvent(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteBillingEvent, id)
	return err
}

const deleteBillingEventsInIDRange = `-- name: DeleteBillingEventsInIDRange :exec
DELETE
FROM podpoint.billing_events
WHERE id BETWEEN $1 AND $2
`

type DeleteBillingEventsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteBillingEventsInIDRange(ctx context.Context, arg DeleteBillingEventsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteBillingEventsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteCharge = `-- name: DeleteCharge :exec
DELETE
FROM podpoint.charges
WHERE id = $1
`

func (q *Queries) DeleteCharge(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteCharge, id)
	return err
}

const deleteCharges = `-- name: DeleteCharges :exec
DELETE
FROM podpoint.charges
WHERE id = ANY ($1::int[])
`

func (q *Queries) DeleteCharges(ctx context.Context, dollar_1 []int32) error {
	_, err := q.db.ExecContext(ctx, deleteCharges, pq.Array(dollar_1))
	return err
}

const deleteChargesInIDRange = `-- name: DeleteChargesInIDRange :exec
DELETE
FROM podpoint.charges
WHERE id BETWEEN $1 AND $2
`

type DeleteChargesInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteChargesInIDRange(ctx context.Context, arg DeleteChargesInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteChargesInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteClaimedChargesInIDRange = `-- name: DeleteClaimedChargesInIDRange :exec
DELETE
FROM podpoint.claimed_charges
WHERE id BETWEEN $1 AND $2
`

type DeleteClaimedChargesInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteClaimedChargesInIDRange(ctx context.Context, arg DeleteClaimedChargesInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteClaimedChargesInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteEvDriverDomainsInIDRange = `-- name: DeleteEvDriverDomainsInIDRange :exec
DELETE
FROM podpoint.ev_driver_domains
WHERE id BETWEEN $1 AND $2
`

type DeleteEvDriverDomainsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteEvDriverDomainsInIDRange(ctx context.Context, arg DeleteEvDriverDomainsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteEvDriverDomainsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteEvDriversInIDRange = `-- name: DeleteEvDriversInIDRange :exec
DELETE
FROM podpoint.ev_drivers
WHERE id BETWEEN $1 AND $2
`

type DeleteEvDriversInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteEvDriversInIDRange(ctx context.Context, arg DeleteEvDriversInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteEvDriversInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteGroup = `-- name: DeleteGroup :exec
DELETE FROM podpoint.groups WHERE id = $1
`

func (q *Queries) DeleteGroup(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteGroup, id)
	return err
}

const deleteGroupBillingAccount = `-- name: DeleteGroupBillingAccount :exec
DELETE FROM podpoint.group_billing_accounts WHERE id = $1
`

func (q *Queries) DeleteGroupBillingAccount(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteGroupBillingAccount, id)
	return err
}

const deleteGroupBillingAccountsInIDRange = `-- name: DeleteGroupBillingAccountsInIDRange :exec
DELETE
FROM podpoint.group_billing_accounts
WHERE id BETWEEN $1 AND $2
`

type DeleteGroupBillingAccountsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteGroupBillingAccountsInIDRange(ctx context.Context, arg DeleteGroupBillingAccountsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteGroupBillingAccountsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteGroupsInIDRange = `-- name: DeleteGroupsInIDRange :exec
DELETE
FROM podpoint.groups
WHERE id BETWEEN $1 AND $2
`

type DeleteGroupsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteGroupsInIDRange(ctx context.Context, arg DeleteGroupsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteGroupsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteLocation = `-- name: DeleteLocation :exec
DELETE
FROM podpoint.pod_locations
WHERE id = $1
`

func (q *Queries) DeleteLocation(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteLocation, id)
	return err
}

const deleteLocationsInIDRange = `-- name: DeleteLocationsInIDRange :exec
DELETE
FROM podpoint.pod_locations
WHERE id BETWEEN $1 AND $2
`

type DeleteLocationsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteLocationsInIDRange(ctx context.Context, arg DeleteLocationsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteLocationsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteMembersInIDRange = `-- name: DeleteMembersInIDRange :exec
DELETE
FROM podpoint.members
WHERE id BETWEEN $1 AND $2
`

type DeleteMembersInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteMembersInIDRange(ctx context.Context, arg DeleteMembersInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteMembersInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteOrganisation = `-- name: DeleteOrganisation :exec
DELETE
FROM podpoint.groups
WHERE id = $1
`

func (q *Queries) DeleteOrganisation(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteOrganisation, id)
	return err
}

const deletePodModelsInIDRange = `-- name: DeletePodModelsInIDRange :exec
DELETE
FROM podpoint.pod_models
WHERE id BETWEEN $1 AND $2
`

type DeletePodModelsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeletePodModelsInIDRange(ctx context.Context, arg DeletePodModelsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deletePodModelsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteRevenueProfileTiersInIDRange = `-- name: DeleteRevenueProfileTiersInIDRange :exec
DELETE
FROM podpoint.revenue_profile_tiers
WHERE id BETWEEN $1 AND $2
`

type DeleteRevenueProfileTiersInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteRevenueProfileTiersInIDRange(ctx context.Context, arg DeleteRevenueProfileTiersInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteRevenueProfileTiersInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteRevenueProfilesInIDRange = `-- name: DeleteRevenueProfilesInIDRange :exec
DELETE
FROM podpoint.revenue_profiles
WHERE id BETWEEN $1 AND $2
`

type DeleteRevenueProfilesInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteRevenueProfilesInIDRange(ctx context.Context, arg DeleteRevenueProfilesInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteRevenueProfilesInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteTariff = `-- name: DeleteTariff :exec
DELETE FROM podpoint.tariffs WHERE id = $1
`

func (q *Queries) DeleteTariff(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteTariff, id)
	return err
}

const deleteTariffTier = `-- name: DeleteTariffTier :exec
DELETE FROM podpoint.tariff_tiers WHERE id = $1
`

func (q *Queries) DeleteTariffTier(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteTariffTier, id)
	return err
}

const deleteTariffTiersInIDRange = `-- name: DeleteTariffTiersInIDRange :exec
DELETE
FROM podpoint.tariff_tiers
WHERE id BETWEEN $1 AND $2
`

type DeleteTariffTiersInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteTariffTiersInIDRange(ctx context.Context, arg DeleteTariffTiersInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteTariffTiersInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteTariffsInIDRange = `-- name: DeleteTariffsInIDRange :exec
DELETE
FROM podpoint.tariffs
WHERE id BETWEEN $1 AND $2
`

type DeleteTariffsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteTariffsInIDRange(ctx context.Context, arg DeleteTariffsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteTariffsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteUnit = `-- name: DeleteUnit :exec
DELETE
FROM podpoint.pod_units
WHERE id = $1
`

func (q *Queries) DeleteUnit(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteUnit, id)
	return err
}

const deleteUnitsInIDRange = `-- name: DeleteUnitsInIDRange :exec
DELETE
FROM podpoint.pod_units
WHERE id BETWEEN $1 AND $2
`

type DeleteUnitsInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteUnitsInIDRange(ctx context.Context, arg DeleteUnitsInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteUnitsInIDRange, arg.ID, arg.ID_2)
	return err
}

const deleteUser = `-- name: DeleteUser :exec
DELETE
FROM podpoint.users
WHERE id = $1
`

func (q *Queries) DeleteUser(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteUser, id)
	return err
}

const deleteUsersInIDRange = `-- name: DeleteUsersInIDRange :exec
DELETE
FROM podpoint.users
WHERE id BETWEEN $1 AND $2
`

type DeleteUsersInIDRangeParams struct {
	ID   int64 `json:"id"`
	ID_2 int64 `json:"id_2"`
}

func (q *Queries) DeleteUsersInIDRange(ctx context.Context, arg DeleteUsersInIDRangeParams) error {
	_, err := q.db.ExecContext(ctx, deleteUsersInIDRange, arg.ID, arg.ID_2)
	return err
}

const retrieveBillingAccount = `-- name: RetrieveBillingAccount :one
SELECT id, user_id, uid, balance, currency, business_name, line_1, line_2, postal_town, postcode, country, phone, mobile, payment_processor_id, created_at, updated_at, deleted_at FROM podpoint.billing_accounts WHERE id = $1
`

func (q *Queries) RetrieveBillingAccount(ctx context.Context, id int64) (PodpointBillingAccount, error) {
	row := q.db.QueryRowContext(ctx, retrieveBillingAccount, id)
	var i PodpointBillingAccount
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Uid,
		&i.Balance,
		&i.Currency,
		&i.BusinessName,
		&i.Line1,
		&i.Line2,
		&i.PostalTown,
		&i.Postcode,
		&i.Country,
		&i.Phone,
		&i.Mobile,
		&i.PaymentProcessorID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const retrieveCharge = `-- name: RetrieveCharge :one
SELECT id, location_id, unit_id, job_id, door, energy_cost, tag_id, billing_account_id, billing_event_id, group_id, claimed_charge_id, charge_cycle_id, starts_at, ends_at, start_event_processed_at, end_event_processed_at, kwh_used, duration, is_closed, created_at, updated_at, deleted_at, generation_kwh_used FROM podpoint.charges WHERE id = $1
`

func (q *Queries) RetrieveCharge(ctx context.Context, id int64) (PodpointCharge, error) {
	row := q.db.QueryRowContext(ctx, retrieveCharge, id)
	var i PodpointCharge
	err := row.Scan(
		&i.ID,
		&i.LocationID,
		&i.UnitID,
		&i.JobID,
		&i.Door,
		&i.EnergyCost,
		&i.TagID,
		&i.BillingAccountID,
		&i.BillingEventID,
		&i.GroupID,
		&i.ClaimedChargeID,
		&i.ChargeCycleID,
		&i.StartsAt,
		&i.EndsAt,
		&i.StartEventProcessedAt,
		&i.EndEventProcessedAt,
		&i.KwhUsed,
		&i.Duration,
		&i.IsClosed,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.GenerationKwhUsed,
	)
	return i, err
}

const retrieveGuestAuthoriser = `-- name: RetrieveGuestAuthoriser :one
SELECT id FROM podpoint.authorisers WHERE type = 'guest' AND deleted_at IS NULL
`

func (q *Queries) RetrieveGuestAuthoriser(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, retrieveGuestAuthoriser)
	var id int64
	err := row.Scan(&id)
	return id, err
}

const updateTariffTierRatesForLocationID = `-- name: UpdateTariffTierRatesForLocationID :exec
UPDATE podpoint.tariff_tiers
SET rate = $1
WHERE id IN (SELECT tt.id
             FROM podpoint.tariff_tiers tt
                    INNER JOIN podpoint.tariffs t ON t.id = tt.tariff_id
                    INNER JOIN podpoint.pod_addresses pa ON t.id = pa.tariff_id
                    INNER JOIN podpoint.pod_locations pl ON pl.address_id = pa.id
             WHERE pl.id = $2)
`

type UpdateTariffTierRatesForLocationIDParams struct {
	Rate int64 `json:"rate"`
	ID   int64 `json:"id"`
}

func (q *Queries) UpdateTariffTierRatesForLocationID(ctx context.Context, arg UpdateTariffTierRatesForLocationIDParams) error {
	_, err := q.db.ExecContext(ctx, updateTariffTierRatesForLocationID, arg.Rate, arg.ID)
	return err
}
