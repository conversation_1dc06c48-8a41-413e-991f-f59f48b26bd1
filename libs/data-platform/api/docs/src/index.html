<!DOCTYPE html>
<html>
  <head>
    <title>Data Platform API</title>
    <style type="text/css">
      body {
        font-family: Trebuchet MS, sans-serif;
        font-size: 15px;
        color: #444;
        margin-right: 24px;
      }

      h1 {
        font-size: 25px;
      }
      h2 {
        font-size: 20px;
      }
      h3 {
        font-size: 16px;
        font-weight: bold;
      }
      hr {
        height: 1px;
        border: 0;
        color: #ddd;
        background-color: #ddd;
      }

      .app-desc {
        clear: both;
        margin-left: 20px;
      }
      .param-name {
        width: 100%;
      }
      .license-info {
        margin-left: 20px;
      }

      .license-url {
        margin-left: 20px;
      }

      .model {
        margin: 0 0 0px 20px;
      }

      .method {
        margin-left: 20px;
      }

      .method-notes {
        margin: 10px 0 20px 0;
        font-size: 90%;
        color: #555;
      }

      pre {
        padding: 10px;
        margin-bottom: 2px;
      }

      .http-method {
        text-transform: uppercase;
      }

      pre.get {
        background-color: #0f6ab4;
      }

      pre.post {
        background-color: #10a54a;
      }

      pre.put {
        background-color: #c5862b;
      }

      pre.delete {
        background-color: #a41e22;
      }

      .huge {
        color: #fff;
      }

      pre.example {
        background-color: #f3f3f3;
        padding: 10px;
        border: 1px solid #ddd;
      }

      code {
        white-space: pre;
      }

      .nickname {
        font-weight: bold;
      }

      .method-path {
        font-size: 1.5em;
        background-color: #0f6ab4;
      }

      .up {
        float: right;
      }

      .parameter {
        width: 500px;
      }

      .param {
        width: 500px;
        padding: 10px 0 0 20px;
        font-weight: bold;
      }

      .param-desc {
        width: 700px;
        padding: 0 0 0 20px;
        color: #777;
      }

      .param-type {
        font-style: italic;
      }

      .param-enum-header {
        width: 700px;
        padding: 0 0 0 60px;
        color: #777;
        font-weight: bold;
      }

      .param-enum {
        width: 700px;
        padding: 0 0 0 80px;
        color: #777;
        font-style: italic;
      }

      .field-label {
        padding: 0;
        margin: 0;
        clear: both;
      }

      .field-items {
        padding: 0 0 15px 0;
        margin-bottom: 15px;
      }

      .return-type {
        clear: both;
        padding-bottom: 10px;
      }

      .param-header {
        font-weight: bold;
      }

      .method-tags {
        text-align: right;
      }

      .method-tag {
        background: none repeat scroll 0% 0% #24a600;
        border-radius: 3px;
        padding: 2px 10px;
        margin: 2px;
        color: #fff;
        display: inline-block;
        text-decoration: none;
      }
    </style>
  </head>
  <body>
    <h1>Data Platform API</h1>
    <div class="app-desc">
      Collection of APIs fulfilling needs in the experience domain.
    </div>
    <div class="app-desc">
      More information:
      <a href="https://openapi-generator.tech"
        >https://openapi-generator.tech</a
      >
    </div>
    <div class="app-desc">
      Contact Info: <a href="<EMAIL>"><EMAIL></a>
    </div>
    <div class="app-desc">Version: 0.0.1</div>
    <div class="app-desc">BasePath:</div>
    <div class="license-info">All rights reserved</div>
    <div class="license-url">http://apache.org/licenses/LICENSE-2.0.html</div>
    <h2>Access</h2>

    <h2><a name="__Methods">Methods</a></h2>
    [ Jump to <a href="#__Models">Models</a> ]

    <h3>Table of Contents</h3>
    <div class="method-summary"></div>
    <h4><a href="#CarbonIntensity">CarbonIntensity</a></h4>
    <ul>
      <li>
        <a href="#carbon intensityRetrieve regional forecast 48 hours from date"
          ><code
            ><span class="http-method">get</span>
            /regional/intensity/{from}/fw48h/regionid/{regionId}</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#ChargeAuthorisation">ChargeAuthorisation</a></h4>
    <ul>
      <li>
        <a href="#charge AuthorisationAuthoriseCharge"
          ><code
            ><span class="http-method">post</span>
            /charge-authorisations/{authorisationMethod}</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#ChargeCommands">ChargeCommands</a></h4>
    <ul>
      <li>
        <a href="#charge commandsCorrect energy cost"
          ><code
            ><span class="http-method">post</span>
            /commands/charges/{chargeID}/correct-energy-cost</code
          ></a
        >
      </li>
      <li>
        <a href="#charge commandsCorrect settlement amount"
          ><code
            ><span class="http-method">post</span>
            /commands/charges/{chargeID}/correct-settlement-amount</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#ChargeStatistics">ChargeStatistics</a></h4>
    <ul>
      <li>
        <a href="#charge StatisticsCharger Charge Statistics"
          ><code
            ><span class="http-method">get</span>
            /chargers/{chargerId}/charge-statistics</code
          ></a
        >
      </li>
      <li>
        <a href="#charge StatisticsGroup Charge Statistics"
          ><code
            ><span class="http-method">get</span>
            /groups/{groupId}/charge-statistics</code
          ></a
        >
      </li>
      <li>
        <a href="#charge StatisticsGroup Usage Summaries"
          ><code
            ><span class="http-method">get</span>
            /groups/{groupId}/charge-statistics/{interval}</code
          ></a
        >
      </li>
      <li>
        <a href="#charge StatisticsGroup and Charger Usage Summaries"
          ><code
            ><span class="http-method">get</span>
            /groups/{groupId}/chargers/{chargerId}/charge-statistics/{interval}</code
          ></a
        >
      </li>
      <li>
        <a href="#charge StatisticsGroup and Site Usage Summaries"
          ><code
            ><span class="http-method">get</span>
            /groups/{groupId}/sites/{siteId}/charge-statistics/{interval}</code
          ></a
        >
      </li>
      <li>
        <a href="#charge StatisticsSite Charge Statistics"
          ><code
            ><span class="http-method">get</span>
            /sites/{siteId}/charge-statistics</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#Chargers">Chargers</a></h4>
    <ul>
      <li>
        <a href="#chargersRetrieve DNO regions"
          ><code><span class="http-method">get</span> /dno-regions</code></a
        >
      </li>
      <li>
        <a href="#chargersRetrieve charger region"
          ><code
            ><span class="http-method">get</span>
            /chargers/{ppId}/dnoregion</code
          ></a
        >
      </li>
      <li>
        <a href="#chargersRetrieve charging limit"
          ><code
            ><span class="http-method">get</span> /chargers/{ppID}/limit</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#ChargingStatistics">ChargingStatistics</a></h4>
    <ul>
      <li>
        <a href="#charging statisticsCharger statistics"
          ><code
            ><span class="http-method">get</span>
            /charges/charger/{locationId}</code
          ></a
        >
      </li>
      <li>
        <a href="#charging statisticsOrganisation statistics"
          ><code
            ><span class="http-method">get</span>
            /charges/group/{organisationId}</code
          ></a
        >
      </li>
      <li>
        <a href="#charging statisticsSite statistics"
          ><code
            ><span class="http-method">get</span> /charges/site/{siteId}</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#DriverCharges">DriverCharges</a></h4>
    <ul>
      <li>
        <a href="#driver chargesCreate driver expenses"
          ><code
            ><span class="http-method">post</span>
            /drivers/{driverId}/organisations/{organisationId}/expenses</code
          ></a
        >
      </li>
      <li>
        <a href="#driver chargesRetrieve many"
          ><code
            ><span class="http-method">get</span>
            /organisations/{organisationId}/drivers/{userId}/charges</code
          ></a
        >
      </li>
      <li>
        <a href="#driver chargesRetrieve organisation drivers statistics"
          ><code
            ><span class="http-method">post</span>
            /organisations/{organisationId}/drivers/stats</code
          ></a
        >
      </li>
      <li>
        <a href="#driver chargesSubmit driver expenses"
          ><code
            ><span class="http-method">post</span>
            /drivers/{driverId}/groups/{organisationId}/expenses</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#Drivers">Drivers</a></h4>
    <ul>
      <li>
        <a href="#driversRetrieveCharge"
          ><code
            ><span class="http-method">get</span>
            /drivers/{driverId}/charges/{chargeId}</code
          ></a
        >
      </li>
      <li>
        <a href="#driversRetrieveCharges"
          ><code
            ><span class="http-method">get</span>
            /drivers/{driverId}/charges</code
          ></a
        >
      </li>
      <li>
        <a href="#driversRetrieveStats"
          ><code
            ><span class="http-method">get</span>
            /drivers/{driverId}/stats</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#LinkUser">LinkUser</a></h4>
    <ul>
      <li>
        <a href="#link userLink user to home charger"
          ><code
            ><span class="http-method">post</span>
            /link-user/{userId}/charger/{ppId}</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#OrganisationCharges">OrganisationCharges</a></h4>
    <ul>
      <li>
        <a href="#organisation chargesExpenses by organisation"
          ><code
            ><span class="http-method">get</span>
            /organisations/{organisationId}/submitted-charges</code
          ></a
        >
      </li>
      <li>
        <a
          href="#organisation chargesExpenses by organisation, grouped by driver"
          ><code
            ><span class="http-method">get</span>
            /organisations/{organisationId}/submitted-charges/drivers</code
          ></a
        >
      </li>
      <li>
        <a href="#organisation chargesFleet usage by organisation"
          ><code
            ><span class="http-method">get</span>
            /organisations/{organisationId}/fleet-usage</code
          ></a
        >
      </li>
      <li>
        <a href="#organisation chargesMark submitted charges as processed"
          ><code
            ><span class="http-method">post</span>
            /organisations/{organisationId}/process-charges</code
          ></a
        >
      </li>
      <li>
        <a href="#organisation chargesSubmitted charges for driver"
          ><code
            ><span class="http-method">get</span>
            /organisations/{organisationId}/submitted-charges/{driverId}</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#ProjectionCharges">ProjectionCharges</a></h4>
    <ul>
      <li>
        <a href="#projection ChargesProjection Charge Data"
          ><code><span class="http-method">get</span> /charges</code></a
        >
      </li>
    </ul>
    <h4><a href="#ProjectionGroupStatistics">ProjectionGroupStatistics</a></h4>
    <ul>
      <li>
        <a href="#projection Group StatisticsGroup site statistics"
          ><code
            ><span class="http-method">get</span>
            /charges/groups/{groupId}/sites</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#Sites">Sites</a></h4>
    <ul>
      <li>
        <a href="#sitesRetrieveChargeStatsGroupedBySite"
          ><code><span class="http-method">get</span> /sites</code></a
        >
      </li>
    </ul>
    <h4><a href="#Usage">Usage</a></h4>
    <ul>
      <li>
        <a href="#usageUsage by organisation"
          ><code
            ><span class="http-method">get</span>
            /organisations/{organisationId}/stats</code
          ></a
        >
      </li>
      <li>
        <a href="#usageUsage by organisation and charger"
          ><code
            ><span class="http-method">get</span>
            /organisations/{organisationId}/chargers/{locationId}/stats</code
          ></a
        >
      </li>
      <li>
        <a href="#usageUsage by organisation and site"
          ><code
            ><span class="http-method">get</span>
            /organisations/{organisationId}/sites/{siteId}/stats</code
          ></a
        >
      </li>
    </ul>
    <h4><a href="#UserCharges">UserCharges</a></h4>
    <ul>
      <li>
        <a href="#user ChargesGroup And User Charges"
          ><code
            ><span class="http-method">get</span>
            /groups/{groupId}/users/{userId}/charges</code
          ></a
        >
      </li>
    </ul>

    <h1><a name="CarbonIntensity">CarbonIntensity</a></h1>
    <div class="method">
      <a name="carbon intensityRetrieve regional forecast 48 hours from date" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /regional/intensity/{from}/fw48h/regionid/{regionId}</code></pre>
      </div>
      <div class="method-summary">
        Retrieve regional forecast 48 hours from date Carbon intensity (<span
          class="nickname"
          >carbon intensityRetrieve regional forecast 48 hours from date</span
        >)
      </div>
      <div class="method-notes">
        Retrieve half-hourly forecast data 48 hours from provided date.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Datetime is in
          ISO8601 and RFC3339 compliant format. default: null
        </div>
        <div class="param">regionId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Region ID of GB
          region. See list of Region IDs here:
          https://carbon-intensity.github.io/api-definitions/#region-list.
          default: null format: int64
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#Forecast">Forecast</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : {
    "data" : [ {
      "from" : "2018-01-20T12:00Z",
      "generationmix" : [ {
        "fuel" : "Dolorem molestias sed magnam consequatur.",
        "perc" : 0.85192853
      }, {
        "fuel" : "Dolorem molestias sed magnam consequatur.",
        "perc" : 0.85192853
      } ],
      "intensity" : {
        "forecast" : 1609991548261271046,
        "index" : "Amet autem dolorem."
      },
      "to" : "2018-01-20T12:00Z"
    }, {
      "from" : "2018-01-20T12:00Z",
      "generationmix" : [ {
        "fuel" : "Dolorem molestias sed magnam consequatur.",
        "perc" : 0.85192853
      }, {
        "fuel" : "Dolorem molestias sed magnam consequatur.",
        "perc" : 0.85192853
      } ],
      "intensity" : {
        "forecast" : 1609991548261271046,
        "index" : "Amet autem dolorem."
      },
      "to" : "2018-01-20T12:00Z"
    }, {
      "from" : "2018-01-20T12:00Z",
      "generationmix" : [ {
        "fuel" : "Dolorem molestias sed magnam consequatur.",
        "perc" : 0.85192853
      }, {
        "fuel" : "Dolorem molestias sed magnam consequatur.",
        "perc" : 0.85192853
      } ],
      "intensity" : {
        "forecast" : 1609991548261271046,
        "index" : "Amet autem dolorem."
      },
      "to" : "2018-01-20T12:00Z"
    }, {
      "from" : "2018-01-20T12:00Z",
      "generationmix" : [ {
        "fuel" : "Dolorem molestias sed magnam consequatur.",
        "perc" : 0.85192853
      }, {
        "fuel" : "Dolorem molestias sed magnam consequatur.",
        "perc" : 0.85192853
      } ],
      "intensity" : {
        "forecast" : 1609991548261271046,
        "index" : "Amet autem dolorem."
      },
      "to" : "2018-01-20T12:00Z"
    } ],
    "dnoregion" : "Voluptatem molestiae id sit.",
    "regionid" : 573640858367639627,
    "shortname" : "Veniam architecto dolorum libero distinctio doloremque quae."
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#Forecast">Forecast</a>
      <h4 class="field-label">400</h4>
      bad_request: invalid timestamp
      <a href="#"></a>
      <h4 class="field-label">500</h4>
      internal_server_error: Internal Server Error response.
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="ChargeAuthorisation">ChargeAuthorisation</a></h1>
    <div class="method">
      <a name="charge AuthorisationAuthoriseCharge" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="post"
        ><code class="huge"><span class="http-method">post</span> /charge-authorisations/{authorisationMethod}</code></pre>
      </div>
      <div class="method-summary">
        authoriseCharge Charge Authorisation (<span class="nickname"
          >charge AuthorisationAuthoriseCharge</span
        >)
      </div>
      <div class="method-notes">
        Authorises a charge claimed via RFID or OCPI. When RFID it checks that
        the rfid token is registered and that the group it is registered to
        matches the charger's group.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">authorisationMethod (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; One of rfid or
          ocpi. default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Consumes</h3>
      This API call consumes the following media types via the
      <span class="header">Content-Type</span> request header:
      <ul>
        <li><code>application/json</code></li>
      </ul>

      <h3 class="field-label">Request body</h3>
      <div class="field-items">
        <div class="param">
          AuthoriseChargeRequestBody
          <a href="#AuthoriseChargeRequestBody">AuthoriseChargeRequestBody</a>
          (required)
        </div>

        <div class="param-desc">
          <span class="param-type">Body Parameter</span> &mdash;
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#ChargeAuthorisationResponse">ChargeAuthorisationResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "authorised" : true,
  "id" : "04b7320d-c29e-4182-8675-1d6302f00e23",
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Dicta praesentium mollitia repellendus quia quibusdam dolores.",
  "status" : 1365167895320577558
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "fault" : true,
  "id" : "123abc",
  "message" : "parameter 'p' must be an integer",
  "name" : "bad_request",
  "temporary" : false,
  "timeout" : false
}</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#ChargeAuthorisationResponse">ChargeAuthorisationResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      authoriser_not_found: Not Found response.
      <a href="#ChargerPpidNotFound">ChargerPpidNotFound</a>
      <h4 class="field-label">500</h4>
      transaction_not_started: Internal Server Error response.
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="ChargeCommands">ChargeCommands</a></h1>
    <div class="method">
      <a name="charge commandsCorrect energy cost" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="post"
        ><code class="huge"><span class="http-method">post</span> /commands/charges/{chargeID}/correct-energy-cost</code></pre>
      </div>
      <div class="method-summary">
        Correct energy cost Charge commands (<span class="nickname"
          >charge commandsCorrect energy cost</span
        >)
      </div>
      <div class="method-notes">Update the energy cost of a charge.</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">chargeID (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          charge to correct. default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Consumes</h3>
      This API call consumes the following media types via the
      <span class="header">Content-Type</span> request header:
      <ul>
        <li><code>application/json</code></li>
      </ul>

      <h3 class="field-label">Request body</h3>
      <div class="field-items">
        <div class="param">
          CorrectEnergyCostRequestBody
          <a href="#CorrectEnergyCostRequestBody"
            >CorrectEnergyCostRequestBody</a
          >
          (required)
        </div>

        <div class="param-desc">
          <span class="param-type">Body Parameter</span> &mdash;
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#AggregateCostCorrectedResponse"
          >AggregateCostCorrectedResponse</a
        >
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "cost" : 16,
  "id" : "ebe2aada-b053-42be-bc84-b6325d427117",
  "submittedBy" : "John Doe"
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Harum voluptate ut ex voluptatem nemo ut.",
  "status" : 3481799398561556138
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Eos mollitia doloribus atque dolorem voluptatem et.",
  "status" : 3176125574086649892
}</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#AggregateCostCorrectedResponse"
        >AggregateCostCorrectedResponse</a
      >
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#ChargeExpensed">ChargeExpensed</a>
      <h4 class="field-label">404</h4>
      charge_not_found: Not Found response.
      <a href="#ChargeNotFound">ChargeNotFound</a>
      <h4 class="field-label">409</h4>
      duplicate_requests: Conflict response.
      <a href="#EventOutOfDate">EventOutOfDate</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="charge commandsCorrect settlement amount" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="post"
        ><code class="huge"><span class="http-method">post</span> /commands/charges/{chargeID}/correct-settlement-amount</code></pre>
      </div>
      <div class="method-summary">
        Correct settlement amount Charge commands (<span class="nickname"
          >charge commandsCorrect settlement amount</span
        >)
      </div>
      <div class="method-notes">Update the settlement amount of a charge.</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">chargeID (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          charge to correct. default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Consumes</h3>
      This API call consumes the following media types via the
      <span class="header">Content-Type</span> request header:
      <ul>
        <li><code>application/json</code></li>
      </ul>

      <h3 class="field-label">Request body</h3>
      <div class="field-items">
        <div class="param">
          CorrectSettlementAmountRequestBody
          <a href="#CorrectSettlementAmountRequestBody"
            >CorrectSettlementAmountRequestBody</a
          >
          (required)
        </div>

        <div class="param-desc">
          <span class="param-type">Body Parameter</span> &mdash;
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#AggregateSettlementAmountCorrectedResponse"
          >AggregateSettlementAmountCorrectedResponse</a
        >
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "id" : "6f31378a-591b-41bc-b72e-dc75571b155b",
  "settlementAmount" : 16,
  "submittedBy" : "John Doe"
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Harum voluptate ut ex voluptatem nemo ut.",
  "status" : 3481799398561556138
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Eos mollitia doloribus atque dolorem voluptatem et.",
  "status" : 3176125574086649892
}</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#AggregateSettlementAmountCorrectedResponse"
        >AggregateSettlementAmountCorrectedResponse</a
      >
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      charge_not_found: Not Found response.
      <a href="#ChargeNotFound">ChargeNotFound</a>
      <h4 class="field-label">409</h4>
      duplicate_requests: Conflict response.
      <a href="#EventOutOfDate">EventOutOfDate</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="ChargeStatistics">ChargeStatistics</a></h1>
    <div class="method">
      <a name="charge StatisticsCharger Charge Statistics" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /chargers/{chargerId}/charge-statistics</code></pre>
      </div>
      <div class="method-summary">
        Charger Charge Statistics Charge Statistics (<span class="nickname"
          >charge StatisticsCharger Charge Statistics</span
        >)
      </div>
      <div class="method-notes">
        Charge statistics for a charger, derived from projections.<br /><br />The
        from and to attributes are used to filter on the unpluggedAt field. Both
        the from and to fields are inclusive.<br /><br />Predecessor:
        /charges/charger/{locationId}
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">chargerId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; ID of the
          charger (equivalent to PPID). default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive from
          date used for filtering on unpluggedAt default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive to
          date used for filtering on unpluggedAt default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#ProjectionchargerChargeStatisticsResponse"
          >ProjectionchargerChargeStatisticsResponse</a
        >
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : {
    "chargingDuration" : 22245,
    "co2Savings" : 4325.62,
    "energy" : {
      "claimedUsage" : 243.89,
      "cost" : 324550,
      "revenueGeneratingClaimedUsage" : 43.5,
      "totalUsage" : 567.89,
      "unclaimedUsage" : 324.65
    },
    "numberOfCharges" : 10,
    "numberOfUsers" : 2,
    "revenueGenerated" : 457600
  },
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#ProjectionchargerChargeStatisticsResponse"
        >ProjectionchargerChargeStatisticsResponse</a
      >
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="charge StatisticsGroup Charge Statistics" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /groups/{groupId}/charge-statistics</code></pre>
      </div>
      <div class="method-summary">
        Group Charge Statistics Charge Statistics (<span class="nickname"
          >charge StatisticsGroup Charge Statistics</span
        >)
      </div>
      <div class="method-notes">
        Charge statistics for a group from projections.<br /><br />The from and
        to attributes are used to filter on the unpluggedAt field. Both the from
        and to fields are inclusive.<br /><br />Predecessor:
        /charges/group/{organisationId}
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">groupId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          group. default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive from
          date used for filtering on unpluggedAt default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive to
          date used for filtering on unpluggedAt default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#ProjectiongroupChargeStatisticsResponse"
          >ProjectiongroupChargeStatisticsResponse</a
        >
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : {
    "chargingDuration" : 22245,
    "co2Savings" : 4325.62,
    "energy" : {
      "claimedUsage" : 243.89,
      "cost" : 324550,
      "revenueGeneratingClaimedUsage" : 43.5,
      "totalUsage" : 567.89,
      "unclaimedUsage" : 324.65
    },
    "numberOfChargers" : 3,
    "numberOfCharges" : 10,
    "numberOfSites" : 3,
    "numberOfUsers" : 2,
    "revenueGenerated" : 457600
  },
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#ProjectiongroupChargeStatisticsResponse"
        >ProjectiongroupChargeStatisticsResponse</a
      >
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="charge StatisticsGroup Usage Summaries" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /groups/{groupId}/charge-statistics/{interval}</code></pre>
      </div>
      <div class="method-summary">
        Group Usage Summaries Charge Statistics (<span class="nickname"
          >charge StatisticsGroup Usage Summaries</span
        >)
      </div>
      <div class="method-notes">
        Usage summaries for all sites within the group, grouped by the given
        interval.<br /><br />The from and to attributes are used to filter on
        the unpluggedAt field. Both the from and to fields are inclusive.<br /><br />Predecessor:
        /organisations/{organisationId}/stats
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">groupId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          group. default: null format: uuid
        </div>
        <div class="param">interval (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Reporting
          interval default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive from
          date used for filtering on unpluggedAt default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive to
          date used for filtering on unpluggedAt default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#UsageResponse">UsageResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  },
  "usage" : [ {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Et eos quia eum consequatur.",
  "status" : 6074588362548896930
}</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#UsageResponse">UsageResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      group_not_found: group not found
      <a href="#GroupNotFound">GroupNotFound</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="charge StatisticsGroup and Charger Usage Summaries" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /groups/{groupId}/chargers/{chargerId}/charge-statistics/{interval}</code></pre>
      </div>
      <div class="method-summary">
        Group and Charger Usage Summaries Charge Statistics (<span
          class="nickname"
          >charge StatisticsGroup and Charger Usage Summaries</span
        >)
      </div>
      <div class="method-notes">
        Usage summaries for a charger within a group, sorted by the given
        interval.<br /><br />The from and to attributes are used to filter on
        the unpluggedAt field. Both the from and to fields are inclusive.<br /><br />Predecessor:
        /organisations/{organisationId}/chargers/{locationId}/stats
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">groupId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          group. default: null format: uuid
        </div>
        <div class="param">chargerId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; ID of the
          charger (equivalent to PPID). default: null
        </div>
        <div class="param">interval (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Reporting
          interval default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive from
          date used for filtering on unpluggedAt default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive to
          date used for filtering on unpluggedAt default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#UsageResponse">UsageResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  },
  "usage" : [ {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#UsageResponse">UsageResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="charge StatisticsGroup and Site Usage Summaries" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /groups/{groupId}/sites/{siteId}/charge-statistics/{interval}</code></pre>
      </div>
      <div class="method-summary">
        Group and Site Usage Summaries Charge Statistics (<span class="nickname"
          >charge StatisticsGroup and Site Usage Summaries</span
        >)
      </div>
      <div class="method-notes">
        Usage summaries for a site within the group, grouped by the given
        interval.<br /><br />The from and to attributes are used to filter on
        the unpluggedAt field. Both the from and to fields are inclusive.<br /><br />Predecessor:
        /organisations/{organisationId}/sites/{siteId}/stats
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">groupId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          group. default: null format: uuid
        </div>
        <div class="param">siteId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          site. default: null format: uuid
        </div>
        <div class="param">interval (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Reporting
          interval default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive from
          date used for filtering on unpluggedAt default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive to
          date used for filtering on unpluggedAt default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#UsageResponse">UsageResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  },
  "usage" : [ {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#UsageResponse">UsageResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="charge StatisticsSite Charge Statistics" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /sites/{siteId}/charge-statistics</code></pre>
      </div>
      <div class="method-summary">
        Site Charge Statistics Charge Statistics (<span class="nickname"
          >charge StatisticsSite Charge Statistics</span
        >)
      </div>
      <div class="method-notes">
        Charge statistics for a site from projections.<br /><br />The from and
        to attributes are used to filter on the unpluggedAt field. Both the from
        and to fields are inclusive.<br /><br />Predecessor:
        /charges/site/{siteId}
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">siteId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          site. default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive from
          date used for filtering on unpluggedAt default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Inclusive to
          date used for filtering on unpluggedAt default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#ProjectionsiteChargeStatisticsResponse"
          >ProjectionsiteChargeStatisticsResponse</a
        >
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : {
    "chargingDuration" : 22245,
    "co2Savings" : 4325.62,
    "energy" : {
      "claimedUsage" : 243.89,
      "cost" : 324550,
      "revenueGeneratingClaimedUsage" : 43.5,
      "totalUsage" : 567.89,
      "unclaimedUsage" : 324.65
    },
    "numberOfChargers" : 3,
    "numberOfCharges" : 10,
    "numberOfUsers" : 2,
    "revenueGenerated" : 457600
  },
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#ProjectionsiteChargeStatisticsResponse"
        >ProjectionsiteChargeStatisticsResponse</a
      >
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="Chargers">Chargers</a></h1>
    <div class="method">
      <a name="chargersRetrieve DNO regions" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /dno-regions</code></pre>
      </div>
      <div class="method-summary">
        Retrieve DNO regions Chargers (<span class="nickname"
          >chargersRetrieve DNO regions</span
        >)
      </div>
      <div class="method-notes">
        Retrieve the complete collection of DNO region objects.
      </div>

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#Regions">Regions</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : [ {
    "dnoregion" : "Scottish Hydro Electric Power Distribution",
    "regionid" : 1,
    "shortname" : "North Scotland"
  }, {
    "dnoregion" : "Scottish Hydro Electric Power Distribution",
    "regionid" : 1,
    "shortname" : "North Scotland"
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#Regions">Regions</a>
      <h4 class="field-label">500</h4>
      internal_server_error: Internal Server Error response.
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="chargersRetrieve charger region" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /chargers/{ppId}/dnoregion</code></pre>
      </div>
      <div class="method-summary">
        Retrieve charger region Chargers (<span class="nickname"
          >chargersRetrieve charger region</span
        >)
      </div>
      <div class="method-notes">
        For a given PSL / PPID, return the associated DNO region id.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">ppId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; PPID of a given
          charger default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#Region">Region</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "regionid" : 1
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#Region">Region</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when a location does not exist for the
      provided charger psl/ppid.
      <a href="#"></a>
      <h4 class="field-label">404</h4>
      not_found: Returns not found when a DNO region cannot be determined for
      the provided charger psl/ppid.
      <a href="#"></a>
      <h4 class="field-label">500</h4>
      internal_server_error: Internal Server Error response.
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="chargersRetrieve charging limit" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /chargers/{ppID}/limit</code></pre>
      </div>
      <div class="method-summary">
        Retrieve charging limit Chargers (<span class="nickname"
          >chargersRetrieve charging limit</span
        >)
      </div>
      <div class="method-notes">Retrieve limit for charging</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">ppID (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; PPID (PSL
          number) of a charger default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">authoriserUUID (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; UUID of charge
          authoriser - can be user's UUID or app name, like <code>dcs</code> or
          RFID key default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#ChargesLimitResponse">ChargesLimitResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "allowed" : true,
  "balance" : {
    "actual" : 75,
    "currency" : "GBP",
    "minimum" : 50
  },
  "limits" : [ {
    "amount" : 1.23,
    "type" : "energy",
    "unit" : "kWh"
  }, {
    "amount" : 1.23,
    "type" : "energy",
    "unit" : "kWh"
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#ChargesLimitResponse">ChargesLimitResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns 400 Bad Request when PPID or authoriser UUID is
      missing
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      not_found: Returns 404 Not Found, when PPID or authoriser UUID has not
      been found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="ChargingStatistics">ChargingStatistics</a></h1>
    <div class="method">
      <a name="charging statisticsCharger statistics" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /charges/charger/{locationId}</code></pre>
      </div>
      <div class="method-summary">
        Charger statistics Charging statistics (<span class="nickname"
          >charging statisticsCharger statistics</span
        >)
      </div>
      <div class="method-notes">
        Charge statistics for a single charger between two inclusive dates.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">locationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Primary key of
          the charger location. default: null format: int64
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive start date eg: 2022-01-01 default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive end date eg: 2022-01-31 default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#Chargerchargessummary">Chargerchargessummary</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "chargingDuration" : 22245,
  "co2Savings" : 4325.62,
  "energy" : {
    "claimedUsage" : 243.89,
    "cost" : 324550,
    "revenueGeneratingClaimedUsage" : 43.5,
    "totalUsage" : 567.89,
    "unclaimedUsage" : 324.65
  },
  "numberOfCharges" : 2048,
  "numberOfDrivers" : 64,
  "revenueGenerated" : 457600
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#Chargerchargessummary">Chargerchargessummary</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when date parameters are invalid
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      charger_not_found: Charger location not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="charging statisticsOrganisation statistics" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /charges/group/{organisationId}</code></pre>
      </div>
      <div class="method-summary">
        Organisation statistics Charging statistics (<span class="nickname"
          >charging statisticsOrganisation statistics</span
        >)
      </div>
      <div class="method-notes">
        Charge statistics for all sites with charges within an organisation
        (host group) between two inclusive dates. We do not check that the
        'from' and 'to' date parameters constitute a valid date range. Where the
        'to' date is prior to the 'from' date no results will be returned.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive start date eg: 2022-01-01 default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive end date eg: 2022-01-31 default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#Organisationchargessummary">Organisationchargessummary</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "chargingDuration" : 22245,
  "co2Savings" : 4325.62,
  "energy" : {
    "claimedUsage" : 243.89,
    "cost" : 324550,
    "revenueGeneratingClaimedUsage" : 43.5,
    "totalUsage" : 567.89,
    "unclaimedUsage" : 324.65
  },
  "numberOfChargers" : 32,
  "numberOfCharges" : 2048,
  "numberOfDrivers" : 64,
  "numberOfSites" : 16,
  "revenueGenerated" : 457600
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#Organisationchargessummary">Organisationchargessummary</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when organisation ID is malformed or date
      parameters are invalid
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      organisation_not_found: Organisation not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="charging statisticsSite statistics" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /charges/site/{siteId}</code></pre>
      </div>
      <div class="method-summary">
        Site statistics Charging statistics (<span class="nickname"
          >charging statisticsSite statistics</span
        >)
      </div>
      <div class="method-notes">
        Charge statistics for a site between two inclusive dates.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">siteId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: int64
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive start date eg: 2022-01-01 default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive end date eg: 2022-01-31 default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#Sitechargessummary">Sitechargessummary</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "chargingDuration" : 22245,
  "co2Savings" : 4325.62,
  "energy" : {
    "claimedUsage" : 243.89,
    "cost" : 324550,
    "revenueGeneratingClaimedUsage" : 43.5,
    "totalUsage" : 567.89,
    "unclaimedUsage" : 324.65
  },
  "numberOfChargers" : 32,
  "numberOfCharges" : 2048,
  "numberOfDrivers" : 64,
  "revenueGenerated" : 457600
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#Sitechargessummary">Sitechargessummary</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when site ID is malformed
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      site_not_found: site not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="DriverCharges">DriverCharges</a></h1>
    <div class="method">
      <a name="driver chargesCreate driver expenses" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="post"
        ><code class="huge"><span class="http-method">post</span> /drivers/{driverId}/organisations/{organisationId}/expenses</code></pre>
      </div>
      <div class="method-summary">
        Create driver expenses Driver charges (<span class="nickname"
          >driver chargesCreate driver expenses</span
        >)
      </div>
      <div class="method-notes">
        Submitting a list of charges as expenses for a driver within an
        organisation.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">driverId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: int64
        </div>
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: int64
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Consumes</h3>
      This API call consumes the following media types via the
      <span class="header">Content-Type</span> request header:
      <ul>
        <li><code>application/json</code></li>
      </ul>

      <h3 class="field-label">Request body</h3>
      <div class="field-items">
        <div class="param">
          CreateDriverExpensesRequestBody
          <a href="#CreateDriverExpensesRequestBody"
            >CreateDriverExpensesRequestBody</a
          >
          (required)
        </div>

        <div class="param-desc">
          <span class="param-type">Body Parameter</span> &mdash;
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#CreatedExpenseResponse">CreatedExpenseResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "expenses" : [ {
    "chargeId" : 123,
    "id" : 456
  }, {
    "chargeId" : 123,
    "id" : 456
  }, {
    "chargeId" : 123,
    "id" : 456
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Sed eos consectetur similique incidunt et.",
  "status" : 2960999694147322496
}</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">201</h4>
      Created response.
      <a href="#CreatedExpenseResponse">CreatedExpenseResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Driver ID, Organisation ID or Expenses payload is malformed.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      charge_not_found: charge not found
      <a href="#DriverNotFound">DriverNotFound</a>
      <h4 class="field-label">409</h4>
      duplicate_charge_submission: duplicate charge submission
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="driver chargesRetrieve many" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /organisations/{organisationId}/drivers/{userId}/charges</code></pre>
      </div>
      <div class="method-summary">
        Retrieve many Driver charges (<span class="nickname"
          >driver chargesRetrieve many</span
        >)
      </div>
      <div class="method-notes">Retrieve charges for a driver</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; The UUID of the
          group within which charges for the associated user have taken place
          default: null format: uuid
        </div>
        <div class="param">userId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; The UUID of the
          user default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Query param
          filter from charge endsAt datetime default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Query param
          filter to charge endsAt datetime default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#DriverChargesResponse">DriverChargesResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "charges" : [ {
    "businessName" : "Pod Point - Software Team",
    "chargeCost" : 324550,
    "chargerName" : "Nick-Gary",
    "chargingDuration" : 123,
    "co2Savings" : 4325.62,
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "pluggedInDuration" : 123,
    "revenueGenerated" : 457600,
    "startTime" : "2022-09-05T14:58:33Z"
  }, {
    "businessName" : "Pod Point - Software Team",
    "chargeCost" : 324550,
    "chargerName" : "Nick-Gary",
    "chargingDuration" : 123,
    "co2Savings" : 4325.62,
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "pluggedInDuration" : 123,
    "revenueGenerated" : 457600,
    "startTime" : "2022-09-05T14:58:33Z"
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#DriverChargesResponse">DriverChargesResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      organisation_not_found: organisation not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="driver chargesRetrieve organisation drivers statistics" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="post"
        ><code class="huge"><span class="http-method">post</span> /organisations/{organisationId}/drivers/stats</code></pre>
      </div>
      <div class="method-summary">
        Retrieve organisation drivers statistics Driver charges (<span
          class="nickname"
          >driver chargesRetrieve organisation drivers statistics</span
        >)
      </div>
      <div class="method-notes">
        Retrieve charge statistics for given organisation drivers
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; The UUID of the
          group within which charges for the associated drivers have taken place
          default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Consumes</h3>
      This API call consumes the following media types via the
      <span class="header">Content-Type</span> request header:
      <ul>
        <li><code>application/json</code></li>
      </ul>

      <h3 class="field-label">Request body</h3>
      <div class="field-items">
        <div class="param">
          RetrieveOrganisationDriversStatisticsRequestBody
          <a href="#RetrieveOrganisationDriversStatisticsRequestBody"
            >RetrieveOrganisationDriversStatisticsRequestBody</a
          >
          (required)
        </div>

        <div class="param-desc">
          <span class="param-type">Body Parameter</span> &mdash;
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Query param
          filter from charge endsAt datetime default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Query param
          filter to charge endsAt datetime default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#OrganisationDriversChargeStatisticsResponse"
          >OrganisationDriversChargeStatisticsResponse</a
        >
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "charges" : [ {
    "chargingDuration" : 123,
    "co2Savings" : 4325.62,
    "driver" : {
      "email" : "<EMAIL>",
      "firstName" : "Max",
      "fullName" : "Max Verstappen",
      "id" : 123,
      "lastName" : "Verstappen"
    },
    "numberOfCharges" : 2048,
    "pluggedInDuration" : 123,
    "revenueGenerated" : 457600,
    "totalCost" : 324550,
    "totalUsage" : 567.89
  }, {
    "chargingDuration" : 123,
    "co2Savings" : 4325.62,
    "driver" : {
      "email" : "<EMAIL>",
      "firstName" : "Max",
      "fullName" : "Max Verstappen",
      "id" : 123,
      "lastName" : "Verstappen"
    },
    "numberOfCharges" : 2048,
    "pluggedInDuration" : 123,
    "revenueGenerated" : 457600,
    "totalCost" : 324550,
    "totalUsage" : 567.89
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#OrganisationDriversChargeStatisticsResponse"
        >OrganisationDriversChargeStatisticsResponse</a
      >
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      organisation_not_found: organisation not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="driver chargesSubmit driver expenses" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="post"
        ><code class="huge"><span class="http-method">post</span> /drivers/{driverId}/groups/{organisationId}/expenses</code></pre>
      </div>
      <div class="method-summary">
        Submit driver expenses Driver charges (<span class="nickname"
          >driver chargesSubmit driver expenses</span
        >)
      </div>
      <div class="method-notes">
        Submitting a list of charges as expenses for a driver within a group.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">driverId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          driver. default: null format: uuid
        </div>
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: int64
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Consumes</h3>
      This API call consumes the following media types via the
      <span class="header">Content-Type</span> request header:
      <ul>
        <li><code>application/json</code></li>
      </ul>

      <h3 class="field-label">Request body</h3>
      <div class="field-items">
        <div class="param">
          SubmitDriverExpensesRequestBody
          <a href="#SubmitDriverExpensesRequestBody"
            >SubmitDriverExpensesRequestBody</a
          >
          (required)
        </div>

        <div class="param-desc">
          <span class="param-type">Body Parameter</span> &mdash;
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#SubmittedExpenseResponse">SubmittedExpenseResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "expenses" : [ {
    "chargeId" : "ccfb6246-8b69-4c6a-b019-653e1b155d5b",
    "id" : 456
  }, {
    "chargeId" : "ccfb6246-8b69-4c6a-b019-653e1b155d5b",
    "id" : 456
  }, {
    "chargeId" : "ccfb6246-8b69-4c6a-b019-653e1b155d5b",
    "id" : 456
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Sed eos consectetur similique incidunt et.",
  "status" : 2960999694147322496
}</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">201</h4>
      Created response.
      <a href="#SubmittedExpenseResponse">SubmittedExpenseResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Driver ID, Group ID or Expenses payload is malformed.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      charge_not_found: charge not found
      <a href="#DriverNotFound">DriverNotFound</a>
      <h4 class="field-label">409</h4>
      duplicate_charge_submission: duplicate charge submission
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="Drivers">Drivers</a></h1>
    <div class="method">
      <a name="driversRetrieveCharge" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /drivers/{driverId}/charges/{chargeId}</code></pre>
      </div>
      <div class="method-summary">
        retrieveCharge drivers (<span class="nickname"
          >driversRetrieveCharge</span
        >)
      </div>
      <div class="method-notes">Retrieve charge details.</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">driverId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; ID for the
          driver default: null format: uuid
        </div>
        <div class="param">chargeId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Charge ID
          default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#DriversChargeResponse">DriversChargeResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : {
    "charger" : {
      "door" : "A",
      "id" : "veefil-602300633",
      "name" : "Amoy-Reef",
      "pluggedInAt" : "2018-05-15T12:00:00+12:00",
      "pluggedInDuration" : 3600,
      "siteName" : "Ecclestone Court Car Park",
      "type" : "home",
      "unpluggedAt" : "2018-05-15T12:00:00+12:00"
    },
    "cost" : {
      "amount" : 2356,
      "currency" : "GBP"
    },
    "duration" : 22245,
    "endedAt" : "2018-05-15T12:00:00+12:00",
    "energyTotal" : 567.89,
    "expensedTo" : {
      "id" : "2072da84-bb3c-4c6a-afa5-e9b93b4fc682",
      "name" : "Adept Power Solutions Ltd"
    },
    "generationEnergyTotal" : 67.89,
    "gridEnergyTotal" : 500,
    "id" : "0d4c7438-d837-44ad-a96d-41c7fc616959",
    "startedAt" : "2018-05-15T12:00:00+12:00"
  },
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Sapiente officiis quo dolorum ut rem.",
  "status" : 7784555528056234299
}</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#DriversChargeResponse">DriversChargeResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      charge_not_found: Not Found response.
      <a href="#DriversChargeNotFound">DriversChargeNotFound</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="driversRetrieveCharges" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /drivers/{driverId}/charges</code></pre>
      </div>
      <div class="method-summary">
        retrieveCharges drivers (<span class="nickname"
          >driversRetrieveCharges</span
        >)
      </div>
      <div class="method-notes">
        Retrieve charge details for a given time range, sorted by started_at in
        descending order, falling back to plugged_in_at when necessary. When
        filtering, we first look for started_at and otherwise use the passed
        times to filter by plugged_in_at if started_at wasn't present.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">driverId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          driver default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive start date eg: 2022-01-01 default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive end date eg: 2022-01-31 default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#DriversChargesResponse">DriversChargesResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : {
    "charges" : [ {
      "charger" : {
        "door" : "A",
        "id" : "veefil-602300633",
        "name" : "Amoy-Reef",
        "pluggedInAt" : "2018-05-15T12:00:00+12:00",
        "pluggedInDuration" : 3600,
        "siteName" : "Ecclestone Court Car Park",
        "type" : "home",
        "unpluggedAt" : "2018-05-15T12:00:00+12:00"
      },
      "cost" : {
        "amount" : 2356,
        "currency" : "GBP"
      },
      "duration" : 22245,
      "endedAt" : "2018-05-15T12:00:00+12:00",
      "energyTotal" : 567.89,
      "expensedTo" : {
        "id" : "2072da84-bb3c-4c6a-afa5-e9b93b4fc682",
        "name" : "Adept Power Solutions Ltd"
      },
      "generationEnergyTotal" : 67.89,
      "gridEnergyTotal" : 500,
      "id" : "0d4c7438-d837-44ad-a96d-41c7fc616959",
      "startedAt" : "2018-05-15T12:00:00+12:00"
    }, {
      "charger" : {
        "door" : "A",
        "id" : "veefil-602300633",
        "name" : "Amoy-Reef",
        "pluggedInAt" : "2018-05-15T12:00:00+12:00",
        "pluggedInDuration" : 3600,
        "siteName" : "Ecclestone Court Car Park",
        "type" : "home",
        "unpluggedAt" : "2018-05-15T12:00:00+12:00"
      },
      "cost" : {
        "amount" : 2356,
        "currency" : "GBP"
      },
      "duration" : 22245,
      "endedAt" : "2018-05-15T12:00:00+12:00",
      "energyTotal" : 567.89,
      "expensedTo" : {
        "id" : "2072da84-bb3c-4c6a-afa5-e9b93b4fc682",
        "name" : "Adept Power Solutions Ltd"
      },
      "generationEnergyTotal" : 67.89,
      "gridEnergyTotal" : 500,
      "id" : "0d4c7438-d837-44ad-a96d-41c7fc616959",
      "startedAt" : "2018-05-15T12:00:00+12:00"
    } ],
    "count" : 22245
  },
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#DriversChargesResponse">DriversChargesResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="driversRetrieveStats" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /drivers/{driverId}/stats</code></pre>
      </div>
      <div class="method-summary">
        retrieveStats drivers (<span class="nickname">driversRetrieveStats</span
        >)
      </div>
      <div class="method-notes">
        Retrieve driver stats for a given time range, sorted by started_at in
        descending order, falling back to plugged_in_at when necessary. When
        filtering, we first look for started_at and otherwise use the passed
        times to filter by plugged_in_at if started_at wasn't present.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">driverId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          driver default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive start date eg: 2022-01-01 default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive end date eg: 2022-01-31 default: null format: date
        </div>
        <div class="param">interval (optional)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Time duration
          interval data should be provided in. default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#DriverStatsResponse">DriverStatsResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : {
    "intervals" : [ {
      "from" : "2022-10-12T00:00:00Z",
      "stats" : {
        "cost" : {
          "home" : [ {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          } ],
          "private" : [ {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          } ],
          "public" : [ {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          } ],
          "total" : [ {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          } ]
        },
        "duration" : {
          "home" : 680,
          "private" : 170,
          "public" : 170,
          "total" : 850
        },
        "energy" : {
          "home" : {
            "generation" : 12.5,
            "grid" : 123.4,
            "total" : 135.9
          },
          "private" : {
            "generation" : 12.5,
            "grid" : 123.4,
            "total" : 135.9
          },
          "public" : {
            "generation" : 12.5,
            "grid" : 123.4,
            "total" : 135.9
          },
          "total" : {
            "generation" : 12.5,
            "grid" : 123.4,
            "total" : 135.9
          }
        }
      },
      "to" : "2022-10-19T00:00:00Z"
    }, {
      "from" : "2022-10-12T00:00:00Z",
      "stats" : {
        "cost" : {
          "home" : [ {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          } ],
          "private" : [ {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          } ],
          "public" : [ {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          } ],
          "total" : [ {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          }, {
            "amount" : 2356,
            "currency" : "GBP"
          } ]
        },
        "duration" : {
          "home" : 680,
          "private" : 170,
          "public" : 170,
          "total" : 850
        },
        "energy" : {
          "home" : {
            "generation" : 12.5,
            "grid" : 123.4,
            "total" : 135.9
          },
          "private" : {
            "generation" : 12.5,
            "grid" : 123.4,
            "total" : 135.9
          },
          "public" : {
            "generation" : 12.5,
            "grid" : 123.4,
            "total" : 135.9
          },
          "total" : {
            "generation" : 12.5,
            "grid" : 123.4,
            "total" : 135.9
          }
        }
      },
      "to" : "2022-10-19T00:00:00Z"
    } ],
    "summary" : {
      "cost" : {
        "home" : [ {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        } ],
        "private" : [ {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        } ],
        "public" : [ {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        } ],
        "total" : [ {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        }, {
          "amount" : 2356,
          "currency" : "GBP"
        } ]
      },
      "duration" : {
        "home" : 680,
        "private" : 170,
        "public" : 170,
        "total" : 850
      },
      "energy" : {
        "home" : {
          "generation" : 12.5,
          "grid" : 123.4,
          "total" : 135.9
        },
        "private" : {
          "generation" : 12.5,
          "grid" : 123.4,
          "total" : 135.9
        },
        "public" : {
          "generation" : 12.5,
          "grid" : 123.4,
          "total" : 135.9
        },
        "total" : {
          "generation" : 12.5,
          "grid" : 123.4,
          "total" : 135.9
        }
      }
    }
  },
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#DriverStatsResponse">DriverStatsResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="LinkUser">LinkUser</a></h1>
    <div class="method">
      <a name="link userLink user to home charger" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="post"
        ><code class="huge"><span class="http-method">post</span> /link-user/{userId}/charger/{ppId}</code></pre>
      </div>
      <div class="method-summary">
        Link user to home charger Link user (<span class="nickname"
          >link userLink user to home charger</span
        >)
      </div>
      <div class="method-notes"></div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">ppId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; PPID (PSL
          number) of a charger default: null
        </div>
        <div class="param">userId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; The UUID of the
          user default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">202</h4>
      Accepted response.
      <a href="#"></a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      user_not_found: User not found
      <a href="#"></a>
      <h4 class="field-label">500</h4>
      internal_server_error: Internal Server Error response.
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="OrganisationCharges">OrganisationCharges</a></h1>
    <div class="method">
      <a name="organisation chargesExpenses by organisation" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /organisations/{organisationId}/submitted-charges</code></pre>
      </div>
      <div class="method-summary">
        Expenses by organisation Organisation charges (<span class="nickname"
          >organisation chargesExpenses by organisation</span
        >)
      </div>
      <div class="method-notes">
        Get all expensable charges per organisation between two dates (maximum 1
        month).
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">status (optional)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Filters the
          submitted expenses based on their status. Omitting this will return
          all submitted expenses. default: null
        </div>
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Charges
          expensed from and including this date will be returned. default: null
          format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Charges
          expensed up to and not including this date will be returned. default:
          null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#OrganisationChargesResponse">OrganisationChargesResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "expensableCharges" : [ {
    "chargeCost" : 123,
    "chargerName" : "Kent-Jake",
    "driver" : {
      "email" : "<EMAIL>",
      "firstName" : "Max",
      "fullName" : "Max Verstappen",
      "id" : 123,
      "lastName" : "Verstappen"
    },
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "id" : 123,
    "location" : {
      "address" : {
        "country" : "United Kingdom",
        "line1" : "234 Banner St",
        "line2" : "Westminster",
        "postcode" : "EC1Y 8QE",
        "prettyPrint" : "234 Banner St, Westminster, London, EC1Y 8QE",
        "town" : "London"
      },
      "id" : 456,
      "locationType" : "home"
    },
    "pluggedInAt" : "2022-09-30T15:35:00Z",
    "processedByFullName" : "John Smith",
    "processedTime" : "2022-09-05T14:58:33Z",
    "startTime" : "2022-09-05T14:58:33Z",
    "submittedTime" : "2022-09-05T17:58:33Z",
    "unpluggedAt" : "2022-09-30T16:46:00Z"
  }, {
    "chargeCost" : 123,
    "chargerName" : "Kent-Jake",
    "driver" : {
      "email" : "<EMAIL>",
      "firstName" : "Max",
      "fullName" : "Max Verstappen",
      "id" : 123,
      "lastName" : "Verstappen"
    },
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "id" : 123,
    "location" : {
      "address" : {
        "country" : "United Kingdom",
        "line1" : "234 Banner St",
        "line2" : "Westminster",
        "postcode" : "EC1Y 8QE",
        "prettyPrint" : "234 Banner St, Westminster, London, EC1Y 8QE",
        "town" : "London"
      },
      "id" : 456,
      "locationType" : "home"
    },
    "pluggedInAt" : "2022-09-30T15:35:00Z",
    "processedByFullName" : "John Smith",
    "processedTime" : "2022-09-05T14:58:33Z",
    "startTime" : "2022-09-05T14:58:33Z",
    "submittedTime" : "2022-09-05T17:58:33Z",
    "unpluggedAt" : "2022-09-30T16:46:00Z"
  }, {
    "chargeCost" : 123,
    "chargerName" : "Kent-Jake",
    "driver" : {
      "email" : "<EMAIL>",
      "firstName" : "Max",
      "fullName" : "Max Verstappen",
      "id" : 123,
      "lastName" : "Verstappen"
    },
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "id" : 123,
    "location" : {
      "address" : {
        "country" : "United Kingdom",
        "line1" : "234 Banner St",
        "line2" : "Westminster",
        "postcode" : "EC1Y 8QE",
        "prettyPrint" : "234 Banner St, Westminster, London, EC1Y 8QE",
        "town" : "London"
      },
      "id" : 456,
      "locationType" : "home"
    },
    "pluggedInAt" : "2022-09-30T15:35:00Z",
    "processedByFullName" : "John Smith",
    "processedTime" : "2022-09-05T14:58:33Z",
    "startTime" : "2022-09-05T14:58:33Z",
    "submittedTime" : "2022-09-05T17:58:33Z",
    "unpluggedAt" : "2022-09-30T16:46:00Z"
  }, {
    "chargeCost" : 123,
    "chargerName" : "Kent-Jake",
    "driver" : {
      "email" : "<EMAIL>",
      "firstName" : "Max",
      "fullName" : "Max Verstappen",
      "id" : 123,
      "lastName" : "Verstappen"
    },
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "id" : 123,
    "location" : {
      "address" : {
        "country" : "United Kingdom",
        "line1" : "234 Banner St",
        "line2" : "Westminster",
        "postcode" : "EC1Y 8QE",
        "prettyPrint" : "234 Banner St, Westminster, London, EC1Y 8QE",
        "town" : "London"
      },
      "id" : 456,
      "locationType" : "home"
    },
    "pluggedInAt" : "2022-09-30T15:35:00Z",
    "processedByFullName" : "John Smith",
    "processedTime" : "2022-09-05T14:58:33Z",
    "startTime" : "2022-09-05T14:58:33Z",
    "submittedTime" : "2022-09-05T17:58:33Z",
    "unpluggedAt" : "2022-09-30T16:46:00Z"
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#OrganisationChargesResponse">OrganisationChargesResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when org ID is malformed
      <a href="#TimeRangeOutOfBounds">TimeRangeOutOfBounds</a>
      <h4 class="field-label">404</h4>
      organisation_not_found: Organisation not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a
        name="organisation chargesExpenses by organisation, grouped by driver"
      />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /organisations/{organisationId}/submitted-charges/drivers</code></pre>
      </div>
      <div class="method-summary">
        Expenses by organisation, grouped by driver Organisation charges (<span
          class="nickname"
          >organisation chargesExpenses by organisation, grouped by driver</span
        >)
      </div>
      <div class="method-notes">
        Get all expensable charges per organisation between two dates (maximum 1
        month). This result set will be grouped per-user.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">status (optional)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Filters the
          submitted expenses based on their status. Omitting this will return
          all submitted expenses. default: null
        </div>
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Charges
          expensed from and including this date will be returned. default: null
          format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Charges
          expensed up to and not including this date will be returned. default:
          null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#OrganisationChargesDriverSummaryResponse"
          >OrganisationChargesDriverSummaryResponse</a
        >
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "driverExpensableChargeSummaries" : [ {
    "driver" : {
      "email" : "<EMAIL>",
      "firstName" : "Max",
      "fullName" : "Max Verstappen",
      "id" : 123,
      "lastName" : "Verstappen"
    },
    "submittedChargeIds" : [ 1, 2, 4, 42, 54 ],
    "totalCharges" : 1,
    "totalCost" : {
      "home" : 12300,
      "public" : 45670
    },
    "totalUsage" : {
      "home" : 12.34,
      "public" : 56.78,
      "total" : 12.34
    }
  }, {
    "driver" : {
      "email" : "<EMAIL>",
      "firstName" : "Max",
      "fullName" : "Max Verstappen",
      "id" : 123,
      "lastName" : "Verstappen"
    },
    "submittedChargeIds" : [ 1, 2, 4, 42, 54 ],
    "totalCharges" : 1,
    "totalCost" : {
      "home" : 12300,
      "public" : 45670
    },
    "totalUsage" : {
      "home" : 12.34,
      "public" : 56.78,
      "total" : 12.34
    }
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#OrganisationChargesDriverSummaryResponse"
        >OrganisationChargesDriverSummaryResponse</a
      >
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when org ID is malformed
      <a href="#TimeRangeOutOfBounds">TimeRangeOutOfBounds</a>
      <h4 class="field-label">404</h4>
      organisation_not_found: Organisation not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="organisation chargesFleet usage by organisation" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /organisations/{organisationId}/fleet-usage</code></pre>
      </div>
      <div class="method-summary">
        Fleet usage by organisation Organisation charges (<span class="nickname"
          >organisation chargesFleet usage by organisation</span
        >)
      </div>
      <div class="method-notes">
        This calendars month view of when the charges have been completed. It
        will only show the charges the drivers have submitted to expense
        regardless of the state.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#FleetUsageResponse">FleetUsageResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "co2Savings" : 4325.62,
  "numberOfDrivers" : 367,
  "totalCharges" : {
    "home" : 367,
    "public" : 367,
    "total" : 367
  },
  "totalUsage" : {
    "home" : 12.34,
    "public" : 56.78,
    "total" : 12.34
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#FleetUsageResponse">FleetUsageResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when org ID is malformed
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      organisation_not_found: organisation not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="organisation chargesMark submitted charges as processed" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="post"
        ><code class="huge"><span class="http-method">post</span> /organisations/{organisationId}/process-charges</code></pre>
      </div>
      <div class="method-summary">
        Mark submitted charges as processed Organisation charges (<span
          class="nickname"
          >organisation chargesMark submitted charges as processed</span
        >)
      </div>
      <div class="method-notes">Mark submitted charges as processed</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Consumes</h3>
      This API call consumes the following media types via the
      <span class="header">Content-Type</span> request header:
      <ul>
        <li><code>application/json</code></li>
      </ul>

      <h3 class="field-label">Request body</h3>
      <div class="field-items">
        <div class="param">
          MarkSubmittedChargesAsProcessedRequestBody
          <a href="#MarkSubmittedChargesAsProcessedRequestBody"
            >MarkSubmittedChargesAsProcessedRequestBody</a
          >
          (required)
        </div>

        <div class="param-desc">
          <span class="param-type">Body Parameter</span> &mdash;
        </div>
      </div>
      <!-- field-items -->

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#"></a>
      <h4 class="field-label">400</h4>
      charge_not_found_in_organisation: Bad Request response.
      <a href="#"></a>
      <h4 class="field-label">404</h4>
      organisation_not_found: Not Found response.
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="organisation chargesSubmitted charges for driver" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /organisations/{organisationId}/submitted-charges/{driverId}</code></pre>
      </div>
      <div class="method-summary">
        Submitted charges for driver Organisation charges (<span
          class="nickname"
          >organisation chargesSubmitted charges for driver</span
        >)
      </div>
      <div class="method-notes">Retrieve submitted charges for a driver</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: uuid
        </div>
        <div class="param">driverId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; default: null
          format: int64
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#SubmittedChargesResponse">SubmittedChargesResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "driver" : {
    "email" : "<EMAIL>",
    "firstName" : "Max",
    "fullName" : "Max Verstappen",
    "id" : 123,
    "lastName" : "Verstappen"
  },
  "submittedCharges" : [ {
    "chargeCost" : 123,
    "chargerName" : "Kent-Jake",
    "duration" : 123,
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "id" : 123,
    "location" : {
      "address" : {
        "country" : "United Kingdom",
        "line1" : "234 Banner St",
        "line2" : "Westminster",
        "postcode" : "EC1Y 8QE",
        "prettyPrint" : "234 Banner St, Westminster, London, EC1Y 8QE",
        "town" : "London"
      },
      "id" : 456,
      "locationType" : "home"
    },
    "pluggedInAt" : "2018-05-15T12:00:00+12:00",
    "processedByFullName" : "John Smith",
    "processedTime" : "2022-09-05T14:58:33Z",
    "startTime" : "2022-09-05T14:58:33Z",
    "status" : "NEW",
    "submittedTime" : "2022-09-05T17:58:33Z",
    "unpluggedAt" : "2018-05-15T12:00:00+12:00"
  }, {
    "chargeCost" : 123,
    "chargerName" : "Kent-Jake",
    "duration" : 123,
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "id" : 123,
    "location" : {
      "address" : {
        "country" : "United Kingdom",
        "line1" : "234 Banner St",
        "line2" : "Westminster",
        "postcode" : "EC1Y 8QE",
        "prettyPrint" : "234 Banner St, Westminster, London, EC1Y 8QE",
        "town" : "London"
      },
      "id" : 456,
      "locationType" : "home"
    },
    "pluggedInAt" : "2018-05-15T12:00:00+12:00",
    "processedByFullName" : "John Smith",
    "processedTime" : "2022-09-05T14:58:33Z",
    "startTime" : "2022-09-05T14:58:33Z",
    "status" : "NEW",
    "submittedTime" : "2022-09-05T17:58:33Z",
    "unpluggedAt" : "2018-05-15T12:00:00+12:00"
  }, {
    "chargeCost" : 123,
    "chargerName" : "Kent-Jake",
    "duration" : 123,
    "endTime" : "2022-09-05T17:58:33Z",
    "energyUsage" : 98.76,
    "id" : 123,
    "location" : {
      "address" : {
        "country" : "United Kingdom",
        "line1" : "234 Banner St",
        "line2" : "Westminster",
        "postcode" : "EC1Y 8QE",
        "prettyPrint" : "234 Banner St, Westminster, London, EC1Y 8QE",
        "town" : "London"
      },
      "id" : 456,
      "locationType" : "home"
    },
    "pluggedInAt" : "2018-05-15T12:00:00+12:00",
    "processedByFullName" : "John Smith",
    "processedTime" : "2022-09-05T14:58:33Z",
    "startTime" : "2022-09-05T14:58:33Z",
    "status" : "NEW",
    "submittedTime" : "2022-09-05T17:58:33Z",
    "unpluggedAt" : "2018-05-15T12:00:00+12:00"
  } ],
  "totalCost" : {
    "home" : 12300,
    "public" : 45670
  },
  "totalUsage" : {
    "home" : 12.34,
    "public" : 56.78,
    "total" : 12.34
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#SubmittedChargesResponse">SubmittedChargesResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when org ID is malformed
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      driver_not_found: driver not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="ProjectionCharges">ProjectionCharges</a></h1>
    <div class="method">
      <a name="projection ChargesProjection Charge Data" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /charges</code></pre>
      </div>
      <div class="method-summary">
        Projection Charge Data Projection Charges (<span class="nickname"
          >projection ChargesProjection Charge Data</span
        >)
      </div>
      <div class="method-notes">
        All projection charge data for a group, site or charger between two
        dates. From the start of the from day provided up to and excluding the
        to date provided.
      </div>

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">groupId (optional)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; UUID of the
          group. default: null format: uuid
        </div>
        <div class="param">siteId (optional)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; UUID of the
          site. default: null format: uuid
        </div>
        <div class="param">chargerId (optional)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Id of the
          charger. default: null
        </div>
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Query param
          filter from charge unpluggedAt datetime. The from field is inclusive.
          default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Query param
          filter to charge unpluggedAt datetime. The to field is exclusive.
          default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#ProjectionChargesResponse">ProjectionChargesResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : [ {
    "chargeDurationTotal" : 22245,
    "chargerId" : "PSL-0001",
    "chargerName" : "Corrupti sit dignissimos nam labore illo.",
    "co2Avoided" : 4325.62,
    "confirmed" : true,
    "door" : "A",
    "driverIDs" : [ "3e4a9823-d60e-4f14-aa88-eea8c327b8f1", "7a8b9c63-6a8b-4a68-8efa-097c1a6d257d" ],
    "endedAt" : "2018-05-15T12:00:00Z",
    "energyCost" : 32,
    "energyTotal" : 567.89,
    "generationEnergyTotal" : 35.44,
    "gridEnergyTotal" : 532.45,
    "id" : "5588483f-7125-414a-887f-6fdc37911182",
    "pluggedInAt" : "2018-05-15T12:00:00Z",
    "revenueGenerated" : 457600,
    "siteName" : "Et illum non quidem sequi veritatis quibusdam.",
    "startedAt" : "2018-05-15T12:00:00Z",
    "unpluggedAt" : "2018-05-15T12:00:00Z"
  }, {
    "chargeDurationTotal" : 22245,
    "chargerId" : "PSL-0001",
    "chargerName" : "Corrupti sit dignissimos nam labore illo.",
    "co2Avoided" : 4325.62,
    "confirmed" : true,
    "door" : "A",
    "driverIDs" : [ "3e4a9823-d60e-4f14-aa88-eea8c327b8f1", "7a8b9c63-6a8b-4a68-8efa-097c1a6d257d" ],
    "endedAt" : "2018-05-15T12:00:00Z",
    "energyCost" : 32,
    "energyTotal" : 567.89,
    "generationEnergyTotal" : 35.44,
    "gridEnergyTotal" : 532.45,
    "id" : "5588483f-7125-414a-887f-6fdc37911182",
    "pluggedInAt" : "2018-05-15T12:00:00Z",
    "revenueGenerated" : 457600,
    "siteName" : "Et illum non quidem sequi veritatis quibusdam.",
    "startedAt" : "2018-05-15T12:00:00Z",
    "unpluggedAt" : "2018-05-15T12:00:00Z"
  }, {
    "chargeDurationTotal" : 22245,
    "chargerId" : "PSL-0001",
    "chargerName" : "Corrupti sit dignissimos nam labore illo.",
    "co2Avoided" : 4325.62,
    "confirmed" : true,
    "door" : "A",
    "driverIDs" : [ "3e4a9823-d60e-4f14-aa88-eea8c327b8f1", "7a8b9c63-6a8b-4a68-8efa-097c1a6d257d" ],
    "endedAt" : "2018-05-15T12:00:00Z",
    "energyCost" : 32,
    "energyTotal" : 567.89,
    "generationEnergyTotal" : 35.44,
    "gridEnergyTotal" : 532.45,
    "id" : "5588483f-7125-414a-887f-6fdc37911182",
    "pluggedInAt" : "2018-05-15T12:00:00Z",
    "revenueGenerated" : 457600,
    "siteName" : "Et illum non quidem sequi veritatis quibusdam.",
    "startedAt" : "2018-05-15T12:00:00Z",
    "unpluggedAt" : "2018-05-15T12:00:00Z"
  } ],
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#ProjectionChargesResponse">ProjectionChargesResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#IdentifierNotProvided">IdentifierNotProvided</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="ProjectionGroupStatistics">ProjectionGroupStatistics</a></h1>
    <div class="method">
      <a name="projection Group StatisticsGroup site statistics" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /charges/groups/{groupId}/sites</code></pre>
      </div>
      <div class="method-summary">
        Group site statistics Projection Group Statistics (<span
          class="nickname"
          >projection Group StatisticsGroup site statistics</span
        >)
      </div>
      <div class="method-notes">
        Charge statistics for all sites within a group for the given month.
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">groupId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          group. default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">year (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Year to be
          queried. default: null format: int64
        </div>
        <div class="param">month (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Month to be
          queried where Jan = 1, Feb = 2 etc... default: null format: int64
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#GroupSitesStatsResponse">GroupSitesStatsResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : [ {
    "co2AvoidedKg" : 4325.62,
    "energyCost" : 3245,
    "energyUsageKwh" : 567.89,
    "numberOfCharges" : 2048,
    "numberOfDrivers" : 64,
    "revenueGenerated" : 457600,
    "siteId" : "12c88855-37f1-4f05-ba8d-0ea371bd6eee",
    "totalDuration" : 22245
  }, {
    "co2AvoidedKg" : 4325.62,
    "energyCost" : 3245,
    "energyUsageKwh" : 567.89,
    "numberOfCharges" : 2048,
    "numberOfDrivers" : 64,
    "revenueGenerated" : 457600,
    "siteId" : "12c88855-37f1-4f05-ba8d-0ea371bd6eee",
    "totalDuration" : 22245
  } ],
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "reason" : "Et eos quia eum consequatur.",
  "status" : 6074588362548896930
}</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#GroupSitesStatsResponse">GroupSitesStatsResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      group_not_found: Group not found
      <a href="#GroupNotFound">GroupNotFound</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="Sites">Sites</a></h1>
    <div class="method">
      <a name="sitesRetrieveChargeStatsGroupedBySite" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /sites</code></pre>
      </div>
      <div class="method-summary">
        retrieveChargeStatsGroupedBySite sites (<span class="nickname"
          >sitesRetrieveChargeStatsGroupedBySite</span
        >)
      </div>
      <div class="method-notes">Retrieve charge information by site.</div>

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive start date eg: 2022-01-01 default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Statistics
          report inclusive end date eg: 2022-01-31 default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#SiteStatsResponse">SiteStatsResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "count" : 650,
  "data" : [ {
    "groupId" : "594c3d4b-c093-40dc-b191-2850071fe1b0",
    "groupName" : "Voluptates aut velit nihil id quam.",
    "id" : "d4d86287-ebd1-454e-9bd2-c3049563c061",
    "name" : "Ipsum provident.",
    "revenueGenerated" : 16,
    "totalEnergy" : 567.89
  }, {
    "groupId" : "594c3d4b-c093-40dc-b191-2850071fe1b0",
    "groupName" : "Voluptates aut velit nihil id quam.",
    "id" : "d4d86287-ebd1-454e-9bd2-c3049563c061",
    "name" : "Ipsum provident.",
    "revenueGenerated" : 16,
    "totalEnergy" : 567.89
  }, {
    "groupId" : "594c3d4b-c093-40dc-b191-2850071fe1b0",
    "groupName" : "Voluptates aut velit nihil id quam.",
    "id" : "d4d86287-ebd1-454e-9bd2-c3049563c061",
    "name" : "Ipsum provident.",
    "revenueGenerated" : 16,
    "totalEnergy" : 567.89
  }, {
    "groupId" : "594c3d4b-c093-40dc-b191-2850071fe1b0",
    "groupName" : "Voluptates aut velit nihil id quam.",
    "id" : "d4d86287-ebd1-454e-9bd2-c3049563c061",
    "name" : "Ipsum provident.",
    "revenueGenerated" : 16,
    "totalEnergy" : 567.89
  } ],
  "from" : "2022-10-12",
  "to" : "2022-10-19"
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#SiteStatsResponse">SiteStatsResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when date is invalid
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="Usage">Usage</a></h1>
    <div class="method">
      <a name="usageUsage by organisation" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /organisations/{organisationId}/stats</code></pre>
      </div>
      <div class="method-summary">
        Usage by organisation usage (<span class="nickname"
          >usageUsage by organisation</span
        >)
      </div>
      <div class="method-notes">
        Organisation usage between two inclusive dates
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          organisation default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; default: null
          format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; default: null
          format: date
        </div>
        <div class="param">interval (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Reporting
          interval default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#UsageResponse">UsageResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  },
  "usage" : [ {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#UsageResponse">UsageResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      organisation_not_found: organisation not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="usageUsage by organisation and charger" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /organisations/{organisationId}/chargers/{locationId}/stats</code></pre>
      </div>
      <div class="method-summary">
        Usage by organisation and charger usage (<span class="nickname"
          >usageUsage by organisation and charger</span
        >)
      </div>
      <div class="method-notes">Charger usage between two inclusive dates</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          organisation default: null format: uuid
        </div>
        <div class="param">locationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Primary key of
          the charger location from podadmin default: null format: int32
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; default: null
          format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; default: null
          format: date
        </div>
        <div class="param">interval (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Reporting
          interval default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#UsageResponse">UsageResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  },
  "usage" : [ {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#UsageResponse">UsageResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      charger_not_found: charger not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <div class="method">
      <a name="usageUsage by organisation and site" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /organisations/{organisationId}/sites/{siteId}/stats</code></pre>
      </div>
      <div class="method-summary">
        Usage by organisation and site usage (<span class="nickname"
          >usageUsage by organisation and site</span
        >)
      </div>
      <div class="method-notes">Site usage between two inclusive dates</div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">organisationId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; UUID of the
          organisation default: null format: uuid
        </div>
        <div class="param">siteId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; Site ID -
          primary key of the podadmin pod_addresses table default: null format:
          int32
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; default: null
          format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; default: null
          format: date
        </div>
        <div class="param">interval (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Reporting
          interval default: null
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#UsageResponse">UsageResponse</a>
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  },
  "usage" : [ {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  }, {
    "co2Savings" : 2469.32,
    "cost" : 135668,
    "intervalStartDate" : "2023-06-07",
    "revenueGenerated" : 16,
    "totalUsage" : 669.2
  } ]
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#UsageResponse">UsageResponse</a>
      <h4 class="field-label">400</h4>
      bad_request: Bad Request response.
      <a href="#Error">Error</a>
      <h4 class="field-label">404</h4>
      site_not_found: site not found
      <a href="#"></a>
    </div>
    <!-- method -->
    <hr />
    <h1><a name="UserCharges">UserCharges</a></h1>
    <div class="method">
      <a name="user ChargesGroup And User Charges" />
      <div class="method-path">
        <a class="up" href="#__Methods">Up</a>
        <pre
          class="get"
        ><code class="huge"><span class="http-method">get</span> /groups/{groupId}/users/{userId}/charges</code></pre>
      </div>
      <div class="method-summary">
        Group And User Charges User Charges (<span class="nickname"
          >user ChargesGroup And User Charges</span
        >)
      </div>
      <div class="method-notes">
        Charges of a user within the group, derived from projections.<br /><br />The
        from and to attributes are used to filter on the unpluggedAt field. Both
        the from and to fields are inclusive.<br /><br />Predecessor:
        /organisations/{organisationId}/drivers/{userId}/charges
      </div>

      <h3 class="field-label">Path parameters</h3>
      <div class="field-items">
        <div class="param">groupId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; The UUID of the
          group within which charges for the associated user have taken place
          default: null format: uuid
        </div>
        <div class="param">userId (required)</div>

        <div class="param-desc">
          <span class="param-type">Path Parameter</span> &mdash; The UUID of the
          user default: null format: uuid
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Query parameters</h3>
      <div class="field-items">
        <div class="param">from (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Query param
          filter from charge endsAt datetime default: null format: date
        </div>
        <div class="param">to (required)</div>

        <div class="param-desc">
          <span class="param-type">Query Parameter</span> &mdash; Query param
          filter to charge endsAt datetime default: null format: date
        </div>
      </div>
      <!-- field-items -->

      <h3 class="field-label">Return type</h3>
      <div class="return-type">
        <a href="#ProjectionGroupAndUserChargesResponse"
          >ProjectionGroupAndUserChargesResponse</a
        >
      </div>

      <!--Todo: process Response Object and its headers, schema, examples -->

      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/json
      </div>
      <pre class="example"><code>{
  "data" : {
    "charges" : [ {
      "businessName" : "Pod Point - Software Team",
      "chargeCost" : 324550,
      "chargerName" : "Nick-Gary",
      "chargingDuration" : 123,
      "co2Savings" : 4325.62,
      "endTime" : "2022-09-05T17:58:33Z",
      "energyUsage" : 98.76,
      "pluggedInDuration" : 123,
      "revenueGenerated" : 457600,
      "startTime" : "2022-09-05T14:58:33Z"
    }, {
      "businessName" : "Pod Point - Software Team",
      "chargeCost" : 324550,
      "chargerName" : "Nick-Gary",
      "chargingDuration" : 123,
      "co2Savings" : 4325.62,
      "endTime" : "2022-09-05T17:58:33Z",
      "energyUsage" : 98.76,
      "pluggedInDuration" : 123,
      "revenueGenerated" : 457600,
      "startTime" : "2022-09-05T14:58:33Z"
    }, {
      "businessName" : "Pod Point - Software Team",
      "chargeCost" : 324550,
      "chargerName" : "Nick-Gary",
      "chargingDuration" : 123,
      "co2Savings" : 4325.62,
      "endTime" : "2022-09-05T17:58:33Z",
      "energyUsage" : 98.76,
      "pluggedInDuration" : 123,
      "revenueGenerated" : 457600,
      "startTime" : "2022-09-05T14:58:33Z"
    } ]
  },
  "meta" : {
    "params" : {
      "Consequatur et error hic sunt qui." : "Porro quae.",
      "Eos eos et laudantium." : "Reprehenderit vitae consequatur quia molestias.",
      "Quis et possimus dicta voluptatem ut." : "Consequatur possimus esse blanditiis."
    }
  }
}</code></pre>
      <h3 class="field-label">Example data</h3>
      <div class="example-data-content-type">
        Content-Type: application/vnd.goa.error
      </div>
      <pre
        class="example"
      ><code>Custom MIME type example not yet supported: application/vnd.goa.error</code></pre>

      <h3 class="field-label">Produces</h3>
      This API call produces the following media types according to the
      <span class="header">Accept</span> request header; the media type will be
      conveyed by the <span class="header">Content-Type</span> response header.
      <ul>
        <li><code>application/json</code></li>
        <li><code>application/vnd.goa.error</code></li>
      </ul>

      <h3 class="field-label">Responses</h3>
      <h4 class="field-label">200</h4>
      OK response.
      <a href="#ProjectionGroupAndUserChargesResponse"
        >ProjectionGroupAndUserChargesResponse</a
      >
      <h4 class="field-label">400</h4>
      bad_request: Returns bad request when request parameters are invalid
      <a href="#Error">Error</a>
    </div>
    <!-- method -->
    <hr />

    <h2><a name="__Models">Models</a></h2>
    [ Jump to <a href="#__Methods">Methods</a> ]

    <h3>Table of Contents</h3>
    <ol>
      <li>
        <a href="#AggregateCostCorrectedResponse"
          ><code>AggregateCostCorrectedResponse</code> -
        </a>
      </li>
      <li>
        <a href="#AggregateSettlementAmountCorrectedResponse"
          ><code>AggregateSettlementAmountCorrectedResponse</code> -
        </a>
      </li>
      <li>
        <a href="#AuthoriseChargeRequestBody"
          ><code>AuthoriseChargeRequestBody</code> -
        </a>
      </li>
      <li>
        <a href="#AuthoriserNotFound"><code>AuthoriserNotFound</code> - </a>
      </li>
      <li>
        <a href="#Balance"><code>Balance</code> - </a>
      </li>
      <li>
        <a href="#Breakdown"><code>Breakdown</code> - </a>
      </li>
      <li>
        <a href="#Charge"><code>Charge</code> - </a>
      </li>
      <li>
        <a href="#ChargeAggregateNotFound"
          ><code>ChargeAggregateNotFound</code> -
        </a>
      </li>
      <li>
        <a href="#ChargeAuthorisationResponse"
          ><code>ChargeAuthorisationResponse</code> -
        </a>
      </li>
      <li>
        <a href="#ChargeEnergySummary"><code>ChargeEnergySummary</code> - </a>
      </li>
      <li>
        <a href="#ChargeExpensed"><code>ChargeExpensed</code> - </a>
      </li>
      <li>
        <a href="#ChargeNotFound"><code>ChargeNotFound</code> - </a>
      </li>
      <li>
        <a href="#Charger"><code>Charger</code> - </a>
      </li>
      <li>
        <a href="#ChargerChargeStatistics"
          ><code>ChargerChargeStatistics</code> -
        </a>
      </li>
      <li>
        <a href="#ChargerPpidNotFound"><code>ChargerPpidNotFound</code> - </a>
      </li>
      <li>
        <a href="#Chargerchargessummary"
          ><code>Chargerchargessummary</code> -
        </a>
      </li>
      <li>
        <a href="#Charges"><code>Charges</code> - </a>
      </li>
      <li>
        <a href="#Charges2"><code>Charges2</code> - </a>
      </li>
      <li>
        <a href="#Charges3"><code>Charges3</code> - </a>
      </li>
      <li>
        <a href="#ChargesLimitResponse"><code>ChargesLimitResponse</code> - </a>
      </li>
      <li>
        <a href="#CorrectEnergyCostRequestBody"
          ><code>CorrectEnergyCostRequestBody</code> -
        </a>
      </li>
      <li>
        <a href="#CorrectSettlementAmountRequestBody"
          ><code>CorrectSettlementAmountRequestBody</code> -
        </a>
      </li>
      <li>
        <a href="#Cost"><code>Cost</code> - </a>
      </li>
      <li>
        <a href="#CreateDriverExpensesRequestBody"
          ><code>CreateDriverExpensesRequestBody</code> -
        </a>
      </li>
      <li>
        <a href="#CreateExpenseRequest"><code>CreateExpenseRequest</code> - </a>
      </li>
      <li>
        <a href="#CreatedExpense"><code>CreatedExpense</code> - </a>
      </li>
      <li>
        <a href="#CreatedExpenseResponse"
          ><code>CreatedExpenseResponse</code> -
        </a>
      </li>
      <li>
        <a href="#Dnoregion"><code>Dnoregion</code> - </a>
      </li>
      <li>
        <a href="#Driver"><code>Driver</code> - </a>
      </li>
      <li>
        <a href="#DriverCharge"><code>DriverCharge</code> - </a>
      </li>
      <li>
        <a href="#DriverChargesResponse"
          ><code>DriverChargesResponse</code> -
        </a>
      </li>
      <li>
        <a href="#DriverNotFound"><code>DriverNotFound</code> - </a>
      </li>
      <li>
        <a href="#DriverStatsData"><code>DriverStatsData</code> - </a>
      </li>
      <li>
        <a href="#DriverStatsResponse"><code>DriverStatsResponse</code> - </a>
      </li>
      <li>
        <a href="#DriversChargeNotFound"
          ><code>DriversChargeNotFound</code> -
        </a>
      </li>
      <li>
        <a href="#DriversChargeResponse"
          ><code>DriversChargeResponse</code> -
        </a>
      </li>
      <li>
        <a href="#DriversChargesResponse"
          ><code>DriversChargesResponse</code> -
        </a>
      </li>
      <li>
        <a href="#Duration"><code>Duration</code> - </a>
      </li>
      <li>
        <a href="#Energy"><code>Energy</code> - </a>
      </li>
      <li>
        <a href="#EnergyStatistics"><code>EnergyStatistics</code> - </a>
      </li>
      <li>
        <a href="#Error"><code>Error</code> - </a>
      </li>
      <li>
        <a href="#EventOutOfDate"><code>EventOutOfDate</code> - </a>
      </li>
      <li>
        <a href="#ExpensableCharge"><code>ExpensableCharge</code> - </a>
      </li>
      <li>
        <a href="#ExpensableChargeDriverSummary"
          ><code>ExpensableChargeDriverSummary</code> -
        </a>
      </li>
      <li>
        <a href="#ExpensedTo"><code>ExpensedTo</code> - </a>
      </li>
      <li>
        <a href="#FleetUsageResponse"><code>FleetUsageResponse</code> - </a>
      </li>
      <li>
        <a href="#Forecast"><code>Forecast</code> - </a>
      </li>
      <li>
        <a href="#Forecastdata"><code>Forecastdata</code> - </a>
      </li>
      <li>
        <a href="#GroupChargeStatistics"
          ><code>GroupChargeStatistics</code> -
        </a>
      </li>
      <li>
        <a href="#GroupNotFound"><code>GroupNotFound</code> - </a>
      </li>
      <li>
        <a href="#GroupSitesStats"><code>GroupSitesStats</code> - </a>
      </li>
      <li>
        <a href="#GroupSitesStatsResponse"
          ><code>GroupSitesStatsResponse</code> -
        </a>
      </li>
      <li>
        <a href="#IdentifierNotProvided"
          ><code>IdentifierNotProvided</code> -
        </a>
      </li>
      <li>
        <a href="#Intensity"><code>Intensity</code> - </a>
      </li>
      <li>
        <a href="#Interval"><code>Interval</code> - </a>
      </li>
      <li>
        <a href="#Limit"><code>Limit</code> - </a>
      </li>
      <li>
        <a href="#MarkSubmittedChargesAsProcessedRequestBody"
          ><code>MarkSubmittedChargesAsProcessedRequestBody</code> -
        </a>
      </li>
      <li>
        <a href="#Meta"><code>Meta</code> - </a>
      </li>
      <li>
        <a href="#Mix"><code>Mix</code> - </a>
      </li>
      <li>
        <a href="#Money"><code>Money</code> - </a>
      </li>
      <li>
        <a href="#MoneyInt64"><code>MoneyInt64</code> - </a>
      </li>
      <li>
        <a href="#OrganisationChargesDriverSummaryResponse"
          ><code>OrganisationChargesDriverSummaryResponse</code> -
        </a>
      </li>
      <li>
        <a href="#OrganisationChargesResponse"
          ><code>OrganisationChargesResponse</code> -
        </a>
      </li>
      <li>
        <a href="#OrganisationDriversChargeStatistics"
          ><code>OrganisationDriversChargeStatistics</code> -
        </a>
      </li>
      <li>
        <a href="#OrganisationDriversChargeStatisticsResponse"
          ><code>OrganisationDriversChargeStatisticsResponse</code> -
        </a>
      </li>
      <li>
        <a href="#Organisationchargessummary"
          ><code>Organisationchargessummary</code> -
        </a>
      </li>
      <li>
        <a href="#Period"><code>Period</code> - </a>
      </li>
      <li>
        <a href="#ProjectionChargesResponse"
          ><code>ProjectionChargesResponse</code> -
        </a>
      </li>
      <li>
        <a href="#ProjectionGroupAndUserChargesResponse"
          ><code>ProjectionGroupAndUserChargesResponse</code> -
        </a>
      </li>
      <li>
        <a href="#ProjectionchargerChargeStatisticsResponse"
          ><code>ProjectionchargerChargeStatisticsResponse</code> -
        </a>
      </li>
      <li>
        <a href="#ProjectiongroupChargeStatisticsResponse"
          ><code>ProjectiongroupChargeStatisticsResponse</code> -
        </a>
      </li>
      <li>
        <a href="#ProjectionsiteChargeStatisticsResponse"
          ><code>ProjectionsiteChargeStatisticsResponse</code> -
        </a>
      </li>
      <li>
        <a href="#Region"><code>Region</code> - </a>
      </li>
      <li>
        <a href="#Regions"><code>Regions</code> - </a>
      </li>
      <li>
        <a href="#RetrieveOrganisationDriversStatisticsRequestBody"
          ><code>RetrieveOrganisationDriversStatisticsRequestBody</code> -
        </a>
      </li>
      <li>
        <a href="#SiteChargeStatistics"><code>SiteChargeStatistics</code> - </a>
      </li>
      <li>
        <a href="#SiteStats"><code>SiteStats</code> - </a>
      </li>
      <li>
        <a href="#SiteStatsResponse"><code>SiteStatsResponse</code> - </a>
      </li>
      <li>
        <a href="#Sitechargessummary"><code>Sitechargessummary</code> - </a>
      </li>
      <li>
        <a href="#StatsSummary"><code>StatsSummary</code> - </a>
      </li>
      <li>
        <a href="#SubmitDriverExpensesRequestBody"
          ><code>SubmitDriverExpensesRequestBody</code> -
        </a>
      </li>
      <li>
        <a href="#SubmitExpenseRequest"><code>SubmitExpenseRequest</code> - </a>
      </li>
      <li>
        <a href="#SubmittedCharge"><code>SubmittedCharge</code> - </a>
      </li>
      <li>
        <a href="#SubmittedChargeAddress"
          ><code>SubmittedChargeAddress</code> -
        </a>
      </li>
      <li>
        <a href="#SubmittedChargeLocation"
          ><code>SubmittedChargeLocation</code> -
        </a>
      </li>
      <li>
        <a href="#SubmittedChargesResponse"
          ><code>SubmittedChargesResponse</code> -
        </a>
      </li>
      <li>
        <a href="#SubmittedExpense"><code>SubmittedExpense</code> - </a>
      </li>
      <li>
        <a href="#SubmittedExpenseResponse"
          ><code>SubmittedExpenseResponse</code> -
        </a>
      </li>
      <li>
        <a href="#TimeRangeOutOfBounds"><code>TimeRangeOutOfBounds</code> - </a>
      </li>
      <li>
        <a href="#TotalCharges"><code>TotalCharges</code> - </a>
      </li>
      <li>
        <a href="#TotalCost"><code>TotalCost</code> - </a>
      </li>
      <li>
        <a href="#TotalUsage"><code>TotalUsage</code> - </a>
      </li>
      <li>
        <a href="#TransactionNotStarted"
          ><code>TransactionNotStarted</code> -
        </a>
      </li>
      <li>
        <a href="#Usage"><code>Usage</code> - </a>
      </li>
      <li>
        <a href="#UsageResponse"><code>UsageResponse</code> - </a>
      </li>
      <li>
        <a href="#UserCharge"><code>UserCharge</code> - </a>
      </li>
      <li>
        <a href="#UserChargesSchema"><code>UserChargesSchema</code> - </a>
      </li>
    </ol>

    <div class="model">
      <h3>
        <a name="AggregateCostCorrectedResponse"
          ><code>AggregateCostCorrectedResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">cost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> New cost of
          the charge in lowest denomination of its currency (pence, euro cents).
          format: int64
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> UUID of the
          charge. format: uuid
        </div>
        <div class="param">submittedBy (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Who has
          submitted the correction request.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="AggregateSettlementAmountCorrectedResponse"
          ><code>AggregateSettlementAmountCorrectedResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> UUID of the
          charge. format: uuid
        </div>
        <div class="param">settlementAmount</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> New
          settlement amount of the charge in lowest denomination of its currency
          (pence, euro cents). format: int64
        </div>
        <div class="param">submittedBy (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Who has
          submitted the correction request.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="AuthoriseChargeRequestBody"
          ><code>AuthoriseChargeRequestBody</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargerId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> PPID (PSL
          number) of a charger
        </div>
        <div class="param">door</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charger
          door. A, B or C
        </div>
        <div class="param-enum-header">Enum:</div>
        <div class="param-enum">A</div>
        <div class="param-enum">B</div>
        <div class="param-enum">C</div>
        <div class="param">token</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> RFID card
          token, OCPI token, or Billing event ID for guest authorisation.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="AuthoriserNotFound"><code>AuthoriserNotFound</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        No authoriser can be found for the given identifier (rfid/user/other).
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Balance"><code>Balance</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">actual (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Actual user
          balance (or guest pre-authorised amount) in pence/cents/øre format:
          int64
        </div>
        <div class="param">currency (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Balance
          currency code
        </div>
        <div class="param-enum-header">Enum:</div>
        <div class="param-enum">GBP</div>
        <div class="param-enum">EUR</div>
        <div class="param-enum">NOK</div>
        <div class="param">minimum (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Minimum
          balance required to start charge in pence/cents/øre format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Breakdown"><code>Breakdown</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">generation (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span>
          Generation energy in kWh format: double
        </div>
        <div class="param">grid (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Grid
          energy in kWh format: double
        </div>
        <div class="param">total</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Total
          energy in kWh format: double
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Charge"><code>Charge</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">charger</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Charger">Charger</a></span>
        </div>
        <div class="param">cost (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Money">Money</a></span>
        </div>
        <div class="param">duration (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Duration of
          charging in seconds. format: int64
        </div>
        <div class="param">endedAt (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format, localised with timezone
          offset.
        </div>
        <div class="param">energyTotal (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in kWh. format: double
        </div>
        <div class="param">expensedTo (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#ExpensedTo">ExpensedTo</a></span>
        </div>
        <div class="param">generationEnergyTotal (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in kWh that was generated e.g. Solar. format: double
        </div>
        <div class="param">gridEnergyTotal (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in kWh that is imported from the grid. format: double
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> Charges
          unique identifier. format: uuid
        </div>
        <div class="param">startedAt (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format, localised with timezone
          offset.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ChargeAggregateNotFound"
          ><code>ChargeAggregateNotFound</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        Charge aggregate cannot be found by its uuid.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ChargeAuthorisationResponse"
          ><code>ChargeAuthorisationResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">authorised</div>
        <div class="param-desc">
          <span class="param-type"><a href="#boolean">Boolean</a></span> Whether
          the charge is authorised.
        </div>
        <div class="param">id (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> UUID
          representing the charge authorisation, present if authorisation was
          successful. format: uuid
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ChargeEnergySummary"><code>ChargeEnergySummary</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">claimedUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh, filtering out any unclaimed charges. format:
          double
        </div>
        <div class="param">cost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Energy cost
          in pence. format: int64
        </div>
        <div class="param">revenueGeneratingClaimedUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh by claimed charges with a positive revenue.
          format: double
        </div>
        <div class="param">totalUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh. format: double
        </div>
        <div class="param">unclaimedUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh, filtering out any claimed charges. format:
          double
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ChargeExpensed"><code>ChargeExpensed</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        The cost of an already expensed charge cannot be updated.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ChargeNotFound"><code>ChargeNotFound</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        ChargeNotFound is the error returned when there is no charge found for
        the given charge ID.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Charger"><code>Charger</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">door</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charger
          door used.
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Chargers
          unique identifier.
        </div>
        <div class="param">name (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Chargers
          user-friendly name.
        </div>
        <div class="param">pluggedInAt (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format, localised with timezone
          offset.
        </div>
        <div class="param">pluggedInDuration (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Duration a
          charger has been in use in seconds. format: int64
        </div>
        <div class="param">siteName (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Name of
          the site where the charger is located.
        </div>
        <div class="param">type</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Type of
          charger.
        </div>
        <div class="param-enum-header">Enum:</div>
        <div class="param-enum">public</div>
        <div class="param-enum">private</div>
        <div class="param-enum">home</div>
        <div class="param">unpluggedAt (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format, localised with timezone
          offset.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ChargerChargeStatistics"
          ><code>ChargerChargeStatistics</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> sum of
          charging duration in seconds format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 saved
          in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission
          per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
          format: double
        </div>
        <div class="param">energy</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#EnergyStatistics">EnergyStatistics</a></span
          >
        </div>
        <div class="param">numberOfCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct charge uuids format: int64
        </div>
        <div class="param">numberOfUsers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct user ids format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> sum of
          settlement amount in pence format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ChargerPpidNotFound"><code>ChargerPpidNotFound</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        ChargerPpidNotFound is the error returned when a charger location cannot
        be found by its ppid.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Chargerchargessummary"
          ><code>Chargerchargessummary</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total
          duration of charging in seconds. format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">energy (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#ChargeEnergySummary">ChargeEnergySummary</a></span
          >
        </div>
        <div class="param">numberOfCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of charges. format: int64
        </div>
        <div class="param">numberOfDrivers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of unique drivers. format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Charges"><code>Charges</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">charges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Charge">array[Charge]</a></span>
          Charge data for driver.
        </div>
        <div class="param">count</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Count of
          charges returned. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Charges2"><code>Charges2</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargeDurationTotal</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Duration of
          charging in seconds. format: int64
        </div>
        <div class="param">chargerId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charger
          ID
        </div>
        <div class="param">chargerName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charger
          name
        </div>
        <div class="param">co2Avoided</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">confirmed</div>
        <div class="param-desc">
          <span class="param-type"><a href="#boolean">Boolean</a></span> Charge
          is confirmed or not. Identified by whether the authoriserID is present
          or not.
        </div>
        <div class="param">door</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charger
          door used.
        </div>
        <div class="param">driverIDs</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">array[String]</a></span>
          Driver ID
        </div>
        <div class="param">endedAt</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format.
        </div>
        <div class="param">energyCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Cost of
          energy used in pence format: int64
        </div>
        <div class="param">energyTotal</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in kWh. format: double
        </div>
        <div class="param">generationEnergyTotal</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span>
          Generation energy used in kWh. format: double
        </div>
        <div class="param">gridEnergyTotal</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Grid
          energy used in kWh. format: double
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> UUID of
          the charge
        </div>
        <div class="param">pluggedInAt</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format.
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
        <div class="param">siteName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Site name
        </div>
        <div class="param">startedAt</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format.
        </div>
        <div class="param">unpluggedAt</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Charges3"><code>Charges3</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargeDurationTotal</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Duration of
          charging in seconds. format: int64
        </div>
        <div class="param">chargerId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charger
          ID
        </div>
        <div class="param">chargerName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charger
          name
        </div>
        <div class="param">co2Avoided</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">confirmed</div>
        <div class="param-desc">
          <span class="param-type"><a href="#boolean">Boolean</a></span> Charge
          is confirmed or not. Identified by whether the authoriserID is present
          or not.
        </div>
        <div class="param">door</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charger
          door used.
        </div>
        <div class="param">driverIDs</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">array[String]</a></span>
          Driver ID
        </div>
        <div class="param">endedAt</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format.
        </div>
        <div class="param">energyCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Cost of
          energy used in pence format: int64
        </div>
        <div class="param">energyTotal</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in kWh. format: double
        </div>
        <div class="param">generationEnergyTotal</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span>
          Generation energy used in kWh. format: double
        </div>
        <div class="param">gridEnergyTotal</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Grid
          energy used in kWh. format: double
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> UUID of
          the charge
        </div>
        <div class="param">pluggedInAt</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format.
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
        <div class="param">siteName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Site name
        </div>
        <div class="param">startedAt</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format.
        </div>
        <div class="param">unpluggedAt</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ChargesLimitResponse"><code>ChargesLimitResponse</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        Information about charge allowance and related limits
      </div>
      <div class="field-items">
        <div class="param">allowed</div>
        <div class="param-desc">
          <span class="param-type"><a href="#boolean">Boolean</a></span>
          Indicator of whether charge is allowed
        </div>
        <div class="param">balance</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Balance">Balance</a></span>
        </div>
        <div class="param">limits (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Limit">array[Limit]</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="CorrectEnergyCostRequestBody"
          ><code>CorrectEnergyCostRequestBody</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">cost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> New cost of
          the charge in lowest denomination of its currency (pence, euro cents).
          format: int64
        </div>
        <div class="param">submittedBy (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Who has
          submitted the correction request.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="CorrectSettlementAmountRequestBody"
          ><code>CorrectSettlementAmountRequestBody</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">settlementAmount</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> New
          settlement amount of the charge in lowest denomination of its currency
          (pence, euro cents). format: int64
        </div>
        <div class="param">submittedBy (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Who has
          submitted the correction request.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Cost"><code>Cost</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">home</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#MoneyInt64">array[MoneyInt64]</a></span
          >
          Total cost of all charges at home chargers in pence/cent/ore
        </div>
        <div class="param">private</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#MoneyInt64">array[MoneyInt64]</a></span
          >
          Total cost of all chargers at private chargers in pence/cent/ore
        </div>
        <div class="param">public</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#MoneyInt64">array[MoneyInt64]</a></span
          >
          Total cost of all chargers at public chargers in pence/cent/ore
        </div>
        <div class="param">total</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#MoneyInt64">array[MoneyInt64]</a></span
          >
          Total cost of all charges in pence/cent/ore
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="CreateDriverExpensesRequestBody"
          ><code>CreateDriverExpensesRequestBody</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">expenses (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#CreateExpenseRequest"
              >array[CreateExpenseRequest]</a
            ></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="CreateExpenseRequest"><code>CreateExpenseRequest</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargeId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Primary key
          of the submitted charge. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="CreatedExpense"><code>CreatedExpense</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargeId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> ID of the
          charge associated with the expense. format: int64
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Primary key
          of the expense. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="CreatedExpenseResponse"
          ><code>CreatedExpenseResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">expenses (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#CreatedExpense">array[CreatedExpense]</a></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Dnoregion"><code>Dnoregion</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">dnoregion</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> DNO
          region full name.
        </div>
        <div class="param">regionid</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> National grid
          DNO region id. format: int64
        </div>
        <div class="param">shortname</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> DNO
          region short name.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Driver"><code>Driver</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">email</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Email
          address of the driver.
        </div>
        <div class="param">firstName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> First
          name of the driver.
        </div>
        <div class="param">fullName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Full name
          of the driver.
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Primary key
          of the driver. format: int64
        </div>
        <div class="param">lastName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Last name
          of the driver.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="DriverCharge"><code>DriverCharge</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">businessName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Business
          to whom this charge is associated
        </div>
        <div class="param">chargeCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Charge cost
          in pence format: int64
        </div>
        <div class="param">chargerName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Name of
          charger associated with charge
        </div>
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Charge
          duration in seconds format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">endTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charge
          end time UTC
        </div>
        <div class="param">energyUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in kWh format: double
        </div>
        <div class="param">pluggedInDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Plugged-in
          duration in seconds format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
        <div class="param">startTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charge
          start time UTC
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="DriverChargesResponse"
          ><code>DriverChargesResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">List of driver's charges</div>
      <div class="field-items">
        <div class="param">charges (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#DriverCharge">array[DriverCharge]</a></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="DriverNotFound"><code>DriverNotFound</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        No driver has been found for a given driverID.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="DriverStatsData"><code>DriverStatsData</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">intervals (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#Interval">array[Interval]</a></span
          >
          Driver statistics broken down by the requested interval type.
        </div>
        <div class="param">summary</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#StatsSummary">StatsSummary</a></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="DriverStatsResponse"><code>DriverStatsResponse</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#DriverStatsData">DriverStatsData</a></span
          >
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="DriversChargeNotFound"
          ><code>DriversChargeNotFound</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        No charge can be found for the given user and charge IDs.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="DriversChargeResponse"
          ><code>DriversChargeResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Charge">Charge</a></span>
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="DriversChargesResponse"
          ><code>DriversChargesResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Charges">Charges</a></span>
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Duration"><code>Duration</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">home</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total
          duration of all charges at home chargers in secs format: int64
        </div>
        <div class="param">private</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total
          duration of all charges at private chargers in secs format: int64
        </div>
        <div class="param">public</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total
          duration of all charges at public chargers in secs format: int64
        </div>
        <div class="param">total</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total
          duration of all charges in secs format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Energy"><code>Energy</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">home</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Breakdown">Breakdown</a></span>
        </div>
        <div class="param">private</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Breakdown">Breakdown</a></span>
        </div>
        <div class="param">public</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Breakdown">Breakdown</a></span>
        </div>
        <div class="param">total</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Breakdown">Breakdown</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="EnergyStatistics"><code>EnergyStatistics</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">claimedUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh, filtering out any unclaimed charges. format:
          double
        </div>
        <div class="param">cost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Energy cost
          in pence. format: int64
        </div>
        <div class="param">revenueGeneratingClaimedUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh by claimed charges with a positive revenue.
          format: double
        </div>
        <div class="param">totalUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh. format: double
        </div>
        <div class="param">unclaimedUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh, filtering out any claimed charges. format:
          double
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Error"><code>Error</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">fault</div>
        <div class="param-desc">
          <span class="param-type"><a href="#boolean">Boolean</a></span> Is the
          error a server-side fault?
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> ID is a
          unique identifier for this particular occurrence of the problem.
        </div>
        <div class="param">message</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Message
          is a human-readable explanation specific to this occurrence of the
          problem.
        </div>
        <div class="param">name</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Name is
          the name of this class of errors.
        </div>
        <div class="param">temporary</div>
        <div class="param-desc">
          <span class="param-type"><a href="#boolean">Boolean</a></span> Is the
          error temporary?
        </div>
        <div class="param">timeout</div>
        <div class="param-desc">
          <span class="param-type"><a href="#boolean">Boolean</a></span> Is the
          error a timeout?
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="EventOutOfDate"><code>EventOutOfDate</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        EventOutOfDate is the error returned when there is another update on the
        same charge, resulting in a unique constraint on the database.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ExpensableCharge"><code>ExpensableCharge</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">Expensable charge</div>
      <div class="field-items">
        <div class="param">chargeCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Charge cost
          in pence format: int64
        </div>
        <div class="param">chargerName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Public
          charger name or home charger PSL
        </div>
        <div class="param">driver</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Driver">Driver</a></span>
        </div>
        <div class="param">endTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charge
          end time UTC
        </div>
        <div class="param">energyUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in KWh format: double
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Primary key
          of the charge format: int64
        </div>
        <div class="param">location</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#SubmittedChargeLocation"
              >SubmittedChargeLocation</a
            ></span
          >
        </div>
        <div class="param">pluggedInAt (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Plugged
          in at time UTC
        </div>
        <div class="param">processedByFullName (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Who
          processed this expense (if processed)
        </div>
        <div class="param">processedTime (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Processed
          time UTC (if processed)
        </div>
        <div class="param">startTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charge
          start time UTC
        </div>
        <div class="param">submittedTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Submitted
          for approval time UTC
        </div>
        <div class="param">unpluggedAt (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Unplugged
          at time UTC
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ExpensableChargeDriverSummary"
          ><code>ExpensableChargeDriverSummary</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">driver</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Driver">Driver</a></span>
        </div>
        <div class="param">submittedChargeIds (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">array[Long]</a></span> List
          of submitted charge IDs. format: int64
        </div>
        <div class="param">totalCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Number of
          charges. format: int64
        </div>
        <div class="param">totalCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#TotalCost">TotalCost</a></span>
        </div>
        <div class="param">totalUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#TotalUsage">TotalUsage</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ExpensedTo"><code>ExpensedTo</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        Group the charge is to be expensed to.
      </div>
      <div class="field-items">
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> Groups unique
          identifier. format: uuid
        </div>
        <div class="param">name</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Groups
          name.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="FleetUsageResponse"><code>FleetUsageResponse</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">numberOfDrivers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Number of
          drivers. format: int64
        </div>
        <div class="param">totalCharges</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#TotalCharges">TotalCharges</a></span
          >
        </div>
        <div class="param">totalUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#TotalUsage">TotalUsage</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Forecast"><code>Forecast</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#Forecastdata">Forecastdata</a></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Forecastdata"><code>Forecastdata</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Period">array[Period]</a></span>
        </div>
        <div class="param">dnoregion</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">regionid</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
        <div class="param">shortname</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="GroupChargeStatistics"
          ><code>GroupChargeStatistics</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> sum of
          charging duration in seconds format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 saved
          in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission
          per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
          format: double
        </div>
        <div class="param">energy</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#EnergyStatistics">EnergyStatistics</a></span
          >
        </div>
        <div class="param">numberOfChargers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct charger ids format: int64
        </div>
        <div class="param">numberOfCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct charge uuids format: int64
        </div>
        <div class="param">numberOfSites</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct sites format: int64
        </div>
        <div class="param">numberOfUsers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct user ids format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> sum of
          settlement amount in pence format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="GroupNotFound"><code>GroupNotFound</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        GroupNotFound is the error returned when there is no group for a given
        {groupId}.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="GroupSitesStats"><code>GroupSitesStats</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">co2AvoidedKg</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">energyCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total energy
          cost in pence. format: int64
        </div>
        <div class="param">energyUsageKwh</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Total
          energy usage in kWh. format: double
        </div>
        <div class="param">numberOfCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of charges. format: int64
        </div>
        <div class="param">numberOfDrivers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of unique drivers. format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
        <div class="param">siteId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> Site ID
          format: uuid
        </div>
        <div class="param">totalDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total
          duration of charging in seconds. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="GroupSitesStatsResponse"
          ><code>GroupSitesStatsResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#GroupSitesStats">array[GroupSitesStats]</a></span
          >
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="IdentifierNotProvided"
          ><code>IdentifierNotProvided</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        IdentifierNotProvided is the error returned when an identifier is not
        provided.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Intensity"><code>Intensity</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">forecast</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
        <div class="param">index</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Intensity
          index with values: very low, low, moderate, high, very high
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Interval"><code>Interval</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">from</div>
        <div class="param-desc">
          <span class="param-type"><a href="#DateTime">Date</a></span>
          Statistics date time range start eg: 2022-01-01T00:00:00Z format:
          date-time
        </div>
        <div class="param">stats</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#StatsSummary">StatsSummary</a></span
          >
        </div>
        <div class="param">to</div>
        <div class="param-desc">
          <span class="param-type"><a href="#DateTime">Date</a></span>
          Statistics report inclusive end date eg: 2022-01-31T00:00:00Z format:
          date-time
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Limit"><code>Limit</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">amount (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Amount
          for a limit format: double
        </div>
        <div class="param">type (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Type of a
          limit
        </div>
        <div class="param-enum-header">Enum:</div>
        <div class="param-enum">energy</div>
        <div class="param-enum">duration</div>
        <div class="param">unit (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Unit for
          a limit - currently only kWh, but could be time interval in the future
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="MarkSubmittedChargesAsProcessedRequestBody"
          ><code>MarkSubmittedChargesAsProcessedRequestBody</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">approverId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
        <div class="param">submittedChargeIds</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">array[Long]</a></span>
          format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Meta"><code>Meta</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">params</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#AnyType">map[String, oas_any_type_not_mapped]</a></span
          >
          Passed parameters
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Mix"><code>Mix</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">fuel</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Fuel type
          with values: gas, coal, biomass, nuclear, hydro, storage, imports,
          other, wind, solar
        </div>
        <div class="param">perc</div>
        <div class="param-desc">
          <span class="param-type"><a href="#float">Float</a></span> format:
          float
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Money"><code>Money</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        An amount of money with its currency type.
      </div>
      <div class="field-items">
        <div class="param">amount</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Amount in
          smallest denomination of the associated currency format: int64
        </div>
        <div class="param">currency</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> ISO
          currency code
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="MoneyInt64"><code>MoneyInt64</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        An amount of money with its currency type.
      </div>
      <div class="field-items">
        <div class="param">amount</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Amount in
          smallest denomination of the associated currency format: int64
        </div>
        <div class="param">currency</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> ISO
          currency code
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="OrganisationChargesDriverSummaryResponse"
          ><code>OrganisationChargesDriverSummaryResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        Expensable charges summary grouped per driver
      </div>
      <div class="field-items">
        <div class="param">driverExpensableChargeSummaries</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#ExpensableChargeDriverSummary"
              >array[ExpensableChargeDriverSummary]</a
            ></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="OrganisationChargesResponse"
          ><code>OrganisationChargesResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">Expensable charges</div>
      <div class="field-items">
        <div class="param">expensableCharges</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#ExpensableCharge">array[ExpensableCharge]</a></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="OrganisationDriversChargeStatistics"
          ><code>OrganisationDriversChargeStatistics</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Aggregate
          charge duration in seconds format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">driver</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Driver">Driver</a></span>
        </div>
        <div class="param">numberOfCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of charges. format: int64
        </div>
        <div class="param">pluggedInDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Aggregate
          plugged-in duration in seconds format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
        <div class="param">totalCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Aggregate
          charge cost in pence format: int64
        </div>
        <div class="param">totalUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh. format: double
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="OrganisationDriversChargeStatisticsResponse"
          ><code>OrganisationDriversChargeStatisticsResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        Aggregate statistics for each organisation driver
      </div>
      <div class="field-items">
        <div class="param">charges (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#OrganisationDriversChargeStatistics"
              >array[OrganisationDriversChargeStatistics]</a
            ></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Organisationchargessummary"
          ><code>Organisationchargessummary</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total
          duration of charging in seconds. format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">energy (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#ChargeEnergySummary">ChargeEnergySummary</a></span
          >
        </div>
        <div class="param">numberOfChargers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of Chargers. format: int64
        </div>
        <div class="param">numberOfCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of charges. format: int64
        </div>
        <div class="param">numberOfDrivers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of unique drivers. format: int64
        </div>
        <div class="param">numberOfSites</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of Sites. format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Period"><code>Period</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">from</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">generationmix</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Mix">array[Mix]</a></span>
        </div>
        <div class="param">intensity</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Intensity">Intensity</a></span>
        </div>
        <div class="param">to</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ProjectionChargesResponse"
          ><code>ProjectionChargesResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#Charges3">array[Charges3]</a></span
          >
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ProjectionGroupAndUserChargesResponse"
          ><code>ProjectionGroupAndUserChargesResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#UserChargesSchema">UserChargesSchema</a></span
          >
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ProjectionchargerChargeStatisticsResponse"
          ><code>ProjectionchargerChargeStatisticsResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#ChargerChargeStatistics"
              >ChargerChargeStatistics</a
            ></span
          >
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ProjectiongroupChargeStatisticsResponse"
          ><code>ProjectiongroupChargeStatisticsResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#GroupChargeStatistics">GroupChargeStatistics</a></span
          >
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="ProjectionsiteChargeStatisticsResponse"
          ><code>ProjectionsiteChargeStatisticsResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#SiteChargeStatistics">SiteChargeStatistics</a></span
          >
        </div>
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Region"><code>Region</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">regionid</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> National grid
          DNO region id. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Regions"><code>Regions</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#Dnoregion">array[Dnoregion]</a></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="RetrieveOrganisationDriversStatisticsRequestBody"
          ><code>RetrieveOrganisationDriversStatisticsRequestBody</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">driverIds</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">array[UUID]</a></span>
          format: uuid
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SiteChargeStatistics"><code>SiteChargeStatistics</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> sum of
          charging duration in seconds format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 saved
          in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission
          per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
          format: double
        </div>
        <div class="param">energy</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#EnergyStatistics">EnergyStatistics</a></span
          >
        </div>
        <div class="param">numberOfChargers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct charger ids format: int64
        </div>
        <div class="param">numberOfCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct charge uuids format: int64
        </div>
        <div class="param">numberOfUsers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> count of
          distinct user ids format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> sum of
          settlement amount in pence format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SiteStats"><code>SiteStats</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">groupId (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> Group unique
          identifier. format: uuid
        </div>
        <div class="param">groupName (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Group
          Name.
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> Site unique
          identifier. format: uuid
        </div>
        <div class="param">name (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Site
          name.
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence format: int64
        </div>
        <div class="param">totalEnergy</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in kWh. format: double
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SiteStatsResponse"><code>SiteStatsResponse</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">count</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Count of
          sites returned. format: int64
        </div>
        <div class="param">data</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#SiteStats">array[SiteStats]</a></span
          >
          Charge data for site.
        </div>
        <div class="param">from</div>
        <div class="param-desc">
          <span class="param-type"><a href="#date">date</a></span> Statistics
          report inclusive start date eg: 2022-01-01 format: date
        </div>
        <div class="param">to</div>
        <div class="param-desc">
          <span class="param-type"><a href="#date">date</a></span> Statistics
          report inclusive end date eg: 2022-01-31 format: date
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Sitechargessummary"><code>Sitechargessummary</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total
          duration of charging in seconds. format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">energy</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#ChargeEnergySummary">ChargeEnergySummary</a></span
          >
        </div>
        <div class="param">numberOfChargers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of Chargers. format: int64
        </div>
        <div class="param">numberOfCharges</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of charges. format: int64
        </div>
        <div class="param">numberOfDrivers</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Total number
          of unique drivers. format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="StatsSummary"><code>StatsSummary</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">cost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Cost">Cost</a></span>
        </div>
        <div class="param">duration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Duration">Duration</a></span>
        </div>
        <div class="param">energy</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Energy">Energy</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SubmitDriverExpensesRequestBody"
          ><code>SubmitDriverExpensesRequestBody</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">expenses (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#SubmitExpenseRequest"
              >array[SubmitExpenseRequest]</a
            ></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SubmitExpenseRequest"><code>SubmitExpenseRequest</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargeId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> UUID of the
          submitted charge. format: uuid
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SubmittedCharge"><code>SubmittedCharge</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargeCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Charge cost
          in pence format: int64
        </div>
        <div class="param">chargerName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Public
          charger name or home charger PSL
        </div>
        <div class="param">duration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Charge
          duration in seconds format: int64
        </div>
        <div class="param">endTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charge
          end time UTC
        </div>
        <div class="param">energyUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in KWh format: double
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Primary key
          of the charge format: int64
        </div>
        <div class="param">location</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#SubmittedChargeLocation"
              >SubmittedChargeLocation</a
            ></span
          >
        </div>
        <div class="param">pluggedInAt (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format, localised with timezone
          offset.
        </div>
        <div class="param">processedByFullName (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Who
          processed this expense (if processed)
        </div>
        <div class="param">processedTime (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Processed
          time UTC (if processed)
        </div>
        <div class="param">startTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charge
          start time UTC
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Whether
          the charge is NEW or PROCESSED
        </div>
        <div class="param-enum-header">Enum:</div>
        <div class="param-enum">NEW</div>
        <div class="param-enum">PROCESSED</div>
        <div class="param">submittedTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Submitted
          for approval time UTC
        </div>
        <div class="param">unpluggedAt (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Datetime
          is in ISO8601 and RFC3339 compliant format, localised with timezone
          offset.
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SubmittedChargeAddress"
          ><code>SubmittedChargeAddress</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">country (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Country
          full name
        </div>
        <div class="param">line1</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Address
          line 1
        </div>
        <div class="param">line2 (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Address
          line 2
        </div>
        <div class="param">postcode (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Postcode
        </div>
        <div class="param">prettyPrint</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
          User-friendly string representation of address
        </div>
        <div class="param">town</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Town
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SubmittedChargeLocation"
          ><code>SubmittedChargeLocation</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">address</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#SubmittedChargeAddress">SubmittedChargeAddress</a></span
          >
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> ID of the
          charge location format: int64
        </div>
        <div class="param">locationType</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Type of
          the location: home or public
        </div>
        <div class="param-enum-header">Enum:</div>
        <div class="param-enum">home</div>
        <div class="param-enum">public</div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SubmittedChargesResponse"
          ><code>SubmittedChargesResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        List of submitted charges and driver data
      </div>
      <div class="field-items">
        <div class="param">driver</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Driver">Driver</a></span>
        </div>
        <div class="param">submittedCharges</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#SubmittedCharge">array[SubmittedCharge]</a></span
          >
        </div>
        <div class="param">totalCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#TotalCost">TotalCost</a></span>
        </div>
        <div class="param">totalUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#TotalUsage">TotalUsage</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SubmittedExpense"><code>SubmittedExpense</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">chargeId</div>
        <div class="param-desc">
          <span class="param-type"><a href="#UUID">UUID</a></span> UUID of the
          charge associated with the expense. format: uuid
        </div>
        <div class="param">id</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Primary key
          of the expense. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="SubmittedExpenseResponse"
          ><code>SubmittedExpenseResponse</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">expenses (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#SubmittedExpense">array[SubmittedExpense]</a></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="TimeRangeOutOfBounds"><code>TimeRangeOutOfBounds</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        TimeRangeOutOfBounds is the error returned when the requested time
        window exceeds the maximum range.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="TotalCharges"><code>TotalCharges</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">home</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Number of
          charges attributed to Home chargers. format: int64
        </div>
        <div class="param">public</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Number of
          charges attributed to Public chargers. format: int64
        </div>
        <div class="param">total</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Number of
          charges. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="TotalCost"><code>TotalCost</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">home</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Amount
          expensed in pence for home charges. format: int64
        </div>
        <div class="param">public</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Amount
          expensed in pence for public charges. format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="TotalUsage"><code>TotalUsage</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">home</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Total
          number of kWh used for home charges. format: double
        </div>
        <div class="param">public</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Total
          number of kWh used for public charges. format: double
        </div>
        <div class="param">total (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Total
          number of kWh used. format: double
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="TransactionNotStarted"
          ><code>TransactionNotStarted</code> -
        </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">
        TransactionNotStarted is the error returned when a charge cycle
        transaction was not created as part of OCPI charge authorisation.
      </div>
      <div class="field-items">
        <div class="param">reason</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span>
        </div>
        <div class="param">status</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> format: int64
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="Usage"><code>Usage</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">cost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Energy cost
          in pence format: int64
        </div>
        <div class="param">intervalStartDate</div>
        <div class="param-desc">
          <span class="param-type"><a href="#date">date</a></span> The start
          date of this usage interval format: date
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence format: int64
        </div>
        <div class="param">totalUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used this period in kWh format: double
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="UsageResponse"><code>UsageResponse</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">Basic usage by time periods</div>
      <div class="field-items">
        <div class="param">meta</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Meta">Meta</a></span>
        </div>
        <div class="param">usage (optional)</div>
        <div class="param-desc">
          <span class="param-type"><a href="#Usage">array[Usage]</a></span>
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="UserCharge"><code>UserCharge</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description"></div>
      <div class="field-items">
        <div class="param">businessName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Business
          to whom this charge is associated
        </div>
        <div class="param">chargeCost</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Charge cost
          in pence format: int64
        </div>
        <div class="param">chargerName</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Name of
          charger associated with charge
        </div>
        <div class="param">chargingDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Charge
          duration in seconds format: int64
        </div>
        <div class="param">co2Savings</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> CO2 in kg
          = (Average ICE CO2 emission per mile - Average EV CO2 emission per
          mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh.
          Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4) format:
          double
        </div>
        <div class="param">endTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charge
          end time UTC
        </div>
        <div class="param">energyUsage</div>
        <div class="param-desc">
          <span class="param-type"><a href="#double">Double</a></span> Energy
          used in kWh format: double
        </div>
        <div class="param">pluggedInDuration</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Plugged-in
          duration in seconds format: int64
        </div>
        <div class="param">revenueGenerated</div>
        <div class="param-desc">
          <span class="param-type"><a href="#long">Long</a></span> Revenue
          generated in pence. format: int64
        </div>
        <div class="param">startTime</div>
        <div class="param-desc">
          <span class="param-type"><a href="#string">String</a></span> Charge
          start time UTC
        </div>
      </div>
      <!-- field-items -->
    </div>
    <div class="model">
      <h3>
        <a name="UserChargesSchema"><code>UserChargesSchema</code> - </a>
        <a class="up" href="#__Models">Up</a>
      </h3>
      <div class="model-description">List of user's charges</div>
      <div class="field-items">
        <div class="param">charges (optional)</div>
        <div class="param-desc">
          <span class="param-type"
            ><a href="#UserCharge">array[UserCharge]</a></span
          >
        </div>
      </div>
      <!-- field-items -->
    </div>
  </body>
</html>
