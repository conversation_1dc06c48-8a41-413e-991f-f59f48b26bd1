# data-platform-golang-migrate

Library containing data-platform's database migrations for our experience/data_platform Postgres
database.

[golang-migrate](https://github.com/golang-migrate/migrate) is used to deploy and maintain the
migrations scripts.

./data_platform/migrate contains the definitions for our experience database along with a test
suite to assure the up and down scripts can be run successfully.

Any addition or changes to the up or down scripts should be confirmed by running the test suite
and if adding additional scripts maintaining the relevant tests.
