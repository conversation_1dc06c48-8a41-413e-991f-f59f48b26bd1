CREATE TABLE IF NOT EXISTS event_store.claimed_historic_events
(
  id                  BIGSERIAL PRIMARY KEY,
  data                JSONB NOT NULL,
  processed_at        TIMESTAMPTZ,
  charge_ends_at      TIMESTAMPTZ
);

GRANT SELECT, INSERT, UPDATE ON event_store.claimed_historic_events TO xdp_api;
GRANT USAGE, SELECT ON SEQUENCE event_store.claimed_historic_events_id_seq TO xdp_api;


CREATE INDEX IF NOT EXISTS claimed_historic_events_charge_ends_at_idx
  ON event_store.claimed_historic_events (charge_ends_at);
