CREATE TABLE IF NOT EXISTS podpoint.billing_accounts
(
  id                   bigint       not null primary key,
  user_id              bigint,
  uid                  varchar(36)  not null,
  balance              integer      not null,
  currency             varchar(3)   not null,
  business_name        varchar(255) not null,
  line_1               varchar(255) not null,
  line_2               varchar(255) not null,
  postal_town          varchar(255) not null,
  postcode             varchar(255) not null,
  country              varchar(3)   not null,
  phone                varchar(255),
  mobile               varchar(255),
  payment_processor_id varchar(255),
  created_at           timestamp,
  updated_at           timestamp,
  deleted_at           timestamp
);

ALTER TABLE IF EXISTS podpoint.billing_accounts
  OWNER TO postgres;

GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.billing_accounts TO xdp_api;

CREATE TABLE IF NOT EXISTS podpoint.ev_drivers
(
  id          bigint       not null primary key,
  email       varchar(255) not null,
  first_name  varchar(255) not null,
  last_name   varchar(255) not null,
  group_id    bigint       not null,
  can_expense smallint     not null,
  created_at  timestamp,
  updated_at  timestamp,
  deleted_at  timestamp
  );

ALTER TABLE IF EXISTS podpoint.ev_drivers
  OWNER TO postgres;

GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.ev_drivers TO xdp_api;
