-- Ensure there is only one entry in projection per site per month
ALTER TABLE IF EXISTS projections.site_stats_monthly
  DROP CONSTRAINT IF EXISTS site_stats_monthly_site_month_unique;
ALTER TABLE IF EXISTS projections.site_stats_monthly
  ADD CONSTRAINT site_stats_monthly_site_month_unique UNIQUE (site_id, month);

-- Data in `month` can only refer to the first of the month e.g. 2024-02-01 NOT 2024-02-13
ALTER TABLE IF EXISTS projections.site_stats_monthly
  DROP CONSTRAINT IF EXISTS site_stats_monthly_month_start_only_check;
ALTER TABLE IF EXISTS projections.site_stats_monthly
  ADD CONSTRAINT site_stats_monthly_month_start_only_check CHECK (extract (day from month) = 1);
