CREATE TABLE IF NOT EXISTS event_store.energy_cost_currency_updated_events
(
  id           BIGSERIAL PRIMARY KEY,
  data         JSONB NOT NULL,
  processed_at TIMESTAMPTZ,
  unplugged_at TIMESTAMPTZ
);

GRANT SELECT, INSERT, UPDATE ON event_store.energy_cost_currency_updated_events TO xdp_api;
GRANT USAGE, SELECT ON SEQUENCE event_store.energy_cost_currency_updated_events_id_seq TO xdp_api;


CREATE INDEX IF NOT EXISTS energy_cost_currency_updated_events_unplugged_at_idx
  ON event_store.energy_cost_currency_updated_events (unplugged_at);
