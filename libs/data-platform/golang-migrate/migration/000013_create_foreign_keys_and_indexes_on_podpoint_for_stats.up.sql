CREATE INDEX IF NOT EXISTS pod_locations_address_id_idx ON podpoint.pod_locations (address_id);
CREATE UNIQUE INDEX IF NOT EXISTS pod_locations_unit_id_idx ON podpoint.pod_locations (unit_id);
CREATE INDEX IF NOT EXISTS charges_unit_id_idx ON podpoint.charges (unit_id);
CREATE UNIQUE INDEX IF NOT EXISTS charges_billing_event_id_idx ON podpoint.charges (billing_event_id);
CREATE INDEX IF NOT EXISTS charges_ends_at_idx ON podpoint.charges (ends_at);
