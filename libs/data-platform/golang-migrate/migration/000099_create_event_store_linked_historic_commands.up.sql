CREATE TABLE IF NOT EXISTS event_store.linked_historic_commands
(
  id                  BIGSERIAL PRIMARY KEY,
  data                JSONB NOT NULL,
  processed_at        TIMESTAMPTZ,
  charge_ends_at      TIMESTAMPTZ
);

GRANT SELECT, INSERT, UPDATE ON event_store.linked_historic_commands TO xdp_api;
GRANT USAGE, SELECT ON SEQUENCE event_store.linked_historic_commands_id_seq TO xdp_api;


CREATE INDEX IF NOT EXISTS linked_historic_commands_charge_ends_at_idx
  ON event_store.linked_historic_commands (charge_ends_at);
