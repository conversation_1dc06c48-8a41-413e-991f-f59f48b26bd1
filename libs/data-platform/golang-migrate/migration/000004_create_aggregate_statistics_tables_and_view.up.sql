CREATE TABLE IF NOT EXISTS aggregate_statistics.monthly_site_statistics
(
  id                  BIGSERIAL PRIMARY KEY,
  site_id             INTEGER NOT NULL,
  number_of_charges   INTEGER NOT NULL,
  energy_usage        DECIMAL NOT NULL,
  charging_duration   INTEGER NOT NULL,
  plugged_in_duration INTEGER NOT NULL,
  revenue_generated   INTEGER NOT NULL,
  energy_cost         INTEGER NOT NULL,
  reporting_period    VARCHAR NOT NULL
);

CREATE INDEX IF NOT EXISTS monthly_site_stats_site_id_idx ON aggregate_statistics.monthly_site_statistics (site_id);

CREATE OR REPLACE VIEW aggregate_statistics.monthly_site_statistics_view AS
  SELECT pa.id                                                      AS site_id,
         COUNT(c.id)                                                AS number_of_charges,
         SUM(be.presentment_amount * -1)                            AS revenue_generated,
         ROUND(SUM(c.kwh_used), 2)                                  AS energy_usage,
         SUM(EXTRACT(EPOCH FROM (c.ends_at - c.starts_at)))::bigint AS plugged_in_duration,
         SUM(c.duration)::bigint                                    AS charging_duration,
         SUM(c.energy_cost)                                         AS energy_cost,
         TO_CHAR(c.starts_at, 'yyyy-mm')                            AS reporting_period
  FROM podpoint.pod_addresses pa
         INNER JOIN podpoint.pod_locations pl ON pl.address_id = pa.id
         INNER JOIN podpoint.pod_units pu ON pl.unit_id = pu.id
         LEFT JOIN podpoint.charges c
                   ON c.unit_id = pu.id AND c.ends_at > (DATE_TRUNC('month', NOW()) - INTERVAL '12 month')
         LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id AND be.deleted_at IS NULL
  WHERE pa.deleted_at IS NULL
    AND pl.deleted_at IS NULL
    AND pu.deleted_at IS NULL
    AND c.deleted_at IS NULL
    AND c.is_closed = 1
  GROUP BY pa.id, TO_CHAR(c.starts_at, 'yyyy-mm')
  ORDER BY reporting_period DESC;

GRANT SELECT, INSERT, DELETE, UPDATE ON aggregate_statistics.monthly_site_statistics_view TO xdp_api;
