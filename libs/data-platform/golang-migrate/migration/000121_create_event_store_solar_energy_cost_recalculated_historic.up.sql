CREATE TABLE IF NOT EXISTS event_store.solar_energy_cost_recalculated_historic
(
  id            BIGSERIAL PRIMARY KEY,
  data          JSONB NOT NULL,
  processed_at  TIMESTAMPTZ,
  unplugged_at  TIMESTAMPTZ
);

GRANT SELECT, INSERT, UPDATE ON event_store.solar_energy_cost_recalculated_historic TO xdp_api;
GRANT USAGE, SELECT ON SEQUENCE event_store.solar_energy_cost_recalculated_historic_id_seq TO xdp_api;

CREATE INDEX IF NOT EXISTS solar_energy_cost_recalculated_historic_unplugged_at_idx
  ON event_store.solar_energy_cost_recalculated_historic (unplugged_at);
