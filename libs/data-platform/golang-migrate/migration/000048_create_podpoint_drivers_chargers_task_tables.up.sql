-- ev_drivers
CREATE TABLE IF NOT EXISTS podpoint.ev_drivers
(
    id         bigint                                    NOT NULL PRIMARY KEY,
    email      varchar(255) COLLATE pg_catalog."default" NOT NULL,
    first_name varchar(255) COLLATE pg_catalog."default" NOT NULL,
    last_name  varchar(255) COLLATE pg_catalog."default" NOT NULL,
    group_id   bigint                                    NOT NULL,
    created_at timestamp WITHOUT TIME ZONE,
    updated_at timestamp WITHOUT TIME ZONE,
    deleted_at timestamp WITHOUT TIME ZONE
);

CREATE INDEX IF NOT EXISTS ev_drivers_group_id_index
    ON podpoint.ev_drivers (group_id);

ALTER TABLE IF EXISTS podpoint.ev_drivers OWNER TO postgres;
GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.ev_drivers TO xdp_api;

-- members
CREATE TABLE IF NOT EXISTS podpoint.members
(
    id         bigint                                    NOT NULL PRIMARY KEY,
    email      varchar(255) COLLATE pg_catalog."default" NOT NULL,
    first_name varchar(255) COLLATE pg_catalog."default" NOT NULL,
    last_name  varchar(255) COLLATE pg_catalog."default" NOT NULL,
    group_id   bigint                                    NOT NULL,
    created_at timestamp WITHOUT TIME ZONE,
    updated_at timestamp WITHOUT TIME ZONE,
    deleted_at timestamp WITHOUT TIME ZONE
);

CREATE INDEX IF NOT EXISTS members_group_id_index
    ON podpoint.members (group_id);

ALTER TABLE IF EXISTS podpoint.members OWNER TO postgres;
GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.members TO xdp_api;

-- pod_models
CREATE TABLE IF NOT EXISTS podpoint.pod_models
(
    id                                bigint                                    NOT NULL PRIMARY KEY,
    vendor_id                         bigint                                    NOT NULL,
    range_id                          bigint,
    pcb_configuration_id              bigint,
    name                              varchar(191) COLLATE pg_catalog."default" NOT NULL,
    supports_payg                     smallint                                  NOT NULL,
    supports_ocpp                     smallint                                  NOT NULL DEFAULT 0,
    supports_contactless              smallint                                  NOT NULL DEFAULT 0,
    supports_simultaneous_dc_charging smallint                                  NOT NULL DEFAULT 0,
    image_url                         varchar(255) COLLATE pg_catalog."default"          DEFAULT NULL,
    created_at                        timestamp WITHOUT TIME ZONE,
    updated_at                        timestamp WITHOUT TIME ZONE,
    deleted_at                        timestamp WITHOUT TIME ZONE
);

ALTER TABLE IF EXISTS podpoint.pod_models OWNER TO postgres;
GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.pod_models TO xdp_api;
