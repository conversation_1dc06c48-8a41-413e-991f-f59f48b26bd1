package dbmigrate

import (
	"experience/libs/shared/go/aws"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"log"

	"github.com/golang-migrate/migrate/v4/database/postgres"
)

const (
	DefaultSourceURL              = "file://libs/data-platform/golang-migrate/migration"
	DefaultNonProdSourceURL       = "file://libs/data-platform/golang-migrate/non-prod/migration"
	DefaultNonProdMigrationsTable = "non_prod_schema_migrations"
)

type MigrateConfig struct {
	config           aws.Config
	datasource       migrate.MigrateDatasource
	NonProdSourceURL string
	SourceURL        string
}

func NewMigrateConfig(c aws.Config, ds migrate.MigrateDatasource) *MigrateConfig {
	return &MigrateConfig{
		config:           c,
		datasource:       ds,
		NonProdSourceURL: DefaultNonProdSourceURL,
		SourceURL:        DefaultSourceURL,
	}
}

func MigrateDown(mc *MigrateConfig, logger *log.Logger) (version, nversion uint) {
	var err error
	if !mc.config.IsProd() {
		pc := &postgres.Config{
			MigrationsTable: DefaultNonProdMigrationsTable,
		}
		nversion, err = migrate.MigrateDown(mc.NonProdSourceURL, mc.datasource.PasswordConfig, pc, mc.config.IsLocal())
		if err != nil {
			logger.Fatalf("non-prod database migrate down error: %v\n", err)
		}
		logger.Printf("non-prod database migration down version: %d\n", nversion)
	}

	version, err = migrate.MigrateDown(mc.SourceURL, mc.datasource.PasswordConfig, nil, mc.config.IsLocal())
	if err != nil {
		logger.Fatalf("database migrate down error: %v\n", err)
	}
	logger.Printf("database migration down version: %d\n", version)

	return version, nversion
}

func MigrateUp(mc *MigrateConfig, logger *log.Logger) (version, nversion uint) {
	version, err := migrate.MigrateUp(mc.SourceURL, mc.datasource.PasswordConfig, nil, mc.config.IsLocal())
	if err != nil {
		logger.Fatalf("database migrate error: %v\n", err)
	}
	logger.Printf("Database migration version: %d\n", version)

	if !mc.config.IsProd() {
		pc := &postgres.Config{
			MigrationsTable: DefaultNonProdMigrationsTable,
		}
		nversion, err = migrate.MigrateUp(mc.NonProdSourceURL, mc.datasource.PasswordConfig, pc, mc.config.IsLocal())
		if err != nil {
			logger.Fatalf("non-prod database migrate error: %v\n", err)
		}
		logger.Printf("non-prod database migration version: %d\n", nversion)
	}

	return version, nversion
}
