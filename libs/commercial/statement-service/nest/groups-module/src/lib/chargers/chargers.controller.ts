import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { ChargerInterceptor } from './charger.interceptor';
import { ChargersService } from './chargers.service';
import { Response } from 'express';
import {
  SubscriptionCharger,
  SubscriptionChargerWithAdditionalInfo,
} from '@experience/commercial/statement-service/shared';

@Controller('groups/:groupId/chargers')
@UseInterceptors(ChargerInterceptor)
export class ChargersController {
  constructor(private readonly chargersService: ChargersService) {}

  @Get()
  async findChargersByGroupId(
    @Param('groupId', ParseUUIDPipe) groupId: string
  ): Promise<SubscriptionChargerWithAdditionalInfo[]> {
    return this.chargersService.findChargersByGroupId(groupId);
  }

  @Post()
  async createOrUpdateChargersByGroupId(
    @Param('groupId', ParseUUIDPipe) groupId: string,
    @Body() subscriptionChargers: SubscriptionCharger[],
    @Res() response: Response
  ): Promise<void> {
    const { created } =
      await this.chargersService.createOrUpdateChargersByGroupId(
        groupId,
        subscriptionChargers
      );
    created
      ? response
          .status(HttpStatus.CREATED)
          .json({ message: 'Subscription created' })
      : response.status(HttpStatus.NO_CONTENT).send();
  }
}
