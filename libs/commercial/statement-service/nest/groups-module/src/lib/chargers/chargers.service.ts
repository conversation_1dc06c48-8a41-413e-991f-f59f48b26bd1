import { Pod as Charger } from '@experience/commercial/site-admin/typescript/domain-model';
import {
  CreateOrUpdateStripeSubscriptionResponse,
  SITE_ADMIN_API_URL,
  Socket,
  SubscriptionCharger,
  SubscriptionChargerWithAdditionalInfo,
} from '@experience/commercial/statement-service/shared';
import { Injectable, Logger } from '@nestjs/common';
import {
  InvalidChargerException,
  SubscriptionChargerDeleteException,
} from '@experience/commercial/statement-service/nest/shared';
import {
  StatementsPrismaClient,
  SubscriptionChargerEntity,
} from '@experience/commercial/statement-service/prisma/statements/client';

@Injectable()
export class ChargersService {
  private readonly logger = new Logger(ChargersService.name);

  constructor(private readonly database: StatementsPrismaClient) {}

  async findChargersByGroupId(
    groupId: string
  ): Promise<SubscriptionChargerWithAdditionalInfo[]> {
    this.logger.log('finding chargers by group id');
    const response = await fetch(
      `${SITE_ADMIN_API_URL}/pods?groupUid=${groupId}`
    );

    const chargers = (await response.json()) as Charger[];

    const subscriptionChargers: SubscriptionChargerEntity[] =
      await this.database.subscriptionCharger.findMany({
        where: {
          groupId,
          deletedAt: null,
        },
      });

    return chargers.flatMap((charger) =>
      this.mapGroupLevelCharger(charger, subscriptionChargers)
    );
  }

  async createOrUpdateChargersByGroupId(
    groupId: string,
    subscriptionChargers: SubscriptionCharger[]
  ): Promise<CreateOrUpdateStripeSubscriptionResponse> {
    this.logger.log(
      { groupId, subscriptionChargers },
      'updating subscription chargers'
    );

    const ppidMatcher = /^(PG|PSL)-[0-9]{1,7}$/;

    subscriptionChargers.forEach(({ ppid, socket }) => {
      if (![Socket.A, Socket.B].includes(socket) || !ppidMatcher.test(ppid)) {
        this.logger.error({ ppid, socket }, `Invalid charger`);
        throw new InvalidChargerException();
      }
    });

    const existingChargers = await this.database.subscriptionCharger.findMany({
      where: {
        groupId,
        deletedAt: null,
      },
    });

    if (existingChargers.length > 0 && subscriptionChargers.length === 0) {
      throw new SubscriptionChargerDeleteException();
    }

    const isNewSubscription = existingChargers.length === 0;

    isNewSubscription
      ? this.logger.log({ groupId }, 'creating new subscription')
      : this.logger.log({ groupId }, 'updating existing subscription');

    const chargerIdsToDelete = existingChargers
      .filter(
        (existingCharger) =>
          !subscriptionChargers.some(
            (newCharger) =>
              existingCharger.ppid === newCharger.ppid &&
              existingCharger.socket === newCharger.socket
          )
      )
      .map((charger) => charger.id);

    const newChargersToSave = subscriptionChargers
      .filter(
        (newCharger) =>
          !existingChargers.some(
            (existingCharger) =>
              existingCharger.ppid === newCharger.ppid &&
              existingCharger.socket === newCharger.socket
          )
      )
      .map(({ ppid, socket }) => ({
        ppid,
        socket,
        groupId,
      }));

    await this.database.$transaction([
      this.database.subscriptionCharger.updateMany({
        where: {
          id: {
            in: chargerIdsToDelete,
          },
        },
        data: {
          deletedAt: new Date(),
        },
      }),
      this.database.subscriptionCharger.createMany({
        data: newChargersToSave,
        skipDuplicates: true,
      }),
    ]);

    return { created: isNewSubscription };
  }

  private mapGroupLevelCharger = (
    charger: Charger,
    subscriptionChargers: SubscriptionChargerEntity[]
  ): SubscriptionChargerWithAdditionalInfo[] =>
    charger.sockets.map((socket) => {
      const hasSubscription = subscriptionChargers.some(
        (subscriptionCharger) =>
          subscriptionCharger.ppid === charger.ppid &&
          socket.door === subscriptionCharger.socket
      );

      return {
        name: charger?.name,
        ppid: charger.ppid,
        siteAddress: charger.site?.address.name,
        socket: socket.door as Socket,
        hasSubscription: hasSubscription,
      };
    });
}
