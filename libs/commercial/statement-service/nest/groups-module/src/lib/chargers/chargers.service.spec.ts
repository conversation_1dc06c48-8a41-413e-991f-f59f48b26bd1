import * as MockDate from 'mockdate';
import { ChargersService } from './chargers.service';
import { ConfigService } from '@nestjs/config';
import {
  HealthIndicatorService,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import {
  InvalidChargerException,
  SUBSCRIPTION_CHARGER_ERROR_MESSAGE,
} from '@experience/commercial/statement-service/nest/shared';
import {
  SITE_ADMIN_API_URL,
  Socket,
  TEST_GROUP,
  TEST_SUBSCRIPTION_CHARGER,
  TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO,
} from '@experience/commercial/statement-service/shared';
import {
  StatementsPrismaClient,
  TEST_SUBSCRIPTION_CHARGER_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { TEST_POD_WITH_SITE } from '@experience/commercial/site-admin/typescript/domain-model';
import { Test } from '@nestjs/testing';

describe('ChargersService', () => {
  let service: ChargersService;
  let prismaClient: StatementsPrismaClient;
  MockDate.set(new Date('2024-01-01T12:02:00.000Z'));

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        ChargersService,
        ConfigService,
        HealthIndicatorService,
        PrismaHealthIndicator,
        StatementsPrismaClient,
      ],
    }).compile();

    service = module.get<ChargersService>(ChargersService);
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(prismaClient).toBeDefined();
  });

  it('should find chargers by group ID', async () => {
    const mockFetchResponse = jest
      .fn()
      .mockResolvedValueOnce([TEST_POD_WITH_SITE]);
    const mockFetch = jest.spyOn(global, 'fetch');
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: mockFetchResponse,
      status: 200,
    } as unknown as Response);
    const mockFindMany = (prismaClient.subscriptionCharger.findMany = jest
      .fn()
      .mockResolvedValueOnce([
        { ...TEST_SUBSCRIPTION_CHARGER_ENTITY, ppid: 'BAR' },
      ]));

    const result = await service.findChargersByGroupId(TEST_GROUP.groupId);

    expect(mockFetch).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/pods?groupUid=036c0720-f908-45fc-ae7c-031a47c2e278`
    );
    expect(mockFindMany).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP.groupId, deletedAt: null },
    });
    expect(result).toEqual([
      { ...TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO, ppid: 'BAR' },
    ]);
  });

  it('should return empty array when no chargers are found', async () => {
    const mockFetchResponse = jest.fn().mockResolvedValueOnce([]);
    const mockFetch = jest.spyOn(global, 'fetch');
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: mockFetchResponse,
      status: 200,
    } as unknown as Response);
    const mockFindMany = (prismaClient.subscriptionCharger.findMany = jest
      .fn()
      .mockResolvedValueOnce([]));

    const result = await service.findChargersByGroupId(TEST_GROUP.groupId);

    expect(mockFetch).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/pods?groupUid=036c0720-f908-45fc-ae7c-031a47c2e278`
    );
    expect(mockFindMany).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP.groupId, deletedAt: null },
    });
    expect(result).toEqual([]);
  });

  it('should create chargers by group ID', async () => {
    jest
      .spyOn(prismaClient, '$transaction')
      .mockImplementation((operations) => Promise.all(operations));
    const mockFindMany = jest
      .spyOn(prismaClient.subscriptionCharger, 'findMany')
      .mockResolvedValueOnce([]);
    const mockUpdateMany = jest
      .spyOn(prismaClient.subscriptionCharger, 'updateMany')
      .mockResolvedValueOnce({ count: 0 });
    const mockCreateMany = jest
      .spyOn(prismaClient.subscriptionCharger, 'createMany')
      .mockResolvedValueOnce({ count: 2 });

    const response = await service.createOrUpdateChargersByGroupId(
      TEST_GROUP.groupId,
      [
        TEST_SUBSCRIPTION_CHARGER,
        { ...TEST_SUBSCRIPTION_CHARGER, socket: Socket.B },
      ]
    );

    expect(response).toEqual({ created: true });

    expect(mockFindMany).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP.groupId, deletedAt: null },
    });
    expect(mockUpdateMany).toHaveBeenCalledWith({
      data: {
        deletedAt: new Date(),
      },
      where: {
        id: {
          in: [],
        },
      },
    });
    expect(mockCreateMany).toHaveBeenCalledWith({
      data: [
        {
          groupId: TEST_GROUP.groupId,
          ppid: TEST_SUBSCRIPTION_CHARGER.ppid,
          socket: TEST_SUBSCRIPTION_CHARGER.socket,
        },
        {
          groupId: TEST_GROUP.groupId,
          ppid: TEST_SUBSCRIPTION_CHARGER.ppid,
          socket: Socket.B,
        },
      ],
      skipDuplicates: true,
    });
  });

  it('should update existing chargers by group ID', async () => {
    jest
      .spyOn(prismaClient, '$transaction')
      .mockImplementation((operations) => Promise.all(operations));
    const mockFindMany = jest
      .spyOn(prismaClient.subscriptionCharger, 'findMany')
      .mockResolvedValueOnce([TEST_SUBSCRIPTION_CHARGER_ENTITY]);
    const mockUpdateMany = jest
      .spyOn(prismaClient.subscriptionCharger, 'updateMany')
      .mockResolvedValueOnce({ count: 1 });
    const mockCreateMany = jest
      .spyOn(prismaClient.subscriptionCharger, 'createMany')
      .mockResolvedValueOnce({ count: 1 });

    const response = await service.createOrUpdateChargersByGroupId(
      TEST_GROUP.groupId,
      [{ ...TEST_SUBSCRIPTION_CHARGER, socket: Socket.B }]
    );

    expect(response).toEqual({ created: false });

    expect(mockFindMany).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP.groupId, deletedAt: null },
    });
    expect(mockUpdateMany).toHaveBeenCalledWith({
      data: {
        deletedAt: new Date(),
      },
      where: {
        id: {
          in: [TEST_SUBSCRIPTION_CHARGER_ENTITY.id],
        },
      },
    });
    expect(mockCreateMany).toHaveBeenCalledWith({
      data: [
        {
          groupId: TEST_GROUP.groupId,
          ppid: TEST_SUBSCRIPTION_CHARGER.ppid,
          socket: Socket.B,
        },
      ],
      skipDuplicates: true,
    });
  });

  it('should throw SubscriptionChargerDeleteException when attempting to remove the last charger on a subscription', async () => {
    jest
      .spyOn(prismaClient, '$transaction')
      .mockImplementation((operations) => Promise.all(operations));
    const mockFindMany = jest
      .spyOn(prismaClient.subscriptionCharger, 'findMany')
      .mockResolvedValueOnce([TEST_SUBSCRIPTION_CHARGER_ENTITY]);

    await expect(
      service.createOrUpdateChargersByGroupId(TEST_GROUP.groupId, [])
    ).rejects.toThrow(SUBSCRIPTION_CHARGER_ERROR_MESSAGE);

    expect(mockFindMany).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP.groupId, deletedAt: null },
    });
  });

  it.each([
    ['an invalid ppid', 'Hello-There', 'A' as Socket],
    ['an invalid socket', 'PSL-12345', 'D' as Socket],
    ['a missing ppid', undefined, 'A' as Socket],
    ['a missing socket', 'PSL-12345', undefined],
  ])(
    'should throw InvalidChargerException for a charger with %s',
    async (_, ppid, socket) => {
      await expect(
        service.createOrUpdateChargersByGroupId(TEST_GROUP.groupId, [
          { ppid: ppid as string, socket: socket as Socket },
        ])
      ).rejects.toThrow(InvalidChargerException);
    }
  );
});
