import {
  ChargerErrorCodes,
  InvalidChargerException,
  SubscriptionChargerDeleteException,
} from '@experience/commercial/statement-service/nest/shared';
import { HttpInterceptor } from '@experience/shared/nest/utils';
import { HttpStatus, Injectable } from '@nestjs/common';

@Injectable()
export class ChargerInterceptor extends HttpInterceptor {
  constructor() {
    super([
      {
        code: ChargerErrorCodes.INVALID_CHARGER,
        name: InvalidChargerException,
        statusCode: HttpStatus.BAD_REQUEST,
      },
      {
        code: ChargerErrorCodes.SUBSCRIPTION_CHARGER_DELETE_ERROR,
        name: SubscriptionChargerDeleteException,
        statusCode: HttpStatus.BAD_REQUEST,
      },
    ]);
  }
}
