import { ConfigModule } from '@nestjs/config';
import {
  HealthIndicatorService,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import {
  StatementsPrismaClient,
  TEST_GROUP_CONFIG_ENTITY,
  TEST_SITE_CONFIG_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import {
  TEST_GROUP,
  TEST_SITE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { Test } from '@nestjs/testing';
import { UpdateGroupAndSitesCommand } from './update-groups-and-sites.command';

describe('Update groups and sites command', () => {
  let command: UpdateGroupAndSitesCommand;
  let prismaClient: StatementsPrismaClient;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [
            () => ({
              SITE_ADMIN_API_URL: 'http://localhost:4102',
            }),
          ],
        }),
      ],
      providers: [
        HealthIndicatorService,
        PrismaHealthIndicator,
        StatementsPrismaClient,
        UpdateGroupAndSitesCommand,
      ],
    }).compile();

    command = module.get<UpdateGroupAndSitesCommand>(
      UpdateGroupAndSitesCommand
    );
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
  });

  it('should look up changed group/sites from SMS and update them in statement service accordingly', async () => {
    const mockFetch = jest.spyOn(global, 'fetch');

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce([TEST_GROUP]),
      } as unknown as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(TEST_SITE),
      } as unknown as Response);

    const mockFindGroups = (prismaClient.groupConfig.findMany = jest.fn());
    const mockFindSites = (prismaClient.siteConfig.findMany = jest.fn());
    const mockUpdateGroup = (prismaClient.groupConfig.update = jest.fn());
    const mockUpdateSite = (prismaClient.siteConfig.update = jest.fn());

    mockFindGroups.mockResolvedValueOnce([
      { ...TEST_GROUP_CONFIG_ENTITY, groupId: TEST_GROUP.uid },
    ]);
    mockFindSites.mockResolvedValueOnce([
      { ...TEST_SITE_CONFIG_ENTITY, siteId: TEST_SITE.id.toString() },
    ]);

    await command.run();

    expect(mockFetch).toHaveBeenNthCalledWith(
      1,
      'http://localhost:4102/user/groups?groupId=1'
    );
    expect(mockFetch).toHaveBeenNthCalledWith(
      2,
      `http://localhost:4102/sites/${TEST_SITE.id}?groupUid=${TEST_SITE_CONFIG_ENTITY.groupId}`
    );
    expect(mockFindGroups).toHaveBeenCalledWith({});
    expect(mockFindSites).toHaveBeenCalledWith({});
    expect(mockUpdateGroup).toHaveBeenCalledWith({
      where: {
        groupId: TEST_GROUP.uid,
      },
      data: {
        groupName: TEST_GROUP.name,
      },
    });
    expect(mockUpdateSite).toHaveBeenCalledWith({
      where: {
        siteId: TEST_SITE.id.toString(),
      },
      data: {
        siteName: TEST_SITE.address.name,
      },
    });
  });

  it('should only update if there is a matching group or site', async () => {
    const mockFetch = jest.spyOn(global, 'fetch');

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce([TEST_GROUP]),
      } as unknown as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce({ ...TEST_SITE, id: 123 }),
      } as unknown as Response);

    const mockFindGroups = (prismaClient.groupConfig.findMany = jest.fn());
    const mockFindSites = (prismaClient.siteConfig.findMany = jest.fn());
    const mockUpdateGroup = (prismaClient.groupConfig.update = jest.fn());
    const mockUpdateSite = (prismaClient.siteConfig.update = jest.fn());

    mockFindGroups.mockResolvedValueOnce([
      { ...TEST_GROUP_CONFIG_ENTITY, groupId: TEST_GROUP.uid },
    ]);
    mockFindSites.mockResolvedValueOnce([
      { ...TEST_SITE_CONFIG_ENTITY, siteId: TEST_SITE.id.toString() },
    ]);

    await command.run();

    expect(mockUpdateGroup).toHaveBeenCalledWith({
      where: {
        groupId: TEST_GROUP.uid,
      },
      data: {
        groupName: TEST_GROUP.name,
      },
    });
    expect(mockUpdateSite).not.toHaveBeenCalled();
  });

  it('should only update if the group or site name has changed', async () => {
    const mockFetch = jest.spyOn(global, 'fetch');

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: jest
          .fn()
          .mockResolvedValueOnce([
            { ...TEST_GROUP, name: TEST_GROUP_CONFIG_ENTITY.groupName },
          ]),
      } as unknown as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(TEST_SITE),
      } as unknown as Response);

    const mockFindGroups = (prismaClient.groupConfig.findMany = jest.fn());
    const mockFindSites = (prismaClient.siteConfig.findMany = jest.fn());
    const mockUpdateGroup = (prismaClient.groupConfig.update = jest.fn());
    const mockUpdateSite = (prismaClient.siteConfig.update = jest.fn());

    mockFindGroups.mockResolvedValueOnce([
      { ...TEST_GROUP_CONFIG_ENTITY, groupId: TEST_GROUP.uid },
    ]);
    mockFindSites.mockResolvedValueOnce([
      { ...TEST_SITE_CONFIG_ENTITY, siteId: TEST_SITE.id.toString() },
    ]);

    await command.run();

    expect(mockUpdateGroup).not.toHaveBeenCalled();
    expect(mockUpdateSite).toHaveBeenCalledWith({
      where: {
        siteId: TEST_SITE.id.toString(),
      },
      data: {
        siteName: TEST_SITE.address.name,
      },
    });
  });
});
