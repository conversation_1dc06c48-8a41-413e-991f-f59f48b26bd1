import { Command, CommandRunner } from 'nest-commander';
import {
  Group,
  Site,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  Group as GroupConfig,
  Site as SiteConfig,
} from '@experience/commercial/statement-service/shared';
import { Logger } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { StatementsPrismaClient } from '@experience/commercial/statement-service/prisma/statements/client';
import {
  mapGroupConfigToDto,
  mapSiteConfigToDto,
} from '@experience/commercial/statement-service/nest/shared';

@Command({ name: 'update-configs' })
export class UpdateGroupAndSitesCommand extends CommandRunner {
  private readonly logger = new Logger(UpdateGroupAndSitesCommand.name);

  constructor(
    private readonly nestConfigService: NestConfigService,
    private readonly database: StatementsPrismaClient
  ) {
    super();
  }

  async run(): Promise<void> {
    const siteAdminApiUrl = this.nestConfigService.get('SITE_ADMIN_API_URL');

    this.logger.log('updating group and site records that have changed');

    const changedGroups = await this.getChangedGroups(siteAdminApiUrl);
    const changedSites = await this.getChangedSites(siteAdminApiUrl);

    await Promise.all([
      this.updateChangedGroups(changedGroups),
      this.updateChangedSites(changedSites),
    ]);
  }

  private async getChangedGroups(siteAdminApiUrl: string): Promise<Group[]> {
    this.logger.log('getting changed groups');

    const statementGroups = await this.database.groupConfig
      .findMany({})
      .then((entities) => entities.map(mapGroupConfigToDto));

    const groupResponse = await fetch(
      `${siteAdminApiUrl}/user/groups?groupId=1`
    );
    const groups = (await groupResponse.json()) as Group[];

    return statementGroups.reduce((result: Group[], group: GroupConfig) => {
      const foundGroup = groups.find((item) => item.uid === group.groupId);

      if (foundGroup && foundGroup.name !== group.groupName) {
        result.push(foundGroup);
      }

      return result;
    }, [] as Group[]);
  }

  private async getChangedSites(siteAdminApiUrl: string): Promise<Site[]> {
    this.logger.log('getting changed sites');

    const statementSites = await this.database.siteConfig
      .findMany({})
      .then((entities) => entities.map(mapSiteConfigToDto));

    const sites: Site[] = [];

    for (const { siteId, groupId } of statementSites) {
      const response = await fetch(
        `${siteAdminApiUrl}/sites/${siteId}?groupUid=${groupId}`
      );

      if (response.ok) {
        const foundSite = await response.json();
        sites.push(foundSite);
      } else {
        this.logger.warn({ siteId }, 'site not found');
      }
    }

    return statementSites.reduce((result: Site[], site: SiteConfig) => {
      const matchingSite = sites.find(
        (item) => item.id.toString() === site.siteId
      );

      if (matchingSite && matchingSite.address.name !== site.siteName) {
        result.push(matchingSite);
      }

      return result;
    }, [] as Site[]);
  }

  private async updateChangedGroups(changedGroups: Group[]): Promise<void> {
    this.logger.log(
      { ids: changedGroups.map((group) => group.uid) },
      'updating groups'
    );

    changedGroups.map(async ({ uid: groupId, name: groupName }) => {
      await this.database.groupConfig.update({
        where: { groupId },
        data: {
          groupName,
        },
      });
    });
  }

  private async updateChangedSites(changedSites: Site[]): Promise<void> {
    this.logger.log(
      { ids: changedSites.map((site) => site.id) },
      'updating sites'
    );

    changedSites.map(async ({ id: siteId, address: { name: siteName } }) => {
      await this.database.siteConfig.update({
        where: { siteId: siteId.toString() },
        data: {
          siteName,
        },
      });
    });
  }
}
