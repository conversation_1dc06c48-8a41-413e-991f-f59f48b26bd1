import {
  CHARGER_NOT_FOUND_ERROR_MESSAGE,
  CREATE_STRIPE_SUBSCRIPTION_ERROR_MESSAGE,
  DOCUMENT_NOT_FOUND_ERROR_MESSAGE,
  DOCUMENT_UPLOAD_FAILED_ERROR_MESSAGE,
  GROUP_NOT_FOUND_ERROR_MESSAGE,
  INVALID_CHARGER_ERROR_MESSAGE,
  SITE_NOT_FOUND_ERROR_MESSAGE,
  STATEMENT_ALREADY_PAID_OUT_ERROR_MESSAGE,
  STRIPE_CONNECTED_ACCOUNT_ALREADY_EXISTS_ERROR_MESSAGE,
  STRIPE_CONNECTED_ACCOUNT_NOT_FOUND_ERROR_MESSAGE,
  STRIPE_CUSTOMER_ALREADY_EXISTS_ERROR_MESSAGE,
  STRIPE_CUSTOMER_DETAILS_MISSING_ERROR_MESSAGE,
  STRIPE_CUSTOMER_NOT_FOUND_ERROR_MESSAGE,
  STRIPE_CUSTOMER_SUBSCRIPTION_ID_MISSING_ERROR_MESSAGE,
  STRIPE_SUBSCRIPTION_ALREADY_EXISTS_ERROR_MESSAGE,
  STRIPE_SUBSCRIPTION_INVOICE_NOT_FINALISED_ERROR_MESSAGE,
  STRIPE_SUBSCRIPTION_INVOICE_NOT_FOUND_ERROR_MESSAGE,
  STRIPE_SUBSCRIPTION_NOT_FOUND_ERROR_MESSAGE,
  STRIPE_SUBSCRIPTION_PRICE_NOT_FOUND_ERROR_MESSAGE,
  SUBSCRIPTION_CHARGER_ERROR_MESSAGE,
  WORK_ITEM_NOT_FOUND_ERROR_MESSAGE,
} from './constants';
import { Status } from '@experience/commercial/statement-service/prisma/statements/client';

export class InvalidStatusTransitionException extends Error {
  constructor(previous: Status, next: Status) {
    super(`Invalid status transition: ${previous} to ${next}`);
  }
}

export class SiteNotFoundException extends Error {
  constructor() {
    super(SITE_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class WorkItemNotFoundException extends Error {
  constructor() {
    super(WORK_ITEM_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class GroupNotFoundException extends Error {
  constructor() {
    super(GROUP_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class ChargerNotFoundException extends Error {
  constructor() {
    super(CHARGER_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class InvalidChargerException extends Error {
  constructor() {
    super(INVALID_CHARGER_ERROR_MESSAGE);
  }
}

export class SubscriptionChargerDeleteException extends Error {
  constructor() {
    super(SUBSCRIPTION_CHARGER_ERROR_MESSAGE);
  }
}

export class StripeCustomerAlreadyExistsException extends Error {
  constructor() {
    super(STRIPE_CUSTOMER_ALREADY_EXISTS_ERROR_MESSAGE);
  }
}

export class StripeCustomerDetailsMissingException extends Error {
  constructor() {
    super(STRIPE_CUSTOMER_DETAILS_MISSING_ERROR_MESSAGE);
  }
}

export class StripeConnectedAccountAlreadyExistsException extends Error {
  constructor() {
    super(STRIPE_CONNECTED_ACCOUNT_ALREADY_EXISTS_ERROR_MESSAGE);
  }
}

export class StripeConnectedAccountNotFoundException extends Error {
  constructor() {
    super(STRIPE_CONNECTED_ACCOUNT_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class StripeCustomerNotFoundException extends Error {
  constructor() {
    super(STRIPE_CUSTOMER_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class StripePriceNotFoundException extends Error {
  constructor() {
    super(STRIPE_SUBSCRIPTION_PRICE_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class StripeSubscriptionAlreadyExistsException extends Error {
  constructor() {
    super(STRIPE_SUBSCRIPTION_ALREADY_EXISTS_ERROR_MESSAGE);
  }
}

export class StripeSubscriptionInvoiceNotFoundException extends Error {
  constructor() {
    super(STRIPE_SUBSCRIPTION_INVOICE_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class StripeSubscriptionInvoiceNotFinalisedException extends Error {
  constructor() {
    super(STRIPE_SUBSCRIPTION_INVOICE_NOT_FINALISED_ERROR_MESSAGE);
  }
}

export class CreateStripeSubscriptionException extends Error {
  constructor() {
    super(CREATE_STRIPE_SUBSCRIPTION_ERROR_MESSAGE);
  }
}

export class StripeSubscriptionNotFoundException extends Error {
  constructor() {
    super(STRIPE_SUBSCRIPTION_NOT_FOUND_ERROR_MESSAGE);
  }
}

export class StripeCustomerSubscriptionIdMissingException extends Error {
  constructor() {
    super(STRIPE_CUSTOMER_SUBSCRIPTION_ID_MISSING_ERROR_MESSAGE);
  }
}

export class StatementAlreadyPaidOutException extends Error {
  constructor() {
    super(STATEMENT_ALREADY_PAID_OUT_ERROR_MESSAGE);
  }
}

export class DocumentUploadFailedException extends Error {
  constructor() {
    super(DOCUMENT_UPLOAD_FAILED_ERROR_MESSAGE);
  }
}

export class DocumentNotFoundException extends Error {
  constructor() {
    super(DOCUMENT_NOT_FOUND_ERROR_MESSAGE);
  }
}
