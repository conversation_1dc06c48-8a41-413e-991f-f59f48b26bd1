export enum CommonErrorCodes {
  INVALID_STATUS_TRANSITION = 'INVALID_STATUS_TRANSITION',
}

export enum GroupErrorCodes {
  GROUP_NOT_FOUND = 'GROUP_NOT_FOUND',
}

export enum SiteErrorCodes {
  SITE_NOT_FOUND = 'SITE_NOT_FOUND',
}

export enum ChargerErrorCodes {
  CHARGER_NOT_FOUND = 'CHARGER_NOT_FOUND',
  INVALID_CHARGER = 'INVALID_CHARGER',
  SUBSCRIPTION_CHARGER_DELETE_ERROR = 'SUBSCRIPTION_CHARGER_DELETE_ERROR',
}

export enum WorkItemErrorCodes {
  WORK_ITEM_NOT_FOUND = 'WORK_ITEM_NOT_FOUND',
}

export enum StripeErrorCodes {
  STRIPE_CUSTOMER_ALREADY_EXISTS = 'STRIPE_CUSTOMER_ALREADY_EXISTS',
  STRIPE_CUSTOMER_DETAILS_MISSING = 'STRIPE_CUSTOMER_DETAILS_MISSING',
  STRIPE_CONNECTED_ACCOUNT_ALREADY_EXISTS = 'STRIPE_CONNECTED_ACCOUNT_ALREADY_EXISTS',
  STRIPE_CONNECTED_ACCOUNT_NOT_FOUND = 'STRIPE_CONNECTED_ACCOUNT_NOT_FOUND',
  STRIPE_PRICE_NOT_FOUND = 'STRIPE_PRICE_NOT_FOUND',
  STRIPE_SUBSCRIPTION_ALREADY_EXISTS = 'STRIPE_SUBSCRIPTION_ALREADY_EXISTS',
}

export enum DocumentErrorCodes {
  DOCUMENT_UPLOAD_FAILED = 'DOCUMENT_UPLOAD_FAILED',
  DOCUMENT_NOT_FOUND = 'DOCUMENT_NOT_FOUND',
}

export const WORK_ITEM_NOT_FOUND_ERROR_MESSAGE = 'Work item not found';
export const SITE_NOT_FOUND_ERROR_MESSAGE = 'Site not found';
export const GROUP_NOT_FOUND_ERROR_MESSAGE = 'Group not found';
export const CHARGER_NOT_FOUND_ERROR_MESSAGE = 'Charger not found';
export const INVALID_CHARGER_ERROR_MESSAGE = 'Invalid charger';
export const SUBSCRIPTION_CHARGER_ERROR_MESSAGE =
  'Subscription must have at least one charger';
export const STRIPE_CUSTOMER_ALREADY_EXISTS_ERROR_MESSAGE =
  'Stripe customer already exists';
export const STRIPE_CUSTOMER_DETAILS_MISSING_ERROR_MESSAGE =
  'Stripe customer details are missing';
export const STRIPE_CUSTOMER_SUBSCRIPTION_ID_MISSING_ERROR_MESSAGE =
  'Stripe customer subscription id does not exist';
export const STRIPE_CUSTOMER_NOT_FOUND_ERROR_MESSAGE =
  'No customer found for this group';
export const STRIPE_CONNECTED_ACCOUNT_ALREADY_EXISTS_ERROR_MESSAGE =
  'Stripe connected account already exists';
export const STRIPE_CONNECTED_ACCOUNT_NOT_FOUND_ERROR_MESSAGE =
  'No connected account found for this group';
export const STRIPE_SUBSCRIPTION_INVOICE_NOT_FOUND_ERROR_MESSAGE =
  'No invoice found for this subscription';
export const STRIPE_SUBSCRIPTION_INVOICE_NOT_FINALISED_ERROR_MESSAGE =
  'Invoice is not finalised';
export const STRIPE_SUBSCRIPTION_NOT_FOUND_ERROR_MESSAGE =
  'No subscription found for this group';
export const STRIPE_SUBSCRIPTION_ALREADY_EXISTS_ERROR_MESSAGE =
  'Stripe subscription already exists for this group';
export const STRIPE_SUBSCRIPTION_PRICE_NOT_FOUND_ERROR_MESSAGE =
  'No price found for this subscription';
export const CREATE_STRIPE_SUBSCRIPTION_ERROR_MESSAGE =
  'Error creating a subscription';
export const STATEMENT_ALREADY_PAID_OUT_ERROR_MESSAGE =
  'Statement has already been paid out';
export const DOCUMENT_UPLOAD_FAILED_ERROR_MESSAGE = 'Document upload failed';
export const DOCUMENT_NOT_FOUND_ERROR_MESSAGE = 'Document not found';
