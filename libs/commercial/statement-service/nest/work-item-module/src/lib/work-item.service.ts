import {
  AssignUserToWorkItemRequest,
  StatsWorkItems,
  UpdateAutomatedStatusRequest,
  UpdateSiteRequest,
  UpdateWorkItemStatusRequest,
  WorkItem,
} from '@experience/commercial/statement-service/shared';
import {
  GroupConfig,
  SiteConfig,
  Status,
  User,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { Injectable, Logger } from '@nestjs/common';
import {
  InvalidStatusTransitionException,
  SiteNotFoundException,
  WorkItemNotFoundException,
  isValidTransition,
  mapWorkItemEntityToDto,
  mapWorkItemStatsToDto,
} from '@experience/commercial/statement-service/nest/shared';
import {
  SiteStats,
  SitesApi,
} from '@experience/shared/axios/data-platform-api-client';
import { SitesService } from '@experience/commercial/statement-service/nest/sites-module';
import { StatementService } from '@experience/commercial/statement-service/nest/statement-module';
import {
  StatementsPrismaClient,
  WorkItemEntity,
  workItemDeepIncludeOptions,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { mapWorkItemStatusToStatus } from '@experience/commercial/statement-service/nest/shared';
import dayjs from 'dayjs';

@Injectable()
export class WorkItemService {
  private readonly logger = new Logger(WorkItemService.name);

  constructor(
    private readonly sitesApi: SitesApi,
    private readonly statementsDatabase: StatementsPrismaClient,
    private readonly statementsService: StatementService,
    private readonly sitesService: SitesService
  ) {}

  async countWorkItems(): Promise<StatsWorkItems> {
    this.logger.log('counting work items');

    const [
      statusNewStats,
      statusReadyStats,
      statusGeneratedStats,
      statusSentStats,
    ] = await Promise.all([
      this.findAllWorkItemsByStatus(
        Status.NEW,
        dayjs(0).toDate(),
        dayjs().endOf('month').toDate()
      ),
      this.findAllWorkItemsByStatus(
        Status.READY,
        dayjs(0).toDate(),
        dayjs().endOf('month').toDate()
      ),
      this.findAllWorkItemsByStatus(
        Status.GENERATED,
        dayjs(0).toDate(),
        dayjs().endOf('month').toDate()
      ),
      this.findAllWorkItemsByStatus(
        Status.SENT,
        dayjs().subtract(1, 'month').startOf('month').toDate(),
        dayjs().subtract(1, 'month').endOf('month').toDate()
      ),
    ]);

    return mapWorkItemStatsToDto(
      statusNewStats,
      statusReadyStats,
      statusGeneratedStats,
      statusSentStats
    );
  }

  async findAllWorkItems(): Promise<WorkItem[]> {
    this.logger.log('finding work items');

    const workItems = await this.statementsDatabase.workItem.findMany({
      ...workItemDeepIncludeOptions,
      where: {
        deletedAt: null,
      },
      orderBy: [
        {
          month: 'asc',
        },
        {
          groupName: 'asc',
        },
        {
          siteName: 'asc',
        },
      ],
    });
    const siteConfigs = await this.findSiteConfigs();

    return workItems
      .filter((workItem) => workItem.status !== Status.CANCELLED)
      .filter((workItem) => workItem.status !== Status.SENT)
      .map((item) =>
        mapWorkItemEntityToDto(
          item,
          siteConfigs.find(
            (config: SiteConfig) => config.siteId === item.siteId
          )
        )
      );
  }

  async findAllWorkItemsByStatus(
    status: Status,
    startDate: Date,
    endDate: Date
  ): Promise<WorkItem[]> {
    this.logger.log('finding work items by status');

    const workItems = await this.statementsDatabase.workItem.findMany({
      ...workItemDeepIncludeOptions,
      where: {
        status: status,
        deletedAt: null,
        month: { gte: startDate, lte: endDate },
      },
    });

    const siteConfigs = await this.findSiteConfigs();

    return workItems.map((item) =>
      mapWorkItemEntityToDto(
        item,
        siteConfigs.find((config: SiteConfig) => config.siteId === item.siteId)
      )
    );
  }

  async findWorkItem(workItemId: string): Promise<WorkItem> {
    this.logger.log({ workItemId }, 'finding work item');

    const workItemEntity: WorkItemEntity | null =
      await this.statementsDatabase.workItem.findUnique({
        ...workItemDeepIncludeOptions,
        where: {
          id: workItemId,
          deletedAt: null,
        },
      });

    if (!workItemEntity) {
      throw new WorkItemNotFoundException();
    }

    const previousStatement =
      await this.statementsService.getMostRecentSentStatementBySiteId(
        workItemEntity.siteId
      );

    await this.sitesService.findOrUpsertSite({
      groupId: workItemEntity.groupId,
      siteId: workItemEntity.siteId,
      siteName: workItemEntity.siteName,
    });

    const workItem = mapWorkItemEntityToDto(workItemEntity);

    return {
      ...workItem,
      previousStatement,
    };
  }

  async assignUserToWorkItem(
    workItemId: string,
    request: AssignUserToWorkItemRequest
  ): Promise<void> {
    this.logger.log({ request }, 'assigning user to work item');

    const user = await this.findOrCreateUser(request.email, request.name);
    const workItem = await this.statementsDatabase.workItem.findUnique({
      where: { id: workItemId },
    });

    if (!workItem) {
      throw new Error('Work item not found');
    }

    await this.statementsDatabase.workItem.update({
      where: { id: workItemId },
      data: {
        user: {
          connect: {
            id: user.id,
          },
        },
      },
    });
  }

  async removeUserFromWorkItem(workItemId: string): Promise<void> {
    this.logger.log({ workItemId }, 'removing user from work item');

    const workItem = await this.statementsDatabase.workItem.findUnique({
      where: { id: workItemId },
    });

    if (!workItem) {
      throw new Error('Work item not found');
    }

    await this.statementsDatabase.workItem.update({
      where: { id: workItemId },
      data: {
        user: {
          disconnect: true,
        },
      },
    });
  }

  async updateWorkItemStatus(
    workItemId: string,
    request: UpdateWorkItemStatusRequest
  ) {
    this.logger.log({ workItemId, request }, 'updating work item status');

    const mappedStatus = mapWorkItemStatusToStatus(request.status);

    const workItem = await this.statementsDatabase.workItem.findUnique({
      where: { id: workItemId },
    });

    if (!workItem) {
      throw new WorkItemNotFoundException();
    }

    if (!isValidTransition(workItem.status, mappedStatus)) {
      throw new InvalidStatusTransitionException(workItem.status, mappedStatus);
    }

    await this.statementsDatabase.workItem.update({
      where: { id: workItemId },
      data: {
        status: {
          set: mappedStatus,
        },
      },
    });
  }

  async updateAutomatedStatus(
    workItemId: string,
    request: UpdateAutomatedStatusRequest
  ) {
    this.logger.log({ request }, 'updating automated status');

    const workItem = await this.statementsDatabase.workItem.findUnique({
      where: { id: workItemId },
    });

    if (!workItem) {
      throw new WorkItemNotFoundException();
    }

    const siteConfig = await this.statementsDatabase.siteConfig.findUnique({
      where: { siteId: workItem.siteId },
    });

    if (!siteConfig) {
      throw new SiteNotFoundException();
    }

    await this.statementsDatabase.siteConfig.update({
      where: { id: siteConfig.id },
      data: {
        automated: request.automated,
      },
    });
  }

  async updateSiteConfigByWorkItemId(
    workItemId: string,
    request: UpdateSiteRequest
  ) {
    this.logger.log({ workItemId }, 'updating site config by work item id');
    const requestedEmails = request.emails.map(({ email }) => email);
    const emails = [...new Set(requestedEmails)].join(',');

    const workItem = await this.statementsDatabase.workItem.findUnique({
      where: { id: workItemId },
    });

    if (!workItem) {
      throw new WorkItemNotFoundException();
    }

    const siteConfig = await this.statementsDatabase.siteConfig.findUnique({
      where: { siteId: workItem.siteId },
    });

    if (!siteConfig) {
      await this.statementsDatabase.siteConfig.create({
        data: {
          groupId: workItem.groupId,
          siteId: workItem.siteId,
          automated: request.automated,
          emails,
          siteName: workItem.siteName,
        },
      });
    } else {
      await this.statementsDatabase.siteConfig.update({
        where: { id: siteConfig.id },
        data: {
          automated: request.automated,
          emails,
        },
      });
    }

    await this.statementsDatabase.statement.update({
      where: { workItemId },
      data: {
        emails,
      },
    });
  }

  async generateWorkItems(): Promise<void> {
    this.logger.log('generating work items');

    const now = dayjs().utc();
    const startOfDay = now.startOf('day');
    const startOfMonth = now.startOf('month');
    const startDate = now.isBefore(startOfDay.add(1, 'hour'))
      ? startOfMonth
      : startOfDay;

    const { data: sites } =
      await this.sitesApi.sitesRetrieveChargeStatsGroupedBySite(
        startDate.format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD')
      );

    this.logSiteStats(sites.data);

    for (const site of sites.data) {
      if (!this.shouldCreateWorkItem(site)) {
        continue;
      }

      const groupId = site.groupId as string;
      const groupName = site.groupName as string;
      const siteId = site.id as string;
      const siteName = site.name as string;

      await this.findOrCreateGroup(groupId, groupName);

      const existingWorkItem =
        (await this.statementsDatabase.workItem.findFirst({
          where: { siteId, month: startOfMonth.toDate() },
        })) as WorkItemEntity;

      if (existingWorkItem) {
        this.logger.log(
          { existingWorkItem },
          'work item already exists - updating group and site details'
        );
        await this.updateExistingWorkItemDetails(existingWorkItem, site);
        continue;
      }

      this.logger.log({ site }, 'creating work item');

      await this.sitesService.findOrUpsertSite({
        groupId,
        siteId,
        siteName,
      });

      await this.statementsDatabase.workItem.create({
        data: {
          month: startOfMonth.toDate(),
          status: Status.NEW,
          groupId,
          groupName,
          siteId,
          siteName,
        },
      });
    }
  }

  async markWorkItemsReady(): Promise<void> {
    this.logger.log('marking work items ready');

    await this.statementsDatabase.workItem
      .findMany({
        where: {
          status: Status.NEW,
          month: { lt: dayjs().startOf('month').toDate() },
        },
      })
      .then(async (workItems) => {
        workItems.map(async (workItem) => {
          await this.statementsDatabase.workItem.update({
            where: { id: workItem.id },
            data: {
              status: {
                set: Status.READY,
              },
            },
          });
        });
      });
  }

  private shouldCreateWorkItem(site: SiteStats): boolean {
    if (!site.name) {
      this.logger.error({ site }, 'site name is missing from site stats');
      return false;
    }

    if (!site.groupName) {
      this.logger.error({ site }, 'group name is missing from site stats');
      return false;
    }

    if (!site.groupId) {
      this.logger.error({ site }, 'group id is missing from site stats');
      return false;
    }

    if (site.revenueGenerated === 0) {
      this.logger.log({ site }, 'site has generated no revenue');
      return false;
    }

    if (
      [
        'JLL - Dundrum Town Centre',
        'Tesco 75kW Trial',
        'Tesco Stores Ltd',
      ].includes(site.groupName)
    ) {
      this.logger.log({ site }, 'site is part of Tesco group');
      return false;
    }

    return true;
  }

  private async findOrCreateUser(email: string, name: string): Promise<User> {
    const existingUser = await this.statementsDatabase.user.findFirst({
      where: { email },
    });

    if (existingUser) {
      return existingUser;
    }

    return this.statementsDatabase.user.create({
      data: {
        email,
        name,
      },
    });
  }

  private async findOrCreateGroup(
    groupId: string,
    groupName: string
  ): Promise<GroupConfig> {
    const existingGroup = await this.statementsDatabase.groupConfig.findUnique({
      where: { groupId },
    });

    if (existingGroup) {
      return existingGroup;
    }

    return this.statementsDatabase.groupConfig.create({
      data: {
        accountRef: '',
        groupId,
        groupName,
      },
    });
  }

  private findSiteConfigs(): Promise<SiteConfig[]> {
    return this.statementsDatabase.siteConfig.findMany();
  }

  private async updateExistingWorkItemDetails(
    existingWorkItem: WorkItemEntity,
    site: SiteStats
  ): Promise<void> {
    await this.statementsDatabase.workItem.update({
      where: { id: existingWorkItem.id, status: Status.NEW },
      data: {
        groupId: site.groupId,
        groupName: site.groupName,
        siteId: site.id,
        siteName: site.name,
      },
    });
  }

  private logSiteStats(siteStats: SiteStats[]) {
    const sitesWithEnergyDelivered = siteStats.filter(
      (site) => site.totalEnergy > 0
    );
    const sitesWithRevenueGenerated = siteStats.filter(
      (site) => site.revenueGenerated > 0
    );

    const stats = {
      numberOfSitesWithEnergyDelivered: sitesWithEnergyDelivered.length,
      numberOfGroupsWithEnergyDelivered: [
        ...new Set(sitesWithEnergyDelivered.map((site) => site.groupId)),
      ].length,
      numberOfSitesWithRevenueGenerated: sitesWithRevenueGenerated.length,
      numberOfGroupsWithRevenueGenerated: [
        ...new Set(sitesWithRevenueGenerated.map((site) => site.groupId)),
      ].length,
    };

    this.logger.log(stats, 'generating work items from stats');
  }
}
