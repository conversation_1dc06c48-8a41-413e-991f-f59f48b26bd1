import * as MockDate from 'mockdate';
import { AxiosResponseHeaders } from 'axios';
import { ConfigModule } from '@nestjs/config';
import {
  HealthIndicatorService,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import {
  InvalidStatusTransitionException,
  SiteNotFoundException,
  WorkItemNotFoundException,
} from '@experience/commercial/statement-service/nest/shared';
import { Logger } from '@nestjs/common';
import {
  SiteStats,
  SitesApi,
} from '@experience/shared/axios/data-platform-api-client';
import { SitesService } from '@experience/commercial/statement-service/nest/sites-module';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import { StatementService } from '@experience/commercial/statement-service/nest/statement-module';
import {
  StatementsPrismaClient,
  TEST_SITE_CONFIG_ENTITY,
  TEST_WORK_ITEM_ENTITY,
  TEST_WORK_ITEM_ENTITY_WITH_STATEMENT,
  TEST_WORK_ITEM_USER_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { Status } from '@experience/commercial/statement-service/prisma/statements/client';
import { TEST_SITE_STATISTICS_RESPONSE } from '@experience/shared/axios/data-platform-api-client/fixtures';
import {
  TEST_STATEMENT,
  TEST_UPDATE_SITE_REQUEST,
  TEST_WORK_ITEM,
  TEST_WORK_ITEMS,
  WorkItemStatus,
} from '@experience/commercial/statement-service/shared';
import { Test, TestingModule } from '@nestjs/testing';
import { WorkItemService } from './work-item.service';
import { mockDeep } from 'jest-mock-extended';
import dayjs from 'dayjs';

jest.mock('@experience/shared/nest/aws/sqs-module');
jest.mock('@experience/commercial/statement-service/nest/statement-module');
jest.mock('@experience/commercial/statement-service/nest/sites-module');
jest.mock('uuid');

describe('Work item service', () => {
  let service: WorkItemService;
  let statementService: StatementService;
  let sitesService: SitesService;
  let sitesApi: SitesApi;
  let prismaClient: StatementsPrismaClient;
  let mockMonth: Date;
  const logger = mockDeep<Logger>();

  const [firstSite, secondSite] = TEST_SITE_STATISTICS_RESPONSE.data;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              STATEMENTS_TO_GENERATE_QUEUE_URL:
                'http://localhost/000000/statements-to-generate',
              STATEMENTS_TO_SEND_QUEUE_URL:
                'http://localhost/000000/statements-to-send',
            }),
          ],
        }),
      ],
      providers: [
        HealthIndicatorService,
        PrismaHealthIndicator,
        SitesService,
        SqsClientService,
        StatementService,
        StatementsPrismaClient,
        WorkItemService,
        { provide: SitesApi, useValue: new SitesApi() },
      ],
    }).compile();

    module.useLogger(logger);
    service = module.get<WorkItemService>(WorkItemService);
    statementService = module.get<StatementService>(StatementService);
    sitesService = module.get<SitesService>(SitesService);
    sitesApi = module.get<SitesApi>(SitesApi);
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
  });

  beforeEach(() => {
    MockDate.set(new Date(2022, 0, 15, 12, 0, 0, 0));
    mockMonth = dayjs().startOf('month').toDate();
  });

  afterEach(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should fetch a list of all work items with matching site configs', async () => {
    const mockFindMany = (prismaClient.workItem.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_WORK_ITEM_ENTITY_WITH_STATEMENT]));
    const mockFindSiteConfig = (prismaClient.siteConfig.findMany =
      jest.fn()).mockReturnValue([TEST_SITE_CONFIG_ENTITY]);

    const workItems = await service.findAllWorkItems();

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        statement: {
          include: {
            invoice: true,
            statementChargerConfig: true,
          },
        },
        user: true,
      },
      orderBy: [
        {
          month: 'asc',
        },
        {
          groupName: 'asc',
        },
        {
          siteName: 'asc',
        },
      ],
      where: {
        deletedAt: null,
      },
    });
    expect(mockFindSiteConfig).toHaveBeenCalledTimes(1);

    expect(workItems).toEqual([{ ...TEST_WORK_ITEM, automated: true }]);
  });

  it('should fetch a list of all work items with no site config found', async () => {
    const mockFindMany = (prismaClient.workItem.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_WORK_ITEM_ENTITY_WITH_STATEMENT]));
    const mockFindSiteConfig = (prismaClient.siteConfig.findMany =
      jest.fn()).mockReturnValue([]);

    const workItems = await service.findAllWorkItems();

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        statement: {
          include: {
            invoice: true,
            statementChargerConfig: true,
          },
        },
        user: true,
      },
      orderBy: [
        {
          month: 'asc',
        },
        {
          groupName: 'asc',
        },
        {
          siteName: 'asc',
        },
      ],
      where: {
        deletedAt: null,
      },
    });
    expect(mockFindSiteConfig).toHaveBeenCalledTimes(1);

    expect(workItems).toEqual([TEST_WORK_ITEM]);
  });

  it('should fetch a list of all work items with a specific status', async () => {
    const mockFindMany = (prismaClient.workItem.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_WORK_ITEM_ENTITY_WITH_STATEMENT]));
    const mockFindSiteConfig = (prismaClient.siteConfig.findMany =
      jest.fn()).mockReturnValue([TEST_SITE_CONFIG_ENTITY]);

    const workItems = await service.findAllWorkItemsByStatus(
      Status.NEW,
      dayjs('2022-01-01').toDate(),
      dayjs('2022-01-31').toDate()
    );

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        statement: {
          include: {
            invoice: true,
            statementChargerConfig: true,
          },
        },
        user: true,
      },
      where: {
        deletedAt: null,
        month: {
          gte: dayjs('2022-01-01').toDate(),
          lte: dayjs('2022-01-31').toDate(),
        },
        status: Status.NEW,
      },
    });
    expect(mockFindSiteConfig).toHaveBeenCalledTimes(1);

    expect(workItems).toEqual([{ ...TEST_WORK_ITEM, automated: true }]);
  });

  it('should fetch a list of counts of work items by status', async () => {
    const mockFindAllWorkItemsByStatus = (service.findAllWorkItemsByStatus =
      jest
        .fn()
        .mockResolvedValueOnce([
          TEST_WORK_ITEMS,
          TEST_WORK_ITEMS,
          { ...TEST_WORK_ITEMS, automated: true },
          { ...TEST_WORK_ITEMS, automated: true },
        ])
        .mockResolvedValueOnce([TEST_WORK_ITEMS])
        .mockResolvedValueOnce([TEST_WORK_ITEMS])
        .mockResolvedValueOnce([
          TEST_WORK_ITEMS,
          { ...TEST_WORK_ITEMS, automated: true },
        ]));

    const result = await service.countWorkItems();

    expect(mockFindAllWorkItemsByStatus).toHaveBeenNthCalledWith(
      1,
      Status.NEW,
      dayjs(0).toDate(),
      dayjs().endOf('month').toDate()
    );
    expect(mockFindAllWorkItemsByStatus).toHaveBeenNthCalledWith(
      2,
      Status.READY,
      dayjs(0).toDate(),
      dayjs().endOf('month').toDate()
    );
    expect(mockFindAllWorkItemsByStatus).toHaveBeenNthCalledWith(
      3,
      Status.GENERATED,
      dayjs(0).toDate(),
      dayjs().endOf('month').toDate()
    );
    expect(mockFindAllWorkItemsByStatus).toHaveBeenNthCalledWith(
      4,
      Status.SENT,
      dayjs().subtract(1, 'month').startOf('month').toDate(),
      dayjs().subtract(1, 'month').endOf('month').toDate()
    );

    expect(result).toEqual({
      workitems: {
        new: {
          manual: 2,
          automated: 2,
          total: 4,
        },
        ready: {
          manual: 1,
          automated: 0,
          total: 1,
        },
        generated: {
          manual: 1,
          automated: 0,
          total: 1,
        },
        sent: {
          manual: 1,
          automated: 1,
          total: 2,
        },
      },
    });
  });

  it('should fetch a work item', async () => {
    const mockFindUnique = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT));
    const mockFindStatementBySiteId =
      (statementService.getMostRecentSentStatementBySiteId = jest.fn());
    const mockFindOrCreateSiteConfig = jest.spyOn(
      sitesService,
      'findOrUpsertSite'
    );

    const workItem = await service.findWorkItem(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id
    );

    expect(mockFindStatementBySiteId).toHaveBeenCalledWith(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.siteId
    );
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        statement: {
          include: {
            invoice: true,
            statementChargerConfig: true,
          },
        },
        user: true,
      },
      where: {
        deletedAt: null,
        id: '0a69aa8f-fa98-4488-a113-4ea8aca29e5d',
      },
    });
    expect(mockFindOrCreateSiteConfig).toHaveBeenCalledWith({
      groupId: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.groupId,
      siteId: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.siteId,
      siteName: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.siteName,
    });

    expect(workItem).toEqual(TEST_WORK_ITEM);
  });

  it('should fetch a work item with a previous statement', async () => {
    const mockFindUnique = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT));
    const mockFindStatement =
      (statementService.getMostRecentSentStatementBySiteId = jest
        .fn()
        .mockResolvedValueOnce(TEST_STATEMENT));

    const workItem = await service.findWorkItem(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id
    );
    expect(mockFindStatement).toHaveBeenCalledWith(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.siteId
    );

    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        statement: {
          include: {
            invoice: true,
            statementChargerConfig: true,
          },
        },
        user: true,
      },
      where: {
        deletedAt: null,
        id: '0a69aa8f-fa98-4488-a113-4ea8aca29e5d',
      },
    });

    expect(workItem).toEqual({
      ...TEST_WORK_ITEM,
      previousStatement: TEST_STATEMENT,
    });
  });

  it('should throw an exception if the work item is not found', async () => {
    prismaClient.workItem.findUnique = jest.fn().mockReturnValueOnce(null);

    try {
      await service.findWorkItem(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id);
    } catch (error) {
      expect(error).toBeInstanceOf(WorkItemNotFoundException);
    }
  });

  it('should assign active user to work item', async () => {
    const mockFindUser = (prismaClient.user.findFirst = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_USER_ENTITY));
    const mockFindUnique = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT));
    const mockUpdate = (prismaClient.workItem.update = jest.fn());

    await service.assignUserToWorkItem(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id,
      {
        email: TEST_WORK_ITEM_USER_ENTITY.email,
        name: TEST_WORK_ITEM_USER_ENTITY.name,
      }
    );

    expect(mockFindUser).toHaveBeenCalledWith({
      where: {
        email: TEST_WORK_ITEM_USER_ENTITY.email,
      },
    });

    expect(mockFindUnique).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
    });

    expect(mockUpdate).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
      data: {
        user: {
          connect: {
            id: TEST_WORK_ITEM_USER_ENTITY.id,
          },
        },
      },
    });
  });

  it('should assign non-active user to work item', async () => {
    const mockFindUser = (prismaClient.user.findFirst = jest
      .fn()
      .mockReturnValueOnce(null));
    const mockCreateUser = (prismaClient.user.create = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_USER_ENTITY));
    const mockFindUnique = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT));
    const mockUpdate = (prismaClient.workItem.update = jest.fn());

    await service.assignUserToWorkItem(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id,
      {
        email: TEST_WORK_ITEM_USER_ENTITY.email,
        name: TEST_WORK_ITEM_USER_ENTITY.name,
      }
    );

    expect(mockFindUser).toHaveBeenCalledWith({
      where: {
        email: TEST_WORK_ITEM_USER_ENTITY.email,
      },
    });

    expect(mockCreateUser).toHaveBeenCalledWith({
      data: {
        email: TEST_WORK_ITEM_USER_ENTITY.email,
        name: TEST_WORK_ITEM_USER_ENTITY.name,
      },
    });

    expect(mockFindUnique).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
    });

    expect(mockUpdate).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
      data: {
        user: {
          connect: {
            id: TEST_WORK_ITEM_USER_ENTITY.id,
          },
        },
      },
    });
  });

  it('should return an error if work item is not found when assigning a user', async () => {
    prismaClient.user.findFirst = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_USER_ENTITY);
    prismaClient.workItem.findUnique = jest
      .fn()
      .mockRejectedValueOnce(new Error('Work item not found'));

    await expect(() =>
      service.assignUserToWorkItem(TEST_WORK_ITEM_USER_ENTITY.id, {
        email: TEST_WORK_ITEM_USER_ENTITY.email,
        name: TEST_WORK_ITEM_USER_ENTITY.name,
      })
    ).rejects.toThrow('Work item not found');
  });

  it('should remove user from work item', async () => {
    const mockFindStatement = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT));
    const mockUpdateStatement = (prismaClient.workItem.update = jest.fn());

    await service.removeUserFromWorkItem(TEST_WORK_ITEM.id);

    expect(mockFindStatement).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
    });

    expect(mockUpdateStatement).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
      data: {
        user: {
          disconnect: true,
        },
      },
    });
  });

  it('should return an error if work item is not found when removing a user', async () => {
    prismaClient.workItem.findUnique = jest
      .fn()
      .mockRejectedValueOnce(new Error('Work item not found'));

    await expect(() =>
      service.removeUserFromWorkItem(TEST_WORK_ITEM.id)
    ).rejects.toThrow('Work item not found');
  });

  it("should update a work item's status", async () => {
    const mockFindStatement = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce({
        ...TEST_WORK_ITEM_ENTITY_WITH_STATEMENT,
        status: Status.GENERATED,
      }));
    const mockUpdateStatement = (prismaClient.workItem.update = jest.fn());

    await service.updateWorkItemStatus(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id,
      {
        status: WorkItemStatus.SENT,
      }
    );

    expect(mockFindStatement).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
    });

    expect(mockUpdateStatement).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
      data: {
        status: {
          set: Status.SENT,
        },
      },
    });
  });

  it('should return an error if work item is not found when updating status for a work item', async () => {
    prismaClient.workItem.findUnique = jest.fn().mockResolvedValueOnce(null);
    const mockUpdateStatement = (prismaClient.workItem.update = jest.fn());

    await expect(() =>
      service.updateWorkItemStatus(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id, {
        status: WorkItemStatus.SENT,
      })
    ).rejects.toThrow(WorkItemNotFoundException);

    expect(mockUpdateStatement).not.toHaveBeenCalled();
  });

  it('should return an error if status transition is invalid when updating status for a work item', async () => {
    prismaClient.workItem.findUnique = jest.fn().mockReturnValueOnce({
      ...TEST_WORK_ITEM_ENTITY_WITH_STATEMENT,
      status: Status.SENT,
    });
    const mockUpdateStatement = (prismaClient.workItem.update = jest.fn());

    await expect(() =>
      service.updateWorkItemStatus(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id, {
        status: WorkItemStatus.SENT,
      })
    ).rejects.toThrow(InvalidStatusTransitionException);

    expect(mockUpdateStatement).not.toHaveBeenCalled();
  });

  it('should update automated flag for a site config', async () => {
    const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY));
    const mockFindSiteConfig = (prismaClient.siteConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_SITE_CONFIG_ENTITY));
    const mockUpdateSiteConfig = (prismaClient.siteConfig.update = jest.fn());

    await service.updateAutomatedStatus(TEST_WORK_ITEM_ENTITY.id, {
      automated: true,
    });

    expect(mockFindWorkItem).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY.id },
    });

    expect(mockFindSiteConfig).toHaveBeenCalledWith({
      where: { siteId: TEST_WORK_ITEM_ENTITY.siteId },
    });

    expect(mockUpdateSiteConfig).toHaveBeenCalledWith({
      where: { id: TEST_SITE_CONFIG_ENTITY.id },
      data: {
        automated: true,
      },
    });
  });

  it('should return an error if work is not found when updating automated for a site config', async () => {
    prismaClient.workItem.findUnique = jest.fn().mockResolvedValueOnce(null);
    const mockUpdateSiteConfig = (prismaClient.siteConfig.update = jest.fn());

    await expect(() =>
      service.updateAutomatedStatus(TEST_WORK_ITEM_ENTITY.id, {
        automated: true,
      })
    ).rejects.toThrow(WorkItemNotFoundException);

    expect(mockUpdateSiteConfig).not.toHaveBeenCalled();
  });

  it('should return an error if site config is not found when updating automated for a site config', async () => {
    const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY));
    prismaClient.siteConfig.findUnique = jest.fn().mockResolvedValueOnce(null);
    const mockUpdateStatement = (prismaClient.siteConfig.update = jest.fn());

    await expect(() =>
      service.updateAutomatedStatus(TEST_WORK_ITEM_ENTITY.id, {
        automated: true,
      })
    ).rejects.toThrow(SiteNotFoundException);

    expect(mockFindWorkItem).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY.id },
    });
    expect(mockUpdateStatement).not.toHaveBeenCalled();
  });

  it.each([
    ['from the start of the day', 1, '2022-01-15'],
    ['from the start of the month', 0, '2022-01-01'],
  ])(
    'should create new work items from the site statistics api %s',
    async (_, hour, expectedStartDate) => {
      MockDate.set(new Date(2022, 0, 15, hour, 30, 0, 0));
      const mockSitesApi = jest
        .spyOn(sitesApi, 'sitesRetrieveChargeStatsGroupedBySite')
        .mockResolvedValueOnce({
          config: {
            headers: 'header' as unknown as AxiosResponseHeaders,
          },
          data: TEST_SITE_STATISTICS_RESPONSE,
          headers: 'header' as unknown as AxiosResponseHeaders,
          status: 200,
          statusText: 'OK',
        });
      const mockFindStatement = (prismaClient.workItem.findFirst = jest.fn());
      const mockFindGroupConfig = (prismaClient.groupConfig.findUnique = jest
        .fn()
        .mockResolvedValueOnce({
          id: 'this-is-a-uuid',
          groupId: firstSite.groupId,
        })
        .mockResolvedValueOnce({
          id: 'this-is-also-uuid',
          groupId: secondSite.groupId,
        }));
      const mockFindOrCreateSiteConfig = jest.spyOn(
        sitesService,
        'findOrUpsertSite'
      );

      const mockCreateWorkItem = (prismaClient.workItem.create = jest.fn());

      await service.generateWorkItems();

      expect(mockSitesApi).toHaveBeenCalledWith(
        expectedStartDate,
        '2022-01-15'
      );
      expect(mockFindGroupConfig).toHaveBeenCalledTimes(2);
      expect(mockFindStatement).toHaveBeenCalledTimes(2);
      expect(mockFindStatement).toHaveBeenNthCalledWith(1, {
        where: {
          month: mockMonth,
          siteId: firstSite.id,
        },
      });
      expect(mockFindOrCreateSiteConfig).toHaveBeenNthCalledWith(1, {
        groupId: TEST_SITE_STATISTICS_RESPONSE.data[0].groupId,
        siteId: TEST_SITE_STATISTICS_RESPONSE.data[0].id,
        siteName: TEST_SITE_STATISTICS_RESPONSE.data[0].name,
      });
      expect(mockFindOrCreateSiteConfig).toHaveBeenNthCalledWith(2, {
        groupId: TEST_SITE_STATISTICS_RESPONSE.data[1].groupId,
        siteId: TEST_SITE_STATISTICS_RESPONSE.data[1].id,
        siteName: TEST_SITE_STATISTICS_RESPONSE.data[1].name,
      });
      expect(mockFindStatement).toHaveBeenNthCalledWith(2, {
        where: {
          month: mockMonth,
          siteId: secondSite.id,
        },
      });
      expect(mockCreateWorkItem).toHaveBeenCalledTimes(2);
      expect(mockCreateWorkItem).toHaveBeenNthCalledWith(1, {
        data: {
          groupId: firstSite.groupId,
          groupName: firstSite.groupName,
          month: mockMonth,
          siteId: firstSite.id,
          siteName: firstSite.name,
          status: 'NEW',
        },
      });
      expect(mockCreateWorkItem).toHaveBeenNthCalledWith(2, {
        data: {
          groupId: secondSite.groupId,
          groupName: secondSite.groupName,
          month: mockMonth,
          siteId: secondSite.id,
          siteName: secondSite.name,
          status: 'NEW',
        },
      });
    }
  );

  it('should update any existing work items group and site names when generating new work items', async () => {
    const mockSitesApi = jest
      .spyOn(sitesApi, 'sitesRetrieveChargeStatsGroupedBySite')
      .mockResolvedValueOnce({
        config: {
          headers: 'header' as unknown as AxiosResponseHeaders,
        },
        data: TEST_SITE_STATISTICS_RESPONSE,
        headers: 'header' as unknown as AxiosResponseHeaders,
        status: 200,
        statusText: 'OK',
      });
    const mockFindGroupConfig = (prismaClient.groupConfig.findUnique = jest
      .fn()
      .mockResolvedValueOnce({
        id: 'this-is-a-uuid',
        groupId: firstSite.groupId,
      })
      .mockResolvedValueOnce({
        id: 'this-is-also-uuid',
        groupId: secondSite.groupId,
      }));
    const mockFindWorkItem = (prismaClient.workItem.findFirst = jest
      .fn()
      .mockResolvedValueOnce(TEST_WORK_ITEM_ENTITY_WITH_STATEMENT)
      .mockResolvedValueOnce({
        ...TEST_WORK_ITEM_ENTITY_WITH_STATEMENT,
        id: '8ff9ab3f-5860-498c-9bee-6aa0fa44f237',
      }));
    const mockCreateWorkItem = (prismaClient.workItem.create = jest.fn());
    const mockUpdateWorkItem = (prismaClient.workItem.update = jest.fn());

    await service.generateWorkItems();

    expect(mockSitesApi).toHaveBeenCalledWith('2022-01-15', '2022-01-15');
    expect(mockFindGroupConfig).toHaveBeenCalledTimes(2);
    expect(mockFindWorkItem).toHaveBeenCalledTimes(2);
    expect(mockFindWorkItem).toHaveBeenNthCalledWith(1, {
      where: {
        month: mockMonth,
        siteId: firstSite.id,
      },
    });
    expect(mockFindWorkItem).toHaveBeenNthCalledWith(2, {
      where: {
        month: mockMonth,
        siteId: secondSite.id,
      },
    });
    expect(mockUpdateWorkItem).toHaveBeenNthCalledWith(1, {
      where: {
        id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id,
        status: Status.NEW,
      },
      data: {
        groupId: firstSite.groupId,
        groupName: firstSite.groupName,
        siteId: firstSite.id,
        siteName: firstSite.name,
      },
    });
    expect(mockUpdateWorkItem).toHaveBeenNthCalledWith(2, {
      where: { id: '8ff9ab3f-5860-498c-9bee-6aa0fa44f237', status: Status.NEW },
      data: {
        groupId: secondSite.groupId,
        groupName: secondSite.groupName,
        siteId: secondSite.id,
        siteName: secondSite.name,
      },
    });
    expect(mockCreateWorkItem).not.toHaveBeenCalled();
  });

  it('should not create new work items if there is zero revenue generated for the site', async () => {
    const mockSitesApi = jest
      .spyOn(sitesApi, 'sitesRetrieveChargeStatsGroupedBySite')
      .mockResolvedValueOnce({
        config: {
          headers: 'header' as unknown as AxiosResponseHeaders,
        },
        data: {
          ...TEST_SITE_STATISTICS_RESPONSE,
          data: [
            { ...firstSite, revenueGenerated: 0 },
            { ...secondSite, revenueGenerated: 0 },
          ],
        },
        headers: 'header' as unknown as AxiosResponseHeaders,
        status: 200,
        statusText: 'OK',
      });
    const mockFindStatement = (prismaClient.workItem.findFirst = jest.fn());
    const mockCreateWorkItem = (prismaClient.workItem.create = jest.fn());

    await service.generateWorkItems();

    expect(mockSitesApi).toHaveBeenCalledWith('2022-01-15', '2022-01-15');
    expect(mockFindStatement).not.toHaveBeenCalled();
    expect(mockCreateWorkItem).not.toHaveBeenCalled();
  });

  it('should not create new work items if there is an empty response from the site statistics api', async () => {
    const mockSitesApi = jest
      .spyOn(sitesApi, 'sitesRetrieveChargeStatsGroupedBySite')
      .mockResolvedValueOnce({
        config: {
          headers: 'header' as unknown as AxiosResponseHeaders,
        },
        data: {
          ...TEST_SITE_STATISTICS_RESPONSE,
          count: 0,
          data: [],
        },
        headers: 'header' as unknown as AxiosResponseHeaders,
        status: 200,
        statusText: 'OK',
      });
    const mockFindStatement = (prismaClient.workItem.findFirst = jest.fn());
    const mockCreateWorkItem = (prismaClient.workItem.create = jest.fn());

    await service.generateWorkItems();

    expect(mockSitesApi).toHaveBeenCalledWith('2022-01-15', '2022-01-15');
    expect(mockFindStatement).not.toHaveBeenCalled();
    expect(mockCreateWorkItem).not.toHaveBeenCalled();
  });

  it('should create a work items for sites with a matching group ID not in the group table', async () => {
    const mockSitesApi = jest
      .spyOn(sitesApi, 'sitesRetrieveChargeStatsGroupedBySite')
      .mockResolvedValueOnce({
        config: {
          headers: 'header' as unknown as AxiosResponseHeaders,
        },
        data: TEST_SITE_STATISTICS_RESPONSE,
        headers: 'header' as unknown as AxiosResponseHeaders,
        status: 200,
        statusText: 'OK',
      });
    const mockFindGroupConfig = (prismaClient.groupConfig.findUnique = jest
      .fn()
      .mockResolvedValueOnce({
        id: 'this-is-a-uuid',
        groupId: firstSite.groupId,
      }));
    const mockCreateGroupConfig = (prismaClient.groupConfig.create = jest
      .fn()
      .mockResolvedValueOnce({
        id: 'this-is-also-uuid',
        groupId: secondSite.groupId,
      }));
    const mockFindStatement = (prismaClient.workItem.findFirst = jest.fn());
    const mockCreateWorkItem = (prismaClient.workItem.create = jest.fn());

    await service.generateWorkItems();

    expect(mockSitesApi).toHaveBeenCalledWith('2022-01-15', '2022-01-15');
    expect(mockFindGroupConfig).toHaveBeenCalledTimes(2);
    expect(mockCreateGroupConfig).toHaveBeenCalledTimes(1);
    expect(mockFindStatement).toHaveBeenCalledTimes(2);
    expect(mockFindStatement).toHaveBeenNthCalledWith(1, {
      where: { month: mockMonth, siteId: firstSite.id },
    });
    expect(mockCreateWorkItem).toHaveBeenCalledTimes(2);
    expect(mockCreateWorkItem).toHaveBeenNthCalledWith(1, {
      data: {
        groupId: firstSite.groupId,
        groupName: firstSite.groupName,
        month: mockMonth,
        siteId: firstSite.id,
        siteName: firstSite.name,
        status: 'NEW',
      },
    });
    expect(mockCreateWorkItem).toHaveBeenNthCalledWith(2, {
      data: {
        groupId: secondSite.groupId,
        groupName: secondSite.groupName,
        month: mockMonth,
        siteId: secondSite.id,
        siteName: secondSite.name,
        status: 'NEW',
      },
    });
  });

  it.each([
    ['site name', { name: undefined }],
    ['group name', { groupName: undefined }],
    ['group id', { groupId: undefined }],
  ])(
    'should not create new work items if %s field is missing',
    async (logMessage, fieldName) => {
      const invalidSiteStat: SiteStats = {
        ...TEST_SITE_STATISTICS_RESPONSE.data[0],
        ...fieldName,
      };
      const mockSitesApi = jest
        .spyOn(sitesApi, 'sitesRetrieveChargeStatsGroupedBySite')
        .mockResolvedValueOnce({
          config: { headers: 'header' as unknown as AxiosResponseHeaders },
          data: { ...TEST_SITE_STATISTICS_RESPONSE, data: [invalidSiteStat] },
          headers: 'header' as unknown as AxiosResponseHeaders,
          status: 200,
          statusText: 'OK',
        });
      const mockFindStatement = (prismaClient.workItem.findFirst = jest.fn());
      const mockCreateWorkItem = (prismaClient.workItem.create = jest.fn());

      await service.generateWorkItems();

      expect(mockSitesApi).toHaveBeenCalledWith('2022-01-15', '2022-01-15');
      expect(mockFindStatement).not.toHaveBeenCalled();
      expect(mockCreateWorkItem).not.toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith(
        { site: invalidSiteStat },
        `${logMessage} is missing from site stats`,
        'WorkItemService'
      );
    }
  );

  it.each([
    'JLL - Dundrum Town Centre',
    'Tesco 75kW Trial',
    'Tesco Stores Ltd',
  ])(
    'should not create new work items if group name is %s',
    async (groupName) => {
      const tescoSiteStats: SiteStats = {
        ...TEST_SITE_STATISTICS_RESPONSE.data[0],
        groupName,
      };
      const mockSitesApi = jest
        .spyOn(sitesApi, 'sitesRetrieveChargeStatsGroupedBySite')
        .mockResolvedValueOnce({
          config: { headers: 'header' as unknown as AxiosResponseHeaders },
          data: { ...TEST_SITE_STATISTICS_RESPONSE, data: [tescoSiteStats] },
          headers: 'header' as unknown as AxiosResponseHeaders,
          status: 200,
          statusText: 'OK',
        });
      const mockCreateWorkItem = (prismaClient.workItem.create = jest.fn());

      await service.generateWorkItems();

      expect(mockSitesApi).toHaveBeenCalledWith('2022-01-15', '2022-01-15');
      expect(mockCreateWorkItem).not.toHaveBeenCalled();
      expect(logger.log).toHaveBeenCalledWith(
        { site: tescoSiteStats },
        'site is part of Tesco group',
        'WorkItemService'
      );
    }
  );

  it.each([
    ['last months', 1],
    ['previous months', 2],
  ])('should mark %s work items as ready', async (_, number) => {
    const month = dayjs(mockMonth).subtract(number, 'month').toDate();
    const mockFindMany = (prismaClient.workItem.findMany = jest
      .fn()
      .mockResolvedValueOnce([
        { ...TEST_WORK_ITEM_ENTITY_WITH_STATEMENT, month },
      ]));
    const mockUpdate = (prismaClient.workItem.update = jest.fn());

    await service.markWorkItemsReady();

    expect(mockFindMany).toHaveBeenCalledWith({
      where: { status: 'NEW', month: { lt: mockMonth } },
    });

    expect(mockUpdate).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id },
      data: {
        status: {
          set: Status.READY,
        },
      },
    });
  });

  it('should not mark this months work items as ready', async () => {
    prismaClient.workItem.findMany = jest.fn().mockResolvedValueOnce([]);
    const mockUpdate = (prismaClient.workItem.update = jest.fn());

    await service.markWorkItemsReady();

    expect(mockUpdate).not.toHaveBeenCalled();
  });

  it('should update existing site config by work item id', async () => {
    const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY));
    const mockFindSiteConfig = (prismaClient.siteConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_SITE_CONFIG_ENTITY));
    const mockUpdateSiteConfig = (prismaClient.siteConfig.update = jest.fn());
    const mockUpdateStatement = (prismaClient.statement.update = jest.fn());

    await service.updateSiteConfigByWorkItemId(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id,
      TEST_UPDATE_SITE_REQUEST
    );

    expect(mockFindWorkItem).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM.id },
    });
    expect(mockFindSiteConfig).toHaveBeenCalledWith({
      where: { siteId: TEST_WORK_ITEM.siteId },
    });
    expect(mockUpdateSiteConfig).toHaveBeenCalledWith({
      where: { id: TEST_SITE_CONFIG_ENTITY.id },
      data: {
        emails: '<EMAIL>,<EMAIL>',
        automated: false,
      },
    });
    expect(mockUpdateStatement).toHaveBeenCalledWith({
      where: { workItemId: TEST_WORK_ITEM.id },
      data: {
        emails: '<EMAIL>,<EMAIL>',
      },
    });
  });

  it('should create new site config by work item id', async () => {
    const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_WORK_ITEM_ENTITY));
    const mockFindSiteConfig = (prismaClient.siteConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce(null));
    const mockCreateSiteConfig = (prismaClient.siteConfig.create = jest.fn());
    const mockUpdateStatement = (prismaClient.statement.update = jest.fn());

    await service.updateSiteConfigByWorkItemId(
      TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id,
      TEST_UPDATE_SITE_REQUEST
    );

    expect(mockFindWorkItem).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM.id },
    });
    expect(mockFindSiteConfig).toHaveBeenCalledWith({
      where: { siteId: TEST_WORK_ITEM.siteId },
    });
    expect(mockCreateSiteConfig).toHaveBeenCalledWith({
      data: {
        emails: '<EMAIL>,<EMAIL>',
        automated: false,
        groupId: TEST_WORK_ITEM.groupUid,
        siteId: TEST_WORK_ITEM.siteId,
        siteName: TEST_WORK_ITEM.siteName,
      },
    });
    expect(mockUpdateStatement).toHaveBeenCalledWith({
      where: { workItemId: TEST_WORK_ITEM.id },
      data: {
        emails: '<EMAIL>,<EMAIL>',
      },
    });
  });

  it('should throw an error if work item is not found when updating site config', async () => {
    prismaClient.workItem.findUnique = jest.fn().mockResolvedValueOnce(null);

    await expect(() =>
      service.updateSiteConfigByWorkItemId(
        TEST_WORK_ITEM_ENTITY_WITH_STATEMENT.id,
        TEST_UPDATE_SITE_REQUEST
      )
    ).rejects.toThrow(WorkItemNotFoundException);
  });
});
