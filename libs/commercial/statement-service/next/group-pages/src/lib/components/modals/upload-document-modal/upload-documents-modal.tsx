import { Controller, useForm } from 'react-hook-form';
import {
  CreateDocumentRequest,
  DocumentType,
  Group,
} from '@experience/commercial/statement-service/shared';
import {
  FileInput,
  Input,
  InputWidth,
  Modal,
  ModalProps,
  Select,
} from '@experience/shared/react/design-system';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { classValidatorResolver } from '@hookform/resolvers/class-validator';
import { convertSnakeCaseToSentenceCase } from '@experience/shared/typescript/utils';
import { revalidateGroupDocumentsPath } from '../../../actions';
import { uploadDocument } from '@experience/commercial/statement-service/next/actions';
import { useState } from 'react';
import toast from 'react-hot-toast';

interface UploadDocumentsModalProps {
  group: Group;
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const UploadDocumentsModal = ({
  group,
  open,
  setOpen,
}: UploadDocumentsModalProps) => {
  const formId = `upload-documents-${group?.groupId}`;
  const [file, setFile] = useState<File>();

  const documentOptions = Object.values(DocumentType).map((type) => ({
    id: type,
    name: convertSnakeCaseToSentenceCase(type),
  }));

  const {
    control,
    formState: { errors: formErrors, isSubmitting },
    handleSubmit,
  } = useForm<CreateDocumentRequest>({
    reValidateMode: 'onSubmit',
    resolver: classValidatorResolver(CreateDocumentRequest),
  });

  const handleConfirm = async (data: CreateDocumentRequest): Promise<void> => {
    if (!file) {
      return;
    }

    const formData: FormData = new FormData();

    formData.append('groupId', group.groupId);
    formData.append('name', data.name);
    formData.append('startDate', data.startDate);
    if (data.expiryDate !== undefined) {
      formData.append('expiryDate', data.expiryDate);
    }
    formData.append('type', data.type);
    formData.append('file', file);

    try {
      await uploadDocument(formData);
      toast.success('Uploaded document successfully');
      setOpen(false);
      await revalidateGroupDocumentsPath(group.groupId);
    } catch (error) {
      toast.error(`Error uploading document`);
    }
  };

  const content = (
    <form id={formId}>
      <FileInput
        buttonText="Select file"
        file={file}
        id="document-upload"
        label="File"
        name="document-upload"
        setFile={setFile}
      />
      <VerticalSpacer />
      <Controller
        control={control}
        name="name"
        render={({ field: { name, onChange, value } }) => (
          <Input
            errorMessage={formErrors['name']?.message as string}
            id={name}
            label="Name"
            name={name}
            onChange={onChange}
            value={value}
            width={InputWidth.FULL}
            placeholder="e.g. Group name - Document type"
          />
        )}
      />
      <VerticalSpacer />
      <Controller
        control={control}
        name="startDate"
        render={({ field: { name, onChange, value } }) => (
          <Input
            errorMessage={formErrors['startDate']?.message as string}
            id={name}
            label="Start date"
            name={name}
            onChange={onChange}
            value={value}
            width={InputWidth.FULL}
            type="date"
          />
        )}
      />
      <VerticalSpacer />
      <Controller
        control={control}
        name="expiryDate"
        render={({ field: { name, onChange, value } }) => (
          <Input
            id={name}
            label="Expiry date"
            name={name}
            onChange={onChange}
            value={value}
            width={InputWidth.FULL}
            type="date"
          />
        )}
      />
      <VerticalSpacer />
      <Controller
        control={control}
        name="type"
        render={({ field: { name, onChange, value } }) => (
          <Select
            errorMessage={formErrors['type']?.message as string}
            id={name}
            isFocused={true}
            label="Document type"
            name={name}
            onChange={onChange}
            options={documentOptions}
            placeholder="Document type"
            selected={documentOptions.find((option) => option.id === value)}
            width={InputWidth.FULL}
          />
        )}
      />
    </form>
  );

  const modalProps: ModalProps = {
    cancelButtonText: 'Cancel',
    confirmButtonText: 'Confirm',
    content,
    formId,
    handleConfirm: handleSubmit(
      async (data) => await handleConfirm(data as CreateDocumentRequest)
    ),
    handleCancel: () => setOpen(false),
    isSubmitting: isSubmitting,
    isDisabled: !file,
    open,
    setOpen,
    title: 'Upload document',
    width: 'w-[40rem]',
  };

  return <Modal {...modalProps} />;
};

export default UploadDocumentsModal;
