// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`StripeSubscriptions should match snapshot with a stripe subscription 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-center"
      >
        <h3
          class="text-lg font-bold pb-1"
        >
          Subscription ID
        </h3>
        <button
          aria-label="Edit subscription sub_1MowQVLkdIwHu7ixeRlqHVzs"
          class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
          data-testid="edit-subscription-sub_1MowQVLkdIwHu7ixeRlqHVzs"
          id="edit-subscription-sub_1MowQVLkdIwHu7ixeRlqHVzs"
          name="edit-subscription-sub_1MowQVLkdIwHu7ixeRlqHVzs"
          type="button"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
              />
              <path
                d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
              />
            </g>
          </svg>
        </button>
      </div>
      <div>
        <button
          class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75"
          type="button"
        >
          sub_1MowQVLkdIwHu7ixeRlqHVzs
        </button>
      </div>
      <h3
        class="text-lg font-bold pb-2 pt-2"
      >
        Status
      </h3>
      <p
        class="text-md font-normal break-words"
      >
        Active
      </p>
    </section>
  </div>
</body>
`;

exports[`StripeSubscriptions should match snapshot with no stripe subscription 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="h-full flex flex-col"
      >
        <p
          class="text-md font-normal h-full text-center break-words"
        >
          Once the Stripe customer is created, add a subscription for the number of sockets
        </p>
        <div
          class="pb-4"
        />
        <button
          class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 mx-auto"
          data-testid="create-subscription"
          id="create-subscription"
          type="button"
        >
          2. Add a subscription
        </button>
      </div>
    </section>
  </div>
</body>
`;
