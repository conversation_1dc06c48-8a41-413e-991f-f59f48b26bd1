'use client';

import {
  Button,
  Checkbox,
  Table,
  useMultiCheckbox,
} from '@experience/shared/react/design-system';
import { COMMON_INTERNAL_SERVER_ERROR } from '@experience/shared/typescript/validation';
import {
  Group,
  SubscriptionChargerWithAdditionalInfo,
} from '@experience/commercial/statement-service/shared';
import { PageHeader } from '@experience/shared/react/headings';
import {
  VerticalSpacer,
  VerticalSpacerSize,
} from '@experience/shared/react/layouts';
import { updateSubscriptionChargers } from '@experience/commercial/statement-service/next/actions';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

interface ChargersPageProps {
  chargers: SubscriptionChargerWithAdditionalInfo[];
  group: Group;
}

export const ChargersPage = ({ chargers, group }: ChargersPageProps) => {
  const router = useRouter();
  const getOptionValue = ({ ppid, socket }: { ppid: string; socket: string }) =>
    `${ppid}-${socket}`;
  const chargersWithSubscription = chargers
    .filter(({ hasSubscription }) => hasSubscription)
    .map(getOptionValue);
  const { areAllChecked, isChecked, toggle, toggleAll, selection } =
    useMultiCheckbox(chargers.map(getOptionValue), chargersWithSubscription);

  const handleSubmit = async () => {
    try {
      const response = await updateSubscriptionChargers(
        group.groupId,
        selection
      );
      if (response.success) {
        router.push(`/groups/${group.groupId}`);
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      toast.error(COMMON_INTERNAL_SERVER_ERROR, { duration: 8000 });
    }
  };

  return (
    <>
      <PageHeader
        heading={`Manage chargers on subscription - ${group.groupName}`}
      />
      <Table caption="Edit chargers">
        <Table.Header>
          <Table.Row>
            <Table.Heading>
              <Checkbox
                checked={areAllChecked()}
                id="select-all"
                label="Select All"
                onChange={toggleAll}
                srOnlyLabel={true}
              />
            </Table.Heading>
            <Table.Heading>Name</Table.Heading>
            <Table.Heading>PPID</Table.Heading>
            <Table.Heading>Socket</Table.Heading>
            <Table.Heading>Site location</Table.Heading>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {chargers?.map((charger: SubscriptionChargerWithAdditionalInfo) => (
            <Table.Row key={getOptionValue(charger)}>
              <Table.Data>
                <Checkbox
                  checked={isChecked(getOptionValue(charger))}
                  className="-mt-1"
                  id={`select-${getOptionValue(charger)}`}
                  label={`Select ${charger.ppid} ${charger.socket}`}
                  onChange={() => toggle(getOptionValue(charger))}
                  srOnlyLabel={true}
                />
              </Table.Data>
              <Table.Data>{charger.name}</Table.Data>
              <Table.Data>{charger.ppid}</Table.Data>
              <Table.Data>{charger.socket}</Table.Data>
              <Table.Data>{charger.siteAddress}</Table.Data>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
      <VerticalSpacer size={VerticalSpacerSize.Small} />
      <div className="flex justify-end items-center">
        <Button onClick={handleSubmit}>Save changes</Button>
      </div>
    </>
  );
};
