import { COMMON_INTERNAL_SERVER_ERROR } from '@experience/shared/typescript/validation';
import { ChargersPage } from './chargers-page';
import { SUBSCRIPTION_CHARGER_ERROR_MESSAGE } from '@experience/commercial/statement-service/nest/shared';
import {
  Socket,
  TEST_GROUP,
  TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO,
} from '@experience/commercial/statement-service/shared';
import { Toaster } from 'react-hot-toast';
import { render, screen } from '@testing-library/react';
import { updateSubscriptionChargers } from '@experience/commercial/statement-service/next/actions';

jest.mock('@experience/commercial/statement-service/next/actions');
const mockUpdateSubscription = jest.mocked(updateSubscriptionChargers);

const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockRouterPush,
  }),
}));

describe('chargers page', () => {
  const TEST_CHARGER_WITH_SUBSCRIPTION = {
    ...TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO,
    socket: Socket.B,
    hasSubscription: false,
  };
  const defaultProps = {
    group: TEST_GROUP,
    chargers: [
      TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO,
      TEST_CHARGER_WITH_SUBSCRIPTION,
    ],
  };

  it('should render successfully', () => {
    const { baseElement } = render(<ChargersPage {...defaultProps} />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<ChargersPage {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should pre-select chargers with a subscription', () => {
    const { getByLabelText } = render(<ChargersPage {...defaultProps} />);

    expect(getByLabelText(`Select PG-12345 A`)).toBeChecked();
    expect(getByLabelText(`Select PG-12345 B`)).not.toBeChecked();
  });

  it('should save changes when the save button is selected', async () => {
    mockUpdateSubscription.mockResolvedValue({
      success: true,
      message: 'Chargers updated successfully',
    });
    render(<ChargersPage {...defaultProps} />);

    screen.getByLabelText(`Select PG-12345 B`).click();

    screen.getByRole('button', { name: 'Save changes' }).click();
    expect(mockUpdateSubscription).toHaveBeenCalledWith(TEST_GROUP.groupId, [
      'PG-12345-A',
      'PG-12345-B',
    ]);
    //Ensure all async operations complete
    await new Promise((resolve) => setTimeout(resolve, 0));
    expect(mockRouterPush).toHaveBeenCalledWith(
      `/groups/${TEST_GROUP.groupId}`
    );
  });

  it('should display an error toast if the user attempts to remove all chargers from an existing subscription', async () => {
    mockUpdateSubscription.mockResolvedValue({
      success: false,
      message: SUBSCRIPTION_CHARGER_ERROR_MESSAGE,
    });
    render(
      <>
        <Toaster />
        <ChargersPage {...defaultProps} />
      </>
    );

    screen.getByLabelText(`Select PG-12345 A`).click();
    screen.getByRole('button', { name: 'Save changes' }).click();

    expect(
      await screen.findByText(SUBSCRIPTION_CHARGER_ERROR_MESSAGE)
    ).toBeInTheDocument();
    expect(mockRouterPush).not.toHaveBeenCalled();
  });

  it('should display an error toast if the update fails', async () => {
    mockUpdateSubscription.mockRejectedValue(new Error('Update failed'));
    render(
      <>
        <Toaster />
        <ChargersPage {...defaultProps} />
      </>
    );

    screen.getByLabelText(`Select PG-12345 B`).click();

    screen.getByRole('button', { name: 'Save changes' }).click();

    expect(
      await screen.findByText(COMMON_INTERNAL_SERVER_ERROR)
    ).toBeInTheDocument();
    expect(mockRouterPush).not.toHaveBeenCalled();
  });
});
