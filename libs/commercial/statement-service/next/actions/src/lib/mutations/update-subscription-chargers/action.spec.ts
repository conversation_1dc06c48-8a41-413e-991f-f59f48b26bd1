import { SUBSCRIPTION_CHARGER_ERROR_MESSAGE } from '@experience/commercial/statement-service/nest/shared';
import { TEST_GROUP } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { updateSubscriptionChargers } from './action';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Update subscription chargers', () => {
  it.each([
    ['create new', true, 'POST', 201],
    ['update existing', false, 'PUT', 204],
  ])(
    'it should %s subscription chargers',
    async (_, created, method, statusCode) => {
      mockRequestHandler
        .mockResolvedValueOnce({ statusCode })
        .mockResolvedValueOnce({ statusCode: 200 });

      const response = await updateSubscriptionChargers(TEST_GROUP.groupId, [
        'PG-80435-A',
        'PG-80435-B',
      ]);

      expect(response).toEqual({
        success: true,
        message: 'Subscription chargers updated successfully',
      });

      expect(mockRequestHandler).toHaveBeenNthCalledWith(
        1,
        `http://localhost:5102/groups/${TEST_GROUP.groupId}/chargers`,
        {
          headers: {
            'content-type': 'application/json',
          },
          method: 'POST',
          body: JSON.stringify([
            { ppid: 'PG-80435', socket: 'A' },
            { ppid: 'PG-80435', socket: 'B' },
          ]),
        },
        {
          201: expect.any(Function),
          204: expect.any(Function),
          400: expect.any(Function),
        }
      );
      expect(mockRequestHandler).toHaveBeenNthCalledWith(
        2,
        'http://localhost:5102/stripe/subscriptions',
        {
          headers: {
            'content-type': 'application/json',
          },
          method,
          body: JSON.stringify({
            groupId: TEST_GROUP.groupId,
            socketQuantity: 2,
          }),
        }
      );
    }
  );

  it('should return an error code if the update fails', async () => {
    mockRequestHandler.mockResolvedValueOnce({
      statusCode: 400,
      message: SUBSCRIPTION_CHARGER_ERROR_MESSAGE,
    });

    const response = await updateSubscriptionChargers(TEST_GROUP.groupId, [
      'PG-80435-A',
      'PG-80435-B',
    ]);

    expect(response).toEqual({
      success: false,
      message: SUBSCRIPTION_CHARGER_ERROR_MESSAGE,
    });
  });
});
