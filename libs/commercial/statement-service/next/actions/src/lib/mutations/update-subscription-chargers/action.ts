'use server';

import {
  STATEMENT_SERVICE_API_URL,
  Socket,
  SubscriptionCharger,
} from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

interface UpdateSubscriptionChargersResponse {
  success: boolean;
  message: string;
}

export const updateSubscriptionChargers = async (
  groupId: string,
  chargers: string[]
): Promise<UpdateSubscriptionChargersResponse> => {
  const parsedChargers: SubscriptionCharger[] = chargers.map((charger) => {
    const lastHyphenIndex = charger.lastIndexOf('-');
    const ppid = charger.slice(0, lastHyphenIndex);
    const socket = charger.slice(lastHyphenIndex + 1) as Socket;
    return {
      ppid,
      socket,
    };
  });

  const response = await appRequestHandler(
    `${STATEMENT_SERVICE_API_URL}/groups/${groupId}/chargers`,
    {
      headers: {
        'content-type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify(parsedChargers),
    },
    {
      201: async () => ({ statusCode: 201 }),
      204: async () => ({ statusCode: 204 }),
      400: async (response) => await response.json(),
    }
  );

  if (response.statusCode === 400) {
    return {
      success: false,
      message: response.message,
    };
  }

  const method = response.statusCode === 201 ? 'POST' : 'PUT';

  await appRequestHandler(`${STATEMENT_SERVICE_API_URL}/stripe/subscriptions`, {
    headers: {
      'content-type': 'application/json',
    },
    method,
    body: JSON.stringify({
      groupId,
      socketQuantity: parsedChargers.length,
    }),
  });

  return {
    success: true,
    message: 'Subscription chargers updated successfully',
  };
};
