import { Socket } from '../../enums/socket.enum';
import {
  SubscriptionCharger,
  SubscriptionChargerWithAdditionalInfo,
} from '../subscription-charger.dto';

export const TEST_SUBSCRIPTION_CHARGER: SubscriptionCharger = {
  ppid: 'PG-70500',
  socket: Socket.A,
};

export const TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO: SubscriptionChargerWithAdditionalInfo =
  {
    hasSubscription: true,
    name: 'FOO',
    ppid: 'PG-12345',
    siteAddress: 'Discovery House',
    socket: Socket.A,
  };
