generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics", "multiSchema"]
  output          = "../../../../../../../node_modules/@prisma/clients/ocpi/v221"
  binaryTargets   = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  schemas  = ["ocpi/v221"]
  provider = "postgresql"
  url      = env("COMMERCIAL_DB_URL")
}

model Credentials {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  token     String  @unique /// @encrypted
  tokenHash String? @unique /// @encryption:hash(token)
  url       String
  object    Json

  roles CredentialsRole[]

  serverCredentialsId     String?      @unique
  serverCredentials       Credentials? @relation("ServerCredentials", fields: [serverCredentialsId], references: [id])
  linkedClientCredentials Credentials? @relation("ServerCredentials")

  enableCdrsModule     Boolean @default(false)
  enableCommandsModule Boolean @default(false)
  enableSessionsModule Boolean @default(false)
  enableTokensModule   Boolean @default(false)

  pullTokens Boolean @default(false)

  pushCdrs              Boolean @default(false)
  pushCdrsReceiver      String?
  pushEvses             Boolean @default(false)
  pushEvsesReceiver     String?
  pushLocations         Boolean @default(false)
  pushLocationsReceiver String?
  pushSessions          Boolean @default(false)
  pushSessionsReceiver  String?
  pushTariffs           Boolean @default(false)
  pushTariffsReceiver   String?

  clientCredentialsId     String?      @unique
  clientCredentials       Credentials? @relation("ClientCredentials", fields: [clientCredentialsId], references: [id])
  linkedServerCredentials Credentials? @relation("ClientCredentials")
  deletedAt               DateTime?    @db.Timestamptz(3)

  @@schema("ocpi/v221")
}

model CredentialsRole {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  role                CredentialsRoleType
  countryCode         String              @db.VarChar(2)
  partyId             String              @db.VarChar(3)
  businessDetailsName String

  credentials   Credentials @relation(fields: [credentialsId], references: [id], onDelete: Cascade)
  credentialsId String

  @@schema("ocpi/v221")
}

model Location {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  countryCode           String
  partyId               String
  publish               Boolean
  name                  String
  address               String
  city                  String
  postalCode            String
  country               String
  latitude              Decimal  @db.Decimal(9, 7)
  longitude             Decimal  @db.Decimal(10, 7)
  owner                 String
  timezone              String
  chargingWhenClosed    Boolean  @default(true)
  created               DateTime @default(now()) @db.Timestamptz(3)
  lastUpdated           DateTime @updatedAt @db.Timestamptz(3)
  lastUpdatedInPodadmin DateTime @db.Timestamptz(3)
  podadminGroupId       Int?
  podadminSiteId        Int?

  evses        EVSE[]
  openingHours OpeningHours[]

  @@index([lastUpdated(sort: Asc)])
  @@index([timezone, lastUpdated(sort: Asc)])
  @@index([timezone])
  @@schema("ocpi/v221")
}

model OpeningHours {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  weekday         Int
  periodBegin     String
  periodEnd       String
  twentyfourseven Boolean @default(true)

  location   Location @relation(fields: [locationId], references: [id])
  locationId String

  @@schema("ocpi/v221")
}

model EVSE {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  evseId                String               @unique
  status                EVSEStatus
  statusLastUpdated     DateTime             @default(dbgenerated("'1970-01-01 00:00:00+00'::timestamp with time zone")) @db.Timestamptz(3)
  capabilities          EVSECapability[]
  latitude              Decimal              @db.Decimal(9, 7)
  longitude             Decimal              @db.Decimal(10, 7)
  parkingRestrictions   ParkingRestriction[]
  physicalReference     String
  created               DateTime             @default(now()) @db.Timestamptz(3)
  lastUpdated           DateTime             @updatedAt @db.Timestamptz(3)
  lastUpdatedInPodadmin DateTime             @db.Timestamptz(3)
  ppid                  String
  supportsOcpp          Boolean              @default(false)

  connectors Connector[]
  location   Location    @relation(fields: [locationId], references: [id])
  locationId String

  sys_period Unsupported("tstzrange")?

  @@index([locationId])
  @@schema("ocpi/v221")
}

model EVSEHistory {
  pk String @id @default(dbgenerated("gen_random_uuid()"))

  evseId     String
  status     EVSEStatus
  sys_period Unsupported("tstzrange")?

  @@schema("ocpi/v221")
}

model Connector {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  standard              ConnectorStandard
  format                ConnectorFormat
  powerType             ConnectorPowerType
  maxVoltage            Int
  maxAmperage           Int
  maxElectricPower      Int
  socket                String?
  created               DateTime           @default(now()) @db.Timestamptz(3)
  lastUpdated           DateTime           @updatedAt @db.Timestamptz(3)
  lastUpdatedInPodadmin DateTime           @db.Timestamptz(3)

  evse    EVSE     @relation(fields: [evseId], references: [id])
  evseId  String
  tariffs Tariff[]

  @@index([evseId])
  @@schema("ocpi/v221")
}

model Tariff {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  countryCode String
  currency    String
  partyId     String
  created     DateTime @default(now()) @db.Timestamptz(3)
  lastUpdated DateTime @db.Timestamptz(3)

  cdrs            Cdr[]
  chargingPeriods ChargingPeriod[]
  connectors      Connector[]
  elements        TariffElement[]

  @@index([currency])
  @@index([currency, lastUpdated(sort: Asc)])
  @@index([lastUpdated(sort: Asc)])
  @@schema("ocpi/v221")
}

model TariffElement {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  type      TariffDimensionType
  price     Decimal
  stepSize  Int
  startTime String?
  endTime   String?
  dayOfWeek DayOfWeek[]

  tariff   Tariff @relation(fields: [tariffId], references: [id], onDelete: Cascade)
  tariffId String

  @@index([tariffId])
  @@schema("ocpi/v221")
}

model Session {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  countryCode            String        @db.VarChar(2)
  partyId                String        @db.VarChar(3)
  startDateTime          DateTime      @db.Timestamptz(3)
  endDateTime            DateTime?     @db.Timestamptz(3)
  kwh                    Decimal
  authMethod             AuthMethod
  authorisationReference String?
  locationId             String
  evseUid                String
  connectorId            String
  currency               String
  totalCostExclVat       Decimal?
  totalCostInclVat       Decimal?
  status                 SessionStatus
  created                DateTime      @default(now()) @db.Timestamptz(3)
  lastUpdated            DateTime      @updatedAt @db.Timestamptz(3)

  cdrs    Cdr[]
  token   CdrToken @relation(fields: [tokenId], references: [id])
  tokenId String

  @@index([lastUpdated(sort: Asc)])
  @@schema("ocpi/v221")
}

model Cdr {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  countryCode      String    @db.VarChar(2)
  partyId          String    @db.VarChar(3)
  startDateTime    DateTime  @db.Timestamptz(3)
  endDateTime      DateTime  @db.Timestamptz(3)
  currency         String
  totalCostExclVat Decimal
  totalCostInclVat Decimal
  totalEnergy      Decimal
  totalTime        Decimal
  created          DateTime  @default(now()) @db.Timestamptz(3)
  lastUpdated      DateTime  @updatedAt @db.Timestamptz(3)
  emspLocation     String?
  pushed           DateTime? @db.Timestamptz(3)

  chargingPeriods ChargingPeriod[]
  location        CdrLocation      @relation(fields: [locationId], references: [id])
  locationId      String
  session         Session          @relation(fields: [sessionId], references: [id])
  sessionId       String           @unique
  tariff          Tariff           @relation(fields: [tariffId], references: [id])
  tariffId        String

  @@index([lastUpdated(sort: Asc)])
  @@schema("ocpi/v221")
}

model CdrLocation {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  name        String
  address     String
  city        String
  postalCode  String
  country     String
  latitude    Decimal            @db.Decimal(9, 7)
  longitude   Decimal            @db.Decimal(10, 7)
  locationId  String
  evseUid     String
  evseId      String
  connectorId String
  standard    ConnectorStandard
  format      ConnectorFormat
  powerType   ConnectorPowerType

  cdr Cdr[]

  @@schema("ocpi/v221")
}

model CdrToken {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  countryCode String    @db.VarChar(2)
  partyId     String    @db.VarChar(3)
  uid         String
  type        TokenType
  contractId  String

  sessions Session[]

  @@schema("ocpi/v221")
}

model ChargingPeriod {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  startDateTime   DateTime         @db.Timestamptz(3)
  dimensionType   CdrDimensionType
  dimensionVolume Decimal

  cdr      Cdr    @relation(fields: [cdrId], references: [id])
  cdrId    String
  tariff   Tariff @relation(fields: [tariffId], references: [id])
  tariffId String

  @@schema("ocpi/v221")
}

model Token {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  countryCode  String        @db.VarChar(2)
  partyId      String        @db.VarChar(3)
  uid          String
  type         TokenType
  contractId   String
  visualNumber String?
  issuer       String
  groupId      String?
  valid        Boolean
  whitelist    WhitelistType
  language     String?
  created      DateTime      @default(now()) @db.Timestamptz(3)
  lastUpdated  DateTime      @db.Timestamptz(3)

  @@index([countryCode, partyId])
  @@schema("ocpi/v221")
}

model SearchIndex {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  indexData Json
  ppid      String @unique

  @@schema("ocpi/v221")
}

enum ConnectorFormat {
  CABLE
  SOCKET

  @@schema("ocpi/v221")
}

enum ConnectorPowerType {
  AC_1_PHASE
  AC_3_PHASE
  DC

  @@schema("ocpi/v221")
}

enum ConnectorStandard {
  CHADEMO
  DOMESTIC_G
  IEC_62196_T1
  IEC_62196_T2
  IEC_62196_T2_COMBO

  @@schema("ocpi/v221")
}

enum EVSECapability {
  CONTACTLESS_CARD_SUPPORT
  REMOTE_START_STOP_CAPABLE
  START_SESSION_CONNECTOR_REQUIRED
  UNLOCK_CAPABLE

  @@schema("ocpi/v221")
}

enum EVSEStatus {
  AVAILABLE
  BLOCKED
  CHARGING
  INOPERATIVE
  OUTOFORDER
  REMOVED
  UNKNOWN

  @@schema("ocpi/v221")
}

enum ParkingRestriction {
  EV_ONLY
  PLUGGED
  DISABLED
  CUSTOMERS
  MOTORCYCLES

  @@schema("ocpi/v221")
}

enum TariffDimensionType {
  ENERGY
  FLAT

  @@schema("ocpi/v221")
}

enum CredentialsRoleType {
  CPO
  EMSP
  HUB
  NAP
  NSP
  OTHER
  SCSP

  @@schema("ocpi/v221")
}

enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY

  @@schema("ocpi/v221")
}

enum AuthMethod {
  AUTH_REQUEST
  COMMAND
  WHITELIST

  @@schema("ocpi/v221")
}

enum SessionStatus {
  ACTIVE
  COMPLETED
  INVALID
  PENDING

  @@schema("ocpi/v221")
}

enum TokenType {
  AD_HOC_USER
  APP_USER
  OTHER
  RFID

  @@schema("ocpi/v221")
}

enum CdrDimensionType {
  ENERGY

  @@schema("ocpi/v221")
}

enum WhitelistType {
  ALWAYS
  ALLOWED
  ALLOWED_OFFLINE
  NEVER

  @@schema("ocpi/v221")
}
