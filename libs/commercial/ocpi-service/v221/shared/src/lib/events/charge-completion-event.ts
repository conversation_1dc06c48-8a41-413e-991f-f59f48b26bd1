import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const ChargeCompletionEventDetailSchema = z.object({
  authorisation: z
    .object({
      id: z.uuid(),
      authoriserId: z.uuid(),
    })
    .nullish(),
  charge: z.object({
    id: z.uuid(),
    energyTotal: z.number().nullish(),
    pluggedInAt: z.iso.datetime(),
    unpluggedAt: z.iso.datetime(),
  }),
  chargingStation: z.object({
    id: z.string(),
    doorId: z.string(),
  }),
  revenue: z
    .object({
      settlementAmount: z.number().nullish(),
    })
    .nullish(),
});

export class ChargeCompletionEventDetailDto extends createZodDto(
  ChargeCompletionEventDetailSchema
) {}

export type ChargeCompletionEventDetail = z.infer<
  typeof ChargeCompletionEventDetailSchema
>;

export const ChargeCompletionEventSchema = z.object({
  detail: ChargeCompletionEventDetailSchema,
});

export class ChargeCompletionEventDto extends createZodDto(
  ChargeCompletionEventSchema
) {}

export type ChargeCompletionEvent = z.infer<typeof ChargeCompletionEventSchema>;
