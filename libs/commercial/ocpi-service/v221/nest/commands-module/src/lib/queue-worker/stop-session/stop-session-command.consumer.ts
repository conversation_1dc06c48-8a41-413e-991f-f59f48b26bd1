import { AsyncLocalStorage } from 'async_hooks';
import {
  CommandResult,
  CredentialsStore,
  DisplayMessage,
  StopSessionCommand,
  StopSessionCommandSchema,
} from '@experience/commercial/ocpi-service/v221/shared';
import {
  CredentialService,
  getClientCredentialsFromLocalStorage,
} from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { ChargeAuthorisationApi as DataPlatformChargeAuthorisationApi } from '@experience/shared/axios/data-platform-api-client';
import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { Message } from '@aws-sdk/client-sqs';
import { QueueConsumerService } from '@experience/shared/nest/aws/sqs-module';
import { SessionService } from '@experience/commercial/ocpi-service/v221/nest/sessions-module';
import { logFetchWarn } from '@experience/shared/nest/utils';
import { v4 as uuid } from 'uuid';

export abstract class StopSessionCommandQueueConsumerService extends QueueConsumerService {}

@Injectable()
export class StopSessionCommandConsumer implements OnApplicationBootstrap {
  private readonly logger = new Logger(StopSessionCommandConsumer.name);

  constructor(
    private readonly als: AsyncLocalStorage<CredentialsStore>,
    private readonly chargeAuthorisationApi: DataPlatformChargeAuthorisationApi,
    private readonly credentialService: CredentialService,
    private readonly queueConsumerService: StopSessionCommandQueueConsumerService,
    private readonly sessionService: SessionService
  ) {}

  async handle(message: Message) {
    this.logger.log({ message }, 'handling stop session command message');

    const command: StopSessionCommand = StopSessionCommandSchema.parse(
      JSON.parse(message.Body as string).command
    );

    const session = await this.sessionService.find(command.session_id);

    const store = await this.credentialService.createStoreFromToken(
      session.cdr_token
    );

    await this.als.run(store, async () => {
      await this.sendCommandResult(command);
    });
  }

  private async sendCommandResult(command: StopSessionCommand) {
    this.logger.log({ command }, 'sending command result');

    const result: CommandResult = {
      result: 'ACCEPTED',
      message: [
        { language: 'en', text: DisplayMessage.enum.STOP_SESSION_ACCEPTED },
      ],
    };

    const credentials = getClientCredentialsFromLocalStorage(this.als);

    const response = await fetch(command.response_url, {
      headers: {
        Authorization: `Token ${Buffer.from(credentials.token).toString(
          'base64'
        )}`,
        'content-type': 'application/json',
        'x-correlation-id': uuid(),
        'x-request-id': uuid(),
      },
      method: 'POST',
      body: JSON.stringify(result),
    });

    if (!response.ok) {
      await logFetchWarn(
        this.logger,
        { command },
        response,
        'failed to send command result'
      );
      return;
    }

    this.logger.log({ command }, 'sent command result');
  }

  async onApplicationBootstrap(): Promise<void> {
    this.queueConsumerService.setMessageHandler(this.handle.bind(this));
    await this.queueConsumerService.start();
  }
}
