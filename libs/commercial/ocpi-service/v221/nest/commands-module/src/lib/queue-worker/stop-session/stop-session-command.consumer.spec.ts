import { AsyncLocalStorage } from 'async_hooks';
import {
  CommandResultSchema,
  CredentialsStore,
} from '@experience/commercial/ocpi-service/v221/shared';
import { CredentialService } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { SessionService } from '@experience/commercial/ocpi-service/v221/nest/sessions-module';
import { StopSessionCommandConsumer } from './stop-session-command.consumer';
import { StopSessionCommandModule } from './stop-session-command.module';
import {
  TEST_ACTIVE_SESSION,
  TEST_CREDENTIALS_B,
  TEST_CREDENTIALS_STORE,
  TEST_STOP_SESSION_COMMAND,
  TEST_STOP_SESSION_COMMAND_MESSAGE,
  TEST_STOP_SESSION_COMMAND_RESULT_ACCEPTED,
  UUID_FORMAT,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import { Test, TestingModule } from '@nestjs/testing';
import MockDate from 'mockdate';

describe('stop session command consumer', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let consumer: StopSessionCommandConsumer;
  let credentialService: CredentialService;
  let sessionService: SessionService;
  MockDate.set(new Date());

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [StopSessionCommandModule],
    }).compile();

    consumer = module.get<StopSessionCommandConsumer>(
      StopSessionCommandConsumer
    );
    als = module.get(AsyncLocalStorage<CredentialsStore>);
    credentialService = module.get<CredentialService>(CredentialService);
    sessionService = module.get<SessionService>(SessionService);
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(consumer).toBeDefined();
    expect(credentialService).toBeDefined();
    expect(sessionService).toBeDefined();
  });

  it('should handle a successful stop session command', async () => {
    const mockFindSession = (sessionService.find =
      jest.fn()).mockResolvedValueOnce(TEST_ACTIVE_SESSION);
    const mockCreateStoreFromToken = (credentialService.createStoreFromToken =
      jest.fn()).mockResolvedValueOnce(TEST_CREDENTIALS_STORE);
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: { success: true },
      status: 200,
    } as unknown as Response);

    await consumer.handle({
      Body: JSON.stringify(TEST_STOP_SESSION_COMMAND_MESSAGE),
    });

    expect(mockFindSession).toHaveBeenCalledWith(
      TEST_STOP_SESSION_COMMAND.session_id
    );
    expect(mockCreateStoreFromToken).toHaveBeenCalledWith(
      TEST_ACTIVE_SESSION.cdr_token
    );
    expect(mockFetch).toHaveBeenCalledWith(
      TEST_STOP_SESSION_COMMAND.response_url,
      {
        body: JSON.stringify(
          CommandResultSchema.parse(TEST_STOP_SESSION_COMMAND_RESULT_ACCEPTED)
        ),
        headers: {
          Authorization: `Token ${Buffer.from(
            TEST_CREDENTIALS_B.token
          ).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': expect.stringMatching(UUID_FORMAT),
          'x-request-id': expect.stringMatching(UUID_FORMAT),
        },
        method: 'POST',
      }
    );
  });
});
