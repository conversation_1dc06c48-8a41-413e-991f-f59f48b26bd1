import {
  ConnectorNotFoundException,
  EvseNotFoundException,
  LocationNotFoundException,
  OcpiStatusCode,
  OcpiStatusMessage,
} from '@experience/commercial/ocpi-service/v221/shared';
import {
  DATE_FORMAT,
  TEST_CONNECTOR,
  TEST_EVSE,
  TEST_LOCATION,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import { INestApplication } from '@nestjs/common';
import { LocationController } from './location.controller';
import { LocationService } from './location.service';
import { PaginationParams, buildPage } from '@experience/shared/nest/utils';
import { Test, TestingModule } from '@nestjs/testing';
import { convertAllDatesToISOString } from '@experience/shared/typescript/utils';
import request from 'supertest';

jest.mock('./location.service');

describe('LocationController', () => {
  let app: INestApplication;
  let controller: LocationController;
  let service: LocationService;

  const testPaginationParams: PaginationParams = {
    dateFrom: new Date(0),
    dateTo: new Date(),
    limit: 100,
    offset: 0,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [LocationController],
      providers: [{ provide: LocationService, useClass: LocationService }],
    }).compile();

    controller = module.get<LocationController>(LocationController);
    service = module.get<LocationService>(LocationService);

    app = module.createNestApplication();
    await app.init();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should return a list of locations', async () => {
    const mockService = jest
      .spyOn(service, 'findAll')
      .mockResolvedValueOnce(
        buildPage([TEST_LOCATION], 1, testPaginationParams)
      );

    const response = await request(app.getHttpServer()).get('/locations');

    expect(response.body).toEqual({
      data: [convertAllDatesToISOString(TEST_LOCATION)],
      status_code: OcpiStatusCode.SUCCESS,
      status_message: OcpiStatusMessage.SUCCESS,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith(testPaginationParams);
  });

  it('should return a list of locations with a maximum page size', async () => {
    const mockService = jest
      .spyOn(service, 'findAll')
      .mockResolvedValueOnce(
        buildPage([TEST_LOCATION], 1, testPaginationParams)
      );

    const response = await request(app.getHttpServer()).get(
      '/locations?limit=101'
    );

    expect(response.body).toEqual({
      data: [convertAllDatesToISOString(TEST_LOCATION)],
      status_code: OcpiStatusCode.SUCCESS,
      status_message: OcpiStatusMessage.SUCCESS,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith(testPaginationParams);
  });

  it('should return all locations without pagination', async () => {
    const mockService = jest
      .spyOn(service, 'findAllLocations')
      .mockResolvedValueOnce([TEST_LOCATION]);

    const response = await request(app.getHttpServer())
      .get('/locations/all')
      .expect(200);

    expect(response.body).toEqual({
      data: [convertAllDatesToISOString(TEST_LOCATION)],
      status_code: OcpiStatusCode.SUCCESS,
      status_message: OcpiStatusMessage.SUCCESS,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith(undefined, undefined);
  });

  it('should return filtered locations by coordinates', async () => {
    const mockService = jest
      .spyOn(service, 'findAllLocations')
      .mockResolvedValueOnce([TEST_LOCATION]);

    const response = await request(app.getHttpServer())
      .get('/locations/all?latitude=51.5074&longitude=-0.1278')
      .expect(200);

    expect(response.body).toEqual({
      data: [convertAllDatesToISOString(TEST_LOCATION)],
      status_code: OcpiStatusCode.SUCCESS,
      status_message: OcpiStatusMessage.SUCCESS,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith('51.5074', '-0.1278');
  });

  it('should return a location', async () => {
    const mockService = jest
      .spyOn(service, 'findLocation')
      .mockResolvedValueOnce(TEST_LOCATION);

    const response = await request(app.getHttpServer())
      .get(`/locations/${TEST_LOCATION.id}`)
      .expect(200);

    expect(response.body).toEqual({
      data: convertAllDatesToISOString(TEST_LOCATION),
      status_code: OcpiStatusCode.SUCCESS,
      status_message: OcpiStatusMessage.SUCCESS,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith(TEST_LOCATION.id);
  });

  it('should return a 404 if location is not found', async () => {
    const mockService = jest
      .spyOn(service, 'findLocation')
      .mockRejectedValueOnce(new LocationNotFoundException());

    const response = await request(app.getHttpServer())
      .get(`/locations/${TEST_LOCATION.id}`)
      .expect(404);

    expect(response.body).toEqual({
      status_code: OcpiStatusCode.CLIENT_ERROR,
      status_message: OcpiStatusMessage.LOCATION_NOT_FOUND,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith(TEST_LOCATION.id);
  });

  it('should find an evse by location id and evse uid', async () => {
    const mockService = jest
      .spyOn(service, 'findEvse')
      .mockResolvedValueOnce(TEST_EVSE);

    const response = await request(app.getHttpServer()).get(
      `/locations/${TEST_LOCATION.id}/${TEST_EVSE.uid}`
    );

    expect(response.body).toEqual({
      data: convertAllDatesToISOString(TEST_EVSE),
      status_code: OcpiStatusCode.SUCCESS,
      status_message: OcpiStatusMessage.SUCCESS,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith(TEST_LOCATION.id, TEST_EVSE.uid);
  });

  it('should return a 404 and an ocpi client error when the evse is not found', async () => {
    jest
      .spyOn(service, 'findEvse')
      .mockRejectedValueOnce(new EvseNotFoundException());

    const response = await request(app.getHttpServer()).get(
      `/locations/${TEST_LOCATION.id}/${TEST_EVSE.uid}`
    );

    expect(response.body).toEqual({
      status_code: OcpiStatusCode.CLIENT_ERROR,
      status_message: OcpiStatusMessage.EVSE_NOT_FOUND,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(response.status).toBe(404);
  });

  it('should find a connector by location id,  evse uid and connector id', async () => {
    const mockService = jest
      .spyOn(service, 'findConnector')
      .mockResolvedValueOnce(TEST_CONNECTOR);

    const response = await request(app.getHttpServer()).get(
      `/locations/${TEST_LOCATION.id}/${TEST_EVSE.uid}/${TEST_CONNECTOR.id}`
    );

    expect(response.body).toEqual({
      data: convertAllDatesToISOString(TEST_CONNECTOR),
      status_code: OcpiStatusCode.SUCCESS,
      status_message: OcpiStatusMessage.SUCCESS,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(mockService).toHaveBeenCalledWith(
      TEST_LOCATION.id,
      TEST_EVSE.uid,
      TEST_CONNECTOR.id
    );
  });

  it('should return a 404 and an ocpi client error when the connector is not found', async () => {
    jest
      .spyOn(service, 'findConnector')
      .mockRejectedValueOnce(new ConnectorNotFoundException());

    const response = await request(app.getHttpServer()).get(
      `/locations/${TEST_LOCATION.id}/${TEST_EVSE.uid}/${TEST_CONNECTOR.id}`
    );

    expect(response.body).toEqual({
      status_code: OcpiStatusCode.CLIENT_ERROR,
      status_message: OcpiStatusMessage.CONNECTOR_NOT_FOUND,
      timestamp: expect.stringMatching(DATE_FORMAT),
    });
    expect(response.status).toBe(404);
  });
});
