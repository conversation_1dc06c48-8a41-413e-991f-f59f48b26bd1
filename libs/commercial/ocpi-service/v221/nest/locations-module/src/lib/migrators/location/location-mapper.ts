import {
  Location,
  OpeningHours,
  Prisma,
} from '@experience/commercial/ocpi-service/v221/prisma/client';
import {
  ParkingOpeningTimes,
  PodAddresses,
} from '@experience/shared/sequelize/podadmin';
import { joinStrings } from '@experience/shared/typescript/utils';
import { mapFieldsToMigrationUuid } from '@experience/commercial/ocpi-service/v221/shared';

export const mapPodAddressToLocation = (
  entity: PodAddresses
): Omit<Location, 'created' | 'lastUpdated'> => {
  const mostRecentUpdate = new Date(
    Math.max(
      entity.updatedAt?.getTime() ?? 0,
      entity.group.updatedAt?.getTime() ?? 0,
      ...entity.podLocations.map((e) => e.updatedAt?.getTime() ?? 0)
    )
  );

  return {
    address: joinStrings([entity.line1, entity.line2], ' '),
    chargingWhenClosed: true,
    city: entity.postalTown,
    country: 'GBR',
    countryCode: 'GB',
    id: mapFieldsToMigrationUuid({ location: entity.id.toString() }),
    lastUpdatedInPodadmin: mostRecentUpdate,
    latitude: new Prisma.Decimal(entity.podLocations[0]?.latitude ?? 0),
    longitude: new Prisma.Decimal(entity.podLocations[0]?.longitude ?? 0),
    name: entity.businessName,
    owner: entity.group.name,
    partyId: 'POD',
    podadminGroupId: entity.groupId ?? null,
    podadminSiteId: entity.id,
    postalCode: entity.postcode,
    publish: !entity.type.name.toLowerCase().split(' ').includes('private'),
    timezone: entity.podLocations[0]?.timezone ?? 'Unknown',
  };
};

export const mapParkingOpeningTimesToOpeningHours = (
  entity: ParkingOpeningTimes
): Omit<OpeningHours, 'id' | 'locationId'> => {
  const daysOfWeek = { mon: 1, tue: 2, wed: 3, thu: 4, fri: 5, sat: 6, sun: 7 };

  return {
    weekday: daysOfWeek[entity.day],
    twentyfourseven: !!entity.allDay,
    periodBegin: entity.from,
    periodEnd: entity.to,
  };
};
