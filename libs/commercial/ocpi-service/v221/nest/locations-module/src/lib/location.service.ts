import { $Enums } from '@prisma/clients/ocpi/v221';
import { AsyncLocalStorage } from 'async_hooks';
import {
  Connector,
  ConnectorNotFoundException,
  ConnectorSchema,
  CredentialsStore,
  Evse,
  EvseNotFoundException,
  EvseSchema,
  FREE_OF_CHARGE_TARIFF_ID,
  Location,
  LocationNotFoundException,
  LocationSchema,
} from '@experience/commercial/ocpi-service/v221/shared';
import { Injectable, Logger } from '@nestjs/common';
import {
  OCPIPrismaClient,
  Prisma,
} from '@experience/commercial/ocpi-service/v221/prisma/client';
import {
  Page,
  PaginationParams,
  buildPage,
  logFetchWarn,
} from '@experience/shared/nest/utils';
import { buildPaginationArgs } from '@experience/commercial/ocpi-service/v221/prisma/client';
import {
  getClientCredentialsFromLocalStorage,
  getPushEvsesReceiverFromLocalStorage,
  getPushLocationsReceiverFromLocalStorage,
} from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { sort } from 'fast-sort';
import { v4 as uuid } from 'uuid';



/*
we receive status updates for evses outwith our supported timezones, so to handle
these updates we do not use the supportedTimezones when looking up an individual evse,
but do when listing all in the findAll lookup.
*/
export const supportedTimezones = ['Europe/Isle_of_Man', 'Europe/London'];

const connectorIncludes = { include: { tariffs: true } };
const evseIncludes = { include: { connectors: connectorIncludes } };
const locationIncludes = { include: { evses: evseIncludes } };

type ConnectorEntity = Prisma.ConnectorGetPayload<typeof connectorIncludes>;
type EVSEEntity = Prisma.EVSEGetPayload<typeof evseIncludes>;
type LocationEntity = Prisma.LocationGetPayload<typeof locationIncludes>;

@Injectable()
export class LocationService {
  private readonly logger = new Logger(LocationService.name);

  constructor(
    private readonly database: OCPIPrismaClient,
    private readonly als: AsyncLocalStorage<CredentialsStore>
  ) {}

  async findAll(paginationParams: PaginationParams): Promise<Page<Location>> {
    const credentials = this.als.getStore();
    this.logger.log({ credentials, paginationParams }, 'finding all locations');

    const where = {
      timezone: {
        in: supportedTimezones,
      },
    };

    return this.database
      .$transaction([
        this.database.location.findMany({
          ...locationIncludes,
          ...buildPaginationArgs({ ...paginationParams, where }),
        }),
        this.database.location.count({ where }),
      ])
      .then((results: [LocationEntity[], number]) =>
        buildPage(this.mapLocations(results[0]), results[1], paginationParams)
      );
  }

  async findAllLocations(
    latitude?: string,
    longitude?: string
  ): Promise<Location[]> {
    const credentials = this.als.getStore();
    this.logger.log(
      { credentials, latitude, longitude },
      'finding all locations without pagination'
    );

    if (latitude && longitude) {
      const targetLat = parseFloat(latitude);
      const targetLon = parseFloat(longitude);
      const maxDistanceMeters = 10000; // 10 km in meters

      // First, get location IDs within the distance using PostGIS
      const nearbyLocationIds = await this.database.$queryRaw<{ id: string }[]>`
        SELECT id
        FROM "ocpi/v221"."Location"
        WHERE timezone = ANY(${supportedTimezones})
          AND ST_DWithin(
            ST_SetSRID(ST_MakePoint(longitude, latitude), 4326)::geography,
            ST_SetSRID(ST_MakePoint(${targetLon}, ${targetLat}), 4326)::geography,
            ${maxDistanceMeters}
          )
      `;

      if (nearbyLocationIds.length === 0) {
        return [];
      }

      // Then fetch the full location data with includes for the nearby locations
      const entities = await this.database.location.findMany({
        ...locationIncludes,
        where: {
          id: {
            in: nearbyLocationIds.map(loc => loc.id),
          },
          timezone: {
            in: supportedTimezones,
          },
        },
      });

      return this.mapLocations(entities);
    } else {
      // No distance filtering, use regular Prisma query
      const where = {
        timezone: {
          in: supportedTimezones,
        },
      };

      const entities = await this.database.location.findMany({
        ...locationIncludes,
        where,
      });

      return this.mapLocations(entities);
    }
  }

  async findAllEvses(
    paginationParams: PaginationParams,
    excludeRemoved = false
  ): Promise<Page<Evse>> {
    const credentials = this.als.getStore();
    this.logger.log({ credentials, paginationParams }, 'finding all evses');

    return this.database
      .$transaction([
        this.database.evse.findMany({
          ...evseIncludes,
          ...buildPaginationArgs({
            ...paginationParams,
            where: excludeRemoved ? { NOT: { status: 'REMOVED' } } : undefined,
          }),
        }),
        this.database.evse.count(),
      ])
      .then((results: [EVSEEntity[], number]) =>
        buildPage(this.mapEvses(results[0]), results[1], paginationParams)
      );
  }

  async findAllConnectors(
    paginationParams: PaginationParams
  ): Promise<Page<Connector>> {
    const credentials = this.als.getStore();
    this.logger.log(
      { credentials, paginationParams },
      'finding all connectors'
    );

    return this.database
      .$transaction([
        this.database.connector.findMany({
          ...connectorIncludes,
          ...buildPaginationArgs({
            ...paginationParams,
          }),
        }),
        this.database.connector.count(),
      ])
      .then((results: [ConnectorEntity[], number]) =>
        buildPage(this.mapConnectors(results[0]), results[1], paginationParams)
      );
  }

  async findLocation(locationId: string): Promise<Location> {
    this.logger.log({ locationId }, 'finding location');

    return this.database.location
      .findUnique({
        ...locationIncludes,
        where: {
          id: locationId,
        },
      })
      .then((entity: LocationEntity | null) => {
        if (!entity) {
          throw new LocationNotFoundException();
        }
        const location = this.mapLocation(entity);
        if (!location) {
          throw new LocationNotFoundException();
        }
        return location;
      });
  }

  async findLocationByEvseId(evseId: string): Promise<Location> {
    this.logger.log({ evseId }, 'finding location');

    const evse = await this.database.evse.findUnique({ where: { evseId } });
    if (!evse) {
      throw new EvseNotFoundException();
    }

    return this.findLocation(evse.locationId);
  }

  async findLocationByPpid(ppid: string): Promise<Location> {
    this.logger.log({ ppid }, 'finding location');

    const evse = await this.database.evse.findFirst({ where: { ppid } });
    if (!evse) {
      throw new EvseNotFoundException();
    }

    return await this.findLocation(evse.locationId);
  }

  async findEvse(locationId: string, evseUid: string): Promise<Evse> {
    this.logger.log({ locationId, evseUid }, 'finding evse');

    return this.findLocation(locationId)
      .then((location) => location.evses)
      .then((evses) => {
        if (!evses) {
          throw new EvseNotFoundException();
        }
        return evses.find((evse) => evse.uid === evseUid);
      })
      .then((evse) => {
        if (!evse) {
          throw new EvseNotFoundException();
        }
        return evse;
      });
  }

  async findEvseWithPpid(
    locationId: string,
    evseUid: string
  ): Promise<Evse & { ppid: string }> {
    this.logger.log({ locationId, evseUid }, 'finding evse with ppid');

    return this.findEvse(locationId, evseUid).then(async (evse) => {
      const { ppid } = await this.database.evse.findUniqueOrThrow({
        select: { ppid: true },
        where: { id: evse.uid },
      });
      return { ...evse, ppid };
    });
  }

  async findEvseByStatus(
    status: $Enums.EVSEStatus,
    statusUpdateSince?: Date
  ): Promise<Evse[]> {
    this.logger.log({ status }, 'finding evses by status');

    return await this.database.evse
      .findMany({
        ...evseIncludes,
        where: {
          status,
          statusLastUpdated: { gte: statusUpdateSince ?? new Date(0) },
        },
      })
      .then((entities: EVSEEntity[]) => this.mapEvses(entities));
  }

  async findConnector(
    locationId: string,
    evseUid: string,
    connectorId: string
  ): Promise<Connector> {
    this.logger.log({ locationId, evseUid, connectorId }, 'finding connector');

    return this.findEvse(locationId, evseUid)
      .then((evse) => evse.connectors)
      .then((connectors) => {
        if (!connectors) {
          throw new ConnectorNotFoundException();
        }
        return connectors.find((connector) => connector.id === connectorId);
      })
      .then((connector) => {
        if (!connector) {
          throw new ConnectorNotFoundException();
        }
        return connector;
      });
  }

  async findConnectorWithSocket(
    locationId: string,
    evseUid: string,
    connectorId: string
  ): Promise<Connector & { socket: string | null }> {
    this.logger.log(
      { locationId, evseUid, connectorId },
      'finding connector with socket'
    );

    return this.findConnector(locationId, evseUid, connectorId).then(
      async (connector) => {
        const { socket } = await this.database.connector.findUniqueOrThrow({
          select: { socket: true },
          where: { id: connector.id },
        });
        return { ...connector, socket };
      }
    );
  }

  async updateEvseStatus(
    locationId: string,
    evseUid: string,
    status: string,
    updatedAt: Date
  ): Promise<Evse> {
    this.logger.log(
      { locationId, evseUid, status, updatedAt },
      'updating evse status'
    );

    const evse = await this.findEvse(locationId, evseUid);

    const evseStatus = Object.values($Enums.EVSEStatus).find(
      (value) => value === status
    ) as $Enums.EVSEStatus;

    const result = await this.database.evse.updateMany({
      data: {
        status: evseStatus,
        statusLastUpdated: updatedAt,
      },
      where: {
        id: evse.uid,
        OR: [
          {
            statusLastUpdated: { lte: updatedAt },
            NOT: { status: { in: ['REMOVED', evseStatus] } },
          },
          {
            statusLastUpdated: new Date(0),
            NOT: { status: 'REMOVED' },
          },
        ],
      },
    });

    this.logger.log(
      { locationId, evseUid, status, updatedAt, result },
      'updated evse status'
    );

    return await this.findEvse(locationId, evseUid);
  }

  async pushLocation(location: Location) {
    const { token } = getClientCredentialsFromLocalStorage(this.als);
    const url = getPushLocationsReceiverFromLocalStorage(this.als);

    const context = { id: location.id, url };
    this.logger.log(context, 'pushing location to emsp');

    try {
      const response = await fetch(`${url}/GB/POD/${location.id}`, {
        body: JSON.stringify(LocationSchema.parse(location)),
        headers: {
          Authorization: `Token ${Buffer.from(token).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': uuid(),
          'x-request-id': uuid(),
        },
        method: 'PUT',
        signal: AbortSignal.timeout(1000),
      });

      if (!response.ok) {
        await logFetchWarn(
          this.logger,
          context,
          response,
          'failed to push location to emsp'
        );
        return;
      }

      this.logger.log(context, 'pushed location to emsp');
    } catch (error) {
      this.logger.error(
        { error, ...context },
        'failed to push location to emsp'
      );
    }
  }

  async pushEvse(locationId: string, evse: Evse, fields?: (keyof Evse)[]) {
    const { token } = getClientCredentialsFromLocalStorage(this.als);
    const url = getPushEvsesReceiverFromLocalStorage(this.als);

    const context = { id: evse.evse_id, uid: evse.uid, url };
    this.logger.log(context, 'pushing evse to emsp');

    try {
      const response = await fetch(`${url}/GB/POD/${locationId}/${evse.uid}`, {
        body: JSON.stringify(
          EvseSchema.parse(evse),
          fields
            ? ['last_updated', ...fields.map((field) => field.toString())]
            : undefined
        ),
        headers: {
          Authorization: `Token ${Buffer.from(token).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': uuid(),
          'x-request-id': uuid(),
        },
        method: fields ? 'PATCH' : 'PUT',
        signal: AbortSignal.timeout(1000),
      });

      if (!response.ok) {
        await logFetchWarn(
          this.logger,
          context,
          response,
          'failed to push evse to emsp'
        );
        return;
      }

      this.logger.log(context, 'pushed evse to emsp');
    } catch (error) {
      this.logger.error({ error, ...context }, 'failed to push evse to emsp');
    }
  }

  private mapLocations(entities: LocationEntity[]): Location[] {
    const locations = entities
      .map((entity: LocationEntity) => this.mapLocation(entity))
      .filter((location): location is Location => location != null);

    return sort(locations).by([{ asc: (location) => location.id }]);
  }

  private mapLocation(entity: LocationEntity): Location | null {
    const location: Location = {
      address: entity.address.slice(0, 45),
      city: entity.city.slice(0, 45),
      coordinates: {
        latitude: entity.latitude.toFixed(7),
        longitude: entity.longitude.toFixed(7),
      },
      country: entity.country,
      country_code: entity.countryCode,
      evses: this.mapEvses(entity.evses),
      id: entity.id,
      last_updated: entity.lastUpdated,
      name: entity.name,
      operator: {
        name: 'Pod Point',
        website: 'https://podenergy.com',
      },
      owner: {
        name: entity.owner,
      },
      party_id: entity.partyId,
      postal_code: entity.postalCode,
      publish: entity.publish,
      time_zone: entity.timezone,
    };

    const parsedLocation = LocationSchema.safeParse(location);

    if (!parsedLocation.success) {
      this.logger.error(
        { id: location.id, issues: parsedLocation.error.issues },
        'failed to parse location'
      );
      return null;
    }

    return parsedLocation.data;
  }

  private mapEvses(entities: EVSEEntity[]): Evse[] {
    const evses = entities
      .map((entity: EVSEEntity) => this.mapEvse(entity))
      .filter((evse): evse is Evse => evse != null);

    return sort(evses).by([{ asc: (evse) => evse.evse_id }]);
  }

  private mapEvse(entity: EVSEEntity): Evse | null {
    const evse = {
      capabilities: entity.capabilities,
      connectors: this.mapConnectors(entity.connectors),
      coordinates: {
        latitude: entity.latitude.toFixed(7),
        longitude: entity.longitude.toFixed(7),
      },
      evse_id: entity.evseId ?? undefined,
      last_updated: entity.lastUpdated,
      parking_restrictions: entity.parkingRestrictions,
      physical_reference:
        entity.physicalReference.length > 16
          ? entity.ppid
          : entity.physicalReference,
      status: entity.status,
      uid: entity.id,
    };

    const parsedEvse = EvseSchema.safeParse(evse);

    if (!parsedEvse.success) {
      this.logger.error(
        { uid: evse.uid, issues: parsedEvse.error.issues },
        'failed to parse evse'
      );
      return null;
    }

    return parsedEvse.data;
  }

  private mapConnectors(entities: ConnectorEntity[]): Connector[] {
    const connectors = entities
      .map((entity: ConnectorEntity) => this.mapConnector(entity))
      .filter((connector): connector is Connector => connector != null);

    return sort(connectors).by([{ asc: (connector) => connector.id }]);
  }

  private mapConnector(entity: ConnectorEntity): Connector | null {
    const connector = {
      format: entity.format,
      id: entity.id,
      last_updated: entity.lastUpdated,
      max_amperage: entity.maxAmperage,
      max_electric_power: entity.maxElectricPower,
      max_voltage: entity.maxVoltage,
      power_type: entity.powerType,
      standard: entity.standard,
      tariff_ids: this.mapConnectorTariffIds(entity),
    };

    const parsedConnector = ConnectorSchema.safeParse(connector);

    if (!parsedConnector.success) {
      this.logger.error(
        { id: connector.id, issues: parsedConnector.error.issues },
        'failed to parse connector'
      );
      return null;
    }

    return parsedConnector.data;
  }

  private mapConnectorTariffIds(entity: ConnectorEntity) {
    return entity.tariffs.length > 0
      ? entity.tariffs.map((tariff) => tariff.id)
      : [FREE_OF_CHARGE_TARIFF_ID];
  }
}
