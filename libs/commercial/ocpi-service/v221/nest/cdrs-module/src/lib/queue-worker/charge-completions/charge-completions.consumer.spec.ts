import * as MockDate from 'mockdate';
import { CdrService } from '../../cdr.service';
import { ChargeCompletionsConsumer } from './charge-completions.consumer';
import { ChargeCompletionsConsumerModule } from './charge-completions.consumer.module';
import { CredentialService } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import {
  EvseNotFoundException,
  SessionNotFoundException,
  Token,
} from '@experience/commercial/ocpi-service/v221/shared';
import { LocationService } from '@experience/commercial/ocpi-service/v221/nest/locations-module';
import { Logger } from '@nestjs/common';
import { Message } from '@aws-sdk/client-sqs';
import { SessionService } from '@experience/commercial/ocpi-service/v221/nest/sessions-module';
import {
  TEST_ACTIVE_SESSION,
  TEST_CDR,
  TEST_CHARGE_COMPLETION_EVENT,
  TEST_COMPLETED_SESSION,
  TEST_CONNECTOR_ID,
  TEST_CREDENTIALS_STORE,
  TEST_CREDENTIALS_STORE_FOR_PUSH_SESSION,
  TEST_EVSE,
  TEST_EVSE_UID,
  TEST_LOCATION,
  TEST_LOCATION_ID,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import { TEST_PROJECTION_CHARGES_RESPONSES } from '@experience/shared/axios/data-platform-api-client/fixtures';
import { Test, TestingModule } from '@nestjs/testing';
import { TokenService } from '@experience/commercial/ocpi-service/v221/nest/tokens-module';
import { mockDeep } from 'jest-mock-extended';
import { setIn } from 'immutable';
import dayjs from 'dayjs';

const message: Message = {
  Body: JSON.stringify(TEST_CHARGE_COMPLETION_EVENT),
};

const testChargeId = '570b2ccf-824d-4076-9da5-000006c29f4a';

const testAuthorisationId = 'd068a913-f9dc-4047-8780-000032b66567';

const projectionsResponse = setIn(
  TEST_PROJECTION_CHARGES_RESPONSES,
  ['data', '0', 'id'],
  testChargeId
);

describe('charge completion consumer', () => {
  let cdrService: CdrService;
  let consumer: ChargeCompletionsConsumer;
  let credentialService: CredentialService;
  let locationService: LocationService;
  let sessionService: SessionService;
  let tokenService: TokenService;
  const logger = mockDeep<Logger>();

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ChargeCompletionsConsumerModule],
    }).compile();

    cdrService = module.get<CdrService>(CdrService);
    consumer = module.get<ChargeCompletionsConsumer>(ChargeCompletionsConsumer);
    credentialService = module.get<CredentialService>(CredentialService);
    locationService = module.get<LocationService>(LocationService);
    sessionService = module.get<SessionService>(SessionService);
    tokenService = module.get<TokenService>(TokenService);
    module.useLogger(logger);

    MockDate.set('2025-02-05');
  });

  afterAll(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(cdrService).toBeDefined();
    expect(consumer).toBeDefined();
    expect(credentialService).toBeDefined();
    expect(locationService).toBeDefined();
    expect(sessionService).toBeDefined();
    expect(tokenService).toBeDefined();
  });

  it('should handle message when session does not exist', async () => {
    const expectedToken: Token = {
      contract_id: 'N/A',
      country_code: 'GB',
      issuer: 'POD',
      last_updated: new Date(),
      party_id: 'POD',
      type: 'APP_USER',
      uid: projectionsResponse.data[0].driverIDs[0],
      valid: true,
      whitelist: 'ALWAYS',
    };
    const mockGetLocation = jest
      .spyOn(locationService, 'findLocationByPpid')
      .mockResolvedValueOnce({
        ...TEST_LOCATION,
        evses: [TEST_EVSE, { ...TEST_EVSE, evse_id: 'GB*POD*E*PG12345E2' }],
      });
    const mockCreateToken = jest
      .spyOn(tokenService, 'createOrUpdateToken')
      .mockResolvedValueOnce();
    const mockFindSession = jest
      .spyOn(sessionService, 'find')
      .mockRejectedValueOnce(new SessionNotFoundException())
      .mockRejectedValueOnce(new SessionNotFoundException())
      .mockRejectedValueOnce(new SessionNotFoundException())
      .mockResolvedValueOnce(TEST_ACTIVE_SESSION);
    const mockStartSession = jest
      .spyOn(sessionService, 'start')
      .mockResolvedValueOnce(TEST_ACTIVE_SESSION);
    const mockStopSession = jest
      .spyOn(sessionService, 'stop')
      .mockResolvedValueOnce(TEST_COMPLETED_SESSION);
    const mockCreateCdr = jest
      .spyOn(cdrService, 'create')
      .mockResolvedValueOnce(TEST_CDR);

    await consumer.handle(message);

    expect(mockGetLocation).toHaveBeenCalledWith('PG-12345');
    expect(mockCreateToken).toHaveBeenCalledWith(
      expectedToken.country_code,
      expectedToken.party_id,
      expectedToken.uid,
      expectedToken.type,
      expectedToken
    );
    expect(mockFindSession).toHaveBeenCalledTimes(4);
    expect(mockFindSession).toHaveBeenNthCalledWith(1, testChargeId); // checking for presence of existing session by charge id
    expect(mockFindSession).toHaveBeenNthCalledWith(2, testAuthorisationId); // checking for presence of existing session by authorisation id
    expect(mockFindSession).toHaveBeenNthCalledWith(3, TEST_ACTIVE_SESSION.id); // waiting for existence of new session
    expect(mockFindSession).toHaveBeenNthCalledWith(4, TEST_ACTIVE_SESSION.id); // waiting for existence of new session
    expect(mockStartSession).toHaveBeenCalledWith(
      {
        connector_id: TEST_CONNECTOR_ID,
        evse_uid: TEST_EVSE_UID,
        location_id: TEST_LOCATION_ID,
        response_url: 'https://roaming.podenergy.com',
        token: expectedToken,
      },
      dayjs('2022-09-27T18:00:00.000Z').toDate(),
      testChargeId
    );
    expect(mockStopSession).toHaveBeenCalledWith(
      {
        response_url: 'https://roaming.podenergy.com',
        session_id: TEST_ACTIVE_SESSION.id,
      },
      dayjs('2022-09-27T18:55:00.000Z').toDate(),
      TEST_PROJECTION_CHARGES_RESPONSES.data[0].energyTotal,
      TEST_PROJECTION_CHARGES_RESPONSES.data[0].revenueGenerated
    );
    expect(mockCreateCdr).toHaveBeenCalledWith(TEST_COMPLETED_SESSION);
    expect(logger.log).toHaveBeenCalledWith(
      { cdr: TEST_CDR },
      'created cdr',
      'ChargeCompletionsConsumer'
    );
  });

  it('should handle message when session is active', async () => {
    const mockGetLocation = jest
      .spyOn(locationService, 'findLocationByPpid')
      .mockResolvedValueOnce({
        ...TEST_LOCATION,
        evses: [TEST_EVSE, { ...TEST_EVSE, evse_id: 'GB*POD*E*PG12345E2' }],
      });
    const mockFindSession = jest
      .spyOn(sessionService, 'find')
      .mockResolvedValueOnce(TEST_ACTIVE_SESSION);
    const mockStopSession = jest
      .spyOn(sessionService, 'stop')
      .mockResolvedValueOnce(TEST_COMPLETED_SESSION);
    const mockCreateCdr = jest
      .spyOn(cdrService, 'create')
      .mockResolvedValueOnce(TEST_CDR);
    const mockCreateStore = jest
      .spyOn(credentialService, 'createStoreFromToken')
      .mockResolvedValueOnce(TEST_CREDENTIALS_STORE);

    await consumer.handle(message);

    expect(mockGetLocation).toHaveBeenCalledWith('PG-12345');
    expect(mockFindSession).toHaveBeenCalledTimes(1);
    expect(mockFindSession).toHaveBeenNthCalledWith(1, testChargeId);
    expect(mockStopSession).toHaveBeenCalledWith(
      {
        response_url: 'https://roaming.podenergy.com',
        session_id: TEST_ACTIVE_SESSION.id,
      },
      dayjs('2022-09-27T18:55:00.000Z').toDate(),
      TEST_PROJECTION_CHARGES_RESPONSES.data[0].energyTotal,
      TEST_PROJECTION_CHARGES_RESPONSES.data[0].revenueGenerated
    );
    expect(mockCreateCdr).toHaveBeenCalledWith(TEST_COMPLETED_SESSION);
    expect(mockCreateStore).toHaveBeenCalledWith(
      TEST_COMPLETED_SESSION.cdr_token
    );
    expect(logger.log).toHaveBeenCalledWith(
      { cdr: TEST_CDR },
      'created cdr',
      'ChargeCompletionsConsumer'
    );
  });

  it('should handle message when session is active and authorised via ocpi', async () => {
    const mockGetLocation = jest
      .spyOn(locationService, 'findLocationByPpid')
      .mockResolvedValueOnce({
        ...TEST_LOCATION,
        evses: [TEST_EVSE, { ...TEST_EVSE, evse_id: 'GB*POD*E*PG12345E2' }],
      });
    const mockFindSession = jest
      .spyOn(sessionService, 'find')
      .mockRejectedValueOnce(new SessionNotFoundException())
      .mockResolvedValueOnce(TEST_ACTIVE_SESSION);
    const mockStopSession = jest
      .spyOn(sessionService, 'stop')
      .mockResolvedValueOnce(TEST_COMPLETED_SESSION);
    const mockCreateCdr = jest
      .spyOn(cdrService, 'create')
      .mockResolvedValueOnce(TEST_CDR);
    const mockCreateStore = jest
      .spyOn(credentialService, 'createStoreFromToken')
      .mockResolvedValueOnce(TEST_CREDENTIALS_STORE);

    await consumer.handle(message);

    expect(mockGetLocation).toHaveBeenCalledWith('PG-12345');
    expect(mockFindSession).toHaveBeenCalledTimes(2);
    expect(mockFindSession).toHaveBeenNthCalledWith(1, testChargeId); // checking for presence of existing session by charge id
    expect(mockFindSession).toHaveBeenNthCalledWith(2, testAuthorisationId); // checking for presence of existing session by authorisation id
    expect(mockStopSession).toHaveBeenCalledWith(
      {
        response_url: 'https://roaming.podenergy.com',
        session_id: TEST_ACTIVE_SESSION.id,
      },
      dayjs('2022-09-27T18:55:00.000Z').toDate(),
      TEST_PROJECTION_CHARGES_RESPONSES.data[0].energyTotal,
      TEST_PROJECTION_CHARGES_RESPONSES.data[0].revenueGenerated
    );
    expect(mockCreateCdr).toHaveBeenCalledWith(TEST_COMPLETED_SESSION);
    expect(mockCreateStore).toHaveBeenCalledWith(
      TEST_COMPLETED_SESSION.cdr_token
    );
    expect(logger.log).toHaveBeenCalledWith(
      { cdr: TEST_CDR },
      'created cdr',
      'ChargeCompletionsConsumer'
    );
  });

  it.each([
    [
      'push session if session is active and there is a push sessions receiver url configured',
      setIn(
        TEST_CREDENTIALS_STORE_FOR_PUSH_SESSION,
        ['pushSessionsReceiver'],
        'https://example.com/push'
      ),
      true,
    ],
    [
      'not push session if session is active and there is no push sessions receiver url configured',
      setIn(TEST_CREDENTIALS_STORE, ['pushSessionsReceiver'], null),
      false,
    ],
  ])('should %s', async (_, credentialsStore, shouldPush) => {
    const completedSession = setIn(
      TEST_COMPLETED_SESSION,
      ['cdr_token', 'party_id'],
      'DCS'
    );

    jest.spyOn(locationService, 'findLocationByPpid').mockResolvedValueOnce({
      ...TEST_LOCATION,
      evses: [TEST_EVSE, { ...TEST_EVSE, evse_id: 'GB*POD*E*PG12345E2' }],
    });
    jest.spyOn(cdrService, 'create').mockResolvedValueOnce(TEST_CDR);
    jest
      .spyOn(sessionService, 'find')
      .mockResolvedValueOnce(
        setIn(TEST_ACTIVE_SESSION, ['cdr_token', 'party_id'], 'DCS')
      );
    jest.spyOn(sessionService, 'stop').mockResolvedValueOnce(completedSession);
    const mockCreateStore = jest
      .spyOn(credentialService, 'createStoreFromToken')
      .mockResolvedValueOnce(credentialsStore);
    const mockPush = jest.spyOn(sessionService, 'push');

    await consumer.handle(message);

    shouldPush
      ? expect(mockPush).toHaveBeenCalledWith(completedSession)
      : expect(mockPush).not.toHaveBeenCalled();

    expect(mockCreateStore).toHaveBeenCalledWith(completedSession.cdr_token);
  });

  it('should handle message when session is completed', async () => {
    const mockGetLocation = jest
      .spyOn(locationService, 'findLocationByPpid')
      .mockResolvedValueOnce({
        ...TEST_LOCATION,
        evses: [TEST_EVSE, { ...TEST_EVSE, evse_id: 'GB*POD*E*PG12345E2' }],
      });
    const mockFindSession = jest
      .spyOn(sessionService, 'find')
      .mockResolvedValueOnce(TEST_COMPLETED_SESSION);
    const mockCreateCdr = jest
      .spyOn(cdrService, 'create')
      .mockResolvedValueOnce(TEST_CDR);

    await consumer.handle(message);

    expect(mockGetLocation).toHaveBeenCalledWith('PG-12345');
    expect(mockFindSession).toHaveBeenCalledTimes(1);
    expect(mockFindSession).toHaveBeenNthCalledWith(1, testChargeId);
    expect(mockCreateCdr).toHaveBeenCalledWith(TEST_COMPLETED_SESSION);
    expect(logger.log).toHaveBeenCalledWith(
      { cdr: TEST_CDR },
      'created cdr',
      'ChargeCompletionsConsumer'
    );
  });

  it('should handle message when session is completed and cdr already exists', async () => {
    const mockGetLocation = jest
      .spyOn(locationService, 'findLocationByPpid')
      .mockResolvedValueOnce({
        ...TEST_LOCATION,
        evses: [TEST_EVSE, { ...TEST_EVSE, evse_id: 'GB*POD*E*PG12345E2' }],
      });
    const mockFindSession = jest
      .spyOn(sessionService, 'find')
      .mockResolvedValueOnce(TEST_COMPLETED_SESSION);
    const mockFindCdr = jest
      .spyOn(cdrService, 'findBySessionId')
      .mockResolvedValueOnce(TEST_CDR);

    await consumer.handle(message);

    expect(mockGetLocation).toHaveBeenCalledWith('PG-12345');
    expect(mockFindSession).toHaveBeenCalledTimes(1);
    expect(mockFindSession).toHaveBeenNthCalledWith(1, testChargeId);
    expect(mockFindCdr).toHaveBeenCalledWith(TEST_COMPLETED_SESSION.id);
    expect(logger.log).toHaveBeenCalledWith(
      { cdr: TEST_CDR },
      'cdr already exists',
      'ChargeCompletionsConsumer'
    );
  });

  it('should log a message if the evse is not found', async () => {
    jest
      .spyOn(locationService, 'findLocationByPpid')
      .mockResolvedValueOnce({ ...TEST_LOCATION, evses: [] });

    await consumer.handle(message);

    expect(logger.log).toHaveBeenCalledWith(
      { parsedEvent: TEST_CHARGE_COMPLETION_EVENT },
      'evse not found',
      'ChargeCompletionsConsumer'
    );
  });

  it('should log a message if the location is not found', async () => {
    const error = new EvseNotFoundException();
    jest
      .spyOn(locationService, 'findLocationByPpid')
      .mockRejectedValueOnce(error);

    await consumer.handle(message);

    expect(logger.log).toHaveBeenCalledWith(
      { parsedEvent: TEST_CHARGE_COMPLETION_EVENT },
      'location not found',
      'ChargeCompletionsConsumer'
    );
  });

  it('should log a message if no authorisation is present', async () => {
    jest
      .spyOn(locationService, 'findLocationByPpid')
      .mockResolvedValueOnce(TEST_LOCATION);

    await consumer.handle({
      Body: JSON.stringify({
        ...TEST_CHARGE_COMPLETION_EVENT,
        detail: {
          ...TEST_CHARGE_COMPLETION_EVENT.detail,
          authorisation: null,
        },
      }),
    });

    expect(logger.log).toHaveBeenCalledWith(
      {
        location: TEST_LOCATION,
        evse: TEST_EVSE,
        charge: TEST_CHARGE_COMPLETION_EVENT.detail.charge,
      },
      'no authorisation found - not creating session',
      'ChargeCompletionsConsumer'
    );
  });
});
