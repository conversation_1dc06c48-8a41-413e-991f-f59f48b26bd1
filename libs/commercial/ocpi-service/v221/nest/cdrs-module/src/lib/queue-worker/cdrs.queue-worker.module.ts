import { ChargeCompletionsConsumerModule } from './charge-completions/charge-completions.consumer.module';
import { ChargeNotificationsConsumerModule } from './charge-notifications/charge-notifications.consumer.module';
import { Module } from '@nestjs/common';
import { PushCdrsConsumerModule } from './push-cdrs/push-cdrs.consumer.module';

@Module({
  imports: [
    ChargeCompletionsConsumerModule,
    ChargeNotificationsConsumerModule,
    PushCdrsConsumerModule,
  ],
})
export class CdrsQueueWorkerModule {}
