import { AsyncLocalStorage } from 'async_hooks';
import {
  Cdr,
  ChargeCompletionEventDetail,
  ChargeCompletionEventSchema,
  CredentialsStore,
  Evse,
  Location,
  Session,
  StartSessionCommand,
  StartSessionCommandSchema,
  StopSessionCommand,
  StopSessionCommandSchema,
  Token,
  parseEvseId,
} from '@experience/commercial/ocpi-service/v221/shared';
import { CdrService } from '../../cdr.service';
import { CredentialService } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { LocationService } from '@experience/commercial/ocpi-service/v221/nest/locations-module';
import { Message } from '@aws-sdk/client-sqs';
import { QueueConsumerService } from '@experience/shared/nest/aws/sqs-module';
import { SessionService } from '@experience/commercial/ocpi-service/v221/nest/sessions-module';
import { TokenService } from '@experience/commercial/ocpi-service/v221/nest/tokens-module';
import { waitForResult } from '@experience/shared/typescript/utils';
import dayjs from 'dayjs';

export abstract class ChargeCompletionsQueueConsumerService extends QueueConsumerService {}

@Injectable()
export class ChargeCompletionsConsumer implements OnApplicationBootstrap {
  private readonly logger = new Logger(ChargeCompletionsConsumer.name);

  constructor(
    private readonly als: AsyncLocalStorage<CredentialsStore>,
    private readonly credentialService: CredentialService,
    private readonly cdrService: CdrService,
    private readonly locationService: LocationService,
    private readonly queueConsumerService: ChargeCompletionsQueueConsumerService,
    private readonly sessionService: SessionService,
    private readonly tokenService: TokenService
  ) {}

  async handle(message: Message): Promise<void> {
    const body = JSON.parse(message.Body as string);
    const parsedEvent = ChargeCompletionEventSchema.parse(body);
    this.logger.log({ parsedEvent }, 'handling charge completion event');

    const { detail } = parsedEvent;
    const { id: ppid, doorId } = detail.chargingStation;

    const location = await this.findLocation(ppid);
    if (!location) {
      this.logger.log({ parsedEvent }, 'location not found');
      return;
    }

    const evse = await this.findEvse(location, ppid, doorId);
    if (!evse) {
      this.logger.log({ parsedEvent }, 'evse not found');
      return;
    }

    const session = await this.createSession(location, evse, detail);

    if (session) {
      await this.createCdr(session);
    }
  }

  private async findLocation(ppid: string): Promise<Location | undefined> {
    return await this.locationService
      .findLocationByPpid(ppid)
      .catch(() => undefined);
  }

  private async findEvse(
    location: Location,
    ppid: string,
    door: string
  ): Promise<Evse | undefined> {
    const evses = location.evses
      ?.filter((evse) => ppid === parseEvseId(evse.evse_id as string).ppid)
      .sort((a, b) => ((a.evse_id as string) > (b.evse_id as string) ? 1 : -1));
    return door === 'A' ? evses?.[0] : evses?.[evses.length - 1];
  }

  private async createToken(authoriserId: string): Promise<Token> {
    this.logger.log({ authoriserId }, 'creating token');

    const token: Token = {
      contract_id: 'N/A',
      country_code: 'GB',
      issuer: 'POD',
      last_updated: new Date(),
      party_id: 'POD',
      type: 'APP_USER' as const,
      uid: authoriserId,
      valid: true,
      whitelist: 'ALWAYS',
    };

    await this.tokenService.createOrUpdateToken(
      token.country_code,
      token.party_id,
      token.uid,
      token.type,
      token
    );

    this.logger.log({ token }, 'created token');

    return token;
  }

  private async createSession(
    location: Location,
    evse: Evse,
    detail: ChargeCompletionEventDetail
  ): Promise<Session | undefined> {
    const { authorisation, charge } = detail;
    this.logger.log(
      { location, evse, charge, authorisation },
      'creating session'
    );

    const existingSession = await this.findExistingSession(detail);

    if (existingSession && existingSession.status === 'COMPLETED') {
      this.logger.log(
        { session: existingSession },
        'session already exists and is completed'
      );
      return existingSession;
    }

    if (existingSession && existingSession.status === 'ACTIVE') {
      this.logger.log(
        { session: existingSession },
        'session already exists and is active'
      );
      return this.handleActiveSession(existingSession, detail);
    }

    if (!authorisation) {
      this.logger.log(
        { location, evse, charge },
        'no authorisation found - not creating session'
      );
      return;
    }

    const token = await this.createToken(authorisation.authoriserId);

    const startedSession = await this.startSession(
      location,
      evse,
      detail,
      token
    );

    const stoppedSession = await this.stopSession(startedSession, detail);

    this.logger.log({ session: stoppedSession }, 'created session');

    return stoppedSession;
  }

  private async startSession(
    location: Location,
    evse: Evse,
    detail: ChargeCompletionEventDetail,
    token: Token
  ): Promise<Session> {
    const startCommand: StartSessionCommand = StartSessionCommandSchema.parse({
      connector_id: evse.connectors[0].id,
      response_url: 'https://roaming.podenergy.com',
      token,
      location_id: location.id,
      evse_uid: evse.uid,
    });

    const { charge } = detail;

    const startedSession = await this.sessionService.start(
      startCommand,
      dayjs(charge.pluggedInAt).toDate(),
      charge.id
    );

    await waitForResult<Session | undefined>({
      request: () => this.findSession(startedSession.id),
    });

    return startedSession;
  }

  private async stopSession(
    session: Session,
    detail: ChargeCompletionEventDetail
  ): Promise<Session> {
    const stopCommand: StopSessionCommand = StopSessionCommandSchema.parse({
      response_url: 'https://roaming.podenergy.com',
      session_id: session.id,
    });

    const { charge, revenue } = detail;

    return await this.sessionService.stop(
      stopCommand,
      dayjs(charge.unpluggedAt).toDate(),
      charge.energyTotal ?? 0,
      revenue?.settlementAmount ?? 0
    );
  }

  private async handleActiveSession(
    activeSession: Session,
    detail: ChargeCompletionEventDetail
  ): Promise<Session> {
    const stoppedSession = await this.stopSession(activeSession, detail);

    const store = await this.credentialService.createStoreFromToken(
      stoppedSession.cdr_token
    );

    if (!store.pushSessionsReceiver) {
      this.logger.log(
        'no push sessions receiver url - skipping pushing session to emsp'
      );
      return stoppedSession;
    }

    await this.als.run(store, async () => {
      await this.sessionService.push(stoppedSession);
    });

    return stoppedSession;
  }

  private async createCdr(session: Session): Promise<Cdr> {
    this.logger.log({ session }, 'creating cdr');

    const existingCdr = await this.findCdr(session.id);

    if (existingCdr) {
      this.logger.log({ cdr: existingCdr }, 'cdr already exists');
      return existingCdr;
    }

    const cdr = await this.cdrService.create(session);

    this.logger.log({ cdr }, 'created cdr');

    return cdr;
  }

  private async findExistingSession(
    detail: ChargeCompletionEventDetail
  ): Promise<Session | undefined> {
    const { id: authorisationId } = detail.authorisation ?? {};
    const { id: chargeId } = detail.charge;

    const session = await this.findSession(chargeId);

    if (session) {
      return session;
    }

    if (authorisationId) {
      return this.findSession(authorisationId);
    }

    return undefined;
  }

  private async findCdr(id: string): Promise<Cdr | undefined> {
    return this.cdrService.findBySessionId(id).catch(() => undefined);
  }

  private async findSession(id: string): Promise<Session | undefined> {
    return this.sessionService.find(id).catch(() => undefined);
  }

  async onApplicationBootstrap() {
    this.queueConsumerService.setMessageHandler(this.handle.bind(this));
    await this.queueConsumerService.start();
  }
}
