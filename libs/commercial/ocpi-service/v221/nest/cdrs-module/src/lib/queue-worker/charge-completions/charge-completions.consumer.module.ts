import { CdrsModule } from '../../cdrs.module';
import {
  ChargeCompletionsConsumer,
  ChargeCompletionsQueueConsumerService,
} from './charge-completions.consumer';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CredentialsModule } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { LocationsModule } from '@experience/commercial/ocpi-service/v221/nest/locations-module';
import { Module } from '@nestjs/common';
import {
  QueueConsumerService,
  SqsConsumerModule,
  SqsConsumerModuleOptions,
} from '@experience/shared/nest/aws/sqs-module';
import { SessionsModule } from '@experience/commercial/ocpi-service/v221/nest/sessions-module';
import { TokensModule } from '@experience/commercial/ocpi-service/v221/nest/tokens-module';

@Module({
  imports: [
    ConfigModule,
    CdrsModule,
    CredentialsModule,
    LocationsModule,
    SessionsModule,
    SqsConsumerModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService): SqsConsumerModuleOptions => ({
        disabled: !config.get<string>('CHARGE_COMPLETIONS_QUEUE_URL'),
        queueUrl: config.get<string>('CHARGE_COMPLETIONS_QUEUE_URL'),
      }),
      inject: [ConfigService],
    }),
    TokensModule,
  ],
  providers: [
    ChargeCompletionsConsumer,
    {
      provide: ChargeCompletionsQueueConsumerService,
      useExisting: QueueConsumerService,
    },
  ],
})
export class ChargeCompletionsConsumerModule {}
