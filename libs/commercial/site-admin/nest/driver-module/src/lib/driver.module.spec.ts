import {
  Driver,
  DriverTariffTier,
  TEST_CREATE_DRIVER_REQUEST,
  TEST_DELETE_DRIVER_REQUEST,
  TEST_UPDATE_DRIVER_REQUEST,
} from '@experience/commercial/site-admin/typescript/domain-model';
import FormData from 'form-data';
import axios, { AxiosResponse } from 'axios';
import fs from 'fs';
import path from 'path';

const dateFormat = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
const createPropertyMatchers = (response: AxiosResponse<Driver[]>) =>
  response.data.map((driver) => ({
    ...driver,
    id: expect.any(Number),
    registeredDate: expect.stringMatching(dateFormat),
  }));

export const describeDriverModule = (baseUrl: string) => {
  describe('driver module', () => {
    describe('driver controller', () => {
      it('should find a list of drivers and hide soft deleted drivers if includeDeleted is false', async () => {
        const response = await axios.get(
          `${baseUrl}/drivers?includeDeleted=false&authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
      });

      it('should find a list of drivers and show soft deleted drivers if includeDeleted is true', async () => {
        const response = await axios.get(
          `${baseUrl}/drivers?includeDeleted=true&authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
      });

      it('should add a new driver', async () => {
        const response = await axios.post(
          `${baseUrl}/drivers?authId=66154039-e231-40f5-8daa-6bb68e6b1890`,
          TEST_CREATE_DRIVER_REQUEST,
          {
            headers: {
              'Content-Type': 'application/json',
              referer: 'https://sites.podenergy.com',
            },
          }
        );

        expect(response.status).toEqual(201);
        expect(response.data).toMatchSnapshot({
          id: expect.any(Number),
          registeredDate: expect.stringMatching(dateFormat),
        });
      });

      it('should update an existing driver', async () => {
        const updateResponse = await axios.put(
          `${baseUrl}/drivers/204?authId=66154039-e231-40f5-8daa-6bb68e6b1890`,
          {
            ...TEST_UPDATE_DRIVER_REQUEST,
            firstName: 'Joe',
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        expect(updateResponse.status).toEqual(204);

        const response = await axios.get(
          `${baseUrl}/drivers?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
      });

      it('should delete an existing driver', async () => {
        const deleteResponse = await axios.delete(
          `${baseUrl}/drivers/204?authId=66154039-e231-40f5-8daa-6bb68e6b1890`,
          {
            data: TEST_DELETE_DRIVER_REQUEST,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        expect(deleteResponse.status).toEqual(204);

        const response = await axios.get(
          `${baseUrl}/drivers?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
      });
    });

    it('should update an existing driver tariff tier', async () => {
      const updateResponse = await axios.put(
        `${baseUrl}/drivers/202?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae`,
        {
          ...TEST_UPDATE_DRIVER_REQUEST,
          tariffTier: DriverTariffTier.MEMBER,
          originalTariffTier: DriverTariffTier.DRIVER,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      expect(updateResponse.status).toEqual(204);

      const response = await axios.get(
        `${baseUrl}/drivers?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae`
      );
      expect(response.status).toEqual(200);
      expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
    });

    describe('bulk upload', () => {
      it.each([['more-drivers.csv'], ['even-more-drivers.csv']])(
        'should upload a csv of drivers (%s)',
        async (file) => {
          const validCsv = fs.createReadStream(
            path.resolve(`${__dirname}/../test/fixtures/${file}`)
          );

          const formData = new FormData();
          formData.append('file', validCsv, {
            header: { 'Content-Type': 'text/csv' },
          });

          const uploadResponse = await axios.post(
            `${baseUrl}/drivers/upload?authId=66154039-e231-40f5-8daa-6bb68e6b1890`,
            formData
          );
          expect(uploadResponse.status).toEqual(201);

          const response = await axios.get(
            `${baseUrl}/drivers?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
          );
          expect(response.status).toEqual(200);
          expect(response.data).toMatchSnapshot(
            createPropertyMatchers(response)
          );
        }
      );

      it('should error if the csv contains errors (%s)', async () => {
        const validCsvWithErrors = fs.createReadStream(
          path.resolve(`${__dirname}/../test/fixtures/drivers-with-errors.csv`)
        );

        const formData = new FormData();
        formData.append('file', validCsvWithErrors, {
          header: { 'Content-Type': 'text/csv' },
        });

        await expect(() =>
          axios.post(
            `${baseUrl}/drivers/upload?authId=66154039-e231-40f5-8daa-6bb68e6b1890`,
            formData
          )
        ).rejects.toThrow('Request failed with status code 400');

        const response = await axios.get(
          `${baseUrl}/drivers?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
      });

      it('should error if the csv contains duplicates (%s)', async () => {
        const duplicateDriverCsv = fs.createReadStream(
          path.resolve(`${__dirname}/../test/fixtures/duplicate-drivers.csv`)
        );

        const formData = new FormData();
        formData.append('file', duplicateDriverCsv, {
          header: { 'Content-Type': 'text/csv' },
        });

        await expect(() =>
          axios.post(
            `${baseUrl}/drivers/upload?authId=66154039-e231-40f5-8daa-6bb68e6b1890`,
            formData
          )
        ).rejects.toThrow('Request failed with status code 409');

        const response = await axios.get(
          `${baseUrl}/drivers?authId=66154039-e231-40f5-8daa-6bb68e6b1890`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
      });
    });

    describe.each([true, false])(
      'driver charge controller with projections feature flag %s',
      (useChargeProjectionEndpoints) => {
        const featureFlagsHeader = useChargeProjectionEndpoints
          ? 'useChargeProjectionEndpoints'
          : '';

        it('should get a driver with a list of charges', async () => {
          const response = await axios.get(
            `${baseUrl}/drivers/201/charges?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae&tariffTier=Driver`,
            { headers: { 'x-feature-flags': featureFlagsHeader } }
          );
          expect(response.status).toEqual(200);
          expect(response.data).toMatchSnapshot();
        });

        it('should get a soft deleted driver with a list of charges', async () => {
          await axios.delete(
            `${baseUrl}/drivers/203?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae`,
            {
              data: TEST_DELETE_DRIVER_REQUEST,
              headers: {
                'Content-Type': 'application/json',
                'x-feature-flags': featureFlagsHeader,
              },
            }
          );

          const response = await axios.get(
            `${baseUrl}/drivers/203/charges?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae&tariffTier=Driver`
          );
          expect(response.status).toEqual(200);
          expect(response.data).toMatchSnapshot();
        });

        it('should generate a csv of charges for a driver', async () => {
          const response = await axios.get(
            `${baseUrl}/drivers/201/charges/csv?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae&tariffTier=Driver`,
            { headers: { 'x-feature-flags': featureFlagsHeader } }
          );
          expect(response.status).toEqual(200);
          expect(response.data).toContain(
            'Charger name,Started at,Ended at,Energy delivered (kWh),Revenue,Total duration,Charging duration,Energy cost,CO2 avoided (kg),Confirmed'
          );
          expect(response.data).toContain(
            'Nick-Gary,2022-09-05T14:58:33Z,2022-09-05T17:58:33Z,98.76,457600,03:00:00,03:00:00,324550,4325.620,Yes'
          );
          expect(response.data).toContain('\uFEFF');
        });
      }
    );
  });
};
