import {
  COMMON_EMAIL_MAX_LENGTH,
  COMMON_EMAIL_MAX_LENGTH_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
  COMMON_NUMERIC_STRING_ERROR,
  COMMON_REQUIRED_ERROR,
} from '@experience/shared/typescript/validation';
import {
  DRIVER_FIRST_NAME_MAX_LENGTH,
  DRIVER_FIRST_NAME_MAX_LENGTH_ERROR,
  DRIVER_INVALID_BULK_UPLOAD_ERROR,
  DRIVER_INVALID_TARIFF_TIER_ERROR,
  DRIVER_LAST_NAME_MAX_LENGTH,
  DRIVER_LAST_NAME_MAX_LENGTH_ERROR,
  DUPLICATE_DRIVER_ERROR,
  DriverErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import {
  DriverCanExpense,
  TEST_ADMINISTRATOR,
  TEST_CREATE_DRIVER_REQUEST,
  TEST_CREATE_MEMBER_REQUEST,
  TEST_DELETE_DRIVER_REQUEST,
  TEST_DRIVER,
  TEST_GROUP,
  TEST_INVITE_DRIVER_REQUEST,
  TEST_MEMBER,
  TEST_UPDATE_DRIVER_REQUEST,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { DriverController } from './driver.controller';
import { DriverService } from './driver.service';
import { DuplicateDriverException } from './driver.exception';
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { TransactionInterceptor } from '@experience/shared/sequelize/podadmin';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import request from 'supertest';

jest.mock('./driver.service');
jest.mock('@experience/shared/sequelize/podadmin');

describe('DriverController', () => {
  let app: INestApplication;
  let driverService: DriverService;
  let transactionInterceptor: TransactionInterceptor;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DriverController],
      providers: [
        { provide: DriverService, useClass: DriverService },
        { provide: TransactionInterceptor, useClass: TransactionInterceptor },
      ],
    }).compile();

    driverService = module.get<DriverService>(DriverService);
    transactionInterceptor = module.get<TransactionInterceptor>(
      TransactionInterceptor
    );

    jest
      .spyOn(transactionInterceptor, 'intercept')
      .mockImplementation(async (context, next) => next.handle());

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Create', () => {
    it('should create by group id', async () => {
      const mockCreateByGroupId = jest
        .spyOn(driverService, 'createByGroupId')
        .mockResolvedValueOnce(TEST_DRIVER);
      const mockInviteByGroupIdAndDriverId = jest
        .spyOn(driverService, 'inviteByGroupIdAndDriverId')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .post(
          `/drivers?groupId=${TEST_GROUP.id}&adminId=${TEST_ADMINISTRATOR.id}`
        )
        .set('Referer', 'https://sites.podenergy.com')
        .send(TEST_CREATE_DRIVER_REQUEST);

      expect(mockCreateByGroupId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_CREATE_DRIVER_REQUEST
      );
      expect(mockInviteByGroupIdAndDriverId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_DRIVER.id,
        TEST_ADMINISTRATOR.id,
        TEST_CREATE_DRIVER_REQUEST.tariffTier,
        new URL('https://sites.podenergy.com')
      );
      expect(response.status).toEqual(201);
      expect(response.body).toEqual(TEST_DRIVER);
    });

    it('should throw a duplicate driver exception if the driver already exists', async () => {
      const mockCreateByGroupId = jest
        .spyOn(driverService, 'createByGroupId')
        .mockRejectedValueOnce(new DuplicateDriverException());
      const mockInviteByGroupIdAndDriverId = jest
        .spyOn(driverService, 'inviteByGroupIdAndDriverId')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .post(
          `/drivers?groupId=${TEST_GROUP.id}&adminId=${TEST_ADMINISTRATOR.id}`
        )
        .set('Referer', 'https://sites.podenergy.com')
        .send(TEST_CREATE_DRIVER_REQUEST);

      expect(mockCreateByGroupId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_CREATE_DRIVER_REQUEST
      );
      expect(mockInviteByGroupIdAndDriverId).not.toHaveBeenCalled();
      expect(response.status).toEqual(409);
      expect(response.body).toEqual({
        error: DriverErrorCodes.DUPLICATE_DRIVER_ERROR,
        message: DUPLICATE_DRIVER_ERROR,
        statusCode: 409,
      });
    });

    it('should bulk create by group id', async () => {
      const mockCreateByGroupId = jest
        .spyOn(driverService, 'createByGroupId')
        .mockResolvedValueOnce(TEST_DRIVER)
        .mockResolvedValueOnce(TEST_MEMBER);
      const mockInviteByGroupIdAndDriverId = jest
        .spyOn(driverService, 'inviteByGroupIdAndDriverId')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .post(
          `/drivers/upload?groupId=${TEST_GROUP.id}&adminId=${TEST_ADMINISTRATOR.id}`
        )
        .attach('file', `${__dirname}/../test/fixtures/drivers.csv`);

      expect(mockCreateByGroupId).toHaveBeenCalledWith(TEST_GROUP.id, {
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: DriverCanExpense.YES,
      });
      expect(mockCreateByGroupId).toHaveBeenCalledWith(TEST_GROUP.id, {
        ...TEST_CREATE_MEMBER_REQUEST,
        canExpense: DriverCanExpense.NO,
      });
      expect(mockInviteByGroupIdAndDriverId).not.toHaveBeenCalled();
      expect(response.status).toEqual(201);
      expect(response.body).toEqual([TEST_DRIVER, TEST_MEMBER]);
    });

    it('should throw a validation error when file type is wrong when bulk creating by group id', async () => {
      const mockCreateByGroupId = jest.spyOn(driverService, 'createByGroupId');

      const response = await request(app.getHttpServer())
        .post(
          `/drivers/upload?groupId=${TEST_GROUP.id}&adminId=${TEST_ADMINISTRATOR.id}`
        )
        .attach('file', `${__dirname}/../test/fixtures/drivers.txt`);

      expect(mockCreateByGroupId).not.toHaveBeenCalled();
      expect(response.status).toEqual(400);
      expect(response.body).toEqual({
        error: 'Bad Request',
        message:
          'Validation failed (current file type is text/plain, expected type is csv)',
        statusCode: 400,
      });
    });

    it.each([
      [
        'invalid-drivers-first-name.csv',
        `${DRIVER_INVALID_BULK_UPLOAD_ERROR}\n\n,Bloggs,<EMAIL>,Driver,Yes`,
      ],
      [
        'invalid-drivers-last-name.csv',
        `${DRIVER_INVALID_BULK_UPLOAD_ERROR}\n\nJane,,<EMAIL>,Driver,Yes`,
      ],
      [
        'invalid-drivers-email.csv',
        `${DRIVER_INVALID_BULK_UPLOAD_ERROR}\n\nJane,Bloggs,,Driver,Yes`,
      ],
      [
        'invalid-drivers-tariff-tier.csv',
        `${DRIVER_INVALID_BULK_UPLOAD_ERROR}\n\nJane,Bloggs,<EMAIL>,Public,Yes`,
      ],
      [
        'invalid-drivers-can-expense.csv',
        `${DRIVER_INVALID_BULK_UPLOAD_ERROR}\n\nJane,Bloggs,<EMAIL>,Driver,1`,
      ],
      [
        'invalid-drivers-multiple-errors.csv',
        `${DRIVER_INVALID_BULK_UPLOAD_ERROR}\n\n,Bloggs,<EMAIL>,Driver,Yes\nJane,,<EMAIL>,Driver,Yes`,
      ],
      [
        'invalid-drivers-missing-column.csv',
        `${DRIVER_INVALID_BULK_UPLOAD_ERROR}`,
      ],
      [
        'invalid-drivers-extra-column.csv',
        `${DRIVER_INVALID_BULK_UPLOAD_ERROR}`,
      ],
      ['invalid-drivers-empty.csv', `${DRIVER_INVALID_BULK_UPLOAD_ERROR}`],
    ])(
      'should throw a validation error when file contents are invalid when bulk creating by group id (%s)',
      async (file, expectedMessage) => {
        const mockCreateByGroupId = jest.spyOn(
          driverService,
          'createByGroupId'
        );

        const response = await request(app.getHttpServer())
          .post(
            `/drivers/upload?groupId=${TEST_GROUP.id}&adminId=${TEST_ADMINISTRATOR.id}`
          )
          .attach('file', `${__dirname}/../test/fixtures/${file}`);

        expect(mockCreateByGroupId).not.toHaveBeenCalled();
        expect(response.status).toEqual(400);
        expect(response.body).toEqual({
          error: 'Bad Request',
          message: expectedMessage,
          statusCode: 400,
        });
      }
    );

    it('should throw a validation error when there are duplicate drivers when bulk creating by group id', async () => {
      jest
        .spyOn(driverService, 'createByGroupId')
        .mockRejectedValueOnce(new DuplicateDriverException());

      const response = await request(app.getHttpServer())
        .post(
          `/drivers/upload?groupId=${TEST_GROUP.id}&adminId=${TEST_ADMINISTRATOR.id}`
        )
        .attach('file', `${__dirname}/../test/fixtures/drivers.csv`);

      expect(response.status).toEqual(409);
      expect(response.body).toEqual({
        error: DriverErrorCodes.DUPLICATE_DRIVER_ERROR,
        message: DUPLICATE_DRIVER_ERROR,
        statusCode: 409,
      });
    });

    it.each([null, undefined, 'one'])(
      'should throw a validation error when group id is %s when creating by group id',
      (groupId) =>
        request(app.getHttpServer())
          .post(`/drivers?groupId=${groupId}`)
          .set('Referer', 'https://sites.podenergy.com')
          .send(TEST_CREATE_DRIVER_REQUEST)
          .expect(400)
          .expect({
            statusCode: 400,
            message: COMMON_NUMERIC_STRING_ERROR,
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, 'one'])(
      'should throw a validation error when admin id is %s when creating by group id',
      (adminId) =>
        request(app.getHttpServer())
          .post(`/drivers?groupId=${TEST_GROUP.id}&adminId=${adminId}`)
          .set('Referer', 'https://sites.podenergy.com')
          .send(TEST_CREATE_DRIVER_REQUEST)
          .expect(400)
          .expect({
            statusCode: 400,
            message: COMMON_NUMERIC_STRING_ERROR,
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, '', ' '])(
      'should throw a validation error when first name is %s when creating by group id',
      (firstName) =>
        request(app.getHttpServer())
          .post(`/drivers?groupId=${TEST_GROUP.id}`)
          .set('Referer', 'https://sites.podenergy.com')
          .send({ ...TEST_CREATE_DRIVER_REQUEST, firstName })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, '', ' '])(
      'should throw a validation error when last name is %s when creating by group id',
      (lastName) =>
        request(app.getHttpServer())
          .post(`/drivers?groupId=${TEST_GROUP.id}`)
          .set('Referer', 'https://sites.podenergy.com')
          .send({ ...TEST_CREATE_DRIVER_REQUEST, lastName })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, '', ' '])(
      'should throw a validation error when email is %s when creating by group id',
      (email) =>
        request(app.getHttpServer())
          .post(`/drivers?groupId=${TEST_GROUP.id}`)
          .set('Referer', 'https://sites.podenergy.com')
          .send({ ...TEST_CREATE_DRIVER_REQUEST, email })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it('should throw a validation error on create when first name is longer than the first name field max length', () =>
      request(app.getHttpServer())
        .post(`/drivers?groupId=${TEST_GROUP.id}`)
        .set('Referer', 'https://sites.podenergy.com')
        .send({
          ...TEST_CREATE_DRIVER_REQUEST,
          firstName: 't'.repeat(DRIVER_FIRST_NAME_MAX_LENGTH + 1),
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_FIRST_NAME_MAX_LENGTH_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on create when last name is longer than the last name field max length', () =>
      request(app.getHttpServer())
        .post(`/drivers?groupId=${TEST_GROUP.id}`)
        .set('Referer', 'https://sites.podenergy.com')
        .send({
          ...TEST_CREATE_DRIVER_REQUEST,
          lastName: 't'.repeat(DRIVER_LAST_NAME_MAX_LENGTH + 1),
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_LAST_NAME_MAX_LENGTH_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on create when email is longer than the email field max length', () =>
      request(app.getHttpServer())
        .post(`/drivers?groupId=${TEST_GROUP.id}`)
        .set('Referer', 'https://sites.podenergy.com')
        .send({
          ...TEST_CREATE_DRIVER_REQUEST,
          email: 't'.repeat(COMMON_EMAIL_MAX_LENGTH + 1),
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_EMAIL_MAX_LENGTH_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on create when the email address is not valid', () =>
      request(app.getHttpServer())
        .post(`/drivers?groupId=${TEST_GROUP.id}`)
        .set('Referer', 'https://sites.podenergy.com')
        .send({
          ...TEST_CREATE_DRIVER_REQUEST,
          email: 'hello',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_INVALID_EMAIL_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on create when the tariff tier is not specified', () =>
      request(app.getHttpServer())
        .post(`/drivers?groupId=${TEST_GROUP.id}`)
        .set('Referer', 'https://sites.podenergy.com')
        .send({
          ...TEST_CREATE_DRIVER_REQUEST,
          tariffTier: undefined,
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on create when the tariff tier is not valid', () =>
      request(app.getHttpServer())
        .post(`/drivers?groupId=${TEST_GROUP.id}`)
        .set('Referer', 'https://sites.podenergy.com')
        .send({
          ...TEST_CREATE_DRIVER_REQUEST,
          tariffTier: 'invalidTier',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));
  });

  describe('Read', () => {
    it('should find by group id', async () => {
      jest
        .spyOn(driverService, 'findByGroupId')
        .mockResolvedValueOnce([TEST_DRIVER]);

      await request(app.getHttpServer())
        .get(`/drivers?groupId=${TEST_GROUP.id}`)
        .expect(200)
        .expect([TEST_DRIVER]);

      expect(driverService.findByGroupId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        false
      );
    });

    it.each([true, false])(
      'should find by group id with include deleted flag %s',
      async (includeDeleted) => {
        jest
          .spyOn(driverService, 'findByGroupId')
          .mockResolvedValueOnce([TEST_DRIVER]);

        await request(app.getHttpServer())
          .get(
            `/drivers?groupId=${TEST_GROUP.id}&includeDeleted=${includeDeleted}`
          )
          .expect(200)
          .expect([TEST_DRIVER]);

        expect(driverService.findByGroupId).toHaveBeenCalledWith(
          TEST_GROUP.id,
          includeDeleted
        );
      }
    );

    it('should return an empty array if there are no drivers', () => {
      jest.spyOn(driverService, 'findByGroupId').mockResolvedValueOnce([]);

      return request(app.getHttpServer())
        .get(`/drivers?groupId=${TEST_GROUP.id}`)
        .expect(200)
        .expect([]);
    });

    it.each([null, undefined, 'one'])(
      'should throw a validation error when group id is %s when getting by group id',
      (groupId) =>
        request(app.getHttpServer())
          .get(`/drivers?groupId=${groupId}`)
          .expect(400)
          .expect({
            statusCode: 400,
            message: COMMON_NUMERIC_STRING_ERROR,
            error: 'Bad Request',
          })
    );
  });

  describe('Update', () => {
    it('update by group id', async () => {
      const mockCreateByGroupId = jest.spyOn(
        driverService,
        'updateByGroupIdAndDriverId'
      );

      const response = await request(app.getHttpServer())
        .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send(TEST_UPDATE_DRIVER_REQUEST);

      expect(mockCreateByGroupId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_DRIVER.id,
        TEST_UPDATE_DRIVER_REQUEST
      );
      expect(response.status).toEqual(204);
    });

    it.each([null, undefined, 'one'])(
      'should throw a validation error when group id is %s when creating by group id',
      (groupId) =>
        request(app.getHttpServer())
          .put(`/drivers/${TEST_DRIVER.id}?groupId=${groupId}`)
          .send(TEST_UPDATE_DRIVER_REQUEST)
          .expect(400)
          .expect({
            statusCode: 400,
            message: COMMON_NUMERIC_STRING_ERROR,
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, '', ' '])(
      'should throw a validation error when first name is %s when updating by group id',
      (firstName) =>
        request(app.getHttpServer())
          .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_UPDATE_DRIVER_REQUEST, firstName })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, '', ' '])(
      'should throw a validation error when last name is %s when updating by group id',
      (lastName) =>
        request(app.getHttpServer())
          .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_UPDATE_DRIVER_REQUEST, lastName })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, '', ' '])(
      'should throw a validation error when email is %s when updating by group id',
      (email) =>
        request(app.getHttpServer())
          .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_UPDATE_DRIVER_REQUEST, email })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it('should throw a validation error on update when first name is longer than the first name field max length', () =>
      request(app.getHttpServer())
        .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_UPDATE_DRIVER_REQUEST,
          firstName: 't'.repeat(DRIVER_FIRST_NAME_MAX_LENGTH + 1),
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_FIRST_NAME_MAX_LENGTH_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on update when last name is longer than the last name field max length', () =>
      request(app.getHttpServer())
        .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_UPDATE_DRIVER_REQUEST,
          lastName: 't'.repeat(DRIVER_LAST_NAME_MAX_LENGTH + 1),
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_LAST_NAME_MAX_LENGTH_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on update when email is longer than the email field max length', () =>
      request(app.getHttpServer())
        .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_UPDATE_DRIVER_REQUEST,
          email: 't'.repeat(COMMON_EMAIL_MAX_LENGTH + 1),
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_EMAIL_MAX_LENGTH_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on update when the email address is not valid', () =>
      request(app.getHttpServer())
        .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_UPDATE_DRIVER_REQUEST,
          email: 'hello',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_INVALID_EMAIL_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on update when the tariff tier is not specified', () =>
      request(app.getHttpServer())
        .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_UPDATE_DRIVER_REQUEST,
          tariffTier: undefined,
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on update when the tariff tier is not valid', () =>
      request(app.getHttpServer())
        .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_UPDATE_DRIVER_REQUEST,
          tariffTier: 'invalidTier',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on update when the original tariff tier is not valid', () =>
      request(app.getHttpServer())
        .put(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_UPDATE_DRIVER_REQUEST,
          originalTariffTier: 'invalidTier',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));
  });

  describe('Delete', () => {
    it('should delete by group id', async () => {
      const mockDeleteByGroupId = jest.spyOn(
        driverService,
        'deleteByGroupIdAndDriverId'
      );

      const response = await request(app.getHttpServer())
        .delete(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send(TEST_DELETE_DRIVER_REQUEST);

      expect(mockDeleteByGroupId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_DRIVER.id,
        TEST_DELETE_DRIVER_REQUEST
      );
      expect(response.status).toEqual(204);
    });

    it('should throw a validation error on delete when the tariff tier is not specified', () =>
      request(app.getHttpServer())
        .delete(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_DELETE_DRIVER_REQUEST,
          tariffTier: undefined,
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on delete when the tariff tier is not valid', () =>
      request(app.getHttpServer())
        .delete(`/drivers/${TEST_DRIVER.id}?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_DELETE_DRIVER_REQUEST,
          tariffTier: 'invalidTier',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));
  });

  describe('Invite', () => {
    it('should invite by group id', async () => {
      const mockInviteByGroupId = jest.spyOn(
        driverService,
        'inviteByGroupIdAndDriverId'
      );

      const response = await request(app.getHttpServer())
        .post(
          `/drivers/${TEST_DRIVER.id}/invitation?groupId=${TEST_GROUP.id}&adminId=${TEST_ADMINISTRATOR.id}`
        )
        .set('Referer', 'https://sites.podenergy.com')
        .send(TEST_INVITE_DRIVER_REQUEST);

      expect(mockInviteByGroupId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_MEMBER.id,
        TEST_ADMINISTRATOR.id,
        TEST_MEMBER.tariffTier,
        new URL('https://sites.podenergy.com')
      );
      expect(response.status).toEqual(204);
    });

    it('should throw a validation error on invite when the tariff tier is not specified', () =>
      request(app.getHttpServer())
        .post(`/drivers/${TEST_DRIVER.id}/invitation?groupId=${TEST_GROUP.id}`)
        .set('Referer', 'https://sites.podenergy.com')
        .send({
          ...TEST_INVITE_DRIVER_REQUEST,
          tariffTier: undefined,
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));

    it('should throw a validation error on invite when the tariff tier is not valid', () =>
      request(app.getHttpServer())
        .post(`/drivers/${TEST_DRIVER.id}/invitation?groupId=${TEST_GROUP.id}`)
        .set('Referer', 'https://sites.podenergy.com')
        .send({
          ...TEST_INVITE_DRIVER_REQUEST,
          tariffTier: 'invalidTier',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DRIVER_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));
  });
});
