// eslint-disable-next-line @nx/enforce-module-boundaries
import { AdminService } from '@experience/commercial/site-admin/nest/admin-module';
import {
  Driver,
  DriverStatus,
  DriverTariffTier,
  TEST_ADMINISTRATOR,
  TEST_CREATE_DRIVER_REQUEST,
  TEST_DELETED_DRIVER,
  TEST_DELETE_DRIVER_REQUEST,
  TEST_DOMAIN,
  TEST_DRIVER,
  TEST_DRIVER_WITH_GROUP,
  TEST_GROUP,
  TEST_MEMBER,
  TEST_MEMBER_WITH_GROUP,
  TEST_UPDATE_DRIVER_REQUEST,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  DriverNotFoundException,
  DuplicateDriverException,
} from './driver.exception';
import {
  DriverService,
  EmailParameters,
  includeOptions,
} from './driver.service';
import {
  EvDrivers,
  Members,
  TEST_DELETED_EV_DRIVERS_ENTITY,
  TEST_DELETED_MEMBERS_ENTITY,
  TEST_EV_DRIVERS_ENTITY,
  TEST_EV_DRIVERS_ENTITY_WITH_GROUP,
  TEST_GROUP_ENTITY,
  TEST_MEMBERS_ENTITY,
  TEST_MEMBERS_ENTITY_WITH_GROUP,
  TEST_USER_ENTITY,
  Users,
} from '@experience/shared/sequelize/podadmin';
import { Op } from 'sequelize';
import { SimpleEmailService } from '@experience/shared/nest/aws/ses-module';
import { Test, TestingModule } from '@nestjs/testing';
import { duplicateDriverTestCases } from '../test/utils';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';
import dayjs from 'dayjs';
import fs from 'fs';
import path from 'path';

jest.mock('@experience/shared/nest/aws/ses-module');
jest.mock('@experience/commercial/site-admin/nest/admin-module');

const nonNetworkHtmlEmailPath = path.resolve(
  './assets/site-admin-api/email-templates/en/invitation-non-network-user.html'
);
const nonNetworkTxtEmailPath = path.resolve(
  './assets/site-admin-api/email-templates/en/invitation-non-network-user.txt'
);
const networkHtmlEmailPath = path.resolve(
  './assets/site-admin-api/email-templates/en/invitation-network-user.html'
);
const networkTxtEmailPath = path.resolve(
  './assets/site-admin-api/email-templates/en/invitation-network-user.txt'
);

const nonNetworkHtmlEmail = fs.readFileSync(nonNetworkHtmlEmailPath).toString();
const nonNetworkPlaintextEmail = fs
  .readFileSync(nonNetworkTxtEmailPath)
  .toString();
const networkHtmlEmail = fs.readFileSync(networkHtmlEmailPath).toString();
const networkPlaintextEmail = fs.readFileSync(networkTxtEmailPath).toString();

const adminName = `${TEST_ADMINISTRATOR.firstName} ${TEST_ADMINISTRATOR.lastName}`;

const emailParams: EmailParameters = {
  adminName,
  emailAddress: TEST_DRIVER.email,
  firstName: TEST_DRIVER.firstName,
  groupName: TEST_GROUP_ENTITY.name,
  sender: '<EMAIL>',
  subject:
    'Joe Bloggs invites you to charge your electric vehicle with Registers of Scotland',
  year: dayjs().year().toString(),
};

describe('DriverService', () => {
  let service: DriverService;
  let simpleEmailService: SimpleEmailService;
  let adminService: AdminService;
  let driversRepository: typeof EvDrivers;
  let membersRepository: typeof Members;
  let usersRepository: typeof Users;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DriverService,
        { provide: SimpleEmailService, useClass: SimpleEmailService },
        { provide: AdminService, useClass: AdminService },
        { provide: 'EV_DRIVERS_REPOSITORY', useValue: EvDrivers },
        { provide: 'MEMBERS_REPOSITORY', useValue: Members },
        { provide: 'USERS_REPOSITORY', useValue: Users },
      ],
    }).compile();

    service = module.get<DriverService>(DriverService);
    simpleEmailService = module.get<SimpleEmailService>(SimpleEmailService);
    adminService = module.get<AdminService>(AdminService);
    driversRepository = module.get<typeof EvDrivers>('EV_DRIVERS_REPOSITORY');
    membersRepository = module.get<typeof EvDrivers>('MEMBERS_REPOSITORY');
    usersRepository = module.get<typeof Users>('USERS_REPOSITORY');
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(simpleEmailService).toBeDefined();
    expect(adminService).toBeDefined();
    expect(driversRepository).toBeDefined();
    expect(membersRepository).toBeDefined();
    expect(usersRepository).toBeDefined();
  });

  it.each([true, false])(
    'should find by group id - includeDeleted is %s',
    async (includeDeleted) => {
      const mockFindAllDrivers = jest
        .spyOn(driversRepository, 'findAll')
        .mockResolvedValueOnce([TEST_EV_DRIVERS_ENTITY]);
      const mockFindAllMembers = jest
        .spyOn(membersRepository, 'findAll')
        .mockResolvedValueOnce([TEST_MEMBERS_ENTITY]);
      const mockFindAllUsers = jest
        .spyOn(usersRepository, 'findAll')
        .mockResolvedValueOnce([
          { email: TEST_EV_DRIVERS_ENTITY.email } as Users,
        ]);
      const mockSuppressedDestinations = jest
        .spyOn(simpleEmailService, 'listSuppressedDestinations')
        .mockResolvedValueOnce([]);

      const drivers = await service.findByGroupId(
        TEST_GROUP.id,
        includeDeleted
      );

      expect(mockFindAllDrivers).toHaveBeenCalledWith({
        include: includeOptions,
        where: { groupId: TEST_GROUP.id },
        paranoid: !includeDeleted,
      });
      expect(mockFindAllMembers).toHaveBeenCalledWith({
        include: includeOptions,
        where: { groupId: TEST_GROUP.id },
        paranoid: !includeDeleted,
      });
      expect(mockFindAllUsers).toHaveBeenCalledWith({
        paranoid: false,
        where: {
          email: [TEST_EV_DRIVERS_ENTITY.email, TEST_MEMBERS_ENTITY.email],
        },
      });
      expect(mockSuppressedDestinations).toHaveBeenCalledWith('BOUNCE');
      expect(drivers).toEqual([TEST_MEMBER, TEST_DRIVER]);
    }
  );

  it.each(duplicateDriverTestCases)(
    'should find by group id - %s',
    async (description, { drivers, members }, users, expected) => {
      jest.spyOn(driversRepository, 'findAll').mockResolvedValueOnce(drivers);
      jest.spyOn(membersRepository, 'findAll').mockResolvedValueOnce(members);
      jest.spyOn(usersRepository, 'findAll').mockResolvedValueOnce(users);
      jest
        .spyOn(simpleEmailService, 'listSuppressedDestinations')
        .mockResolvedValueOnce([]);

      const result = await service.findByGroupId(TEST_GROUP.id, true);

      expect(result).toEqual(expected);
    }
  );

  it.each([
    [
      DriverTariffTier.DRIVER,
      TEST_DRIVER,
      { email: TEST_EV_DRIVERS_ENTITY.email } as Users,
      TEST_EV_DRIVERS_ENTITY,
    ],
    [DriverTariffTier.MEMBER, TEST_MEMBER, null, TEST_MEMBERS_ENTITY],
  ])(
    'should find a %s by group id and driver id',
    async (tariffTier, driverType, user, entity) => {
      jest
        .spyOn(driversRepository, 'findOne')
        .mockResolvedValueOnce(entity as EvDrivers);
      jest
        .spyOn(membersRepository, 'findOne')
        .mockResolvedValueOnce(entity as Members);
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(user);

      const driver = await service.findByGroupIdAndDriverId(1, 1, tariffTier);

      expect(driver).toEqual(driverType);
    }
  );

  it('should find a soft deleted driver by group id and driver id', async () => {
    const testDeletedUser = {
      email: TEST_DELETED_EV_DRIVERS_ENTITY.email,
    } as Users;
    jest
      .spyOn(driversRepository, 'findOne')
      .mockResolvedValueOnce(TEST_DELETED_EV_DRIVERS_ENTITY);
    jest
      .spyOn(usersRepository, 'findOne')
      .mockResolvedValueOnce(testDeletedUser);

    const driver = await service.findByGroupIdAndDriverId(
      1,
      1,
      DriverTariffTier.DRIVER
    );

    expect(driver).toEqual(TEST_DELETED_DRIVER);
    expect(driversRepository.findOne).toHaveBeenCalledWith({
      include: includeOptions,
      where: { groupId: 1, id: 1 },
      paranoid: false,
    });
  });

  it('should throw DriverNotFoundException if driver is not found by id', async () => {
    jest.spyOn(driversRepository, 'findOne').mockResolvedValueOnce(null);

    await expect(
      service.findByGroupIdAndDriverId(1, 1, DriverTariffTier.DRIVER)
    ).rejects.toThrow(DriverNotFoundException);
  });

  it('should find by group id and driver email', async () => {
    const mockFindDriver = jest
      .spyOn(driversRepository, 'findOne')
      .mockResolvedValueOnce(TEST_EV_DRIVERS_ENTITY);
    const mockFindMember = jest
      .spyOn(membersRepository, 'findOne')
      .mockResolvedValueOnce(null);
    const mockFindOneByEmail = jest
      .spyOn(usersRepository, 'findOne')
      .mockResolvedValueOnce({ email: TEST_EV_DRIVERS_ENTITY.email } as Users);

    const driver = await service.findByGroupIdAndDriverEmail(
      1,
      TEST_DRIVER.email
    );

    expect(mockFindDriver).toHaveBeenCalledWith({
      where: { groupId: 1, email: TEST_DRIVER.email },
    });
    expect(mockFindMember).toHaveBeenCalledWith({
      where: { groupId: 1, email: TEST_DRIVER.email },
    });
    expect(mockFindOneByEmail).toHaveBeenCalledWith({
      paranoid: false,
      where: { email: TEST_DRIVER.email },
    });
    expect(driver).toEqual(TEST_DRIVER);
  });

  it('should throw a DriverNotFoundException if the driver cant be found', async () => {
    jest.spyOn(driversRepository, 'findOne').mockResolvedValueOnce(null);
    jest.spyOn(adminService, 'findByAdminId').mockResolvedValueOnce(undefined);
    jest.spyOn(membersRepository, 'findOne').mockResolvedValueOnce(null);

    await expect(
      service.findByGroupIdAndDriverEmail(1, TEST_DRIVER.email)
    ).rejects.toThrow(DriverNotFoundException);
  });

  it('should find by email', async () => {
    const mockFindAllDrivers = jest
      .spyOn(driversRepository, 'findAll')
      .mockResolvedValueOnce([TEST_EV_DRIVERS_ENTITY_WITH_GROUP]);
    const mockFindAllMembers = jest
      .spyOn(membersRepository, 'findAll')
      .mockResolvedValueOnce([TEST_MEMBERS_ENTITY_WITH_GROUP]);
    const mockFindAllUsers = jest
      .spyOn(usersRepository, 'findAll')
      .mockResolvedValueOnce([
        { email: TEST_EV_DRIVERS_ENTITY.email } as Users,
      ]);
    const mockSuppressedDestinations = jest
      .spyOn(simpleEmailService, 'listSuppressedDestinations')
      .mockResolvedValueOnce([]);

    const drivers = await service.findByEmail(TEST_DRIVER.email);

    expect(mockFindAllDrivers).toHaveBeenCalledWith({
      include: includeOptions,
      where: { email: TEST_DRIVER.email },
    });
    expect(mockFindAllMembers).toHaveBeenCalledWith({
      include: includeOptions,
      where: { email: TEST_DRIVER.email },
    });
    expect(mockFindAllUsers).toHaveBeenCalledWith({
      paranoid: false,
      where: {
        email: [TEST_EV_DRIVERS_ENTITY.email, TEST_MEMBERS_ENTITY.email],
      },
    });
    expect(mockSuppressedDestinations).not.toHaveBeenCalled();
    expect(drivers).toEqual([TEST_MEMBER_WITH_GROUP, TEST_DRIVER_WITH_GROUP]);
  });

  it.each([
    [true, 1],
    [false, 0],
    ['Yes', 1],
    ['No', 0],
    [undefined, 0],
  ])(
    'should create by group id when tariff tier is driver and canExpense is %s and should send invitation email',
    async (canExpenseInput = false, canExpenseOutput) => {
      const mockDuplicateDriverCheck = jest
        .spyOn(driversRepository, 'findOne')
        .mockResolvedValueOnce(null);
      const mockDuplicateMemberCheck = jest
        .spyOn(membersRepository, 'findOne')
        .mockResolvedValueOnce(null);
      const mockGetGroup = jest.fn();
      mockGetGroup.mockResolvedValueOnce(TEST_GROUP_ENTITY);
      const mockCreate = jest
        .spyOn(driversRepository, 'create')
        .mockResolvedValueOnce({
          ...TEST_EV_DRIVERS_ENTITY,
          getGroup: mockGetGroup,
        });
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce({
        email: TEST_EV_DRIVERS_ENTITY.email,
      } as Users);

      const driver = await service.createByGroupId(TEST_GROUP.id, {
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: canExpenseInput,
        tariffTier: DriverTariffTier.DRIVER,
      });

      expect(mockDuplicateDriverCheck).toHaveBeenCalledWith({
        where: { email: TEST_DRIVER.email, groupId: TEST_GROUP.id },
      });
      expect(mockDuplicateMemberCheck).toHaveBeenCalledWith({
        where: { email: TEST_DRIVER.email, groupId: TEST_GROUP.id },
      });
      expect(mockCreate).toHaveBeenCalledWith({
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: canExpenseOutput,
        groupId: TEST_GROUP.id,
        tariffTier: undefined,
      });
      expect(driver).toEqual(TEST_DRIVER);
    }
  );

  it.each([
    [true, 1],
    [false, 0],
    ['Yes', 1],
    ['No', 0],
    [undefined, 0],
  ])(
    'should create by group id when tariff tier is member and can expense is %s and should send invitation email',
    async (canExpenseInput = false, canExpenseOutput) => {
      const mockDuplicateDriverCheck = jest
        .spyOn(driversRepository, 'findOne')
        .mockResolvedValueOnce(null);
      const mockDuplicateMemberCheck = jest
        .spyOn(membersRepository, 'findOne')
        .mockResolvedValueOnce(null);
      const mockGetGroup = jest.fn();
      mockGetGroup.mockResolvedValueOnce(TEST_GROUP_ENTITY);
      const mockCreate = jest
        .spyOn(membersRepository, 'create')
        .mockResolvedValueOnce({
          ...TEST_MEMBERS_ENTITY,
          getGroup: mockGetGroup,
        });
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

      const driver = await service.createByGroupId(TEST_GROUP.id, {
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: canExpenseInput,
        tariffTier: DriverTariffTier.MEMBER,
      });

      expect(mockDuplicateDriverCheck).toHaveBeenCalledWith({
        where: { email: TEST_DRIVER.email, groupId: TEST_GROUP.id },
      });
      expect(mockDuplicateMemberCheck).toHaveBeenCalledWith({
        where: { email: TEST_DRIVER.email, groupId: TEST_GROUP.id },
      });
      expect(mockCreate).toHaveBeenCalledWith({
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: canExpenseOutput,
        groupId: TEST_GROUP.id,
        tariffTier: undefined,
      });
      expect(driver).toEqual(TEST_MEMBER);
    }
  );

  it.each([
    [true, 1],
    [false, 0],
    ['Yes', 1],
    ['No', 0],
    [undefined, 0],
  ])(
    'should create by group id when tariff tier is driver and can expense is %s and should not send invitation email',
    async (canExpenseInput = false, canExpenseOutput) => {
      const mockDuplicateDriverCheck = jest
        .spyOn(driversRepository, 'findOne')
        .mockResolvedValueOnce(null);
      const mockDuplicateMemberCheck = jest
        .spyOn(membersRepository, 'findOne')
        .mockResolvedValueOnce(null);
      const mockGetGroup = jest.fn();
      mockGetGroup.mockResolvedValueOnce(TEST_GROUP_ENTITY);
      const mockCreate = jest
        .spyOn(driversRepository, 'create')
        .mockResolvedValueOnce({
          ...TEST_EV_DRIVERS_ENTITY,
          getGroup: mockGetGroup,
        });
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce({
        email: TEST_EV_DRIVERS_ENTITY.email,
      } as Users);

      const mockSendEmail = jest.spyOn(simpleEmailService, 'sendEmail');

      const driver = await service.createByGroupId(TEST_GROUP.id, {
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: canExpenseInput,
        tariffTier: DriverTariffTier.DRIVER,
      });

      expect(mockDuplicateDriverCheck).toHaveBeenCalledWith({
        where: { email: TEST_DRIVER.email, groupId: TEST_GROUP.id },
      });
      expect(mockDuplicateMemberCheck).toHaveBeenCalledWith({
        where: { email: TEST_DRIVER.email, groupId: TEST_GROUP.id },
      });
      expect(mockCreate).toHaveBeenCalledWith({
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: canExpenseOutput,
        groupId: TEST_GROUP.id,
        tariffTier: undefined,
      });
      expect(adminService.findByAdminId).not.toHaveBeenCalled();
      expect(driver).toEqual(TEST_DRIVER);
      expect(mockSendEmail).not.toHaveBeenCalled();
    }
  );

  it.each([
    [true, 1],
    [false, 0],
    ['Yes', 1],
    ['No', 0],
    [undefined, 0],
  ])(
    'should create by group id when tariff tier is member and can expense is %s and should not send invitation email',
    async (canExpenseInput = false, canExpenseOutput) => {
      const mockDuplicateDriversCheck = jest
        .spyOn(driversRepository, 'findOne')
        .mockResolvedValueOnce(null);
      const mockDuplicateMemberCheck = jest
        .spyOn(membersRepository, 'findOne')
        .mockResolvedValueOnce(null);
      const mockGetGroup = jest.fn();
      mockGetGroup.mockResolvedValueOnce(TEST_GROUP_ENTITY);
      const mockCreate = jest
        .spyOn(membersRepository, 'create')
        .mockResolvedValueOnce({
          ...TEST_MEMBERS_ENTITY,
          getGroup: mockGetGroup,
        });
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);
      const mockSendEmail = jest.spyOn(simpleEmailService, 'sendEmail');

      const driver = await service.createByGroupId(TEST_GROUP.id, {
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: canExpenseInput,
        tariffTier: DriverTariffTier.MEMBER,
      });

      expect(mockDuplicateDriversCheck).toHaveBeenCalledWith({
        where: { email: TEST_DRIVER.email, groupId: TEST_GROUP.id },
      });
      expect(mockDuplicateMemberCheck).toHaveBeenCalledWith({
        where: { email: TEST_DRIVER.email, groupId: TEST_GROUP.id },
      });
      expect(mockCreate).toHaveBeenCalledWith({
        ...TEST_CREATE_DRIVER_REQUEST,
        canExpense: canExpenseOutput,
        groupId: TEST_GROUP.id,
        tariffTier: undefined,
      });
      expect(adminService.findByAdminId).not.toHaveBeenCalled();
      expect(driver).toEqual(TEST_MEMBER);
      expect(mockSendEmail).not.toHaveBeenCalled();
    }
  );

  it('should throw DuplicateDriverException on create if driver is already present', async () => {
    jest
      .spyOn(driversRepository, 'findOne')
      .mockResolvedValueOnce(TEST_EV_DRIVERS_ENTITY);
    jest.spyOn(membersRepository, 'findOne').mockResolvedValueOnce(null);
    jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

    await expect(
      service.createByGroupId(TEST_GROUP.id, TEST_CREATE_DRIVER_REQUEST)
    ).rejects.toThrow(DuplicateDriverException);
  });

  it('should throw DuplicateDriverException on create if member is already present', async () => {
    jest.spyOn(driversRepository, 'findOne').mockResolvedValueOnce(null);
    jest
      .spyOn(membersRepository, 'findOne')
      .mockResolvedValueOnce(TEST_EV_DRIVERS_ENTITY);
    jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

    await expect(
      service.createByGroupId(TEST_GROUP.id, {
        ...TEST_CREATE_DRIVER_REQUEST,
        tariffTier: DriverTariffTier.MEMBER,
      })
    ).rejects.toThrow(DuplicateDriverException);
  });

  it('should update by group id when tariff tier is driver', async () => {
    const mockUpdate = jest
      .spyOn(driversRepository, 'update')
      .mockResolvedValueOnce([1]);

    await service.updateByGroupIdAndDriverId(TEST_GROUP.id, TEST_DRIVER.id, {
      ...TEST_UPDATE_DRIVER_REQUEST,
      tariffTier: DriverTariffTier.DRIVER,
    });

    expect(mockUpdate).toHaveBeenCalledWith(
      { ...TEST_UPDATE_DRIVER_REQUEST, canExpense: 1, tariffTier: undefined },
      {
        where: {
          groupId: TEST_GROUP.id,
          id: TEST_DRIVER.id,
        },
      }
    );
  });

  it('should update by group id when tariff tier is member', async () => {
    const mockUpdate = jest
      .spyOn(membersRepository, 'update')
      .mockResolvedValueOnce([1]);

    await service.updateByGroupIdAndDriverId(TEST_GROUP.id, TEST_DRIVER.id, {
      ...TEST_UPDATE_DRIVER_REQUEST,
      canExpense: false,
      tariffTier: DriverTariffTier.MEMBER,
    });

    expect(mockUpdate).toHaveBeenCalledWith(
      { ...TEST_UPDATE_DRIVER_REQUEST, canExpense: 0, tariffTier: undefined },
      {
        where: {
          groupId: TEST_GROUP.id,
          id: TEST_DRIVER.id,
        },
      }
    );
  });

  it('should update by group id and switch tariff tier from driver to member', async () => {
    const mockDelete = jest
      .spyOn(driversRepository, 'destroy')
      .mockResolvedValueOnce(0);
    const mockCreate = jest
      .spyOn(membersRepository, 'create')
      .mockResolvedValueOnce(TEST_EV_DRIVERS_ENTITY);
    jest.spyOn(driversRepository, 'findOne').mockResolvedValueOnce(null);
    jest.spyOn(membersRepository, 'findOne').mockResolvedValueOnce(null);
    jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

    await service.updateByGroupIdAndDriverId(TEST_GROUP.id, TEST_DRIVER.id, {
      ...TEST_UPDATE_DRIVER_REQUEST,
      canExpense: false,
      originalTariffTier: DriverTariffTier.DRIVER,
      tariffTier: DriverTariffTier.MEMBER,
    });

    expect(mockDelete).toHaveBeenCalledWith({
      where: {
        groupId: TEST_GROUP.id,
        id: TEST_DRIVER.id,
      },
    });
    expect(mockCreate).toHaveBeenCalledWith({
      canExpense: 0,
      email: TEST_UPDATE_DRIVER_REQUEST.email,
      firstName: TEST_UPDATE_DRIVER_REQUEST.firstName,
      groupId: TEST_GROUP.id,
      lastName: TEST_UPDATE_DRIVER_REQUEST.lastName,
    });
  });

  it('should update by group id and switch tariff tier from member to driver', async () => {
    const mockDelete = jest
      .spyOn(membersRepository, 'destroy')
      .mockResolvedValueOnce(0);
    const mockCreate = jest
      .spyOn(driversRepository, 'create')
      .mockResolvedValueOnce(TEST_MEMBERS_ENTITY);
    jest.spyOn(driversRepository, 'findOne').mockResolvedValueOnce(null);
    jest.spyOn(membersRepository, 'findOne').mockResolvedValueOnce(null);
    jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

    await service.updateByGroupIdAndDriverId(TEST_GROUP.id, TEST_DRIVER.id, {
      ...TEST_UPDATE_DRIVER_REQUEST,
      canExpense: false,
      originalTariffTier: DriverTariffTier.MEMBER,
      tariffTier: DriverTariffTier.DRIVER,
    });

    expect(mockDelete).toHaveBeenCalledWith({
      where: {
        groupId: TEST_GROUP.id,
        id: TEST_DRIVER.id,
      },
    });
    expect(mockCreate).toHaveBeenCalledWith({
      canExpense: 0,
      email: TEST_UPDATE_DRIVER_REQUEST.email,
      firstName: TEST_UPDATE_DRIVER_REQUEST.firstName,
      groupId: TEST_GROUP.id,
      lastName: TEST_UPDATE_DRIVER_REQUEST.lastName,
    });
  });

  it('should throw DuplicateDriverException on update if driver already exists in the new tier when switching tariff tier', async () => {
    jest.spyOn(membersRepository, 'destroy').mockResolvedValueOnce(0);
    jest
      .spyOn(driversRepository, 'findOne')
      .mockResolvedValueOnce(TEST_MEMBERS_ENTITY);
    jest.spyOn(membersRepository, 'findOne').mockResolvedValueOnce(null);
    jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

    await expect(
      service.updateByGroupIdAndDriverId(TEST_GROUP.id, TEST_DRIVER.id, {
        ...TEST_UPDATE_DRIVER_REQUEST,
        canExpense: false,
        tariffTier: DriverTariffTier.DRIVER,
        originalTariffTier: DriverTariffTier.MEMBER,
      })
    ).rejects.toThrow(DuplicateDriverException);
  });

  it('should throw DuplicateDriverException on update if driver already exists in the same tier when switching tariff tier', async () => {
    jest.spyOn(driversRepository, 'destroy').mockResolvedValueOnce(0);
    jest
      .spyOn(driversRepository, 'findOne')
      .mockResolvedValueOnce(TEST_EV_DRIVERS_ENTITY);
    jest.spyOn(membersRepository, 'findOne').mockResolvedValueOnce(null);
    jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

    await expect(
      service.updateByGroupIdAndDriverId(TEST_GROUP.id, TEST_DRIVER.id, {
        ...TEST_UPDATE_DRIVER_REQUEST,
        canExpense: false,
        tariffTier: DriverTariffTier.MEMBER,
        originalTariffTier: DriverTariffTier.DRIVER,
      })
    ).rejects.toThrow(DuplicateDriverException);
  });

  it('should delete by group id when tariff tier is driver', async () => {
    const mockDelete = jest
      .spyOn(driversRepository, 'destroy')
      .mockResolvedValue(1);

    await service.deleteByGroupIdAndDriverId(TEST_GROUP.id, TEST_DRIVER.id, {
      ...TEST_DELETE_DRIVER_REQUEST,
      tariffTier: DriverTariffTier.DRIVER,
    });

    expect(mockDelete).toHaveBeenCalledWith({
      where: {
        id: TEST_DRIVER.id,
        groupId: TEST_GROUP.id,
      },
    });
  });

  it('should delete by group id when tariff tier is member', async () => {
    const mockDelete = jest
      .spyOn(membersRepository, 'destroy')
      .mockResolvedValue(1);

    await service.deleteByGroupIdAndDriverId(TEST_GROUP.id, TEST_DRIVER.id, {
      ...TEST_DELETE_DRIVER_REQUEST,
      tariffTier: DriverTariffTier.MEMBER,
    });

    expect(mockDelete).toHaveBeenCalledWith({
      where: {
        id: TEST_DRIVER.id,
        groupId: TEST_GROUP.id,
      },
    });
  });

  it.each([
    [
      'the account does not exist',
      null,
      nonNetworkHtmlEmail,
      nonNetworkPlaintextEmail,
    ],
    [
      'the account does exist',
      { email: TEST_DRIVER.email } as Users,
      networkHtmlEmail,
      networkPlaintextEmail,
    ],
  ])(
    'should invite by group id when tariff tier is driver, the admin details are present and %s',
    async (_, user, htmlTemplate, textTemplate) => {
      jest.spyOn(driversRepository, 'findOne').mockResolvedValueOnce({
        ...TEST_EV_DRIVERS_ENTITY,
        groupId: TEST_GROUP_ENTITY.id,
        group: TEST_GROUP_ENTITY,
      } as EvDrivers);
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(user);
      jest
        .spyOn(adminService, 'findByAdminId')
        .mockResolvedValue(TEST_ADMINISTRATOR);
      const mockSendEmail = jest.spyOn(simpleEmailService, 'sendEmail');

      await service.inviteByGroupIdAndDriverId(
        TEST_GROUP.id,
        TEST_DRIVER.id,
        TEST_ADMINISTRATOR.id,
        DriverTariffTier.DRIVER,
        new URL('https://sites.podenergy.com')
      );

      expect(mockSendEmail).toHaveBeenCalledWith({
        bodyHtml: injectParametersIntoTemplateString(htmlTemplate, {
          ...emailParams,
        }),
        bodyText: injectParametersIntoTemplateString(textTemplate, {
          ...emailParams,
        }),
        sender: '<EMAIL>',
        subject: `${adminName} invites you to charge your electric vehicle with ${TEST_GROUP_ENTITY.name}`,
        to: TEST_EV_DRIVERS_ENTITY.email,
      });
      expect(adminService.findByAdminId).toHaveBeenCalledWith(
        TEST_ADMINISTRATOR.id
      );
    }
  );

  it.each([
    [
      'the account does not exist',
      null,
      nonNetworkHtmlEmail,
      nonNetworkPlaintextEmail,
    ],
    [
      'the account does exist',
      { email: TEST_DRIVER.email } as Users,
      networkHtmlEmail,
      networkPlaintextEmail,
    ],
  ])(
    'should invite by group id when tariff tier is driver, the admin details are not present and %s',
    async (_, user, htmlTemplate, textTemplate) => {
      jest.spyOn(driversRepository, 'findOne').mockResolvedValueOnce({
        ...TEST_EV_DRIVERS_ENTITY,
        groupId: TEST_GROUP_ENTITY.id,
        group: TEST_GROUP_ENTITY,
      } as EvDrivers);
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(user);
      jest.spyOn(adminService, 'findByAdminId').mockResolvedValue({
        ...TEST_ADMINISTRATOR,
        firstName: undefined,
        lastName: undefined,
      });
      const mockSendEmail = jest.spyOn(simpleEmailService, 'sendEmail');

      await service.inviteByGroupIdAndDriverId(
        TEST_GROUP.id,
        TEST_DRIVER.id,
        TEST_ADMINISTRATOR.id,
        DriverTariffTier.DRIVER,
        new URL('https://sites.podenergy.com')
      );

      expect(mockSendEmail).toHaveBeenCalledWith({
        bodyHtml: injectParametersIntoTemplateString(htmlTemplate, {
          ...emailParams,
          subject: `You have been invited to charge your electric vehicle with ${TEST_GROUP_ENTITY.name}`,
        }),
        bodyText: injectParametersIntoTemplateString(textTemplate, {
          ...emailParams,
          subject: `You have been invited to charge your electric vehicle with ${TEST_GROUP_ENTITY.name}`,
        }),
        sender: '<EMAIL>',
        subject: `You have been invited to charge your electric vehicle with ${TEST_GROUP_ENTITY.name}`,
        to: TEST_EV_DRIVERS_ENTITY.email,
      });
      expect(adminService.findByAdminId).toHaveBeenCalledWith(
        TEST_ADMINISTRATOR.id
      );
    }
  );

  it.each([
    [
      'and the account does not exist',
      null,
      nonNetworkHtmlEmail,
      nonNetworkPlaintextEmail,
    ],
    [
      'and the account does exist',
      { email: TEST_MEMBER.email } as Users,
      networkHtmlEmail,
      networkPlaintextEmail,
    ],
  ])(
    'should invite by group id when tariff tier is member, the admin details are present %s',
    async (_, user, htmlTemplate, textTemplate) => {
      jest.spyOn(membersRepository, 'findOne').mockResolvedValueOnce({
        ...TEST_MEMBERS_ENTITY,
        groupId: TEST_GROUP_ENTITY.id,
        group: TEST_GROUP_ENTITY,
      } as Members);
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(user);
      jest
        .spyOn(adminService, 'findByAdminId')
        .mockResolvedValue(TEST_ADMINISTRATOR);
      const mockSendEmail = jest.spyOn(simpleEmailService, 'sendEmail');

      await service.inviteByGroupIdAndDriverId(
        TEST_GROUP.id,
        TEST_DRIVER.id,
        TEST_ADMINISTRATOR.id,
        DriverTariffTier.MEMBER,
        new URL('https://sites.podenergy.com')
      );

      expect(mockSendEmail).toHaveBeenCalledWith({
        bodyHtml: injectParametersIntoTemplateString(htmlTemplate, {
          ...emailParams,
          firstName: TEST_MEMBER.firstName,
          emailAddress: TEST_MEMBER.email,
        }),
        bodyText: injectParametersIntoTemplateString(textTemplate, {
          ...emailParams,
          firstName: TEST_MEMBER.firstName,
          emailAddress: TEST_MEMBER.email,
        }),
        sender: '<EMAIL>',
        subject: `${adminName} invites you to charge your electric vehicle with ${TEST_GROUP_ENTITY.name}`,
        to: TEST_MEMBERS_ENTITY.email,
      });
      expect(adminService.findByAdminId).toHaveBeenCalledWith(
        TEST_ADMINISTRATOR.id
      );
    }
  );

  it.each([
    [
      'and the account does not exist',
      null,
      nonNetworkHtmlEmail,
      nonNetworkPlaintextEmail,
    ],
    [
      'and the account does exist',
      { email: TEST_MEMBER.email } as Users,
      networkHtmlEmail,
      networkPlaintextEmail,
    ],
  ])(
    'should invite by group id when tariff tier is member, the admin details are not present %s',
    async (_, user, htmlTemplate, textTemplate) => {
      jest.spyOn(membersRepository, 'findOne').mockResolvedValueOnce({
        ...TEST_MEMBERS_ENTITY,
        groupId: TEST_GROUP_ENTITY.id,
        group: TEST_GROUP_ENTITY,
      } as Members);
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(user);
      jest.spyOn(adminService, 'findByAdminId').mockResolvedValue({
        ...TEST_ADMINISTRATOR,
        firstName: undefined,
        lastName: undefined,
      });
      const mockSendEmail = jest.spyOn(simpleEmailService, 'sendEmail');

      await service.inviteByGroupIdAndDriverId(
        TEST_GROUP.id,
        TEST_DRIVER.id,
        TEST_ADMINISTRATOR.id,
        DriverTariffTier.MEMBER,
        new URL('https://sites.podenergy.com')
      );

      expect(mockSendEmail).toHaveBeenCalledWith({
        bodyHtml: injectParametersIntoTemplateString(htmlTemplate, {
          ...emailParams,
          firstName: TEST_MEMBER.firstName,
          emailAddress: TEST_MEMBER.email,
          subject: `You have been invited to charge your electric vehicle with ${TEST_GROUP_ENTITY.name}`,
        }),
        bodyText: injectParametersIntoTemplateString(textTemplate, {
          ...emailParams,
          firstName: TEST_MEMBER.firstName,
          emailAddress: TEST_MEMBER.email,
          subject: `You have been invited to charge your electric vehicle with ${TEST_GROUP_ENTITY.name}`,
        }),
        sender: '<EMAIL>',
        subject: `You have been invited to charge your electric vehicle with ${TEST_GROUP_ENTITY.name}`,
        to: TEST_MEMBERS_ENTITY.email,
      });
      expect(adminService.findByAdminId).toHaveBeenCalledWith(
        TEST_ADMINISTRATOR.id
      );
    }
  );

  it.each([
    [{ deletedAt: null }, DriverStatus.REGISTERED],
    [{ deletedAt: new Date() } as Users, DriverStatus.DEACTIVATED],
    [null, DriverStatus.PENDING],
  ])(
    `should populate driver status when user is %s`,
    async (user, driverStatus) => {
      jest
        .spyOn(driversRepository, 'findAll')
        .mockResolvedValueOnce([TEST_EV_DRIVERS_ENTITY]);
      jest
        .spyOn(membersRepository, 'findAll')
        .mockResolvedValueOnce([TEST_MEMBERS_ENTITY]);
      jest
        .spyOn(usersRepository, 'findAll')
        .mockResolvedValueOnce(
          user
            ? [
                { ...user, email: TEST_EV_DRIVERS_ENTITY.email } as Users,
                { ...user, email: TEST_MEMBERS_ENTITY.email } as Users,
              ]
            : []
        );
      jest
        .spyOn(simpleEmailService, 'listSuppressedDestinations')
        .mockResolvedValueOnce([]);

      const drivers = await service.findByGroupId(TEST_GROUP.id);

      expect(drivers.length).toEqual(2);
      expect(drivers[0].status).toEqual(driverStatus);
      expect(drivers[1].status).toEqual(driverStatus);
    }
  );

  it.each([
    [{ deletedAt: null }, DriverStatus.DELETED],
    [{ deletedAt: new Date() } as Users, DriverStatus.DEACTIVATED],
    [null, DriverStatus.DELETED],
  ])(
    `should populate driver status when driver is deleted and user is %s`,
    async (user, driverStatus) => {
      jest
        .spyOn(driversRepository, 'findAll')
        .mockResolvedValueOnce([TEST_DELETED_EV_DRIVERS_ENTITY]);
      jest
        .spyOn(membersRepository, 'findAll')
        .mockResolvedValueOnce([TEST_DELETED_MEMBERS_ENTITY]);
      jest.spyOn(usersRepository, 'findAll').mockResolvedValueOnce(
        user
          ? [
              {
                ...user,
                email: TEST_DELETED_EV_DRIVERS_ENTITY.email,
              } as Users,
              { ...user, email: TEST_DELETED_MEMBERS_ENTITY.email } as Users,
            ]
          : []
      );
      jest
        .spyOn(simpleEmailService, 'listSuppressedDestinations')
        .mockResolvedValueOnce([]);

      const drivers = await service.findByGroupId(TEST_GROUP.id);

      expect(drivers.length).toEqual(2);
      expect(drivers[0].status).toEqual(driverStatus);
      expect(drivers[1].status).toEqual(driverStatus);
    }
  );

  it(`should map user status to driver status when email case does not match`, async () => {
    jest.spyOn(usersRepository, 'findAll').mockResolvedValueOnce([
      {
        email: TEST_EV_DRIVERS_ENTITY.email.toUpperCase(),
      } as Users,
      {
        email: TEST_MEMBERS_ENTITY.email.toUpperCase(),
      } as Users,
    ]);
    jest
      .spyOn(driversRepository, 'findAll')
      .mockResolvedValueOnce([TEST_EV_DRIVERS_ENTITY]);
    jest
      .spyOn(membersRepository, 'findAll')
      .mockResolvedValueOnce([TEST_MEMBERS_ENTITY]);
    jest
      .spyOn(simpleEmailService, 'listSuppressedDestinations')
      .mockResolvedValueOnce([]);

    const drivers = await service.findByGroupId(TEST_GROUP.id);

    expect(drivers.length).toEqual(2);
    expect(drivers[0].status).toEqual(DriverStatus.REGISTERED);
    expect(drivers[1].status).toEqual(DriverStatus.REGISTERED);
  });

  it('should find driver and member emails by group id', async () => {
    const mockFindDriver = jest
      .spyOn(driversRepository, 'findAll')
      .mockResolvedValueOnce([TEST_EV_DRIVERS_ENTITY]);
    const mockFindMember = jest
      .spyOn(membersRepository, 'findAll')
      .mockResolvedValueOnce([TEST_MEMBERS_ENTITY]);

    const emails = await service.findEmailsByGroupId(TEST_GROUP.id);

    expect(mockFindDriver).toHaveBeenCalledWith({
      attributes: ['email'],
      paranoid: true,
      where: { groupId: TEST_GROUP.id },
    });
    expect(mockFindMember).toHaveBeenCalledWith({
      attributes: ['email'],
      paranoid: true,
      where: { groupId: TEST_GROUP.id },
    });
    expect(emails).toEqual([TEST_DRIVER.email, TEST_MEMBER.email]);
  });

  it('should flag drivers whose email addresses are on the suppressed destination list', async () => {
    jest
      .spyOn(driversRepository, 'findAll')
      .mockResolvedValueOnce([TEST_EV_DRIVERS_ENTITY]);
    jest.spyOn(membersRepository, 'findAll').mockResolvedValueOnce([]);
    jest
      .spyOn(usersRepository, 'findAll')
      .mockResolvedValueOnce([
        { email: TEST_EV_DRIVERS_ENTITY.email } as Users,
      ]);
    const mockSuppressedDestinations = jest
      .spyOn(simpleEmailService, 'listSuppressedDestinations')
      .mockResolvedValueOnce([TEST_DRIVER.email]);

    const drivers = await service.findByGroupId(TEST_GROUP.id);

    expect(mockSuppressedDestinations).toHaveBeenCalledWith('BOUNCE');
    expect(drivers).toEqual([{ ...TEST_DRIVER, emailBounced: true }]);
  });

  it('should handle errors when flagging drivers whose email addresses are on the suppressed destination list', async () => {
    jest
      .spyOn(driversRepository, 'findAll')
      .mockResolvedValueOnce([TEST_EV_DRIVERS_ENTITY]);
    jest.spyOn(membersRepository, 'findAll').mockResolvedValueOnce([]);
    jest
      .spyOn(usersRepository, 'findAll')
      .mockResolvedValueOnce([
        { email: TEST_EV_DRIVERS_ENTITY.email } as Users,
      ]);
    const mockSuppressedDestinations = jest
      .spyOn(simpleEmailService, 'listSuppressedDestinations')
      .mockRejectedValueOnce(new Error('Test error'));

    const drivers = await service.findByGroupId(TEST_GROUP.id);

    expect(mockSuppressedDestinations).toHaveBeenCalledWith('BOUNCE');
    expect(drivers).toEqual([TEST_DRIVER]);
  });

  it('should create missing driver records matching domain', async () => {
    const mockFindAllDrivers = jest
      .spyOn(driversRepository, 'findAll')
      .mockResolvedValueOnce([]);
    const mockFindAllMembers = jest
      .spyOn(membersRepository, 'findAll')
      .mockResolvedValueOnce([TEST_MEMBERS_ENTITY]);
    const mockFindAllUsers = jest
      .spyOn(usersRepository, 'findAll')
      .mockResolvedValueOnce([
        {
          email: TEST_EV_DRIVERS_ENTITY.email,
          firstName: TEST_EV_DRIVERS_ENTITY.firstName,
          lastName: TEST_EV_DRIVERS_ENTITY.lastName,
        } as Users,
      ]);
    const mockBulkCreateDrivers = jest
      .spyOn(driversRepository, 'bulkCreate')
      .mockResolvedValueOnce([]);

    await service.createMissing(TEST_DOMAIN);

    expect(mockFindAllDrivers).toHaveBeenCalledWith({
      attributes: ['email'],
      paranoid: false,
      where: { groupId: TEST_GROUP.id },
    });
    expect(mockFindAllMembers).toHaveBeenCalledWith({
      attributes: ['email'],
      paranoid: false,
      where: { groupId: TEST_GROUP.id },
    });
    expect(mockFindAllUsers).toHaveBeenCalledWith({
      where: {
        [Op.and]: [
          { email: { [Op.like]: `%${TEST_DOMAIN.domainName}` } },
          { email: { [Op.notIn]: [TEST_MEMBERS_ENTITY.email] } },
        ],
      },
      paranoid: false,
    });
    expect(mockBulkCreateDrivers).toHaveBeenCalledWith([
      {
        canExpense: 0,
        email: TEST_EV_DRIVERS_ENTITY.email,
        firstName: TEST_EV_DRIVERS_ENTITY.firstName,
        groupId: TEST_DOMAIN.groupId,
        lastName: TEST_EV_DRIVERS_ENTITY.lastName,
      },
    ]);
  });

  it('should return a map of drivers keyed by user auth id', async () => {
    const TEST_DRIVER_JOE = {
      ...TEST_DRIVER,
      email: '<EMAIL>',
    };
    const mockFindEmailsByGroupId = jest
      .spyOn(service, 'findByGroupId')
      .mockResolvedValue([TEST_DRIVER, TEST_DRIVER_JOE]);
    const mockFindAll = jest
      .spyOn(usersRepository, 'findAll')
      .mockResolvedValue([
        TEST_USER_ENTITY,
        {
          authId: '123e4567-e89b-12d3-a456-426614176543',
          email: '<EMAIL>',
        } as Users,
      ]);
    const expectedMap = new Map<string, Driver>();
    expectedMap.set('123e4567-e89b-12d3-a456-426614174000', TEST_DRIVER_JOE);
    expectedMap.set('123e4567-e89b-12d3-a456-426614176543', TEST_DRIVER);

    const map = await service.findByGroupIdKeyedByAuthId(TEST_GROUP.id);

    expect(map).toEqual(new Map(expectedMap));
    expect(mockFindEmailsByGroupId).toHaveBeenCalledWith(
      TEST_GROUP.id,
      undefined
    );
    expect(mockFindAll).toHaveBeenCalledWith({
      where: { email: [TEST_DRIVER.email, TEST_DRIVER_JOE.email] },
      paranoid: false,
    });
  });
});
