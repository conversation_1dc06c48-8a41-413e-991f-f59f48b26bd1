import { Admin<PERSON>ontroller } from './admin.controller';
import { AdminMiddleware } from './admin.middleware';
import { AdminService } from './admin.service';
import { CacheModule } from '@nestjs/cache-manager';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { ChargeModule } from '@experience/commercial/site-admin/nest/charge-module';
import { ConfigModule } from '@nestjs/config';
import { DeactivateDormantAccountsCommand } from './commands/deactivate-dormant-accounts.command';
import { DeleteDeactivatedAccountsCommand } from './commands/delete-deactivated-accounts.command';
import { GroupController } from './group/group.controller';
import { GroupService } from './group/group.service';
import {
  Groups,
  PodadminSequelizeModule,
  Users,
} from '@experience/shared/sequelize/podadmin';
import {
  Logger,
  MiddlewareConsumer,
  Module,
  OnModuleInit,
} from '@nestjs/common';
import { PasswordResetController } from './password-reset/password-reset.controller';
import { PasswordResetService } from './password-reset/password-reset.service';
import { ReactivateDeactivatedAccountsCommand } from './commands/reactivate-deactivated-accounts.command';
import { SesModule } from '@experience/shared/nest/aws/ses-module';
import { SignInWithEmailController } from './sign-in-with-email/sign-in-with-email.controller';
import { SignInWithEmailService } from './sign-in-with-email/sign-in-with-email.service';
import { updateProjectConfig } from '@experience/shared/firebase/admin';

@Module({
  imports: [
    CacheModule.register({ ttl: 60_000 }),
    ChargeModule,
    ConfigModule,
    PodadminSequelizeModule,
    SesModule,
  ],
  controllers: [
    AdminController,
    GroupController,
    PasswordResetController,
    SignInWithEmailController,
  ],
  providers: [
    AdminService,
    DeactivateDormantAccountsCommand,
    DeleteDeactivatedAccountsCommand,
    GroupService,
    PasswordResetService,
    ReactivateDeactivatedAccountsCommand,
    SignInWithEmailService,
    { provide: 'GROUPS_REPOSITORY', useValue: Groups },
    { provide: 'USERS_REPOSITORY', useValue: Users },
  ],
  exports: [AdminService, GroupService],
})
export class AdminModule implements OnModuleInit {
  private readonly logger = new Logger(AdminModule.name);

  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminMiddleware).forRoutes(AdminController, GroupController);
  }

  onModuleInit() {
    this.logger.log('updating auth project config');
    updateProjectConfig();
  }
}
