import { PasswordResetService } from './password-reset.service';
import { SimpleEmailService } from '@experience/shared/nest/aws/ses-module';
import { TEST_SEND_PASSWORD_RESET_REQUEST } from '@experience/commercial/site-admin/domain/auth';
import { Test, TestingModule } from '@nestjs/testing';

const mockAuth = { generatePasswordResetLink: jest.fn() };
jest.mock('@experience/shared/firebase/admin', () => ({
  getAuth: () => mockAuth,
}));
jest.mock('@experience/shared/nest/aws/ses-module');

describe('PasswordResetService', () => {
  let service: PasswordResetService;
  let simpleEmailService: SimpleEmailService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PasswordResetService,
        {
          provide: SimpleEmailService,
          useClass: SimpleEmailService,
        },
      ],
    }).compile();

    service = module.get(PasswordResetService);
    simpleEmailService = module.get<SimpleEmailService>(SimpleEmailService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(simpleEmailService).toBeDefined();
  });

  it.each([
    [
      'https://sites.pod-point.com',
      'sites.pod-point.com',
      'Site Management Service',
    ],
    [
      'https://sites-internal.pod-point.com',
      'sites-internal.pod-point.com',
      'Internal Site Management Service',
    ],
    [
      'https://sites.podenergy.com',
      'sites.podenergy.com',
      'Site Management Service',
    ],
    [
      'https://sites-internal.podenergy.com',
      'sites-internal.podenergy.com',
      'Internal Site Management Service',
    ],
  ])(
    'should send password reset email with origin: %s',
    async (origin, expectedUrl, expectedServiceName) => {
      const passwordResetLink = `${origin}?mode=resetPassword&oobCode=foobar`;
      mockAuth.generatePasswordResetLink.mockResolvedValueOnce(
        passwordResetLink
      );
      const mockSendEmail = jest
        .spyOn(simpleEmailService, 'sendEmail')
        .mockResolvedValueOnce(true);

      await service.sendPasswordReset(
        TEST_SEND_PASSWORD_RESET_REQUEST,
        new URL(origin)
      );

      expect(mockSendEmail).toHaveBeenCalledTimes(1);
      [
        'reset',
        TEST_SEND_PASSWORD_RESET_REQUEST.email,
        `${expectedUrl}/auth/action?mode=resetPassword&oobCode=foobar`,
        `${expectedUrl}/help`,
        expectedServiceName,
      ].forEach((value) => {
        expect(mockSendEmail).toHaveBeenCalledWith({
          bodyHtml: expect.stringContaining(value),
          bodyText: expect.stringContaining(value),
          sender: `no-reply@${expectedUrl.split(/\.(.+)/)[1]}`,
          subject: 'Reset your password',
          to: TEST_SEND_PASSWORD_RESET_REQUEST.email,
        });
      });
    }
  );

  it.each(['auth/email-not-found', 'auth/internal-error'])(
    'should attempt to send password reset email and handle %s',
    async (errorCode) => {
      mockAuth.generatePasswordResetLink.mockRejectedValueOnce({
        code: errorCode,
      });
      const mockSendEmail = jest.spyOn(simpleEmailService, 'sendEmail');

      await service.sendPasswordReset(
        TEST_SEND_PASSWORD_RESET_REQUEST,
        new URL('https://sites.podenergy.com')
      );

      expect(mockSendEmail).not.toHaveBeenCalled();
    }
  );

  it('should attempt to send password reset email and propagate unexpected error', async () => {
    const error = new Error();
    mockAuth.generatePasswordResetLink.mockRejectedValueOnce(error);
    const mockSendEmail = jest.spyOn(simpleEmailService, 'sendEmail');

    await expect(
      service.sendPasswordReset(
        TEST_SEND_PASSWORD_RESET_REQUEST,
        new URL('https://sites.podenergy.com')
      )
    ).rejects.toThrow(error);

    expect(mockSendEmail).not.toHaveBeenCalled();
  });
});
