import 'whatwg-fetch';
import { Auth } from 'firebase/auth';
import { TEST_USER } from '@experience/commercial/site-admin/typescript/domain-model';
import { render, screen } from '@testing-library/react';
import ProfilePage from './profile-page';

const mockUseUser = jest.fn();

jest.mock('@experience/commercial/site-admin/next/shared', () => ({
  ...jest.requireActual('@experience/commercial/site-admin/next/shared'),
  useUser: () => mockUseUser(),
}));

jest.mock('@experience/shared/next/firebase', () => ({
  getAuth: jest.fn().mockResolvedValue({} as Auth),
}));

describe('ProfilePage', () => {
  beforeEach(() => {
    mockUseUser.mockReturnValueOnce(TEST_USER);
  });

  it('should render successfully', () => {
    const { baseElement } = render(<ProfilePage />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', async () => {
    const { baseElement } = render(<ProfilePage />);

    expect(await screen.findByText('Personal info')).toBeInTheDocument();
    expect(baseElement).toMatchSnapshot();
  });
});
