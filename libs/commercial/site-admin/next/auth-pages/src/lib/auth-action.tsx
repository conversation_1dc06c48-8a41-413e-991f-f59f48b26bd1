import { ErrorPage, NotFoundPage } from '@experience/shared/react/error-pages';
import { Heading, HeadingSizes } from '@experience/shared/react/design-system';
import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import AuthLayout from './components/auth-layout/auth-layout';
import LoginForm from './components/login-form/login-form';
import PasswordResetForm from './components/password-reset-form/password-reset-form';

export enum AuthActionMode {
  RESET_PASSWORD = 'resetPassword',
  SIGN_IN = 'signIn',
  VERIFY_EMAIL = 'verifyEmail',
}

export const AuthAction = () => {
  const params = useSearchParams();
  const router = useRouter();
  const { status } = useSession();

  const oobCode = params.get('oobCode');
  const mode = params.get('mode');

  useEffect(() => {
    if (status === 'authenticated' && mode === AuthActionMode.SIGN_IN) {
      void router.push('/');
    }
  });

  if (!mode || !oobCode) {
    return <ErrorPage />;
  }

  switch (mode as AuthActionMode) {
    case AuthActionMode.SIGN_IN:
      return (
        <>
          <Heading.H1 fontSize={HeadingSizes.L} className="font-bold">
            Hello!
          </Heading.H1>
          <LoginForm oobCode={oobCode as string} />
        </>
      );
    case AuthActionMode.RESET_PASSWORD:
      return <PasswordResetForm oobCode={oobCode as string} />;
    default:
      return <NotFoundPage />;
  }
};

AuthAction.getLayout = (page: React.ReactElement) => (
  <AuthLayout>{page}</AuthLayout>
);

export default AuthAction;
