export const describeGroupsPage = (): void => {
  describe('Groups page', () => {
    beforeEach(() => {
      cy.clickLink('Groups');
    });

    it('should render correctly', () => {
      cy.shouldHaveHeading(1, /Groups/);
      cy.shouldHaveTable('Table of groups');
    });

    it('should show group info page', () => {
      cy.clickFirstLinkWithinTable(/.*/, 'Table of groups');
      cy.shouldHaveHeading(3, 'Business address');
      cy.shouldHaveHeading(3, 'Business name');
      cy.shouldHaveHeading(3, 'Business email address');
      cy.shouldHaveHeading(3, 'Account reference');

      cy.shouldHaveHeading(2, 'Sites');
      cy.shouldHaveTable('Table of sites');

      cy.shouldHaveHeading(2, 'Statements');
      cy.shouldHaveTable('Table of statements');

      cy.shouldHaveHeading(2, 'Subscription payments');
      cy.shouldHaveTable('Table of subscription payments');
    });

    it('should edit a group', () => {
      cy.clickFirstLinkWithinTable(/.*/, 'Table of groups');
      cy.clickButton('Edit business details');
      cy.clickButton('Cancel');
      cy.shouldNotHaveHeading(2, 'Edit details');
    });

    it('should create stripe customer', () => {
      cy.clickFirstLinkWithinTable(/.*/, 'Table of groups');
      cy.clickButton('1. Create Stripe customer');
      cy.shouldHaveHeading(2, 'Create Stripe customer');
    });

    it('should edit a site config', () => {
      cy.clickFirstLinkWithinTable(/.*/, 'Table of groups');
      cy.clickFirstButtonWithinTable(/edit site/i, 'Table of sites');
      cy.shouldHaveHeading(2, 'Edit site details');
      cy.clickButton('Cancel');
      cy.shouldNotHaveHeading(2, 'Edit site details');
    });

    it('should edit the charger fees for a site', () => {
      cy.clickFirstLinkWithinTable(/.*/, 'Table of groups');
      cy.clickFirstButtonWithinTable(/edit fees/i, 'Table of sites');
      cy.shouldHaveHeading(1, 'Charger Fees');
      cy.shouldNotHaveHeading(2, 'Edit site details');
    });

    it('should add a new group', () => {
      cy.clickButton('Add group');
      cy.shouldHaveHeading(1, 'Add group');
      cy.clickFirstButtonWithinTable('Add', 'Table of groups to add');
      cy.shouldHaveHeading(2, 'Add to the statement service?');
      cy.shouldHaveHeading(3, /.*/);
      // We want the last add button on the page
      cy.clickButton('Add', -1);
      cy.shouldHaveHeading(3, 'Business address');
      cy.shouldHaveHeading(3, 'Business name');
      cy.shouldHaveHeading(3, 'Business email address');
      cy.shouldHaveHeading(3, 'Account reference');
      cy.shouldHaveHeading(2, 'Edit details');
      cy.shouldHaveTable('Table of sites');
    });

    it('should create a subscription', () => {
      cy.clickLink('Test Group Inc. with Stripe Customer');
      cy.clickButton('2. Add a subscription');
      cy.shouldHaveHeading(
        1,
        'Manage chargers on subscription - Test Group Inc. with Stripe Customer'
      );
      cy.shouldHaveTable('Edit chargers');
    });

    it('should edit a subscription', () => {
      cy.clickLink('Test Group Inc. with Stripe Subscription');
      cy.clickButton(/Edit subscription/);
      cy.shouldHaveHeading(
        1,
        'Manage chargers on subscription - Test Group Inc. with Stripe Subscription'
      );
      cy.shouldHaveTable('Edit chargers');
      cy.clickCheckbox('Select All');
      cy.clickButton('Save changes');
    });

    it('should get a list of the groups documents', () => {
      cy.visit('/groups/45c9ef22-b44b-4edd-bb66-392e923950af/documents');
      cy.shouldHaveHeading(1, /Documents/);
      cy.shouldHaveTable('Table of documents');
    });

    it('should upload a document', () => {
      cy.visit('/groups/45c9ef22-b44b-4edd-bb66-392e923950af/documents');
      cy.clickButton('Upload');
      cy.shouldHaveHeading(2, 'Upload document');
      cy.findByLabelText('Name').type('Test Contract Document');
      cy.findByLabelText('Document type').click();
      cy.findByRole('option', { name: 'Contract' }).click();
      cy.findByLabelText('Start date').type('2023-10-01');
      cy.findByLabelText('File').selectFile(
        {
          contents: Cypress.Buffer.from('hello'),
          fileName: 'test-document.pdf',
          mimeType: 'application/pdf',
          lastModified: Date.now(),
        },
        { force: true }
      );
      cy.clickButton('Confirm');
      cy.shouldHaveToast('Uploaded document successfully');
    });
  });
};
