import { Class } from 'ts-toolbelt';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { VersioningOptions } from '@nestjs/common/interfaces/version-options.interface';
import { writeFile } from 'node:fs/promises';
import YAML from 'yaml';

type CreateDocumentParams = Parameters<typeof SwaggerModule.createDocument>;

const logger = new Logger();

export const generateSwaggerDocument = async (
  AppModule: Class.Class,
  getSwaggerDocs: (builder?: DocumentBuilder) => DocumentBuilder,
  filePath = '',
  options?: CreateDocumentParams[2],
  versioningOptions?: VersioningOptions
) => {
  try {
    const app = await NestFactory.create(AppModule);

    if (versioningOptions) {
      app.enableVersioning(versioningOptions);
    }

    const config = getSwaggerDocs(new DocumentBuilder()).build();

    const document = SwaggerModule.createDocument(app, config, options);

    if (filePath) {
      const doc = YAML.stringify(document);

      await writeFile(`${filePath}/openapi3.yaml`, doc);
    }

    process.exit(0);
  } catch (error) {
    logger.error({ error }, 'Failed to generate swagger');

    process.exit(1);
  }
};
