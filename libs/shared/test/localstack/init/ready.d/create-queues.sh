#!/usr/bin/env bash

awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-charge-completions --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-charge-notifications --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-charger-status-updates --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-index-locations --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-push-cdrs --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-push-evse-updates --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-push-locations --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-push-sessions --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-push-tariffs --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-refresh-statuses --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-start-session-commands --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-stop-session-commands --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=ocpi-service-unlock-connector-commands --region=eu-west-1

awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=statement-service-statements-to-generate --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=statement-service-statements-to-send --region=eu-west-1

awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=billing_events --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=migrate-users-events --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=driver-account-api-user-profile-events --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=generate_reports --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=notifications_events --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=payments_events --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=subscriptions-api-incoming-events --region=eu-west-1
awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=rewards-api-incoming-events --region=eu-west-1

awslocal sqs create-queue --endpoint=http://localhost:4566 --queue-name=loyalty-card-service-credit-tesco-clubcard-points --region=eu-west-1
