# PodApi

All URIs are relative to _http://localhost_

| Method                                                                                                    | HTTP request                          | Description |
| --------------------------------------------------------------------------------------------------------- | ------------------------------------- | ----------- |
| [**podControllerFindByGroupId**](#podcontrollerfindbygroupid)                                             | **GET** /pods                         |             |
| [**podControllerFindByGroupIdAndPodId**](#podcontrollerfindbygroupidandpodid)                             | **GET** /pods/{podId}                 |             |
| [**podControllerFindSecurityEventsByGroupIdAndPodId**](#podcontrollerfindsecurityeventsbygroupidandpodid) | **GET** /pods/{podId}/events/security |             |
| [**podControllerGenerateCsv**](#podcontrollergeneratecsv)                                                 | **GET** /pods/{podId}/charges         |             |

# **podControllerFindByGroupId**

> Array<Pod> podControllerFindByGroupId()

### Example

```typescript
import { PodApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new PodApi(configuration);

let groupUid: string; // (default to undefined)

const { status, data } = await apiInstance.podControllerFindByGroupId(groupUid);
```

### Parameters

| Name         | Type         | Description | Notes                 |
| ------------ | ------------ | ----------- | --------------------- |
| **groupUid** | [**string**] |             | defaults to undefined |

### Return type

**Array<Pod>**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
| ----------- | ----------- | ---------------- |
| **200**     |             | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **podControllerFindByGroupIdAndPodId**

> Pod podControllerFindByGroupIdAndPodId()

### Example

```typescript
import { PodApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new PodApi(configuration);

let groupId: number; // (default to undefined)
let podId: string; // (default to undefined)

const { status, data } = await apiInstance.podControllerFindByGroupIdAndPodId(groupId, podId);
```

### Parameters

| Name        | Type         | Description | Notes                 |
| ----------- | ------------ | ----------- | --------------------- |
| **groupId** | [**number**] |             | defaults to undefined |
| **podId**   | [**string**] |             | defaults to undefined |

### Return type

**Pod**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
| ----------- | ----------- | ---------------- |
| **200**     |             | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **podControllerFindSecurityEventsByGroupIdAndPodId**

> object podControllerFindSecurityEventsByGroupIdAndPodId()

### Example

```typescript
import { PodApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new PodApi(configuration);

let podId: string; // (default to undefined)
let groupId: number; // (default to undefined)

const { status, data } = await apiInstance.podControllerFindSecurityEventsByGroupIdAndPodId(podId, groupId);
```

### Parameters

| Name        | Type         | Description | Notes                 |
| ----------- | ------------ | ----------- | --------------------- |
| **podId**   | [**string**] |             | defaults to undefined |
| **groupId** | [**number**] |             | defaults to undefined |

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
| ----------- | ----------- | ---------------- |
| **200**     |             | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **podControllerGenerateCsv**

> podControllerGenerateCsv()

### Example

```typescript
import { PodApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new PodApi(configuration);

let groupId: number; // (default to undefined)
let groupUid: string; // (default to undefined)
let podId: number; // (default to undefined)
let date: string; // (default to undefined)

const { status, data } = await apiInstance.podControllerGenerateCsv(groupId, groupUid, podId, date);
```

### Parameters

| Name         | Type         | Description | Notes                 |
| ------------ | ------------ | ----------- | --------------------- |
| **groupId**  | [**number**] |             | defaults to undefined |
| **groupUid** | [**string**] |             | defaults to undefined |
| **podId**    | [**number**] |             | defaults to undefined |
| **date**     | [**string**] |             | defaults to undefined |

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

### HTTP response details

| Status code | Description | Response headers |
| ----------- | ----------- | ---------------- |
| **200**     |             | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
