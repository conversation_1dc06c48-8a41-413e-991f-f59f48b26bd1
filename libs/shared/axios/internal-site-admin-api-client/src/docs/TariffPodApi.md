# TariffPodApi

All URIs are relative to _http://localhost_

| Method                                                                                              | HTTP request                     | Description |
| --------------------------------------------------------------------------------------------------- | -------------------------------- | ----------- |
| [**tariffPodControllerUpdateByGroupIdAndTariffId**](#tariffpodcontrollerupdatebygroupidandtariffid) | **PUT** /tariffs/{tariffId}/pods |             |

# **tariffPodControllerUpdateByGroupIdAndTariffId**

> tariffPodControllerUpdateByGroupIdAndTariffId(assignTariffPodsRequest)

### Example

```typescript
import { TariffPodApi, Configuration, AssignTariffPodsRequest } from './api';

const configuration = new Configuration();
const apiInstance = new TariffPodApi(configuration);

let groupId: number; // (default to undefined)
let groupUid: string; // (default to undefined)
let tariffId: number; // (default to undefined)
let assignTariffPodsRequest: AssignTariffPodsRequest; //

const { status, data } = await apiInstance.tariffPodControllerUpdateByGroupIdAndTariffId(groupId, groupUid, tariffId, assignTariffPodsRequest);
```

### Parameters

| Name                        | Type                        | Description | Notes                 |
| --------------------------- | --------------------------- | ----------- | --------------------- |
| **assignTariffPodsRequest** | **AssignTariffPodsRequest** |             |                       |
| **groupId**                 | [**number**]                |             | defaults to undefined |
| **groupUid**                | [**string**]                |             | defaults to undefined |
| **tariffId**                | [**number**]                |             | defaults to undefined |

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: Not defined

### HTTP response details

| Status code | Description | Response headers |
| ----------- | ----------- | ---------------- |
| **204**     |             | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
