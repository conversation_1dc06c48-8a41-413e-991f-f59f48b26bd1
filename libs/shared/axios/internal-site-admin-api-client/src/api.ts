/* tslint:disable */
/* eslint-disable */
/**
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface AccountingTotals
 */
export interface AccountingTotals {
  /**
   *
   * @type {number}
   * @memberof AccountingTotals
   */
  gross: number;
  /**
   *
   * @type {number}
   * @memberof AccountingTotals
   */
  net: number;
  /**
   *
   * @type {number}
   * @memberof AccountingTotals
   */
  vat: number;
}
/**
 *
 * @export
 * @interface Address
 */
export interface Address {
  /**
   *
   * @type {string}
   * @memberof Address
   */
  country: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  line1: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  line2: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  postcode: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  prettyPrint: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  town: string;
}
/**
 *
 * @export
 * @interface AdjustedFee
 */
export interface AdjustedFee {
  /**
   *
   * @type {string}
   * @memberof AdjustedFee
   */
  ppid: string;
  /**
   *
   * @type {number}
   * @memberof AdjustedFee
   */
  fee?: number;
}
/**
 *
 * @export
 * @interface Admin
 */
export interface Admin {
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  activatedOn?: string;
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  authId: string;
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  email: string;
  /**
   *
   * @type {boolean}
   * @memberof Admin
   */
  emailBounced: boolean;
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  firstName?: string;
  /**
   *
   * @type {number}
   * @memberof Admin
   */
  groupId?: number;
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  groupName?: string;
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  groupUid?: string;
  /**
   *
   * @type {number}
   * @memberof Admin
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  lastLogin?: string;
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  lastName?: string;
  /**
   *
   * @type {string}
   * @memberof Admin
   */
  status: AdminStatusEnum;
}

export const AdminStatusEnum = {
  Registered: 'Registered',
  Pending: 'Pending',
  Deactivated: 'Deactivated',
} as const;

export type AdminStatusEnum =
  (typeof AdminStatusEnum)[keyof typeof AdminStatusEnum];

/**
 *
 * @export
 * @interface Advert
 */
export interface Advert {
  /**
   *
   * @type {number}
   * @memberof Advert
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof Advert
   */
  image: string;
  /**
   *
   * @type {Array<Pod>}
   * @memberof Advert
   */
  pods: Array<Pod>;
  /**
   *
   * @type {string}
   * @memberof Advert
   */
  url: string;
}
/**
 *
 * @export
 * @interface AssignPodToAdvertRequest
 */
export interface AssignPodToAdvertRequest {
  /**
   *
   * @type {string}
   * @memberof AssignPodToAdvertRequest
   */
  name: string;
}
/**
 *
 * @export
 * @interface AssignTariffPodsRequest
 */
export interface AssignTariffPodsRequest {
  /**
   *
   * @type {Array<object>}
   * @memberof AssignTariffPodsRequest
   */
  podIds: Array<object>;
}
/**
 *
 * @export
 * @interface BillingAccount
 */
export interface BillingAccount {
  /**
   *
   * @type {number}
   * @memberof BillingAccount
   */
  balance: number;
  /**
   *
   * @type {number}
   * @memberof BillingAccount
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof BillingAccount
   */
  uid: string;
}
/**
 *
 * @export
 * @interface BillingInformation
 */
export interface BillingInformation {
  /**
   *
   * @type {CustomerDetails}
   * @memberof BillingInformation
   */
  customerDetails: CustomerDetails;
  /**
   *
   * @type {string}
   * @memberof BillingInformation
   */
  onboardingLink: string;
  /**
   *
   * @type {Array<BillingStatement>}
   * @memberof BillingInformation
   */
  statements: Array<BillingStatement>;
  /**
   *
   * @type {Subscription}
   * @memberof BillingInformation
   */
  subscription: Subscription;
}
/**
 *
 * @export
 * @interface BillingStatement
 */
export interface BillingStatement {
  /**
   *
   * @type {string}
   * @memberof BillingStatement
   */
  siteName: string;
  /**
   *
   * @type {string}
   * @memberof BillingStatement
   */
  month: string;
  /**
   *
   * @type {string}
   * @memberof BillingStatement
   */
  feeInvoiceNumber: string;
  /**
   *
   * @type {string}
   * @memberof BillingStatement
   */
  feeInvoiceStatus: string;
  /**
   *
   * @type {string}
   * @memberof BillingStatement
   */
  hostedInvoiceUrl?: string;
  /**
   *
   * @type {string}
   * @memberof BillingStatement
   */
  invoiceId: string;
  /**
   *
   * @type {string}
   * @memberof BillingStatement
   */
  revenuePayoutStatus: string;
  /**
   *
   * @type {string}
   * @memberof BillingStatement
   */
  statementId: string;
}
/**
 *
 * @export
 * @interface Charge
 */
export interface Charge {
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  chargingDuration: string;
  /**
   *
   * @type {boolean}
   * @memberof Charge
   */
  confirmed: boolean;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  confirmedBy?: ChargeConfirmedByEnum;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  co2Savings: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  door?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  endedAt?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  energyCost: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  energyUsage: string;
  /**
   *
   * @type {number}
   * @memberof Charge
   */
  locationId?: number;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  pluggedIn: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  podName?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  ppid?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  revenueGenerated: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  startedAt: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  siteName?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  totalDuration?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  userEmail?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  userName?: string;
  /**
   *
   * @type {string}
   * @memberof Charge
   */
  vehicle?: string;
}

export const ChargeConfirmedByEnum = {
  Driver: 'driver',
  Other: 'other',
} as const;

export type ChargeConfirmedByEnum =
  (typeof ChargeConfirmedByEnum)[keyof typeof ChargeConfirmedByEnum];

/**
 *
 * @export
 * @interface ChargeSummary
 */
export interface ChargeSummary {
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  chargingDuration: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  claimedEnergyUsage: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  co2Savings: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  energyCost: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  energyDelivered: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  energyUsage: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  numberOfCharges: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  revenueGenerated: number;
  /**
   *
   * @type {number}
   * @memberof ChargeSummary
   */
  revenueGeneratingClaimedUsage: number;
}
/**
 *
 * @export
 * @interface Charger
 */
export interface Charger {
  /**
   *
   * @type {Array<Admin>}
   * @memberof Charger
   */
  admins: Array<Admin>;
  /**
   *
   * @type {BillingInformation}
   * @memberof Charger
   */
  billing?: BillingInformation;
  /**
   *
   * @type {Array<Domain>}
   * @memberof Charger
   */
  domains: Array<Domain>;
  /**
   *
   * @type {Array<Driver>}
   * @memberof Charger
   */
  drivers: Array<Driver>;
  /**
   *
   * @type {Group}
   * @memberof Charger
   */
  group: Group;
  /**
   *
   * @type {string}
   * @memberof Charger
   */
  name?: string;
  /**
   *
   * @type {string}
   * @memberof Charger
   */
  ppid: string;
  /**
   *
   * @type {Array<RfidCard>}
   * @memberof Charger
   */
  rfidCards: Array<RfidCard>;
  /**
   *
   * @type {ChargerSettings}
   * @memberof Charger
   */
  settings: ChargerSettings;
  /**
   *
   * @type {Site}
   * @memberof Charger
   */
  site: Site;
  /**
   *
   * @type {Tariff}
   * @memberof Charger
   */
  tariff: Tariff;
}
/**
 *
 * @export
 * @interface ChargerSettings
 */
export interface ChargerSettings {
  /**
   *
   * @type {boolean}
   * @memberof ChargerSettings
   */
  confirmCharge: boolean;
}
/**
 *
 * @export
 * @interface ConfirmChargeRequest
 */
export interface ConfirmChargeRequest {
  /**
   *
   * @type {string}
   * @memberof ConfirmChargeRequest
   */
  door: string;
  /**
   *
   * @type {string}
   * @memberof ConfirmChargeRequest
   */
  driverEmail: string;
}
/**
 *
 * @export
 * @interface ContactDetailsInterface
 */
export interface ContactDetailsInterface {
  /**
   *
   * @type {string}
   * @memberof ContactDetailsInterface
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof ContactDetailsInterface
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof ContactDetailsInterface
   */
  telephone: string;
}
/**
 *
 * @export
 * @interface Coordinates
 */
export interface Coordinates {
  /**
   *
   * @type {number}
   * @memberof Coordinates
   */
  latitude?: number;
  /**
   *
   * @type {number}
   * @memberof Coordinates
   */
  longitude?: number;
}
/**
 *
 * @export
 * @interface CreateAdminRequest
 */
export interface CreateAdminRequest {
  /**
   *
   * @type {string}
   * @memberof CreateAdminRequest
   */
  email: string;
}
/**
 *
 * @export
 * @interface CreateDayNightTariffScheduleRequest
 */
export interface CreateDayNightTariffScheduleRequest {
  /**
   *
   * @type {string}
   * @memberof CreateDayNightTariffScheduleRequest
   */
  dayPrice: string;
  /**
   *
   * @type {string}
   * @memberof CreateDayNightTariffScheduleRequest
   */
  dayStartTime: string;
  /**
   *
   * @type {string}
   * @memberof CreateDayNightTariffScheduleRequest
   */
  nightPrice: string;
  /**
   *
   * @type {string}
   * @memberof CreateDayNightTariffScheduleRequest
   */
  nightStartTime: string;
  /**
   *
   * @type {string}
   * @memberof CreateDayNightTariffScheduleRequest
   */
  tariffTier: CreateDayNightTariffScheduleRequestTariffTierEnum;
}

export const CreateDayNightTariffScheduleRequestTariffTierEnum = {
  Public: 'public',
  Members: 'members',
  Drivers: 'drivers',
} as const;

export type CreateDayNightTariffScheduleRequestTariffTierEnum =
  (typeof CreateDayNightTariffScheduleRequestTariffTierEnum)[keyof typeof CreateDayNightTariffScheduleRequestTariffTierEnum];

/**
 *
 * @export
 * @interface CreateDomainRequest
 */
export interface CreateDomainRequest {
  /**
   *
   * @type {string}
   * @memberof CreateDomainRequest
   */
  domainName: string;
}
/**
 *
 * @export
 * @interface CreateDriverRequest
 */
export interface CreateDriverRequest {
  /**
   *
   * @type {string}
   * @memberof CreateDriverRequest
   */
  canExpense: CreateDriverRequestCanExpenseEnum;
  /**
   *
   * @type {string}
   * @memberof CreateDriverRequest
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof CreateDriverRequest
   */
  firstName: string;
  /**
   *
   * @type {string}
   * @memberof CreateDriverRequest
   */
  lastName: string;
  /**
   *
   * @type {string}
   * @memberof CreateDriverRequest
   */
  tariffTier: CreateDriverRequestTariffTierEnum;
}

export const CreateDriverRequestCanExpenseEnum = {
  True: 'true',
  False: 'false',
  Yes: 'Yes',
  No: 'No',
} as const;

export type CreateDriverRequestCanExpenseEnum =
  (typeof CreateDriverRequestCanExpenseEnum)[keyof typeof CreateDriverRequestCanExpenseEnum];
export const CreateDriverRequestTariffTierEnum = {
  Driver: 'Driver',
  Member: 'Member',
} as const;

export type CreateDriverRequestTariffTierEnum =
  (typeof CreateDriverRequestTariffTierEnum)[keyof typeof CreateDriverRequestTariffTierEnum];

/**
 *
 * @export
 * @interface CreateRfidCardRequest
 */
export interface CreateRfidCardRequest {
  /**
   *
   * @type {string}
   * @memberof CreateRfidCardRequest
   */
  reference: string;
  /**
   *
   * @type {string}
   * @memberof CreateRfidCardRequest
   */
  tag: string;
  /**
   *
   * @type {string}
   * @memberof CreateRfidCardRequest
   */
  uid: string;
}
/**
 *
 * @export
 * @interface CreateSiteStatementRequest
 */
export interface CreateSiteStatementRequest {
  /**
   *
   * @type {Array<AdjustedFee>}
   * @memberof CreateSiteStatementRequest
   */
  adjustedFees: Array<AdjustedFee>;
}
/**
 *
 * @export
 * @interface CreateTariffRequest
 */
export interface CreateTariffRequest {
  /**
   *
   * @type {string}
   * @memberof CreateTariffRequest
   */
  tariffName: string;
}
/**
 *
 * @export
 * @interface CreateTariffScheduleRequest
 */
export interface CreateTariffScheduleRequest {
  /**
   *
   * @type {string}
   * @memberof CreateTariffScheduleRequest
   */
  pricingModel: CreateTariffScheduleRequestPricingModelEnum;
  /**
   *
   * @type {string}
   * @memberof CreateTariffScheduleRequest
   */
  tariffTier: CreateTariffScheduleRequestTariffTierEnum;
  /**
   *
   * @type {number}
   * @memberof CreateTariffScheduleRequest
   */
  endDay: number;
  /**
   *
   * @type {string}
   * @memberof CreateTariffScheduleRequest
   */
  endTime: string;
  /**
   *
   * @type {string}
   * @memberof CreateTariffScheduleRequest
   */
  price: string;
  /**
   *
   * @type {number}
   * @memberof CreateTariffScheduleRequest
   */
  startDay: number;
  /**
   *
   * @type {number}
   * @memberof CreateTariffScheduleRequest
   */
  startSecond?: number;
  /**
   *
   * @type {string}
   * @memberof CreateTariffScheduleRequest
   */
  startTime: string;
}

export const CreateTariffScheduleRequestPricingModelEnum = {
  Duration: 'duration',
  Energy: 'energy',
  Fixed: 'fixed',
} as const;

export type CreateTariffScheduleRequestPricingModelEnum =
  (typeof CreateTariffScheduleRequestPricingModelEnum)[keyof typeof CreateTariffScheduleRequestPricingModelEnum];
export const CreateTariffScheduleRequestTariffTierEnum = {
  Public: 'public',
  Members: 'members',
  Drivers: 'drivers',
} as const;

export type CreateTariffScheduleRequestTariffTierEnum =
  (typeof CreateTariffScheduleRequestTariffTierEnum)[keyof typeof CreateTariffScheduleRequestTariffTierEnum];

/**
 *
 * @export
 * @interface CreateWorkWeekTariffScheduleRequest
 */
export interface CreateWorkWeekTariffScheduleRequest {
  /**
   *
   * @type {string}
   * @memberof CreateWorkWeekTariffScheduleRequest
   */
  weekdayPrice: string;
  /**
   *
   * @type {string}
   * @memberof CreateWorkWeekTariffScheduleRequest
   */
  weekendPrice: string;
  /**
   *
   * @type {string}
   * @memberof CreateWorkWeekTariffScheduleRequest
   */
  tariffTier: CreateWorkWeekTariffScheduleRequestTariffTierEnum;
}

export const CreateWorkWeekTariffScheduleRequestTariffTierEnum = {
  Public: 'public',
  Members: 'members',
  Drivers: 'drivers',
} as const;

export type CreateWorkWeekTariffScheduleRequestTariffTierEnum =
  (typeof CreateWorkWeekTariffScheduleRequestTariffTierEnum)[keyof typeof CreateWorkWeekTariffScheduleRequestTariffTierEnum];

/**
 *
 * @export
 * @interface CustomerDetails
 */
export interface CustomerDetails {
  /**
   *
   * @type {CustomerDetailsAddress}
   * @memberof CustomerDetails
   */
  address?: CustomerDetailsAddress;
  /**
   *
   * @type {string}
   * @memberof CustomerDetails
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof CustomerDetails
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof CustomerDetails
   */
  accountReference?: string;
  /**
   *
   * @type {string}
   * @memberof CustomerDetails
   */
  poNumber?: string;
}
/**
 *
 * @export
 * @interface CustomerDetailsAddress
 */
export interface CustomerDetailsAddress {
  /**
   *
   * @type {string}
   * @memberof CustomerDetailsAddress
   */
  line1?: string;
  /**
   *
   * @type {string}
   * @memberof CustomerDetailsAddress
   */
  line2?: string;
  /**
   *
   * @type {string}
   * @memberof CustomerDetailsAddress
   */
  town?: string;
  /**
   *
   * @type {string}
   * @memberof CustomerDetailsAddress
   */
  county?: string;
  /**
   *
   * @type {string}
   * @memberof CustomerDetailsAddress
   */
  postcode?: string;
}
/**
 *
 * @export
 * @interface DeleteDriverRequest
 */
export interface DeleteDriverRequest {
  /**
   *
   * @type {string}
   * @memberof DeleteDriverRequest
   */
  tariffTier: DeleteDriverRequestTariffTierEnum;
}

export const DeleteDriverRequestTariffTierEnum = {
  Driver: 'Driver',
  Member: 'Member',
} as const;

export type DeleteDriverRequestTariffTierEnum =
  (typeof DeleteDriverRequestTariffTierEnum)[keyof typeof DeleteDriverRequestTariffTierEnum];

/**
 *
 * @export
 * @interface DeleteTariffScheduleRequest
 */
export interface DeleteTariffScheduleRequest {
  /**
   *
   * @type {Array<number>}
   * @memberof DeleteTariffScheduleRequest
   */
  scheduleIds: Array<number>;
}
/**
 *
 * @export
 * @interface Domain
 */
export interface Domain {
  /**
   *
   * @type {string}
   * @memberof Domain
   */
  activatedOn: string;
  /**
   *
   * @type {string}
   * @memberof Domain
   */
  domainName: string;
  /**
   *
   * @type {number}
   * @memberof Domain
   */
  id: number;
  /**
   *
   * @type {number}
   * @memberof Domain
   */
  groupId: number;
}
/**
 *
 * @export
 * @interface Driver
 */
export interface Driver {
  /**
   *
   * @type {boolean}
   * @memberof Driver
   */
  canExpense: boolean;
  /**
   *
   * @type {DriverCharges}
   * @memberof Driver
   */
  chargingStats?: DriverCharges;
  /**
   *
   * @type {string}
   * @memberof Driver
   */
  email: string;
  /**
   *
   * @type {boolean}
   * @memberof Driver
   */
  emailBounced: boolean;
  /**
   *
   * @type {string}
   * @memberof Driver
   */
  firstName: string;
  /**
   *
   * @type {string}
   * @memberof Driver
   */
  fullName: string;
  /**
   *
   * @type {Group}
   * @memberof Driver
   */
  group?: Group;
  /**
   *
   * @type {number}
   * @memberof Driver
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof Driver
   */
  lastName: string;
  /**
   *
   * @type {string}
   * @memberof Driver
   */
  registeredDate: string;
  /**
   *
   * @type {string}
   * @memberof Driver
   */
  status: DriverStatusEnum;
  /**
   *
   * @type {string}
   * @memberof Driver
   */
  tariffTier: DriverTariffTierEnum;
}

export const DriverStatusEnum = {
  Deleted: 'Deleted',
  Deactivated: 'Deactivated',
  Pending: 'Pending',
  Registered: 'Registered',
} as const;

export type DriverStatusEnum =
  (typeof DriverStatusEnum)[keyof typeof DriverStatusEnum];
export const DriverTariffTierEnum = {
  Driver: 'Driver',
  Member: 'Member',
} as const;

export type DriverTariffTierEnum =
  (typeof DriverTariffTierEnum)[keyof typeof DriverTariffTierEnum];

/**
 *
 * @export
 * @interface DriverCharges
 */
export interface DriverCharges {
  /**
   *
   * @type {Array<Charge>}
   * @memberof DriverCharges
   */
  charges: Array<Charge>;
  /**
   *
   * @type {string}
   * @memberof DriverCharges
   */
  co2Avoided: string;
  /**
   *
   * @type {string}
   * @memberof DriverCharges
   */
  energyCost: string;
  /**
   *
   * @type {string}
   * @memberof DriverCharges
   */
  energyDelivered: string;
  /**
   *
   * @type {string}
   * @memberof DriverCharges
   */
  revenue: string;
}
/**
 *
 * @export
 * @interface Group
 */
export interface Group {
  /**
   *
   * @type {string}
   * @memberof Group
   */
  activatedOn?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  contactName?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  contactNumber?: string;
  /**
   *
   * @type {number}
   * @memberof Group
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  name: string;
  /**
   *
   * @type {boolean}
   * @memberof Group
   */
  readOnly?: boolean;
  /**
   *
   * @type {GroupChargeSummary}
   * @memberof Group
   */
  stats?: GroupChargeSummary | null;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  type?: string;
  /**
   *
   * @type {string}
   * @memberof Group
   */
  uid: string;
}
/**
 *
 * @export
 * @interface GroupChargeSummary
 */
export interface GroupChargeSummary {
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  chargingDuration: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  co2Savings: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  energyCost: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  energyDelivered: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  numberOfChargers: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  numberOfCharges: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  numberOfDrivers: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  numberOfSites: number;
  /**
   *
   * @type {number}
   * @memberof GroupChargeSummary
   */
  revenueGenerated: number;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface Insight
 */
export interface Insight {
  /**
   *
   * @type {number}
   * @memberof Insight
   */
  co2Savings: number;
  /**
   *
   * @type {number}
   * @memberof Insight
   */
  cost: number;
  /**
   *
   * @type {string}
   * @memberof Insight
   */
  intervalStartDate: string;
  /**
   *
   * @type {number}
   * @memberof Insight
   */
  revenueGenerated: number;
  /**
   *
   * @type {number}
   * @memberof Insight
   */
  totalUsage: number;
}
/**
 *
 * @export
 * @interface InviteDriverRequest
 */
export interface InviteDriverRequest {
  /**
   *
   * @type {string}
   * @memberof InviteDriverRequest
   */
  tariffTier: InviteDriverRequestTariffTierEnum;
}

export const InviteDriverRequestTariffTierEnum = {
  Driver: 'Driver',
  Member: 'Member',
} as const;

export type InviteDriverRequestTariffTierEnum =
  (typeof InviteDriverRequestTariffTierEnum)[keyof typeof InviteDriverRequestTariffTierEnum];

/**
 *
 * @export
 * @interface OpeningTimeWithDay
 */
export interface OpeningTimeWithDay {
  /**
   *
   * @type {boolean}
   * @memberof OpeningTimeWithDay
   */
  allDay: boolean;
  /**
   *
   * @type {string}
   * @memberof OpeningTimeWithDay
   */
  day: OpeningTimeWithDayDayEnum;
  /**
   *
   * @type {string}
   * @memberof OpeningTimeWithDay
   */
  from: string;
  /**
   *
   * @type {string}
   * @memberof OpeningTimeWithDay
   */
  to: string;
}

export const OpeningTimeWithDayDayEnum = {
  Mon: 'mon',
  Tue: 'tue',
  Wed: 'wed',
  Thu: 'thu',
  Fri: 'fri',
  Sat: 'sat',
  Sun: 'sun',
} as const;

export type OpeningTimeWithDayDayEnum =
  (typeof OpeningTimeWithDayDayEnum)[keyof typeof OpeningTimeWithDayDayEnum];

/**
 *
 * @export
 * @interface OpeningTimes
 */
export interface OpeningTimes {
  /**
   *
   * @type {Array<string>}
   * @memberof OpeningTimes
   */
  notes: Array<string>;
}
/**
 *
 * @export
 * @interface Parking
 */
export interface Parking {
  /**
   *
   * @type {OpeningTimes}
   * @memberof Parking
   */
  openingTimes: OpeningTimes;
}
/**
 *
 * @export
 * @interface Pod
 */
export interface Pod {
  /**
   *
   * @type {number}
   * @memberof Pod
   */
  ageYears?: number;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  confirmChargeEnabled: boolean;
  /**
   *
   * @type {ChargeSummary}
   * @memberof Pod
   */
  chargeSummary?: ChargeSummary;
  /**
   *
   * @type {Coordinates}
   * @memberof Pod
   */
  coordinates: Coordinates;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  description: string;
  /**
   *
   * @type {number}
   * @memberof Pod
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  installDate?: string;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  isEvZone: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  isPublic: boolean;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  lastContact?: string;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  model: string;
  /**
   *
   * @type {Charge}
   * @memberof Pod
   */
  mostRecentCharge?: Charge;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  name?: string;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  ppid: string;
  /**
   *
   * @type {Array<Charge>}
   * @memberof Pod
   */
  recentCharges?: Array<Charge>;
  /**
   *
   * @type {Array<Schedule>}
   * @memberof Pod
   */
  schedules?: Array<Schedule>;
  /**
   *
   * @type {Array<Group>}
   * @memberof Pod
   */
  schemes: Array<Group>;
  /**
   *
   * @type {Site}
   * @memberof Pod
   */
  site?: Site;
  /**
   *
   * @type {Array<Socket>}
   * @memberof Pod
   */
  sockets: Array<Socket>;
  /**
   *
   * @type {string}
   * @memberof Pod
   */
  status: string;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsConfirmCharge?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsContactless: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsEnergyTariff: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsOcpp?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsPerKwh: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsRfid?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof Pod
   */
  supportsTariffs: boolean;
  /**
   *
   * @type {TariffSummary}
   * @memberof Pod
   */
  tariff?: TariffSummary;
}
/**
 *
 * @export
 * @interface RfidCard
 */
export interface RfidCard {
  /**
   *
   * @type {boolean}
   * @memberof RfidCard
   */
  active: boolean;
  /**
   *
   * @type {string}
   * @memberof RfidCard
   */
  reference: string;
  /**
   *
   * @type {Array<string>}
   * @memberof RfidCard
   */
  tags: Array<string>;
  /**
   *
   * @type {string}
   * @memberof RfidCard
   */
  uid: string;
}
/**
 *
 * @export
 * @interface Schedule
 */
export interface Schedule {
  /**
   *
   * @type {number}
   * @memberof Schedule
   */
  endDay: number;
  /**
   *
   * @type {string}
   * @memberof Schedule
   */
  endTime: string;
  /**
   *
   * @type {boolean}
   * @memberof Schedule
   */
  isActive: boolean;
  /**
   *
   * @type {number}
   * @memberof Schedule
   */
  startDay: number;
  /**
   *
   * @type {string}
   * @memberof Schedule
   */
  startTime: string;
}
/**
 *
 * @export
 * @interface SendPasswordResetRequest
 */
export interface SendPasswordResetRequest {
  /**
   *
   * @type {string}
   * @memberof SendPasswordResetRequest
   */
  email: string;
}
/**
 *
 * @export
 * @interface SendSignInWithEmailRequest
 */
export interface SendSignInWithEmailRequest {
  /**
   *
   * @type {string}
   * @memberof SendSignInWithEmailRequest
   */
  email: string;
}
/**
 *
 * @export
 * @interface Site
 */
export interface Site {
  /**
   *
   * @type {Address}
   * @memberof Site
   */
  address: Address;
  /**
   *
   * @type {ChargeSummary}
   * @memberof Site
   */
  chargeSummary?: ChargeSummary;
  /**
   *
   * @type {ContactDetailsInterface}
   * @memberof Site
   */
  contactDetails: ContactDetailsInterface;
  /**
   *
   * @type {string}
   * @memberof Site
   */
  description: string;
  /**
   *
   * @type {number}
   * @memberof Site
   */
  energyCost?: number;
  /**
   *
   * @type {Group}
   * @memberof Site
   */
  group: Group;
  /**
   *
   * @type {number}
   * @memberof Site
   */
  id: number;
  /**
   *
   * @type {Parking}
   * @memberof Site
   */
  parking: Parking;
  /**
   *
   * @type {Array<Pod>}
   * @memberof Site
   */
  pods?: Array<Pod>;
}
/**
 *
 * @export
 * @interface SiteStatement
 */
export interface SiteStatement {
  /**
   *
   * @type {Array<SiteSubStatement>}
   * @memberof SiteStatement
   */
  breakdown?: Array<SiteSubStatement>;
  /**
   *
   * @type {string}
   * @memberof SiteStatement
   */
  date: string;
  /**
   *
   * @type {string}
   * @memberof SiteStatement
   */
  groupName: string;
  /**
   *
   * @type {string}
   * @memberof SiteStatement
   */
  reference: string;
  /**
   *
   * @type {string}
   * @memberof SiteStatement
   */
  siteAddress: string;
  /**
   *
   * @type {string}
   * @memberof SiteStatement
   */
  siteName: string;
  /**
   *
   * @type {number}
   * @memberof SiteStatement
   */
  claimedEnergyDelivered: number;
  /**
   *
   * @type {number}
   * @memberof SiteStatement
   */
  energyDelivered: number;
  /**
   *
   * @type {AccountingTotals}
   * @memberof SiteStatement
   */
  fees: AccountingTotals;
  /**
   *
   * @type {number}
   * @memberof SiteStatement
   */
  numberOfCharges: number;
  /**
   *
   * @type {number}
   * @memberof SiteStatement
   */
  paidEnergyDelivered: number;
  /**
   *
   * @type {AccountingTotals}
   * @memberof SiteStatement
   */
  revenue: AccountingTotals;
}
/**
 *
 * @export
 * @interface SiteSubStatement
 */
export interface SiteSubStatement {
  /**
   *
   * @type {string}
   * @memberof SiteSubStatement
   */
  date: string;
  /**
   *
   * @type {string}
   * @memberof SiteSubStatement
   */
  groupName: string;
  /**
   *
   * @type {string}
   * @memberof SiteSubStatement
   */
  reference: string;
  /**
   *
   * @type {string}
   * @memberof SiteSubStatement
   */
  siteAddress: string;
  /**
   *
   * @type {string}
   * @memberof SiteSubStatement
   */
  siteName: string;
  /**
   *
   * @type {number}
   * @memberof SiteSubStatement
   */
  claimedEnergyDelivered: number;
  /**
   *
   * @type {number}
   * @memberof SiteSubStatement
   */
  energyDelivered: number;
  /**
   *
   * @type {AccountingTotals}
   * @memberof SiteSubStatement
   */
  fees: AccountingTotals;
  /**
   *
   * @type {number}
   * @memberof SiteSubStatement
   */
  numberOfCharges: number;
  /**
   *
   * @type {number}
   * @memberof SiteSubStatement
   */
  paidEnergyDelivered: number;
  /**
   *
   * @type {AccountingTotals}
   * @memberof SiteSubStatement
   */
  revenue: AccountingTotals;
}
/**
 *
 * @export
 * @interface Socket
 */
export interface Socket {
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  door: string;
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  firmwareVersion: string;
  /**
   *
   * @type {boolean}
   * @memberof Socket
   */
  isUpdateAvailable: boolean;
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  lastContact?: string;
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  serialNumber: string;
  /**
   *
   * @type {string}
   * @memberof Socket
   */
  status: SocketStatusEnum;
}

export const SocketStatusEnum = {
  Available: 'Available',
  Charging: 'Charging',
  Offline: 'Offline',
  Unavailable: 'Unavailable',
} as const;

export type SocketStatusEnum =
  (typeof SocketStatusEnum)[keyof typeof SocketStatusEnum];

/**
 *
 * @export
 * @interface Subscription
 */
export interface Subscription {
  /**
   *
   * @type {Array<SubscriptionCharger>}
   * @memberof Subscription
   */
  chargers: Array<SubscriptionCharger>;
  /**
   *
   * @type {string}
   * @memberof Subscription
   */
  status: string;
  /**
   *
   * @type {Array<SubscriptionInvoice>}
   * @memberof Subscription
   */
  invoices: Array<SubscriptionInvoice>;
}
/**
 *
 * @export
 * @interface SubscriptionCharger
 */
export interface SubscriptionCharger {
  /**
   *
   * @type {string}
   * @memberof SubscriptionCharger
   */
  ppid: string;
  /**
   *
   * @type {object}
   * @memberof SubscriptionCharger
   */
  socket: object;
}
/**
 *
 * @export
 * @interface SubscriptionInvoice
 */
export interface SubscriptionInvoice {
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoice
   */
  status: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoice
   */
  created: string;
  /**
   *
   * @type {number}
   * @memberof SubscriptionInvoice
   */
  amount: number;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoice
   */
  due: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoice
   */
  hostedInvoiceUrl: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoice
   */
  invoiceNumber: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoice
   */
  invoicePdfUrl: string;
  /**
   *
   * @type {string}
   * @memberof SubscriptionInvoice
   */
  customerEmail: string;
}
/**
 *
 * @export
 * @interface Tariff
 */
export interface Tariff {
  /**
   *
   * @type {number}
   * @memberof Tariff
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  currency: string;
  /**
   *
   * @type {Array<Pod>}
   * @memberof Tariff
   */
  pods: Array<Pod>;
  /**
   *
   * @type {TariffScheduleTiers}
   * @memberof Tariff
   */
  schedule?: TariffScheduleTiers;
}
/**
 *
 * @export
 * @interface TariffSchedule
 */
export interface TariffSchedule {
  /**
   *
   * @type {number}
   * @memberof TariffSchedule
   */
  id: number;
  /**
   *
   * @type {Array<TariffScheduleBand>}
   * @memberof TariffSchedule
   */
  bands: Array<TariffScheduleBand>;
  /**
   *
   * @type {number}
   * @memberof TariffSchedule
   */
  endDay: number;
  /**
   *
   * @type {string}
   * @memberof TariffSchedule
   */
  endTime: string;
  /**
   *
   * @type {string}
   * @memberof TariffSchedule
   */
  pricingModel: TariffSchedulePricingModelEnum;
  /**
   *
   * @type {number}
   * @memberof TariffSchedule
   */
  startDay: number;
  /**
   *
   * @type {string}
   * @memberof TariffSchedule
   */
  startTime: string;
  /**
   *
   * @type {string}
   * @memberof TariffSchedule
   */
  tariffTier: TariffScheduleTariffTierEnum;
}

export const TariffSchedulePricingModelEnum = {
  Duration: 'duration',
  Energy: 'energy',
  Fixed: 'fixed',
} as const;

export type TariffSchedulePricingModelEnum =
  (typeof TariffSchedulePricingModelEnum)[keyof typeof TariffSchedulePricingModelEnum];
export const TariffScheduleTariffTierEnum = {
  Public: 'public',
  Members: 'members',
  Drivers: 'drivers',
} as const;

export type TariffScheduleTariffTierEnum =
  (typeof TariffScheduleTariffTierEnum)[keyof typeof TariffScheduleTariffTierEnum];

/**
 *
 * @export
 * @interface TariffScheduleBand
 */
export interface TariffScheduleBand {
  /**
   *
   * @type {number}
   * @memberof TariffScheduleBand
   */
  id: number;
  /**
   *
   * @type {number}
   * @memberof TariffScheduleBand
   */
  after: number;
  /**
   *
   * @type {number}
   * @memberof TariffScheduleBand
   */
  cost: number;
  /**
   *
   * @type {string}
   * @memberof TariffScheduleBand
   */
  prettyPrint: string;
}
/**
 *
 * @export
 * @interface TariffScheduleTiers
 */
export interface TariffScheduleTiers {
  /**
   *
   * @type {Array<TariffSchedule>}
   * @memberof TariffScheduleTiers
   */
  drivers?: Array<TariffSchedule>;
  /**
   *
   * @type {Array<TariffSchedule>}
   * @memberof TariffScheduleTiers
   */
  members?: Array<TariffSchedule>;
  /**
   *
   * @type {Array<TariffSchedule>}
   * @memberof TariffScheduleTiers
   */
  public?: Array<TariffSchedule>;
}
/**
 *
 * @export
 * @interface TariffSummary
 */
export interface TariffSummary {
  /**
   *
   * @type {number}
   * @memberof TariffSummary
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof TariffSummary
   */
  name: string;
}
/**
 *
 * @export
 * @interface UpdateAdditionalInfoRequest
 */
export interface UpdateAdditionalInfoRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateAdditionalInfoRequest
   */
  additionalInfo?: string;
}
/**
 *
 * @export
 * @interface UpdateContactDetailsRequest
 */
export interface UpdateContactDetailsRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateContactDetailsRequest
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof UpdateContactDetailsRequest
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof UpdateContactDetailsRequest
   */
  telephone: string;
}
/**
 *
 * @export
 * @interface UpdateDriverRequest
 */
export interface UpdateDriverRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateDriverRequest
   */
  canExpense: UpdateDriverRequestCanExpenseEnum;
  /**
   *
   * @type {string}
   * @memberof UpdateDriverRequest
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof UpdateDriverRequest
   */
  firstName: string;
  /**
   *
   * @type {string}
   * @memberof UpdateDriverRequest
   */
  lastName: string;
  /**
   *
   * @type {string}
   * @memberof UpdateDriverRequest
   */
  tariffTier: UpdateDriverRequestTariffTierEnum;
  /**
   *
   * @type {string}
   * @memberof UpdateDriverRequest
   */
  originalTariffTier?: UpdateDriverRequestOriginalTariffTierEnum;
}

export const UpdateDriverRequestCanExpenseEnum = {
  True: 'true',
  False: 'false',
  Yes: 'Yes',
  No: 'No',
} as const;

export type UpdateDriverRequestCanExpenseEnum =
  (typeof UpdateDriverRequestCanExpenseEnum)[keyof typeof UpdateDriverRequestCanExpenseEnum];
export const UpdateDriverRequestTariffTierEnum = {
  Driver: 'Driver',
  Member: 'Member',
} as const;

export type UpdateDriverRequestTariffTierEnum =
  (typeof UpdateDriverRequestTariffTierEnum)[keyof typeof UpdateDriverRequestTariffTierEnum];
export const UpdateDriverRequestOriginalTariffTierEnum = {
  Driver: 'Driver',
  Member: 'Member',
} as const;

export type UpdateDriverRequestOriginalTariffTierEnum =
  (typeof UpdateDriverRequestOriginalTariffTierEnum)[keyof typeof UpdateDriverRequestOriginalTariffTierEnum];

/**
 *
 * @export
 * @interface UpdateEnergyCostRequest
 */
export interface UpdateEnergyCostRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateEnergyCostRequest
   */
  energyCost: string;
}
/**
 *
 * @export
 * @interface UpdateRfidCardRequest
 */
export interface UpdateRfidCardRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateRfidCardRequest
   */
  tag: string;
}
/**
 *
 * @export
 * @interface UpdateTariffRequest
 */
export interface UpdateTariffRequest {
  /**
   *
   * @type {number}
   * @memberof UpdateTariffRequest
   */
  id: number;
}
/**
 *
 * @export
 * @interface UpdateUserRequest
 */
export interface UpdateUserRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateUserRequest
   */
  firstName: string;
  /**
   *
   * @type {string}
   * @memberof UpdateUserRequest
   */
  lastName: string;
}
/**
 *
 * @export
 * @interface UpsertOpeningTimesRequest
 */
export interface UpsertOpeningTimesRequest {
  /**
   *
   * @type {Array<OpeningTimeWithDay>}
   * @memberof UpsertOpeningTimesRequest
   */
  openingTimes: Array<OpeningTimeWithDay>;
}
/**
 *
 * @export
 * @interface User
 */
export interface User {
  /**
   *
   * @type {string}
   * @memberof User
   */
  activatedOn?: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  authId: string;
  /**
   *
   * @type {BillingAccount}
   * @memberof User
   */
  billingAccount?: BillingAccount;
  /**
   *
   * @type {string}
   * @memberof User
   */
  email: string;
  /**
   *
   * @type {boolean}
   * @memberof User
   */
  emailVerified: boolean;
  /**
   *
   * @type {string}
   * @memberof User
   */
  firstName?: string;
  /**
   *
   * @type {number}
   * @memberof User
   */
  groupId?: number;
  /**
   *
   * @type {string}
   * @memberof User
   */
  groupName?: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  groupUid?: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  groupType?: UserGroupTypeEnum;
  /**
   *
   * @type {number}
   * @memberof User
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof User
   */
  lastName?: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  status: UserStatusEnum;
  /**
   *
   * @type {object}
   * @memberof User
   */
  termsAndConditions: object;
}

export const UserGroupTypeEnum = {
  PodPoint: 'podPoint',
  Host: 'host',
  Scheme: 'scheme',
  Manufacturer: 'manufacturer',
} as const;

export type UserGroupTypeEnum =
  (typeof UserGroupTypeEnum)[keyof typeof UserGroupTypeEnum];
export const UserStatusEnum = {
  Activated: 'Activated',
  Deactivated: 'Deactivated',
} as const;

export type UserStatusEnum =
  (typeof UserStatusEnum)[keyof typeof UserStatusEnum];

/**
 * AdditionalInfoApi - axios parameter creator
 * @export
 */
export const AdditionalInfoApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateAdditionalInfoRequest} updateAdditionalInfoRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    additionalInfoControllerUpsertByGroupUid: async (
      groupUid: string,
      siteId: number,
      updateAdditionalInfoRequest: UpdateAdditionalInfoRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'additionalInfoControllerUpsertByGroupUid',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'additionalInfoControllerUpsertByGroupUid',
        'siteId',
        siteId
      );
      // verify required parameter 'updateAdditionalInfoRequest' is not null or undefined
      assertParamExists(
        'additionalInfoControllerUpsertByGroupUid',
        'updateAdditionalInfoRequest',
        updateAdditionalInfoRequest
      );
      const localVarPath = `/sites/{siteId}/additional-information`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateAdditionalInfoRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AdditionalInfoApi - functional programming interface
 * @export
 */
export const AdditionalInfoApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    AdditionalInfoApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateAdditionalInfoRequest} updateAdditionalInfoRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async additionalInfoControllerUpsertByGroupUid(
      groupUid: string,
      siteId: number,
      updateAdditionalInfoRequest: UpdateAdditionalInfoRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.additionalInfoControllerUpsertByGroupUid(
          groupUid,
          siteId,
          updateAdditionalInfoRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AdditionalInfoApi.additionalInfoControllerUpsertByGroupUid'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AdditionalInfoApi - factory interface
 * @export
 */
export const AdditionalInfoApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AdditionalInfoApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateAdditionalInfoRequest} updateAdditionalInfoRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    additionalInfoControllerUpsertByGroupUid(
      groupUid: string,
      siteId: number,
      updateAdditionalInfoRequest: UpdateAdditionalInfoRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .additionalInfoControllerUpsertByGroupUid(
          groupUid,
          siteId,
          updateAdditionalInfoRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AdditionalInfoApi - object-oriented interface
 * @export
 * @class AdditionalInfoApi
 * @extends {BaseAPI}
 */
export class AdditionalInfoApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {UpdateAdditionalInfoRequest} updateAdditionalInfoRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdditionalInfoApi
   */
  public additionalInfoControllerUpsertByGroupUid(
    groupUid: string,
    siteId: number,
    updateAdditionalInfoRequest: UpdateAdditionalInfoRequest,
    options?: RawAxiosRequestConfig
  ) {
    return AdditionalInfoApiFp(this.configuration)
      .additionalInfoControllerUpsertByGroupUid(
        groupUid,
        siteId,
        updateAdditionalInfoRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * AdminApi - axios parameter creator
 * @export
 */
export const AdminApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} adminUid
     * @param {CreateAdminRequest} createAdminRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerCreateByGroupId: async (
      groupId: number,
      adminUid: string,
      createAdminRequest: CreateAdminRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('adminControllerCreateByGroupId', 'groupId', groupId);
      // verify required parameter 'adminUid' is not null or undefined
      assertParamExists('adminControllerCreateByGroupId', 'adminUid', adminUid);
      // verify required parameter 'createAdminRequest' is not null or undefined
      assertParamExists(
        'adminControllerCreateByGroupId',
        'createAdminRequest',
        createAdminRequest
      );
      const localVarPath = `/admins`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (adminUid !== undefined) {
        localVarQueryParameter['adminUid'] = adminUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createAdminRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {number} adminId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerDeleteByGroupId: async (
      id: number,
      groupId: number,
      adminId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('adminControllerDeleteByGroupId', 'id', id);
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('adminControllerDeleteByGroupId', 'groupId', groupId);
      // verify required parameter 'adminId' is not null or undefined
      assertParamExists('adminControllerDeleteByGroupId', 'adminId', adminId);
      const localVarPath = `/admins/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (adminId !== undefined) {
        localVarQueryParameter['adminId'] = adminId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerFindByGroupId: async (
      groupId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('adminControllerFindByGroupId', 'groupId', groupId);
      const localVarPath = `/admins`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerInviteByGroupIdAndDriverId: async (
      id: number,
      groupId: number,
      adminUid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('adminControllerInviteByGroupIdAndDriverId', 'id', id);
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'adminControllerInviteByGroupIdAndDriverId',
        'groupId',
        groupId
      );
      // verify required parameter 'adminUid' is not null or undefined
      assertParamExists(
        'adminControllerInviteByGroupIdAndDriverId',
        'adminUid',
        adminUid
      );
      const localVarPath = `/admins/{id}/invitation`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (adminUid !== undefined) {
        localVarQueryParameter['adminUid'] = adminUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {number} adminId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerReactivateByGroupIdAndAdminId: async (
      id: number,
      groupId: number,
      adminId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists(
        'adminControllerReactivateByGroupIdAndAdminId',
        'id',
        id
      );
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'adminControllerReactivateByGroupIdAndAdminId',
        'groupId',
        groupId
      );
      // verify required parameter 'adminId' is not null or undefined
      assertParamExists(
        'adminControllerReactivateByGroupIdAndAdminId',
        'adminId',
        adminId
      );
      const localVarPath = `/admins/{id}/status`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (adminId !== undefined) {
        localVarQueryParameter['adminId'] = adminId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AdminApi - functional programming interface
 * @export
 */
export const AdminApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = AdminApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} adminUid
     * @param {CreateAdminRequest} createAdminRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async adminControllerCreateByGroupId(
      groupId: number,
      adminUid: string,
      createAdminRequest: CreateAdminRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.adminControllerCreateByGroupId(
          groupId,
          adminUid,
          createAdminRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AdminApi.adminControllerCreateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {number} adminId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async adminControllerDeleteByGroupId(
      id: number,
      groupId: number,
      adminId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.adminControllerDeleteByGroupId(
          id,
          groupId,
          adminId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AdminApi.adminControllerDeleteByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async adminControllerFindByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Admin>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.adminControllerFindByGroupId(
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AdminApi.adminControllerFindByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async adminControllerInviteByGroupIdAndDriverId(
      id: number,
      groupId: number,
      adminUid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.adminControllerInviteByGroupIdAndDriverId(
          id,
          groupId,
          adminUid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AdminApi.adminControllerInviteByGroupIdAndDriverId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {number} adminId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async adminControllerReactivateByGroupIdAndAdminId(
      id: number,
      groupId: number,
      adminId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.adminControllerReactivateByGroupIdAndAdminId(
          id,
          groupId,
          adminId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AdminApi.adminControllerReactivateByGroupIdAndAdminId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AdminApi - factory interface
 * @export
 */
export const AdminApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AdminApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} adminUid
     * @param {CreateAdminRequest} createAdminRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerCreateByGroupId(
      groupId: number,
      adminUid: string,
      createAdminRequest: CreateAdminRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .adminControllerCreateByGroupId(
          groupId,
          adminUid,
          createAdminRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {number} adminId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerDeleteByGroupId(
      id: number,
      groupId: number,
      adminId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .adminControllerDeleteByGroupId(id, groupId, adminId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerFindByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Admin>> {
      return localVarFp
        .adminControllerFindByGroupId(groupId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerInviteByGroupIdAndDriverId(
      id: number,
      groupId: number,
      adminUid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .adminControllerInviteByGroupIdAndDriverId(
          id,
          groupId,
          adminUid,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} id
     * @param {number} groupId
     * @param {number} adminId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    adminControllerReactivateByGroupIdAndAdminId(
      id: number,
      groupId: number,
      adminId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .adminControllerReactivateByGroupIdAndAdminId(
          id,
          groupId,
          adminId,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AdminApi - object-oriented interface
 * @export
 * @class AdminApi
 * @extends {BaseAPI}
 */
export class AdminApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {string} adminUid
   * @param {CreateAdminRequest} createAdminRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdminApi
   */
  public adminControllerCreateByGroupId(
    groupId: number,
    adminUid: string,
    createAdminRequest: CreateAdminRequest,
    options?: RawAxiosRequestConfig
  ) {
    return AdminApiFp(this.configuration)
      .adminControllerCreateByGroupId(
        groupId,
        adminUid,
        createAdminRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} id
   * @param {number} groupId
   * @param {number} adminId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdminApi
   */
  public adminControllerDeleteByGroupId(
    id: number,
    groupId: number,
    adminId: number,
    options?: RawAxiosRequestConfig
  ) {
    return AdminApiFp(this.configuration)
      .adminControllerDeleteByGroupId(id, groupId, adminId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdminApi
   */
  public adminControllerFindByGroupId(
    groupId: number,
    options?: RawAxiosRequestConfig
  ) {
    return AdminApiFp(this.configuration)
      .adminControllerFindByGroupId(groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} id
   * @param {number} groupId
   * @param {string} adminUid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdminApi
   */
  public adminControllerInviteByGroupIdAndDriverId(
    id: number,
    groupId: number,
    adminUid: string,
    options?: RawAxiosRequestConfig
  ) {
    return AdminApiFp(this.configuration)
      .adminControllerInviteByGroupIdAndDriverId(id, groupId, adminUid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} id
   * @param {number} groupId
   * @param {number} adminId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdminApi
   */
  public adminControllerReactivateByGroupIdAndAdminId(
    id: number,
    groupId: number,
    adminId: number,
    options?: RawAxiosRequestConfig
  ) {
    return AdminApiFp(this.configuration)
      .adminControllerReactivateByGroupIdAndAdminId(
        id,
        groupId,
        adminId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * AdvertApi - axios parameter creator
 * @export
 */
export const AdvertApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} advertId
     * @param {AssignPodToAdvertRequest} assignPodToAdvertRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    advertControllerAssignByAdvertId: async (
      advertId: number,
      assignPodToAdvertRequest: AssignPodToAdvertRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'advertId' is not null or undefined
      assertParamExists(
        'advertControllerAssignByAdvertId',
        'advertId',
        advertId
      );
      // verify required parameter 'assignPodToAdvertRequest' is not null or undefined
      assertParamExists(
        'advertControllerAssignByAdvertId',
        'assignPodToAdvertRequest',
        assignPodToAdvertRequest
      );
      const localVarPath = `/adverts/{advertId}/pods`.replace(
        `{${'advertId'}}`,
        encodeURIComponent(String(advertId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        assignPodToAdvertRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    advertControllerFindAll: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/adverts`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} advertId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    advertControllerFindByAdvertId: async (
      advertId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'advertId' is not null or undefined
      assertParamExists('advertControllerFindByAdvertId', 'advertId', advertId);
      const localVarPath = `/adverts/{advertId}`.replace(
        `{${'advertId'}}`,
        encodeURIComponent(String(advertId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} advertId
     * @param {number} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    advertControllerRemoveByPodId: async (
      advertId: number,
      podId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'advertId' is not null or undefined
      assertParamExists('advertControllerRemoveByPodId', 'advertId', advertId);
      // verify required parameter 'podId' is not null or undefined
      assertParamExists('advertControllerRemoveByPodId', 'podId', podId);
      const localVarPath = `/adverts/{advertId}/pods/{podId}`
        .replace(`{${'advertId'}}`, encodeURIComponent(String(advertId)))
        .replace(`{${'podId'}}`, encodeURIComponent(String(podId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AdvertApi - functional programming interface
 * @export
 */
export const AdvertApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = AdvertApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} advertId
     * @param {AssignPodToAdvertRequest} assignPodToAdvertRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async advertControllerAssignByAdvertId(
      advertId: number,
      assignPodToAdvertRequest: AssignPodToAdvertRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.advertControllerAssignByAdvertId(
          advertId,
          assignPodToAdvertRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AdvertApi.advertControllerAssignByAdvertId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async advertControllerFindAll(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Advert>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.advertControllerFindAll(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AdvertApi.advertControllerFindAll']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} advertId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async advertControllerFindByAdvertId(
      advertId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Advert>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.advertControllerFindByAdvertId(
          advertId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AdvertApi.advertControllerFindByAdvertId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} advertId
     * @param {number} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async advertControllerRemoveByPodId(
      advertId: number,
      podId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.advertControllerRemoveByPodId(
          advertId,
          podId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AdvertApi.advertControllerRemoveByPodId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AdvertApi - factory interface
 * @export
 */
export const AdvertApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AdvertApiFp(configuration);
  return {
    /**
     *
     * @param {number} advertId
     * @param {AssignPodToAdvertRequest} assignPodToAdvertRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    advertControllerAssignByAdvertId(
      advertId: number,
      assignPodToAdvertRequest: AssignPodToAdvertRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .advertControllerAssignByAdvertId(
          advertId,
          assignPodToAdvertRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    advertControllerFindAll(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Advert>> {
      return localVarFp
        .advertControllerFindAll(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} advertId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    advertControllerFindByAdvertId(
      advertId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Advert> {
      return localVarFp
        .advertControllerFindByAdvertId(advertId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} advertId
     * @param {number} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    advertControllerRemoveByPodId(
      advertId: number,
      podId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .advertControllerRemoveByPodId(advertId, podId, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AdvertApi - object-oriented interface
 * @export
 * @class AdvertApi
 * @extends {BaseAPI}
 */
export class AdvertApi extends BaseAPI {
  /**
   *
   * @param {number} advertId
   * @param {AssignPodToAdvertRequest} assignPodToAdvertRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdvertApi
   */
  public advertControllerAssignByAdvertId(
    advertId: number,
    assignPodToAdvertRequest: AssignPodToAdvertRequest,
    options?: RawAxiosRequestConfig
  ) {
    return AdvertApiFp(this.configuration)
      .advertControllerAssignByAdvertId(
        advertId,
        assignPodToAdvertRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdvertApi
   */
  public advertControllerFindAll(options?: RawAxiosRequestConfig) {
    return AdvertApiFp(this.configuration)
      .advertControllerFindAll(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} advertId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdvertApi
   */
  public advertControllerFindByAdvertId(
    advertId: number,
    options?: RawAxiosRequestConfig
  ) {
    return AdvertApiFp(this.configuration)
      .advertControllerFindByAdvertId(advertId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} advertId
   * @param {number} podId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AdvertApi
   */
  public advertControllerRemoveByPodId(
    advertId: number,
    podId: number,
    options?: RawAxiosRequestConfig
  ) {
    return AdvertApiFp(this.configuration)
      .advertControllerRemoveByPodId(advertId, podId, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * BillingApi - axios parameter creator
 * @export
 */
export const BillingApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    billingControllerFindBillingInformationByGroupUid: async (
      groupUid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'billingControllerFindBillingInformationByGroupUid',
        'groupUid',
        groupUid
      );
      const localVarPath = `/billing`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * BillingApi - functional programming interface
 * @export
 */
export const BillingApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = BillingApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async billingControllerFindBillingInformationByGroupUid(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<BillingInformation>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.billingControllerFindBillingInformationByGroupUid(
          groupUid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'BillingApi.billingControllerFindBillingInformationByGroupUid'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * BillingApi - factory interface
 * @export
 */
export const BillingApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = BillingApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    billingControllerFindBillingInformationByGroupUid(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<BillingInformation> {
      return localVarFp
        .billingControllerFindBillingInformationByGroupUid(groupUid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * BillingApi - object-oriented interface
 * @export
 * @class BillingApi
 * @extends {BaseAPI}
 */
export class BillingApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingApi
   */
  public billingControllerFindBillingInformationByGroupUid(
    groupUid: string,
    options?: RawAxiosRequestConfig
  ) {
    return BillingApiFp(this.configuration)
      .billingControllerFindBillingInformationByGroupUid(groupUid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * BillingInvoiceApi - axios parameter creator
 * @export
 */
export const BillingInvoiceApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    billingInvoiceControllerGetInvoicePdf: async (
      invoiceId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'invoiceId' is not null or undefined
      assertParamExists(
        'billingInvoiceControllerGetInvoicePdf',
        'invoiceId',
        invoiceId
      );
      const localVarPath = `/billing/invoices/{invoiceId}/pdf`.replace(
        `{${'invoiceId'}}`,
        encodeURIComponent(String(invoiceId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * BillingInvoiceApi - functional programming interface
 * @export
 */
export const BillingInvoiceApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    BillingInvoiceApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async billingInvoiceControllerGetInvoicePdf(
      invoiceId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.billingInvoiceControllerGetInvoicePdf(
          invoiceId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'BillingInvoiceApi.billingInvoiceControllerGetInvoicePdf'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * BillingInvoiceApi - factory interface
 * @export
 */
export const BillingInvoiceApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = BillingInvoiceApiFp(configuration);
  return {
    /**
     *
     * @param {string} invoiceId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    billingInvoiceControllerGetInvoicePdf(
      invoiceId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .billingInvoiceControllerGetInvoicePdf(invoiceId, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * BillingInvoiceApi - object-oriented interface
 * @export
 * @class BillingInvoiceApi
 * @extends {BaseAPI}
 */
export class BillingInvoiceApi extends BaseAPI {
  /**
   *
   * @param {string} invoiceId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingInvoiceApi
   */
  public billingInvoiceControllerGetInvoicePdf(
    invoiceId: string,
    options?: RawAxiosRequestConfig
  ) {
    return BillingInvoiceApiFp(this.configuration)
      .billingInvoiceControllerGetInvoicePdf(invoiceId, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * BillingStatementApi - axios parameter creator
 * @export
 */
export const BillingStatementApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    billingStatementControllerGetStatementPdf: async (
      statementId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'statementId' is not null or undefined
      assertParamExists(
        'billingStatementControllerGetStatementPdf',
        'statementId',
        statementId
      );
      const localVarPath = `/billing/statements/{statementId}/pdf`.replace(
        `{${'statementId'}}`,
        encodeURIComponent(String(statementId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * BillingStatementApi - functional programming interface
 * @export
 */
export const BillingStatementApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    BillingStatementApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async billingStatementControllerGetStatementPdf(
      statementId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.billingStatementControllerGetStatementPdf(
          statementId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'BillingStatementApi.billingStatementControllerGetStatementPdf'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * BillingStatementApi - factory interface
 * @export
 */
export const BillingStatementApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = BillingStatementApiFp(configuration);
  return {
    /**
     *
     * @param {string} statementId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    billingStatementControllerGetStatementPdf(
      statementId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .billingStatementControllerGetStatementPdf(statementId, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * BillingStatementApi - object-oriented interface
 * @export
 * @class BillingStatementApi
 * @extends {BaseAPI}
 */
export class BillingStatementApi extends BaseAPI {
  /**
   *
   * @param {string} statementId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BillingStatementApi
   */
  public billingStatementControllerGetStatementPdf(
    statementId: string,
    options?: RawAxiosRequestConfig
  ) {
    return BillingStatementApiFp(this.configuration)
      .billingStatementControllerGetStatementPdf(statementId, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * BouncedEmailsApi - axios parameter creator
 * @export
 */
export const BouncedEmailsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    bouncedEmailsControllerFindAll: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/support/emails/bounced`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} emailAddress
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    bouncedEmailsControllerRemoveByEmailAddress: async (
      emailAddress: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'emailAddress' is not null or undefined
      assertParamExists(
        'bouncedEmailsControllerRemoveByEmailAddress',
        'emailAddress',
        emailAddress
      );
      const localVarPath = `/support/emails/bounced/{emailAddress}`.replace(
        `{${'emailAddress'}}`,
        encodeURIComponent(String(emailAddress))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * BouncedEmailsApi - functional programming interface
 * @export
 */
export const BouncedEmailsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    BouncedEmailsApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async bouncedEmailsControllerFindAll(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<string>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.bouncedEmailsControllerFindAll(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['BouncedEmailsApi.bouncedEmailsControllerFindAll']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} emailAddress
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async bouncedEmailsControllerRemoveByEmailAddress(
      emailAddress: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.bouncedEmailsControllerRemoveByEmailAddress(
          emailAddress,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'BouncedEmailsApi.bouncedEmailsControllerRemoveByEmailAddress'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * BouncedEmailsApi - factory interface
 * @export
 */
export const BouncedEmailsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = BouncedEmailsApiFp(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    bouncedEmailsControllerFindAll(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<string>> {
      return localVarFp
        .bouncedEmailsControllerFindAll(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} emailAddress
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    bouncedEmailsControllerRemoveByEmailAddress(
      emailAddress: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .bouncedEmailsControllerRemoveByEmailAddress(emailAddress, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * BouncedEmailsApi - object-oriented interface
 * @export
 * @class BouncedEmailsApi
 * @extends {BaseAPI}
 */
export class BouncedEmailsApi extends BaseAPI {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BouncedEmailsApi
   */
  public bouncedEmailsControllerFindAll(options?: RawAxiosRequestConfig) {
    return BouncedEmailsApiFp(this.configuration)
      .bouncedEmailsControllerFindAll(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} emailAddress
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BouncedEmailsApi
   */
  public bouncedEmailsControllerRemoveByEmailAddress(
    emailAddress: string,
    options?: RawAxiosRequestConfig
  ) {
    return BouncedEmailsApiFp(this.configuration)
      .bouncedEmailsControllerRemoveByEmailAddress(emailAddress, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargerApi - axios parameter creator
 * @export
 */
export const ChargerApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} identifier
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargerControllerFindByIdentifier: async (
      identifier: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'identifier' is not null or undefined
      assertParamExists(
        'chargerControllerFindByIdentifier',
        'identifier',
        identifier
      );
      const localVarPath = `/support/chargers/{identifier}`.replace(
        `{${'identifier'}}`,
        encodeURIComponent(String(identifier))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargerControllerFindChargerNameByPpid: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargerControllerFindChargerNameByPpid', 'ppid', ppid);
      const localVarPath = `/support/chargers/{ppid}/name`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} name
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargerControllerFindPpidByChargerName: async (
      name: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'name' is not null or undefined
      assertParamExists('chargerControllerFindPpidByChargerName', 'name', name);
      const localVarPath = `/support/chargers/{name}/ppid`.replace(
        `{${'name'}}`,
        encodeURIComponent(String(name))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargerApi - functional programming interface
 * @export
 */
export const ChargerApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ChargerApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} identifier
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargerControllerFindByIdentifier(
      identifier: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Charger>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargerControllerFindByIdentifier(
          identifier,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargerApi.chargerControllerFindByIdentifier']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargerControllerFindChargerNameByPpid(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargerControllerFindChargerNameByPpid(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargerApi.chargerControllerFindChargerNameByPpid'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} name
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargerControllerFindPpidByChargerName(
      name: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargerControllerFindPpidByChargerName(
          name,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargerApi.chargerControllerFindPpidByChargerName'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargerApi - factory interface
 * @export
 */
export const ChargerApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargerApiFp(configuration);
  return {
    /**
     *
     * @param {string} identifier
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargerControllerFindByIdentifier(
      identifier: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Charger> {
      return localVarFp
        .chargerControllerFindByIdentifier(identifier, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargerControllerFindChargerNameByPpid(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<string> {
      return localVarFp
        .chargerControllerFindChargerNameByPpid(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} name
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargerControllerFindPpidByChargerName(
      name: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<string> {
      return localVarFp
        .chargerControllerFindPpidByChargerName(name, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargerApi - object-oriented interface
 * @export
 * @class ChargerApi
 * @extends {BaseAPI}
 */
export class ChargerApi extends BaseAPI {
  /**
   *
   * @param {string} identifier
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargerApi
   */
  public chargerControllerFindByIdentifier(
    identifier: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargerApiFp(this.configuration)
      .chargerControllerFindByIdentifier(identifier, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargerApi
   */
  public chargerControllerFindChargerNameByPpid(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargerApiFp(this.configuration)
      .chargerControllerFindChargerNameByPpid(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} name
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargerApi
   */
  public chargerControllerFindPpidByChargerName(
    name: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargerApiFp(this.configuration)
      .chargerControllerFindPpidByChargerName(name, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ContactDetailsApi - axios parameter creator
 * @export
 */
export const ContactDetailsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateContactDetailsRequest} updateContactDetailsRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId: async (
      groupUid: string,
      siteId: number,
      updateContactDetailsRequest: UpdateContactDetailsRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId',
        'siteId',
        siteId
      );
      // verify required parameter 'updateContactDetailsRequest' is not null or undefined
      assertParamExists(
        'contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId',
        'updateContactDetailsRequest',
        updateContactDetailsRequest
      );
      const localVarPath = `/sites/{siteId}/contact-details`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateContactDetailsRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ContactDetailsApi - functional programming interface
 * @export
 */
export const ContactDetailsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ContactDetailsApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateContactDetailsRequest} updateContactDetailsRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      updateContactDetailsRequest: UpdateContactDetailsRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId(
          groupUid,
          siteId,
          updateContactDetailsRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ContactDetailsApi.contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ContactDetailsApi - factory interface
 * @export
 */
export const ContactDetailsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ContactDetailsApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateContactDetailsRequest} updateContactDetailsRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      updateContactDetailsRequest: UpdateContactDetailsRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId(
          groupUid,
          siteId,
          updateContactDetailsRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ContactDetailsApi - object-oriented interface
 * @export
 * @class ContactDetailsApi
 * @extends {BaseAPI}
 */
export class ContactDetailsApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {UpdateContactDetailsRequest} updateContactDetailsRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ContactDetailsApi
   */
  public contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId(
    groupUid: string,
    siteId: number,
    updateContactDetailsRequest: UpdateContactDetailsRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ContactDetailsApiFp(this.configuration)
      .contactDetailsControllerUpdateContactDetailsByGroupUidAndSiteId(
        groupUid,
        siteId,
        updateContactDetailsRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DomainApi - axios parameter creator
 * @export
 */
export const DomainApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {CreateDomainRequest} createDomainRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    domainControllerCreateByGroupId: async (
      groupId: number,
      createDomainRequest: CreateDomainRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('domainControllerCreateByGroupId', 'groupId', groupId);
      // verify required parameter 'createDomainRequest' is not null or undefined
      assertParamExists(
        'domainControllerCreateByGroupId',
        'createDomainRequest',
        createDomainRequest
      );
      const localVarPath = `/domains`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createDomainRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} domainId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    domainControllerDeleteByGroupId: async (
      groupId: number,
      domainId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('domainControllerDeleteByGroupId', 'groupId', groupId);
      // verify required parameter 'domainId' is not null or undefined
      assertParamExists(
        'domainControllerDeleteByGroupId',
        'domainId',
        domainId
      );
      const localVarPath = `/domains/{domainId}`.replace(
        `{${'domainId'}}`,
        encodeURIComponent(String(domainId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    domainControllerFindByGroupId: async (
      groupId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('domainControllerFindByGroupId', 'groupId', groupId);
      const localVarPath = `/domains`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} domainId
     * @param {CreateDomainRequest} createDomainRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    domainControllerUpdateByGroupId: async (
      groupId: number,
      domainId: number,
      createDomainRequest: CreateDomainRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('domainControllerUpdateByGroupId', 'groupId', groupId);
      // verify required parameter 'domainId' is not null or undefined
      assertParamExists(
        'domainControllerUpdateByGroupId',
        'domainId',
        domainId
      );
      // verify required parameter 'createDomainRequest' is not null or undefined
      assertParamExists(
        'domainControllerUpdateByGroupId',
        'createDomainRequest',
        createDomainRequest
      );
      const localVarPath = `/domains/{domainId}`.replace(
        `{${'domainId'}}`,
        encodeURIComponent(String(domainId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createDomainRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DomainApi - functional programming interface
 * @export
 */
export const DomainApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DomainApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {CreateDomainRequest} createDomainRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async domainControllerCreateByGroupId(
      groupId: number,
      createDomainRequest: CreateDomainRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Domain>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.domainControllerCreateByGroupId(
          groupId,
          createDomainRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DomainApi.domainControllerCreateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} domainId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async domainControllerDeleteByGroupId(
      groupId: number,
      domainId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.domainControllerDeleteByGroupId(
          groupId,
          domainId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DomainApi.domainControllerDeleteByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async domainControllerFindByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Domain>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.domainControllerFindByGroupId(
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DomainApi.domainControllerFindByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} domainId
     * @param {CreateDomainRequest} createDomainRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async domainControllerUpdateByGroupId(
      groupId: number,
      domainId: number,
      createDomainRequest: CreateDomainRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.domainControllerUpdateByGroupId(
          groupId,
          domainId,
          createDomainRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DomainApi.domainControllerUpdateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DomainApi - factory interface
 * @export
 */
export const DomainApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DomainApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {CreateDomainRequest} createDomainRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    domainControllerCreateByGroupId(
      groupId: number,
      createDomainRequest: CreateDomainRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Domain> {
      return localVarFp
        .domainControllerCreateByGroupId(groupId, createDomainRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} domainId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    domainControllerDeleteByGroupId(
      groupId: number,
      domainId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .domainControllerDeleteByGroupId(groupId, domainId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    domainControllerFindByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Domain>> {
      return localVarFp
        .domainControllerFindByGroupId(groupId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} domainId
     * @param {CreateDomainRequest} createDomainRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    domainControllerUpdateByGroupId(
      groupId: number,
      domainId: number,
      createDomainRequest: CreateDomainRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .domainControllerUpdateByGroupId(
          groupId,
          domainId,
          createDomainRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DomainApi - object-oriented interface
 * @export
 * @class DomainApi
 * @extends {BaseAPI}
 */
export class DomainApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {CreateDomainRequest} createDomainRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DomainApi
   */
  public domainControllerCreateByGroupId(
    groupId: number,
    createDomainRequest: CreateDomainRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DomainApiFp(this.configuration)
      .domainControllerCreateByGroupId(groupId, createDomainRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} domainId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DomainApi
   */
  public domainControllerDeleteByGroupId(
    groupId: number,
    domainId: number,
    options?: RawAxiosRequestConfig
  ) {
    return DomainApiFp(this.configuration)
      .domainControllerDeleteByGroupId(groupId, domainId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DomainApi
   */
  public domainControllerFindByGroupId(
    groupId: number,
    options?: RawAxiosRequestConfig
  ) {
    return DomainApiFp(this.configuration)
      .domainControllerFindByGroupId(groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} domainId
   * @param {CreateDomainRequest} createDomainRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DomainApi
   */
  public domainControllerUpdateByGroupId(
    groupId: number,
    domainId: number,
    createDomainRequest: CreateDomainRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DomainApiFp(this.configuration)
      .domainControllerUpdateByGroupId(
        groupId,
        domainId,
        createDomainRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DriverApi - axios parameter creator
 * @export
 */
export const DriverApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerBulkCreateByGroupId: async (
      groupId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'driverControllerBulkCreateByGroupId',
        'groupId',
        groupId
      );
      const localVarPath = `/drivers/upload`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {CreateDriverRequest} createDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerCreateByGroupId: async (
      groupId: number,
      adminId: number,
      createDriverRequest: CreateDriverRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('driverControllerCreateByGroupId', 'groupId', groupId);
      // verify required parameter 'adminId' is not null or undefined
      assertParamExists('driverControllerCreateByGroupId', 'adminId', adminId);
      // verify required parameter 'createDriverRequest' is not null or undefined
      assertParamExists(
        'driverControllerCreateByGroupId',
        'createDriverRequest',
        createDriverRequest
      );
      const localVarPath = `/drivers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (adminId !== undefined) {
        localVarQueryParameter['adminId'] = adminId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createDriverRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} driverId
     * @param {DeleteDriverRequest} deleteDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerDeleteByGroupId: async (
      groupId: number,
      driverId: number,
      deleteDriverRequest: DeleteDriverRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('driverControllerDeleteByGroupId', 'groupId', groupId);
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'driverControllerDeleteByGroupId',
        'driverId',
        driverId
      );
      // verify required parameter 'deleteDriverRequest' is not null or undefined
      assertParamExists(
        'driverControllerDeleteByGroupId',
        'deleteDriverRequest',
        deleteDriverRequest
      );
      const localVarPath = `/drivers/{driverId}`.replace(
        `{${'driverId'}}`,
        encodeURIComponent(String(driverId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        deleteDriverRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerFindByGroupId: async (
      groupId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('driverControllerFindByGroupId', 'groupId', groupId);
      const localVarPath = `/drivers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {number} driverId
     * @param {InviteDriverRequest} inviteDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerInviteByGroupIdAndDriverId: async (
      groupId: number,
      adminId: number,
      driverId: number,
      inviteDriverRequest: InviteDriverRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'driverControllerInviteByGroupIdAndDriverId',
        'groupId',
        groupId
      );
      // verify required parameter 'adminId' is not null or undefined
      assertParamExists(
        'driverControllerInviteByGroupIdAndDriverId',
        'adminId',
        adminId
      );
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'driverControllerInviteByGroupIdAndDriverId',
        'driverId',
        driverId
      );
      // verify required parameter 'inviteDriverRequest' is not null or undefined
      assertParamExists(
        'driverControllerInviteByGroupIdAndDriverId',
        'inviteDriverRequest',
        inviteDriverRequest
      );
      const localVarPath = `/drivers/{driverId}/invitation`.replace(
        `{${'driverId'}}`,
        encodeURIComponent(String(driverId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (adminId !== undefined) {
        localVarQueryParameter['adminId'] = adminId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        inviteDriverRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} driverId
     * @param {UpdateDriverRequest} updateDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerUpdateByGroupId: async (
      groupId: number,
      driverId: number,
      updateDriverRequest: UpdateDriverRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('driverControllerUpdateByGroupId', 'groupId', groupId);
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'driverControllerUpdateByGroupId',
        'driverId',
        driverId
      );
      // verify required parameter 'updateDriverRequest' is not null or undefined
      assertParamExists(
        'driverControllerUpdateByGroupId',
        'updateDriverRequest',
        updateDriverRequest
      );
      const localVarPath = `/drivers/{driverId}`.replace(
        `{${'driverId'}}`,
        encodeURIComponent(String(driverId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateDriverRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DriverApi - functional programming interface
 * @export
 */
export const DriverApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DriverApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverControllerBulkCreateByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Driver>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverControllerBulkCreateByGroupId(
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriverApi.driverControllerBulkCreateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {CreateDriverRequest} createDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverControllerCreateByGroupId(
      groupId: number,
      adminId: number,
      createDriverRequest: CreateDriverRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Driver>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverControllerCreateByGroupId(
          groupId,
          adminId,
          createDriverRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriverApi.driverControllerCreateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} driverId
     * @param {DeleteDriverRequest} deleteDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverControllerDeleteByGroupId(
      groupId: number,
      driverId: number,
      deleteDriverRequest: DeleteDriverRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverControllerDeleteByGroupId(
          groupId,
          driverId,
          deleteDriverRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriverApi.driverControllerDeleteByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverControllerFindByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Driver>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverControllerFindByGroupId(
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriverApi.driverControllerFindByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {number} driverId
     * @param {InviteDriverRequest} inviteDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverControllerInviteByGroupIdAndDriverId(
      groupId: number,
      adminId: number,
      driverId: number,
      inviteDriverRequest: InviteDriverRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverControllerInviteByGroupIdAndDriverId(
          groupId,
          adminId,
          driverId,
          inviteDriverRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverApi.driverControllerInviteByGroupIdAndDriverId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} driverId
     * @param {UpdateDriverRequest} updateDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverControllerUpdateByGroupId(
      groupId: number,
      driverId: number,
      updateDriverRequest: UpdateDriverRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverControllerUpdateByGroupId(
          groupId,
          driverId,
          updateDriverRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriverApi.driverControllerUpdateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DriverApi - factory interface
 * @export
 */
export const DriverApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DriverApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerBulkCreateByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Driver>> {
      return localVarFp
        .driverControllerBulkCreateByGroupId(groupId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {CreateDriverRequest} createDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerCreateByGroupId(
      groupId: number,
      adminId: number,
      createDriverRequest: CreateDriverRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Driver> {
      return localVarFp
        .driverControllerCreateByGroupId(
          groupId,
          adminId,
          createDriverRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} driverId
     * @param {DeleteDriverRequest} deleteDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerDeleteByGroupId(
      groupId: number,
      driverId: number,
      deleteDriverRequest: DeleteDriverRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .driverControllerDeleteByGroupId(
          groupId,
          driverId,
          deleteDriverRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerFindByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Driver>> {
      return localVarFp
        .driverControllerFindByGroupId(groupId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {number} driverId
     * @param {InviteDriverRequest} inviteDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerInviteByGroupIdAndDriverId(
      groupId: number,
      adminId: number,
      driverId: number,
      inviteDriverRequest: InviteDriverRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .driverControllerInviteByGroupIdAndDriverId(
          groupId,
          adminId,
          driverId,
          inviteDriverRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} driverId
     * @param {UpdateDriverRequest} updateDriverRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverControllerUpdateByGroupId(
      groupId: number,
      driverId: number,
      updateDriverRequest: UpdateDriverRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .driverControllerUpdateByGroupId(
          groupId,
          driverId,
          updateDriverRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DriverApi - object-oriented interface
 * @export
 * @class DriverApi
 * @extends {BaseAPI}
 */
export class DriverApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverApi
   */
  public driverControllerBulkCreateByGroupId(
    groupId: number,
    options?: RawAxiosRequestConfig
  ) {
    return DriverApiFp(this.configuration)
      .driverControllerBulkCreateByGroupId(groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} adminId
   * @param {CreateDriverRequest} createDriverRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverApi
   */
  public driverControllerCreateByGroupId(
    groupId: number,
    adminId: number,
    createDriverRequest: CreateDriverRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DriverApiFp(this.configuration)
      .driverControllerCreateByGroupId(
        groupId,
        adminId,
        createDriverRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} driverId
   * @param {DeleteDriverRequest} deleteDriverRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverApi
   */
  public driverControllerDeleteByGroupId(
    groupId: number,
    driverId: number,
    deleteDriverRequest: DeleteDriverRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DriverApiFp(this.configuration)
      .driverControllerDeleteByGroupId(
        groupId,
        driverId,
        deleteDriverRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverApi
   */
  public driverControllerFindByGroupId(
    groupId: number,
    options?: RawAxiosRequestConfig
  ) {
    return DriverApiFp(this.configuration)
      .driverControllerFindByGroupId(groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} adminId
   * @param {number} driverId
   * @param {InviteDriverRequest} inviteDriverRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverApi
   */
  public driverControllerInviteByGroupIdAndDriverId(
    groupId: number,
    adminId: number,
    driverId: number,
    inviteDriverRequest: InviteDriverRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DriverApiFp(this.configuration)
      .driverControllerInviteByGroupIdAndDriverId(
        groupId,
        adminId,
        driverId,
        inviteDriverRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} driverId
   * @param {UpdateDriverRequest} updateDriverRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverApi
   */
  public driverControllerUpdateByGroupId(
    groupId: number,
    driverId: number,
    updateDriverRequest: UpdateDriverRequest,
    options?: RawAxiosRequestConfig
  ) {
    return DriverApiFp(this.configuration)
      .driverControllerUpdateByGroupId(
        groupId,
        driverId,
        updateDriverRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DriverChargesApi - axios parameter creator
 * @export
 */
export const DriverChargesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {string} tariffTier
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesControllerFindByGroupIdAndDriverId: async (
      groupId: number,
      groupUid: string,
      tariffTier: string,
      driverId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'driverChargesControllerFindByGroupIdAndDriverId',
        'groupId',
        groupId
      );
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'driverChargesControllerFindByGroupIdAndDriverId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'tariffTier' is not null or undefined
      assertParamExists(
        'driverChargesControllerFindByGroupIdAndDriverId',
        'tariffTier',
        tariffTier
      );
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'driverChargesControllerFindByGroupIdAndDriverId',
        'driverId',
        driverId
      );
      const localVarPath = `/drivers/{driverId}/charges`.replace(
        `{${'driverId'}}`,
        encodeURIComponent(String(driverId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (tariffTier !== undefined) {
        localVarQueryParameter['tariffTier'] = tariffTier;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {string} tariffTier
     * @param {number} driverId
     * @param {string} date
     * @param {string} all
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesControllerGenerateCsv: async (
      groupId: number,
      groupUid: string,
      tariffTier: string,
      driverId: number,
      date: string,
      all: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'driverChargesControllerGenerateCsv',
        'groupId',
        groupId
      );
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'driverChargesControllerGenerateCsv',
        'groupUid',
        groupUid
      );
      // verify required parameter 'tariffTier' is not null or undefined
      assertParamExists(
        'driverChargesControllerGenerateCsv',
        'tariffTier',
        tariffTier
      );
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'driverChargesControllerGenerateCsv',
        'driverId',
        driverId
      );
      // verify required parameter 'date' is not null or undefined
      assertParamExists('driverChargesControllerGenerateCsv', 'date', date);
      // verify required parameter 'all' is not null or undefined
      assertParamExists('driverChargesControllerGenerateCsv', 'all', all);
      const localVarPath = `/drivers/{driverId}/charges/csv`.replace(
        `{${'driverId'}}`,
        encodeURIComponent(String(driverId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (tariffTier !== undefined) {
        localVarQueryParameter['tariffTier'] = tariffTier;
      }

      if (date !== undefined) {
        localVarQueryParameter['date'] = date;
      }

      if (all !== undefined) {
        localVarQueryParameter['all'] = all;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DriverChargesApi - functional programming interface
 * @export
 */
export const DriverChargesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    DriverChargesApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {string} tariffTier
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverChargesControllerFindByGroupIdAndDriverId(
      groupId: number,
      groupUid: string,
      tariffTier: string,
      driverId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Driver>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverChargesControllerFindByGroupIdAndDriverId(
          groupId,
          groupUid,
          tariffTier,
          driverId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverChargesApi.driverChargesControllerFindByGroupIdAndDriverId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {string} tariffTier
     * @param {number} driverId
     * @param {string} date
     * @param {string} all
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverChargesControllerGenerateCsv(
      groupId: number,
      groupUid: string,
      tariffTier: string,
      driverId: number,
      date: string,
      all: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverChargesControllerGenerateCsv(
          groupId,
          groupUid,
          tariffTier,
          driverId,
          date,
          all,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverChargesApi.driverChargesControllerGenerateCsv'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DriverChargesApi - factory interface
 * @export
 */
export const DriverChargesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DriverChargesApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {string} tariffTier
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesControllerFindByGroupIdAndDriverId(
      groupId: number,
      groupUid: string,
      tariffTier: string,
      driverId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Driver> {
      return localVarFp
        .driverChargesControllerFindByGroupIdAndDriverId(
          groupId,
          groupUid,
          tariffTier,
          driverId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {string} tariffTier
     * @param {number} driverId
     * @param {string} date
     * @param {string} all
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesControllerGenerateCsv(
      groupId: number,
      groupUid: string,
      tariffTier: string,
      driverId: number,
      date: string,
      all: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .driverChargesControllerGenerateCsv(
          groupId,
          groupUid,
          tariffTier,
          driverId,
          date,
          all,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DriverChargesApi - object-oriented interface
 * @export
 * @class DriverChargesApi
 * @extends {BaseAPI}
 */
export class DriverChargesApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {string} groupUid
   * @param {string} tariffTier
   * @param {number} driverId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverChargesApi
   */
  public driverChargesControllerFindByGroupIdAndDriverId(
    groupId: number,
    groupUid: string,
    tariffTier: string,
    driverId: number,
    options?: RawAxiosRequestConfig
  ) {
    return DriverChargesApiFp(this.configuration)
      .driverChargesControllerFindByGroupIdAndDriverId(
        groupId,
        groupUid,
        tariffTier,
        driverId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {string} groupUid
   * @param {string} tariffTier
   * @param {number} driverId
   * @param {string} date
   * @param {string} all
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverChargesApi
   */
  public driverChargesControllerGenerateCsv(
    groupId: number,
    groupUid: string,
    tariffTier: string,
    driverId: number,
    date: string,
    all: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverChargesApiFp(this.configuration)
      .driverChargesControllerGenerateCsv(
        groupId,
        groupUid,
        tariffTier,
        driverId,
        date,
        all,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * EnergyCostApi - axios parameter creator
 * @export
 */
export const EnergyCostApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateEnergyCostRequest} updateEnergyCostRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    energyCostControllerUpdateContactDetailsByGroupUidAndSiteId: async (
      groupUid: string,
      siteId: number,
      updateEnergyCostRequest: UpdateEnergyCostRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'energyCostControllerUpdateContactDetailsByGroupUidAndSiteId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'energyCostControllerUpdateContactDetailsByGroupUidAndSiteId',
        'siteId',
        siteId
      );
      // verify required parameter 'updateEnergyCostRequest' is not null or undefined
      assertParamExists(
        'energyCostControllerUpdateContactDetailsByGroupUidAndSiteId',
        'updateEnergyCostRequest',
        updateEnergyCostRequest
      );
      const localVarPath = `/sites/{siteId}/energy-cost`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateEnergyCostRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * EnergyCostApi - functional programming interface
 * @export
 */
export const EnergyCostApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    EnergyCostApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateEnergyCostRequest} updateEnergyCostRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async energyCostControllerUpdateContactDetailsByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      updateEnergyCostRequest: UpdateEnergyCostRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.energyCostControllerUpdateContactDetailsByGroupUidAndSiteId(
          groupUid,
          siteId,
          updateEnergyCostRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'EnergyCostApi.energyCostControllerUpdateContactDetailsByGroupUidAndSiteId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * EnergyCostApi - factory interface
 * @export
 */
export const EnergyCostApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = EnergyCostApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpdateEnergyCostRequest} updateEnergyCostRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    energyCostControllerUpdateContactDetailsByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      updateEnergyCostRequest: UpdateEnergyCostRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .energyCostControllerUpdateContactDetailsByGroupUidAndSiteId(
          groupUid,
          siteId,
          updateEnergyCostRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * EnergyCostApi - object-oriented interface
 * @export
 * @class EnergyCostApi
 * @extends {BaseAPI}
 */
export class EnergyCostApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {UpdateEnergyCostRequest} updateEnergyCostRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof EnergyCostApi
   */
  public energyCostControllerUpdateContactDetailsByGroupUidAndSiteId(
    groupUid: string,
    siteId: number,
    updateEnergyCostRequest: UpdateEnergyCostRequest,
    options?: RawAxiosRequestConfig
  ) {
    return EnergyCostApiFp(this.configuration)
      .energyCostControllerUpdateContactDetailsByGroupUidAndSiteId(
        groupUid,
        siteId,
        updateEnergyCostRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ExpenseApi - axios parameter creator
 * @export
 */
export const ExpenseApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerFindAllByGroupUidAndDriverId: async (
      groupUid: string,
      driverId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'expenseControllerFindAllByGroupUidAndDriverId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'expenseControllerFindAllByGroupUidAndDriverId',
        'driverId',
        driverId
      );
      const localVarPath = `/expenses/drivers/{driverId}`.replace(
        `{${'driverId'}}`,
        encodeURIComponent(String(driverId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerFindNewByGroup: async (
      groupUid: string,
      year: number,
      month: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'expenseControllerFindNewByGroup',
        'groupUid',
        groupUid
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists('expenseControllerFindNewByGroup', 'year', year);
      // verify required parameter 'month' is not null or undefined
      assertParamExists('expenseControllerFindNewByGroup', 'month', month);
      const localVarPath = `/expenses/new`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      if (month !== undefined) {
        localVarQueryParameter['month'] = month;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerFindNewByGroupGroupedByDriver: async (
      groupUid: string,
      year: number,
      month: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'expenseControllerFindNewByGroupGroupedByDriver',
        'groupUid',
        groupUid
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists(
        'expenseControllerFindNewByGroupGroupedByDriver',
        'year',
        year
      );
      // verify required parameter 'month' is not null or undefined
      assertParamExists(
        'expenseControllerFindNewByGroupGroupedByDriver',
        'month',
        month
      );
      const localVarPath = `/expenses/new/grouped`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      if (month !== undefined) {
        localVarQueryParameter['month'] = month;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerFindProcessedByGroup: async (
      groupUid: string,
      year: number,
      month: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'expenseControllerFindProcessedByGroup',
        'groupUid',
        groupUid
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists('expenseControllerFindProcessedByGroup', 'year', year);
      // verify required parameter 'month' is not null or undefined
      assertParamExists(
        'expenseControllerFindProcessedByGroup',
        'month',
        month
      );
      const localVarPath = `/expenses/processed`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      if (month !== undefined) {
        localVarQueryParameter['month'] = month;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerGetMonthlyUsageForOrganisation: async (
      groupUid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'expenseControllerGetMonthlyUsageForOrganisation',
        'groupUid',
        groupUid
      );
      const localVarPath = `/expenses/stats/monthly`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} adminId
     * @param {string} groupUid
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerMarkExpensesAsProcessed: async (
      adminId: number,
      groupUid: string,
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'adminId' is not null or undefined
      assertParamExists(
        'expenseControllerMarkExpensesAsProcessed',
        'adminId',
        adminId
      );
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'expenseControllerMarkExpensesAsProcessed',
        'groupUid',
        groupUid
      );
      // verify required parameter 'body' is not null or undefined
      assertParamExists(
        'expenseControllerMarkExpensesAsProcessed',
        'body',
        body
      );
      const localVarPath = `/expenses/process`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (adminId !== undefined) {
        localVarQueryParameter['adminId'] = adminId;
      }

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ExpenseApi - functional programming interface
 * @export
 */
export const ExpenseApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ExpenseApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async expenseControllerFindAllByGroupUidAndDriverId(
      groupUid: string,
      driverId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.expenseControllerFindAllByGroupUidAndDriverId(
          groupUid,
          driverId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ExpenseApi.expenseControllerFindAllByGroupUidAndDriverId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async expenseControllerFindNewByGroup(
      groupUid: string,
      year: number,
      month: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<object>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.expenseControllerFindNewByGroup(
          groupUid,
          year,
          month,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExpenseApi.expenseControllerFindNewByGroup']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async expenseControllerFindNewByGroupGroupedByDriver(
      groupUid: string,
      year: number,
      month: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<object>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.expenseControllerFindNewByGroupGroupedByDriver(
          groupUid,
          year,
          month,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ExpenseApi.expenseControllerFindNewByGroupGroupedByDriver'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async expenseControllerFindProcessedByGroup(
      groupUid: string,
      year: number,
      month: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<object>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.expenseControllerFindProcessedByGroup(
          groupUid,
          year,
          month,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ExpenseApi.expenseControllerFindProcessedByGroup'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async expenseControllerGetMonthlyUsageForOrganisation(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.expenseControllerGetMonthlyUsageForOrganisation(
          groupUid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ExpenseApi.expenseControllerGetMonthlyUsageForOrganisation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} adminId
     * @param {string} groupUid
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async expenseControllerMarkExpensesAsProcessed(
      adminId: number,
      groupUid: string,
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.expenseControllerMarkExpensesAsProcessed(
          adminId,
          groupUid,
          body,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ExpenseApi.expenseControllerMarkExpensesAsProcessed'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ExpenseApi - factory interface
 * @export
 */
export const ExpenseApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ExpenseApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerFindAllByGroupUidAndDriverId(
      groupUid: string,
      driverId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .expenseControllerFindAllByGroupUidAndDriverId(
          groupUid,
          driverId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerFindNewByGroup(
      groupUid: string,
      year: number,
      month: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<object>> {
      return localVarFp
        .expenseControllerFindNewByGroup(groupUid, year, month, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerFindNewByGroupGroupedByDriver(
      groupUid: string,
      year: number,
      month: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<object>> {
      return localVarFp
        .expenseControllerFindNewByGroupGroupedByDriver(
          groupUid,
          year,
          month,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {number} month
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerFindProcessedByGroup(
      groupUid: string,
      year: number,
      month: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<object>> {
      return localVarFp
        .expenseControllerFindProcessedByGroup(groupUid, year, month, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerGetMonthlyUsageForOrganisation(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<object> {
      return localVarFp
        .expenseControllerGetMonthlyUsageForOrganisation(groupUid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} adminId
     * @param {string} groupUid
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expenseControllerMarkExpensesAsProcessed(
      adminId: number,
      groupUid: string,
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .expenseControllerMarkExpensesAsProcessed(
          adminId,
          groupUid,
          body,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ExpenseApi - object-oriented interface
 * @export
 * @class ExpenseApi
 * @extends {BaseAPI}
 */
export class ExpenseApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {number} driverId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExpenseApi
   */
  public expenseControllerFindAllByGroupUidAndDriverId(
    groupUid: string,
    driverId: number,
    options?: RawAxiosRequestConfig
  ) {
    return ExpenseApiFp(this.configuration)
      .expenseControllerFindAllByGroupUidAndDriverId(
        groupUid,
        driverId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} year
   * @param {number} month
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExpenseApi
   */
  public expenseControllerFindNewByGroup(
    groupUid: string,
    year: number,
    month: number,
    options?: RawAxiosRequestConfig
  ) {
    return ExpenseApiFp(this.configuration)
      .expenseControllerFindNewByGroup(groupUid, year, month, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} year
   * @param {number} month
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExpenseApi
   */
  public expenseControllerFindNewByGroupGroupedByDriver(
    groupUid: string,
    year: number,
    month: number,
    options?: RawAxiosRequestConfig
  ) {
    return ExpenseApiFp(this.configuration)
      .expenseControllerFindNewByGroupGroupedByDriver(
        groupUid,
        year,
        month,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} year
   * @param {number} month
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExpenseApi
   */
  public expenseControllerFindProcessedByGroup(
    groupUid: string,
    year: number,
    month: number,
    options?: RawAxiosRequestConfig
  ) {
    return ExpenseApiFp(this.configuration)
      .expenseControllerFindProcessedByGroup(groupUid, year, month, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExpenseApi
   */
  public expenseControllerGetMonthlyUsageForOrganisation(
    groupUid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ExpenseApiFp(this.configuration)
      .expenseControllerGetMonthlyUsageForOrganisation(groupUid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} adminId
   * @param {string} groupUid
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExpenseApi
   */
  public expenseControllerMarkExpensesAsProcessed(
    adminId: number,
    groupUid: string,
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return ExpenseApiFp(this.configuration)
      .expenseControllerMarkExpensesAsProcessed(
        adminId,
        groupUid,
        body,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * GroupApi - axios parameter creator
 * @export
 */
export const GroupApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupControllerFindAllByGroupId: async (
      groupId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('groupControllerFindAllByGroupId', 'groupId', groupId);
      const localVarPath = `/user/groups`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupControllerFindByGroupId: async (
      groupId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('groupControllerFindByGroupId', 'groupId', groupId);
      const localVarPath = `/user/group`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupControllerGenerateCsv: async (
      groupId: number,
      date: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('groupControllerGenerateCsv', 'groupId', groupId);
      // verify required parameter 'date' is not null or undefined
      assertParamExists('groupControllerGenerateCsv', 'date', date);
      const localVarPath = `/user/group/charges/csv`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (date !== undefined) {
        localVarQueryParameter['date'] = date;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * GroupApi - functional programming interface
 * @export
 */
export const GroupApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = GroupApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupControllerFindAllByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Group>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupControllerFindAllByGroupId(
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['GroupApi.groupControllerFindAllByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupControllerFindByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Group>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupControllerFindByGroupId(
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['GroupApi.groupControllerFindByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async groupControllerGenerateCsv(
      groupId: number,
      date: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.groupControllerGenerateCsv(
          groupId,
          date,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['GroupApi.groupControllerGenerateCsv']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * GroupApi - factory interface
 * @export
 */
export const GroupApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = GroupApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupControllerFindAllByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Group>> {
      return localVarFp
        .groupControllerFindAllByGroupId(groupId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupControllerFindByGroupId(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Group> {
      return localVarFp
        .groupControllerFindByGroupId(groupId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    groupControllerGenerateCsv(
      groupId: number,
      date: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .groupControllerGenerateCsv(groupId, date, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * GroupApi - object-oriented interface
 * @export
 * @class GroupApi
 * @extends {BaseAPI}
 */
export class GroupApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof GroupApi
   */
  public groupControllerFindAllByGroupId(
    groupId: number,
    options?: RawAxiosRequestConfig
  ) {
    return GroupApiFp(this.configuration)
      .groupControllerFindAllByGroupId(groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof GroupApi
   */
  public groupControllerFindByGroupId(
    groupId: number,
    options?: RawAxiosRequestConfig
  ) {
    return GroupApiFp(this.configuration)
      .groupControllerFindByGroupId(groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {string} date
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof GroupApi
   */
  public groupControllerGenerateCsv(
    groupId: number,
    date: string,
    options?: RawAxiosRequestConfig
  ) {
    return GroupApiFp(this.configuration)
      .groupControllerGenerateCsv(groupId, date, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * HealthApi - axios parameter creator
 * @export
 */
export const HealthApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthApi - functional programming interface
 * @export
 */
export const HealthApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = HealthApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthApi - factory interface
 * @export
 */
export const HealthApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthApiFp(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthApi - object-oriented interface
 * @export
 * @class HealthApi
 * @extends {BaseAPI}
 */
export class HealthApi extends BaseAPI {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * InsightsApi - axios parameter creator
 * @export
 */
export const InsightsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerFindByGroupUid: async (
      groupUid: string,
      year: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'insightsControllerFindByGroupUid',
        'groupUid',
        groupUid
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists('insightsControllerFindByGroupUid', 'year', year);
      const localVarPath = `/insights`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} chargerId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerFindByGroupUidAndChargerId: async (
      groupUid: string,
      chargerId: number,
      year: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'insightsControllerFindByGroupUidAndChargerId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'chargerId' is not null or undefined
      assertParamExists(
        'insightsControllerFindByGroupUidAndChargerId',
        'chargerId',
        chargerId
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists(
        'insightsControllerFindByGroupUidAndChargerId',
        'year',
        year
      );
      const localVarPath = `/insights/chargers/{chargerId}`.replace(
        `{${'chargerId'}}`,
        encodeURIComponent(String(chargerId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerFindByGroupUidAndSiteId: async (
      groupUid: string,
      siteId: number,
      year: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'insightsControllerFindByGroupUidAndSiteId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'insightsControllerFindByGroupUidAndSiteId',
        'siteId',
        siteId
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists(
        'insightsControllerFindByGroupUidAndSiteId',
        'year',
        year
      );
      const localVarPath = `/insights/sites/{siteId}`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerGenerateCsvByGroupUid: async (
      groupId: number,
      groupUid: string,
      year: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUid',
        'groupId',
        groupId
      );
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUid',
        'groupUid',
        groupUid
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUid',
        'year',
        year
      );
      const localVarPath = `/insights/csv`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} chargerId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerGenerateCsvByGroupUidAndChargerId: async (
      groupId: number,
      groupUid: string,
      chargerId: number,
      year: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUidAndChargerId',
        'groupId',
        groupId
      );
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUidAndChargerId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'chargerId' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUidAndChargerId',
        'chargerId',
        chargerId
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUidAndChargerId',
        'year',
        year
      );
      const localVarPath = `/insights/chargers/{chargerId}/csv`.replace(
        `{${'chargerId'}}`,
        encodeURIComponent(String(chargerId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerGenerateCsvByGroupUidAndSiteId: async (
      groupUid: string,
      siteId: number,
      year: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUidAndSiteId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUidAndSiteId',
        'siteId',
        siteId
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists(
        'insightsControllerGenerateCsvByGroupUidAndSiteId',
        'year',
        year
      );
      const localVarPath = `/insights/sites/{siteId}/csv`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * InsightsApi - functional programming interface
 * @export
 */
export const InsightsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = InsightsApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async insightsControllerFindByGroupUid(
      groupUid: string,
      year: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Insight>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.insightsControllerFindByGroupUid(
          groupUid,
          year,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['InsightsApi.insightsControllerFindByGroupUid']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} chargerId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async insightsControllerFindByGroupUidAndChargerId(
      groupUid: string,
      chargerId: number,
      year: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Insight>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.insightsControllerFindByGroupUidAndChargerId(
          groupUid,
          chargerId,
          year,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'InsightsApi.insightsControllerFindByGroupUidAndChargerId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async insightsControllerFindByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      year: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Insight>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.insightsControllerFindByGroupUidAndSiteId(
          groupUid,
          siteId,
          year,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'InsightsApi.insightsControllerFindByGroupUidAndSiteId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async insightsControllerGenerateCsvByGroupUid(
      groupId: number,
      groupUid: string,
      year: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.insightsControllerGenerateCsvByGroupUid(
          groupId,
          groupUid,
          year,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'InsightsApi.insightsControllerGenerateCsvByGroupUid'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} chargerId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async insightsControllerGenerateCsvByGroupUidAndChargerId(
      groupId: number,
      groupUid: string,
      chargerId: number,
      year: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.insightsControllerGenerateCsvByGroupUidAndChargerId(
          groupId,
          groupUid,
          chargerId,
          year,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'InsightsApi.insightsControllerGenerateCsvByGroupUidAndChargerId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async insightsControllerGenerateCsvByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      year: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.insightsControllerGenerateCsvByGroupUidAndSiteId(
          groupUid,
          siteId,
          year,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'InsightsApi.insightsControllerGenerateCsvByGroupUidAndSiteId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * InsightsApi - factory interface
 * @export
 */
export const InsightsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = InsightsApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerFindByGroupUid(
      groupUid: string,
      year: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Insight>> {
      return localVarFp
        .insightsControllerFindByGroupUid(groupUid, year, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} chargerId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerFindByGroupUidAndChargerId(
      groupUid: string,
      chargerId: number,
      year: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Insight>> {
      return localVarFp
        .insightsControllerFindByGroupUidAndChargerId(
          groupUid,
          chargerId,
          year,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerFindByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      year: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Insight>> {
      return localVarFp
        .insightsControllerFindByGroupUidAndSiteId(
          groupUid,
          siteId,
          year,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerGenerateCsvByGroupUid(
      groupId: number,
      groupUid: string,
      year: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .insightsControllerGenerateCsvByGroupUid(
          groupId,
          groupUid,
          year,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} chargerId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerGenerateCsvByGroupUidAndChargerId(
      groupId: number,
      groupUid: string,
      chargerId: number,
      year: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .insightsControllerGenerateCsvByGroupUidAndChargerId(
          groupId,
          groupUid,
          chargerId,
          year,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} year
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    insightsControllerGenerateCsvByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      year: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .insightsControllerGenerateCsvByGroupUidAndSiteId(
          groupUid,
          siteId,
          year,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * InsightsApi - object-oriented interface
 * @export
 * @class InsightsApi
 * @extends {BaseAPI}
 */
export class InsightsApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {number} year
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsightsApi
   */
  public insightsControllerFindByGroupUid(
    groupUid: string,
    year: number,
    options?: RawAxiosRequestConfig
  ) {
    return InsightsApiFp(this.configuration)
      .insightsControllerFindByGroupUid(groupUid, year, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} chargerId
   * @param {number} year
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsightsApi
   */
  public insightsControllerFindByGroupUidAndChargerId(
    groupUid: string,
    chargerId: number,
    year: number,
    options?: RawAxiosRequestConfig
  ) {
    return InsightsApiFp(this.configuration)
      .insightsControllerFindByGroupUidAndChargerId(
        groupUid,
        chargerId,
        year,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {number} year
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsightsApi
   */
  public insightsControllerFindByGroupUidAndSiteId(
    groupUid: string,
    siteId: number,
    year: number,
    options?: RawAxiosRequestConfig
  ) {
    return InsightsApiFp(this.configuration)
      .insightsControllerFindByGroupUidAndSiteId(
        groupUid,
        siteId,
        year,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {string} groupUid
   * @param {number} year
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsightsApi
   */
  public insightsControllerGenerateCsvByGroupUid(
    groupId: number,
    groupUid: string,
    year: number,
    options?: RawAxiosRequestConfig
  ) {
    return InsightsApiFp(this.configuration)
      .insightsControllerGenerateCsvByGroupUid(groupId, groupUid, year, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {string} groupUid
   * @param {number} chargerId
   * @param {number} year
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsightsApi
   */
  public insightsControllerGenerateCsvByGroupUidAndChargerId(
    groupId: number,
    groupUid: string,
    chargerId: number,
    year: number,
    options?: RawAxiosRequestConfig
  ) {
    return InsightsApiFp(this.configuration)
      .insightsControllerGenerateCsvByGroupUidAndChargerId(
        groupId,
        groupUid,
        chargerId,
        year,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {number} year
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsightsApi
   */
  public insightsControllerGenerateCsvByGroupUidAndSiteId(
    groupUid: string,
    siteId: number,
    year: number,
    options?: RawAxiosRequestConfig
  ) {
    return InsightsApiFp(this.configuration)
      .insightsControllerGenerateCsvByGroupUidAndSiteId(
        groupUid,
        siteId,
        year,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * OpeningTimesApi - axios parameter creator
 * @export
 */
export const OpeningTimesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpsertOpeningTimesRequest} upsertOpeningTimesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    openingTimesControllerUpsertByGroupUid: async (
      groupUid: string,
      siteId: number,
      upsertOpeningTimesRequest: UpsertOpeningTimesRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'openingTimesControllerUpsertByGroupUid',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'openingTimesControllerUpsertByGroupUid',
        'siteId',
        siteId
      );
      // verify required parameter 'upsertOpeningTimesRequest' is not null or undefined
      assertParamExists(
        'openingTimesControllerUpsertByGroupUid',
        'upsertOpeningTimesRequest',
        upsertOpeningTimesRequest
      );
      const localVarPath = `/sites/{siteId}/opening-times`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        upsertOpeningTimesRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * OpeningTimesApi - functional programming interface
 * @export
 */
export const OpeningTimesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    OpeningTimesApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpsertOpeningTimesRequest} upsertOpeningTimesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async openingTimesControllerUpsertByGroupUid(
      groupUid: string,
      siteId: number,
      upsertOpeningTimesRequest: UpsertOpeningTimesRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.openingTimesControllerUpsertByGroupUid(
          groupUid,
          siteId,
          upsertOpeningTimesRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'OpeningTimesApi.openingTimesControllerUpsertByGroupUid'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * OpeningTimesApi - factory interface
 * @export
 */
export const OpeningTimesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = OpeningTimesApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {UpsertOpeningTimesRequest} upsertOpeningTimesRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    openingTimesControllerUpsertByGroupUid(
      groupUid: string,
      siteId: number,
      upsertOpeningTimesRequest: UpsertOpeningTimesRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .openingTimesControllerUpsertByGroupUid(
          groupUid,
          siteId,
          upsertOpeningTimesRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * OpeningTimesApi - object-oriented interface
 * @export
 * @class OpeningTimesApi
 * @extends {BaseAPI}
 */
export class OpeningTimesApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {UpsertOpeningTimesRequest} upsertOpeningTimesRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OpeningTimesApi
   */
  public openingTimesControllerUpsertByGroupUid(
    groupUid: string,
    siteId: number,
    upsertOpeningTimesRequest: UpsertOpeningTimesRequest,
    options?: RawAxiosRequestConfig
  ) {
    return OpeningTimesApiFp(this.configuration)
      .openingTimesControllerUpsertByGroupUid(
        groupUid,
        siteId,
        upsertOpeningTimesRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PasswordResetApi - axios parameter creator
 * @export
 */
export const PasswordResetApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {SendPasswordResetRequest} sendPasswordResetRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    passwordResetControllerSendPasswordReset: async (
      sendPasswordResetRequest: SendPasswordResetRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendPasswordResetRequest' is not null or undefined
      assertParamExists(
        'passwordResetControllerSendPasswordReset',
        'sendPasswordResetRequest',
        sendPasswordResetRequest
      );
      const localVarPath = `/admins/password-reset`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendPasswordResetRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PasswordResetApi - functional programming interface
 * @export
 */
export const PasswordResetApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    PasswordResetApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {SendPasswordResetRequest} sendPasswordResetRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async passwordResetControllerSendPasswordReset(
      sendPasswordResetRequest: SendPasswordResetRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.passwordResetControllerSendPasswordReset(
          sendPasswordResetRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'PasswordResetApi.passwordResetControllerSendPasswordReset'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PasswordResetApi - factory interface
 * @export
 */
export const PasswordResetApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PasswordResetApiFp(configuration);
  return {
    /**
     *
     * @param {SendPasswordResetRequest} sendPasswordResetRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    passwordResetControllerSendPasswordReset(
      sendPasswordResetRequest: SendPasswordResetRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .passwordResetControllerSendPasswordReset(
          sendPasswordResetRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PasswordResetApi - object-oriented interface
 * @export
 * @class PasswordResetApi
 * @extends {BaseAPI}
 */
export class PasswordResetApi extends BaseAPI {
  /**
   *
   * @param {SendPasswordResetRequest} sendPasswordResetRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PasswordResetApi
   */
  public passwordResetControllerSendPasswordReset(
    sendPasswordResetRequest: SendPasswordResetRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PasswordResetApiFp(this.configuration)
      .passwordResetControllerSendPasswordReset(
        sendPasswordResetRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PodApi - axios parameter creator
 * @export
 */
export const PodApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podControllerFindByGroupId: async (
      groupUid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists('podControllerFindByGroupId', 'groupUid', groupUid);
      const localVarPath = `/pods`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {string} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podControllerFindByGroupIdAndPodId: async (
      groupId: number,
      podId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'podControllerFindByGroupIdAndPodId',
        'groupId',
        groupId
      );
      // verify required parameter 'podId' is not null or undefined
      assertParamExists('podControllerFindByGroupIdAndPodId', 'podId', podId);
      const localVarPath = `/pods/{podId}`.replace(
        `{${'podId'}}`,
        encodeURIComponent(String(podId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} podId
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podControllerFindSecurityEventsByGroupIdAndPodId: async (
      podId: string,
      groupId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'podId' is not null or undefined
      assertParamExists(
        'podControllerFindSecurityEventsByGroupIdAndPodId',
        'podId',
        podId
      );
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'podControllerFindSecurityEventsByGroupIdAndPodId',
        'groupId',
        groupId
      );
      const localVarPath = `/pods/{podId}/events/security`.replace(
        `{${'podId'}}`,
        encodeURIComponent(String(podId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} podId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podControllerGenerateCsv: async (
      groupId: number,
      groupUid: string,
      podId: number,
      date: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('podControllerGenerateCsv', 'groupId', groupId);
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists('podControllerGenerateCsv', 'groupUid', groupUid);
      // verify required parameter 'podId' is not null or undefined
      assertParamExists('podControllerGenerateCsv', 'podId', podId);
      // verify required parameter 'date' is not null or undefined
      assertParamExists('podControllerGenerateCsv', 'date', date);
      const localVarPath = `/pods/{podId}/charges`.replace(
        `{${'podId'}}`,
        encodeURIComponent(String(podId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (date !== undefined) {
        localVarQueryParameter['date'] = date;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PodApi - functional programming interface
 * @export
 */
export const PodApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = PodApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async podControllerFindByGroupId(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Pod>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.podControllerFindByGroupId(
          groupUid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PodApi.podControllerFindByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {string} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async podControllerFindByGroupIdAndPodId(
      groupId: number,
      podId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Pod>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.podControllerFindByGroupIdAndPodId(
          groupId,
          podId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PodApi.podControllerFindByGroupIdAndPodId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} podId
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async podControllerFindSecurityEventsByGroupIdAndPodId(
      podId: string,
      groupId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.podControllerFindSecurityEventsByGroupIdAndPodId(
          podId,
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'PodApi.podControllerFindSecurityEventsByGroupIdAndPodId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} podId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async podControllerGenerateCsv(
      groupId: number,
      groupUid: string,
      podId: number,
      date: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.podControllerGenerateCsv(
          groupId,
          groupUid,
          podId,
          date,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PodApi.podControllerGenerateCsv']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PodApi - factory interface
 * @export
 */
export const PodApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PodApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podControllerFindByGroupId(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Pod>> {
      return localVarFp
        .podControllerFindByGroupId(groupUid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {string} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podControllerFindByGroupIdAndPodId(
      groupId: number,
      podId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Pod> {
      return localVarFp
        .podControllerFindByGroupIdAndPodId(groupId, podId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} podId
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podControllerFindSecurityEventsByGroupIdAndPodId(
      podId: string,
      groupId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<object> {
      return localVarFp
        .podControllerFindSecurityEventsByGroupIdAndPodId(
          podId,
          groupId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} podId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podControllerGenerateCsv(
      groupId: number,
      groupUid: string,
      podId: number,
      date: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .podControllerGenerateCsv(groupId, groupUid, podId, date, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PodApi - object-oriented interface
 * @export
 * @class PodApi
 * @extends {BaseAPI}
 */
export class PodApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PodApi
   */
  public podControllerFindByGroupId(
    groupUid: string,
    options?: RawAxiosRequestConfig
  ) {
    return PodApiFp(this.configuration)
      .podControllerFindByGroupId(groupUid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {string} podId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PodApi
   */
  public podControllerFindByGroupIdAndPodId(
    groupId: number,
    podId: string,
    options?: RawAxiosRequestConfig
  ) {
    return PodApiFp(this.configuration)
      .podControllerFindByGroupIdAndPodId(groupId, podId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} podId
   * @param {number} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PodApi
   */
  public podControllerFindSecurityEventsByGroupIdAndPodId(
    podId: string,
    groupId: number,
    options?: RawAxiosRequestConfig
  ) {
    return PodApiFp(this.configuration)
      .podControllerFindSecurityEventsByGroupIdAndPodId(podId, groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {string} groupUid
   * @param {number} podId
   * @param {string} date
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PodApi
   */
  public podControllerGenerateCsv(
    groupId: number,
    groupUid: string,
    podId: number,
    date: string,
    options?: RawAxiosRequestConfig
  ) {
    return PodApiFp(this.configuration)
      .podControllerGenerateCsv(groupId, groupUid, podId, date, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PodChargesApi - axios parameter creator
 * @export
 */
export const PodChargesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {number} podId
     * @param {ConfirmChargeRequest} confirmChargeRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podChargesControllerConfirmChargeByGroupIdAndPodId: async (
      groupId: number,
      adminId: number,
      podId: number,
      confirmChargeRequest: ConfirmChargeRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'podChargesControllerConfirmChargeByGroupIdAndPodId',
        'groupId',
        groupId
      );
      // verify required parameter 'adminId' is not null or undefined
      assertParamExists(
        'podChargesControllerConfirmChargeByGroupIdAndPodId',
        'adminId',
        adminId
      );
      // verify required parameter 'podId' is not null or undefined
      assertParamExists(
        'podChargesControllerConfirmChargeByGroupIdAndPodId',
        'podId',
        podId
      );
      // verify required parameter 'confirmChargeRequest' is not null or undefined
      assertParamExists(
        'podChargesControllerConfirmChargeByGroupIdAndPodId',
        'confirmChargeRequest',
        confirmChargeRequest
      );
      const localVarPath = `/pods/{podId}/charges`.replace(
        `{${'podId'}}`,
        encodeURIComponent(String(podId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (adminId !== undefined) {
        localVarQueryParameter['adminId'] = adminId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        confirmChargeRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PodChargesApi - functional programming interface
 * @export
 */
export const PodChargesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    PodChargesApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {number} podId
     * @param {ConfirmChargeRequest} confirmChargeRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async podChargesControllerConfirmChargeByGroupIdAndPodId(
      groupId: number,
      adminId: number,
      podId: number,
      confirmChargeRequest: ConfirmChargeRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.podChargesControllerConfirmChargeByGroupIdAndPodId(
          groupId,
          adminId,
          podId,
          confirmChargeRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'PodChargesApi.podChargesControllerConfirmChargeByGroupIdAndPodId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PodChargesApi - factory interface
 * @export
 */
export const PodChargesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PodChargesApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} adminId
     * @param {number} podId
     * @param {ConfirmChargeRequest} confirmChargeRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podChargesControllerConfirmChargeByGroupIdAndPodId(
      groupId: number,
      adminId: number,
      podId: number,
      confirmChargeRequest: ConfirmChargeRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .podChargesControllerConfirmChargeByGroupIdAndPodId(
          groupId,
          adminId,
          podId,
          confirmChargeRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PodChargesApi - object-oriented interface
 * @export
 * @class PodChargesApi
 * @extends {BaseAPI}
 */
export class PodChargesApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {number} adminId
   * @param {number} podId
   * @param {ConfirmChargeRequest} confirmChargeRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PodChargesApi
   */
  public podChargesControllerConfirmChargeByGroupIdAndPodId(
    groupId: number,
    adminId: number,
    podId: number,
    confirmChargeRequest: ConfirmChargeRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PodChargesApiFp(this.configuration)
      .podChargesControllerConfirmChargeByGroupIdAndPodId(
        groupId,
        adminId,
        podId,
        confirmChargeRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PodTariffApi - axios parameter creator
 * @export
 */
export const PodTariffApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podTariffControllerRemoveByGroupId: async (
      groupId: number,
      podId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'podTariffControllerRemoveByGroupId',
        'groupId',
        groupId
      );
      // verify required parameter 'podId' is not null or undefined
      assertParamExists('podTariffControllerRemoveByGroupId', 'podId', podId);
      const localVarPath = `/pods/{podId}/tariff`.replace(
        `{${'podId'}}`,
        encodeURIComponent(String(podId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} podId
     * @param {UpdateTariffRequest} updateTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podTariffControllerUpdateByGroupId: async (
      groupId: number,
      podId: number,
      updateTariffRequest: UpdateTariffRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'podTariffControllerUpdateByGroupId',
        'groupId',
        groupId
      );
      // verify required parameter 'podId' is not null or undefined
      assertParamExists('podTariffControllerUpdateByGroupId', 'podId', podId);
      // verify required parameter 'updateTariffRequest' is not null or undefined
      assertParamExists(
        'podTariffControllerUpdateByGroupId',
        'updateTariffRequest',
        updateTariffRequest
      );
      const localVarPath = `/pods/{podId}/tariff`.replace(
        `{${'podId'}}`,
        encodeURIComponent(String(podId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateTariffRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PodTariffApi - functional programming interface
 * @export
 */
export const PodTariffApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    PodTariffApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async podTariffControllerRemoveByGroupId(
      groupId: number,
      podId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.podTariffControllerRemoveByGroupId(
          groupId,
          podId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PodTariffApi.podTariffControllerRemoveByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} podId
     * @param {UpdateTariffRequest} updateTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async podTariffControllerUpdateByGroupId(
      groupId: number,
      podId: number,
      updateTariffRequest: UpdateTariffRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.podTariffControllerUpdateByGroupId(
          groupId,
          podId,
          updateTariffRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PodTariffApi.podTariffControllerUpdateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PodTariffApi - factory interface
 * @export
 */
export const PodTariffApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PodTariffApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} podId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podTariffControllerRemoveByGroupId(
      groupId: number,
      podId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .podTariffControllerRemoveByGroupId(groupId, podId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} podId
     * @param {UpdateTariffRequest} updateTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    podTariffControllerUpdateByGroupId(
      groupId: number,
      podId: number,
      updateTariffRequest: UpdateTariffRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .podTariffControllerUpdateByGroupId(
          groupId,
          podId,
          updateTariffRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PodTariffApi - object-oriented interface
 * @export
 * @class PodTariffApi
 * @extends {BaseAPI}
 */
export class PodTariffApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {number} podId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PodTariffApi
   */
  public podTariffControllerRemoveByGroupId(
    groupId: number,
    podId: number,
    options?: RawAxiosRequestConfig
  ) {
    return PodTariffApiFp(this.configuration)
      .podTariffControllerRemoveByGroupId(groupId, podId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} podId
   * @param {UpdateTariffRequest} updateTariffRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PodTariffApi
   */
  public podTariffControllerUpdateByGroupId(
    groupId: number,
    podId: number,
    updateTariffRequest: UpdateTariffRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PodTariffApiFp(this.configuration)
      .podTariffControllerUpdateByGroupId(
        groupId,
        podId,
        updateTariffRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * RfidCardApi - axios parameter creator
 * @export
 */
export const RfidCardApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {string} adminUid
     * @param {CreateRfidCardRequest} createRfidCardRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rfidCardControllerCreateByGroupId: async (
      groupUid: string,
      adminUid: string,
      createRfidCardRequest: CreateRfidCardRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'rfidCardControllerCreateByGroupId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'adminUid' is not null or undefined
      assertParamExists(
        'rfidCardControllerCreateByGroupId',
        'adminUid',
        adminUid
      );
      // verify required parameter 'createRfidCardRequest' is not null or undefined
      assertParamExists(
        'rfidCardControllerCreateByGroupId',
        'createRfidCardRequest',
        createRfidCardRequest
      );
      const localVarPath = `/rfid/cards`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (adminUid !== undefined) {
        localVarQueryParameter['adminUid'] = adminUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createRfidCardRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rfidCardControllerFindByGroupId: async (
      groupUid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'rfidCardControllerFindByGroupId',
        'groupUid',
        groupUid
      );
      const localVarPath = `/rfid/cards`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {string} adminUid
     * @param {string} cardUid
     * @param {UpdateRfidCardRequest} updateRfidCardRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rfidCardControllerUpdateByGroupId: async (
      groupUid: string,
      adminUid: string,
      cardUid: string,
      updateRfidCardRequest: UpdateRfidCardRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'rfidCardControllerUpdateByGroupId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'adminUid' is not null or undefined
      assertParamExists(
        'rfidCardControllerUpdateByGroupId',
        'adminUid',
        adminUid
      );
      // verify required parameter 'cardUid' is not null or undefined
      assertParamExists(
        'rfidCardControllerUpdateByGroupId',
        'cardUid',
        cardUid
      );
      // verify required parameter 'updateRfidCardRequest' is not null or undefined
      assertParamExists(
        'rfidCardControllerUpdateByGroupId',
        'updateRfidCardRequest',
        updateRfidCardRequest
      );
      const localVarPath = `/rfid/cards/{cardUid}`.replace(
        `{${'cardUid'}}`,
        encodeURIComponent(String(cardUid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (adminUid !== undefined) {
        localVarQueryParameter['adminUid'] = adminUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateRfidCardRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * RfidCardApi - functional programming interface
 * @export
 */
export const RfidCardApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = RfidCardApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {string} adminUid
     * @param {CreateRfidCardRequest} createRfidCardRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rfidCardControllerCreateByGroupId(
      groupUid: string,
      adminUid: string,
      createRfidCardRequest: CreateRfidCardRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rfidCardControllerCreateByGroupId(
          groupUid,
          adminUid,
          createRfidCardRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RfidCardApi.rfidCardControllerCreateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rfidCardControllerFindByGroupId(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<RfidCard>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rfidCardControllerFindByGroupId(
          groupUid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RfidCardApi.rfidCardControllerFindByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {string} adminUid
     * @param {string} cardUid
     * @param {UpdateRfidCardRequest} updateRfidCardRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rfidCardControllerUpdateByGroupId(
      groupUid: string,
      adminUid: string,
      cardUid: string,
      updateRfidCardRequest: UpdateRfidCardRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rfidCardControllerUpdateByGroupId(
          groupUid,
          adminUid,
          cardUid,
          updateRfidCardRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RfidCardApi.rfidCardControllerUpdateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * RfidCardApi - factory interface
 * @export
 */
export const RfidCardApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = RfidCardApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {string} adminUid
     * @param {CreateRfidCardRequest} createRfidCardRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rfidCardControllerCreateByGroupId(
      groupUid: string,
      adminUid: string,
      createRfidCardRequest: CreateRfidCardRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .rfidCardControllerCreateByGroupId(
          groupUid,
          adminUid,
          createRfidCardRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rfidCardControllerFindByGroupId(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<RfidCard>> {
      return localVarFp
        .rfidCardControllerFindByGroupId(groupUid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {string} adminUid
     * @param {string} cardUid
     * @param {UpdateRfidCardRequest} updateRfidCardRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rfidCardControllerUpdateByGroupId(
      groupUid: string,
      adminUid: string,
      cardUid: string,
      updateRfidCardRequest: UpdateRfidCardRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .rfidCardControllerUpdateByGroupId(
          groupUid,
          adminUid,
          cardUid,
          updateRfidCardRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * RfidCardApi - object-oriented interface
 * @export
 * @class RfidCardApi
 * @extends {BaseAPI}
 */
export class RfidCardApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {string} adminUid
   * @param {CreateRfidCardRequest} createRfidCardRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RfidCardApi
   */
  public rfidCardControllerCreateByGroupId(
    groupUid: string,
    adminUid: string,
    createRfidCardRequest: CreateRfidCardRequest,
    options?: RawAxiosRequestConfig
  ) {
    return RfidCardApiFp(this.configuration)
      .rfidCardControllerCreateByGroupId(
        groupUid,
        adminUid,
        createRfidCardRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RfidCardApi
   */
  public rfidCardControllerFindByGroupId(
    groupUid: string,
    options?: RawAxiosRequestConfig
  ) {
    return RfidCardApiFp(this.configuration)
      .rfidCardControllerFindByGroupId(groupUid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {string} adminUid
   * @param {string} cardUid
   * @param {UpdateRfidCardRequest} updateRfidCardRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RfidCardApi
   */
  public rfidCardControllerUpdateByGroupId(
    groupUid: string,
    adminUid: string,
    cardUid: string,
    updateRfidCardRequest: UpdateRfidCardRequest,
    options?: RawAxiosRequestConfig
  ) {
    return RfidCardApiFp(this.configuration)
      .rfidCardControllerUpdateByGroupId(
        groupUid,
        adminUid,
        cardUid,
        updateRfidCardRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ScheduleApi - axios parameter creator
 * @export
 */
export const ScheduleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateDayNightTariffScheduleRequest} createDayNightTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId: async (
      groupId: number,
      tariffId: number,
      createDayNightTariffScheduleRequest: CreateDayNightTariffScheduleRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId',
        'groupId',
        groupId
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId',
        'tariffId',
        tariffId
      );
      // verify required parameter 'createDayNightTariffScheduleRequest' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId',
        'createDayNightTariffScheduleRequest',
        createDayNightTariffScheduleRequest
      );
      const localVarPath = `/tariffs/{tariffId}/schedules/daynight`.replace(
        `{${'tariffId'}}`,
        encodeURIComponent(String(tariffId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createDayNightTariffScheduleRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateTariffScheduleRequest} createTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerCreateScheduleByGroupIdAndTariffId: async (
      groupId: number,
      tariffId: number,
      createTariffScheduleRequest: CreateTariffScheduleRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateScheduleByGroupIdAndTariffId',
        'groupId',
        groupId
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateScheduleByGroupIdAndTariffId',
        'tariffId',
        tariffId
      );
      // verify required parameter 'createTariffScheduleRequest' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateScheduleByGroupIdAndTariffId',
        'createTariffScheduleRequest',
        createTariffScheduleRequest
      );
      const localVarPath = `/tariffs/{tariffId}/schedules`.replace(
        `{${'tariffId'}}`,
        encodeURIComponent(String(tariffId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createTariffScheduleRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateWorkWeekTariffScheduleRequest} createWorkWeekTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId: async (
      groupId: number,
      tariffId: number,
      createWorkWeekTariffScheduleRequest: CreateWorkWeekTariffScheduleRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId',
        'groupId',
        groupId
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId',
        'tariffId',
        tariffId
      );
      // verify required parameter 'createWorkWeekTariffScheduleRequest' is not null or undefined
      assertParamExists(
        'scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId',
        'createWorkWeekTariffScheduleRequest',
        createWorkWeekTariffScheduleRequest
      );
      const localVarPath = `/tariffs/{tariffId}/schedules/workweek`.replace(
        `{${'tariffId'}}`,
        encodeURIComponent(String(tariffId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createWorkWeekTariffScheduleRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {DeleteTariffScheduleRequest} deleteTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerDeleteByGroupId: async (
      groupId: number,
      tariffId: number,
      deleteTariffScheduleRequest: DeleteTariffScheduleRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'scheduleControllerDeleteByGroupId',
        'groupId',
        groupId
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'scheduleControllerDeleteByGroupId',
        'tariffId',
        tariffId
      );
      // verify required parameter 'deleteTariffScheduleRequest' is not null or undefined
      assertParamExists(
        'scheduleControllerDeleteByGroupId',
        'deleteTariffScheduleRequest',
        deleteTariffScheduleRequest
      );
      const localVarPath = `/tariffs/{tariffId}/schedules`.replace(
        `{${'tariffId'}}`,
        encodeURIComponent(String(tariffId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        deleteTariffScheduleRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {number} scheduleId
     * @param {CreateTariffScheduleRequest} createTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerUpdateScheduleByGroupIdAndTariffId: async (
      groupId: number,
      tariffId: number,
      scheduleId: number,
      createTariffScheduleRequest: CreateTariffScheduleRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'scheduleControllerUpdateScheduleByGroupIdAndTariffId',
        'groupId',
        groupId
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'scheduleControllerUpdateScheduleByGroupIdAndTariffId',
        'tariffId',
        tariffId
      );
      // verify required parameter 'scheduleId' is not null or undefined
      assertParamExists(
        'scheduleControllerUpdateScheduleByGroupIdAndTariffId',
        'scheduleId',
        scheduleId
      );
      // verify required parameter 'createTariffScheduleRequest' is not null or undefined
      assertParamExists(
        'scheduleControllerUpdateScheduleByGroupIdAndTariffId',
        'createTariffScheduleRequest',
        createTariffScheduleRequest
      );
      const localVarPath = `/tariffs/{tariffId}/schedules/{scheduleId}`
        .replace(`{${'tariffId'}}`, encodeURIComponent(String(tariffId)))
        .replace(`{${'scheduleId'}}`, encodeURIComponent(String(scheduleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createTariffScheduleRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ScheduleApi - functional programming interface
 * @export
 */
export const ScheduleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ScheduleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateDayNightTariffScheduleRequest} createDayNightTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      createDayNightTariffScheduleRequest: CreateDayNightTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId(
          groupId,
          tariffId,
          createDayNightTariffScheduleRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ScheduleApi.scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateTariffScheduleRequest} createTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async scheduleControllerCreateScheduleByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      createTariffScheduleRequest: CreateTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.scheduleControllerCreateScheduleByGroupIdAndTariffId(
          groupId,
          tariffId,
          createTariffScheduleRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ScheduleApi.scheduleControllerCreateScheduleByGroupIdAndTariffId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateWorkWeekTariffScheduleRequest} createWorkWeekTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      createWorkWeekTariffScheduleRequest: CreateWorkWeekTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId(
          groupId,
          tariffId,
          createWorkWeekTariffScheduleRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ScheduleApi.scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {DeleteTariffScheduleRequest} deleteTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async scheduleControllerDeleteByGroupId(
      groupId: number,
      tariffId: number,
      deleteTariffScheduleRequest: DeleteTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.scheduleControllerDeleteByGroupId(
          groupId,
          tariffId,
          deleteTariffScheduleRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ScheduleApi.scheduleControllerDeleteByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {number} scheduleId
     * @param {CreateTariffScheduleRequest} createTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async scheduleControllerUpdateScheduleByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      scheduleId: number,
      createTariffScheduleRequest: CreateTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.scheduleControllerUpdateScheduleByGroupIdAndTariffId(
          groupId,
          tariffId,
          scheduleId,
          createTariffScheduleRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ScheduleApi.scheduleControllerUpdateScheduleByGroupIdAndTariffId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ScheduleApi - factory interface
 * @export
 */
export const ScheduleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ScheduleApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateDayNightTariffScheduleRequest} createDayNightTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      createDayNightTariffScheduleRequest: CreateDayNightTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId(
          groupId,
          tariffId,
          createDayNightTariffScheduleRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateTariffScheduleRequest} createTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerCreateScheduleByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      createTariffScheduleRequest: CreateTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .scheduleControllerCreateScheduleByGroupIdAndTariffId(
          groupId,
          tariffId,
          createTariffScheduleRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateWorkWeekTariffScheduleRequest} createWorkWeekTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      createWorkWeekTariffScheduleRequest: CreateWorkWeekTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId(
          groupId,
          tariffId,
          createWorkWeekTariffScheduleRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {DeleteTariffScheduleRequest} deleteTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerDeleteByGroupId(
      groupId: number,
      tariffId: number,
      deleteTariffScheduleRequest: DeleteTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .scheduleControllerDeleteByGroupId(
          groupId,
          tariffId,
          deleteTariffScheduleRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {number} scheduleId
     * @param {CreateTariffScheduleRequest} createTariffScheduleRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    scheduleControllerUpdateScheduleByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      scheduleId: number,
      createTariffScheduleRequest: CreateTariffScheduleRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .scheduleControllerUpdateScheduleByGroupIdAndTariffId(
          groupId,
          tariffId,
          scheduleId,
          createTariffScheduleRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ScheduleApi - object-oriented interface
 * @export
 * @class ScheduleApi
 * @extends {BaseAPI}
 */
export class ScheduleApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {number} tariffId
   * @param {CreateDayNightTariffScheduleRequest} createDayNightTariffScheduleRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScheduleApi
   */
  public scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId(
    groupId: number,
    tariffId: number,
    createDayNightTariffScheduleRequest: CreateDayNightTariffScheduleRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ScheduleApiFp(this.configuration)
      .scheduleControllerCreateDayNightScheduleByGroupIdAndTariffId(
        groupId,
        tariffId,
        createDayNightTariffScheduleRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} tariffId
   * @param {CreateTariffScheduleRequest} createTariffScheduleRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScheduleApi
   */
  public scheduleControllerCreateScheduleByGroupIdAndTariffId(
    groupId: number,
    tariffId: number,
    createTariffScheduleRequest: CreateTariffScheduleRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ScheduleApiFp(this.configuration)
      .scheduleControllerCreateScheduleByGroupIdAndTariffId(
        groupId,
        tariffId,
        createTariffScheduleRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} tariffId
   * @param {CreateWorkWeekTariffScheduleRequest} createWorkWeekTariffScheduleRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScheduleApi
   */
  public scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId(
    groupId: number,
    tariffId: number,
    createWorkWeekTariffScheduleRequest: CreateWorkWeekTariffScheduleRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ScheduleApiFp(this.configuration)
      .scheduleControllerCreateWorkWeekScheduleByGroupIdAndTariffId(
        groupId,
        tariffId,
        createWorkWeekTariffScheduleRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} tariffId
   * @param {DeleteTariffScheduleRequest} deleteTariffScheduleRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScheduleApi
   */
  public scheduleControllerDeleteByGroupId(
    groupId: number,
    tariffId: number,
    deleteTariffScheduleRequest: DeleteTariffScheduleRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ScheduleApiFp(this.configuration)
      .scheduleControllerDeleteByGroupId(
        groupId,
        tariffId,
        deleteTariffScheduleRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} tariffId
   * @param {number} scheduleId
   * @param {CreateTariffScheduleRequest} createTariffScheduleRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScheduleApi
   */
  public scheduleControllerUpdateScheduleByGroupIdAndTariffId(
    groupId: number,
    tariffId: number,
    scheduleId: number,
    createTariffScheduleRequest: CreateTariffScheduleRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ScheduleApiFp(this.configuration)
      .scheduleControllerUpdateScheduleByGroupIdAndTariffId(
        groupId,
        tariffId,
        scheduleId,
        createTariffScheduleRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SignInWithEmailApi - axios parameter creator
 * @export
 */
export const SignInWithEmailApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {SendSignInWithEmailRequest} sendSignInWithEmailRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    signInWithEmailControllerSendSignInWithEmail: async (
      sendSignInWithEmailRequest: SendSignInWithEmailRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendSignInWithEmailRequest' is not null or undefined
      assertParamExists(
        'signInWithEmailControllerSendSignInWithEmail',
        'sendSignInWithEmailRequest',
        sendSignInWithEmailRequest
      );
      const localVarPath = `/admins/sign-in-with-email`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendSignInWithEmailRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SignInWithEmailApi - functional programming interface
 * @export
 */
export const SignInWithEmailApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    SignInWithEmailApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {SendSignInWithEmailRequest} sendSignInWithEmailRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async signInWithEmailControllerSendSignInWithEmail(
      sendSignInWithEmailRequest: SendSignInWithEmailRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.signInWithEmailControllerSendSignInWithEmail(
          sendSignInWithEmailRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SignInWithEmailApi.signInWithEmailControllerSendSignInWithEmail'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SignInWithEmailApi - factory interface
 * @export
 */
export const SignInWithEmailApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SignInWithEmailApiFp(configuration);
  return {
    /**
     *
     * @param {SendSignInWithEmailRequest} sendSignInWithEmailRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    signInWithEmailControllerSendSignInWithEmail(
      sendSignInWithEmailRequest: SendSignInWithEmailRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .signInWithEmailControllerSendSignInWithEmail(
          sendSignInWithEmailRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SignInWithEmailApi - object-oriented interface
 * @export
 * @class SignInWithEmailApi
 * @extends {BaseAPI}
 */
export class SignInWithEmailApi extends BaseAPI {
  /**
   *
   * @param {SendSignInWithEmailRequest} sendSignInWithEmailRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SignInWithEmailApi
   */
  public signInWithEmailControllerSendSignInWithEmail(
    sendSignInWithEmailRequest: SendSignInWithEmailRequest,
    options?: RawAxiosRequestConfig
  ) {
    return SignInWithEmailApiFp(this.configuration)
      .signInWithEmailControllerSendSignInWithEmail(
        sendSignInWithEmailRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SiteApi - axios parameter creator
 * @export
 */
export const SiteApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    siteControllerFindByGroupUid: async (
      groupUid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists('siteControllerFindByGroupUid', 'groupUid', groupUid);
      const localVarPath = `/sites`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    siteControllerFindByGroupUidAndSiteId: async (
      groupUid: string,
      siteId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'siteControllerFindByGroupUidAndSiteId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'siteControllerFindByGroupUidAndSiteId',
        'siteId',
        siteId
      );
      const localVarPath = `/sites/{siteId}`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    siteControllerGenerateCsv: async (
      groupUid: string,
      siteId: number,
      date: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists('siteControllerGenerateCsv', 'groupUid', groupUid);
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists('siteControllerGenerateCsv', 'siteId', siteId);
      // verify required parameter 'date' is not null or undefined
      assertParamExists('siteControllerGenerateCsv', 'date', date);
      const localVarPath = `/sites/{siteId}/charges`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (date !== undefined) {
        localVarQueryParameter['date'] = date;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SiteApi - functional programming interface
 * @export
 */
export const SiteApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = SiteApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async siteControllerFindByGroupUid(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Site>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.siteControllerFindByGroupUid(
          groupUid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SiteApi.siteControllerFindByGroupUid']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async siteControllerFindByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Site>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.siteControllerFindByGroupUidAndSiteId(
          groupUid,
          siteId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SiteApi.siteControllerFindByGroupUidAndSiteId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async siteControllerGenerateCsv(
      groupUid: string,
      siteId: number,
      date: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.siteControllerGenerateCsv(
          groupUid,
          siteId,
          date,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SiteApi.siteControllerGenerateCsv']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SiteApi - factory interface
 * @export
 */
export const SiteApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SiteApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    siteControllerFindByGroupUid(
      groupUid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Site>> {
      return localVarFp
        .siteControllerFindByGroupUid(groupUid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    siteControllerFindByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Site> {
      return localVarFp
        .siteControllerFindByGroupUidAndSiteId(groupUid, siteId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    siteControllerGenerateCsv(
      groupUid: string,
      siteId: number,
      date: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .siteControllerGenerateCsv(groupUid, siteId, date, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SiteApi - object-oriented interface
 * @export
 * @class SiteApi
 * @extends {BaseAPI}
 */
export class SiteApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SiteApi
   */
  public siteControllerFindByGroupUid(
    groupUid: string,
    options?: RawAxiosRequestConfig
  ) {
    return SiteApiFp(this.configuration)
      .siteControllerFindByGroupUid(groupUid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SiteApi
   */
  public siteControllerFindByGroupUidAndSiteId(
    groupUid: string,
    siteId: number,
    options?: RawAxiosRequestConfig
  ) {
    return SiteApiFp(this.configuration)
      .siteControllerFindByGroupUidAndSiteId(groupUid, siteId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {string} date
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SiteApi
   */
  public siteControllerGenerateCsv(
    groupUid: string,
    siteId: number,
    date: string,
    options?: RawAxiosRequestConfig
  ) {
    return SiteApiFp(this.configuration)
      .siteControllerGenerateCsv(groupUid, siteId, date, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * StatementApi - axios parameter creator
 * @export
 */
export const StatementApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} period
     * @param {string} dateOverride
     * @param {CreateSiteStatementRequest} createSiteStatementRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerCreateByGroupUidAndSiteId: async (
      groupUid: string,
      siteId: number,
      period: number,
      dateOverride: string,
      createSiteStatementRequest: CreateSiteStatementRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'statementControllerCreateByGroupUidAndSiteId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'statementControllerCreateByGroupUidAndSiteId',
        'siteId',
        siteId
      );
      // verify required parameter 'period' is not null or undefined
      assertParamExists(
        'statementControllerCreateByGroupUidAndSiteId',
        'period',
        period
      );
      // verify required parameter 'dateOverride' is not null or undefined
      assertParamExists(
        'statementControllerCreateByGroupUidAndSiteId',
        'dateOverride',
        dateOverride
      );
      // verify required parameter 'createSiteStatementRequest' is not null or undefined
      assertParamExists(
        'statementControllerCreateByGroupUidAndSiteId',
        'createSiteStatementRequest',
        createSiteStatementRequest
      );
      const localVarPath = `/sites/{siteId}/statement`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (period !== undefined) {
        localVarQueryParameter['period'] = period;
      }

      if (dateOverride !== undefined) {
        localVarQueryParameter['dateOverride'] = dateOverride;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createSiteStatementRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} period
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerFindByGroupUidAndSiteId: async (
      groupUid: string,
      siteId: number,
      period: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'statementControllerFindByGroupUidAndSiteId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'statementControllerFindByGroupUidAndSiteId',
        'siteId',
        siteId
      );
      // verify required parameter 'period' is not null or undefined
      assertParamExists(
        'statementControllerFindByGroupUidAndSiteId',
        'period',
        period
      );
      const localVarPath = `/sites/{siteId}/statement`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      if (period !== undefined) {
        localVarQueryParameter['period'] = period;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * StatementApi - functional programming interface
 * @export
 */
export const StatementApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    StatementApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} period
     * @param {string} dateOverride
     * @param {CreateSiteStatementRequest} createSiteStatementRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async statementControllerCreateByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      period: number,
      dateOverride: string,
      createSiteStatementRequest: CreateSiteStatementRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<SiteStatement>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.statementControllerCreateByGroupUidAndSiteId(
          groupUid,
          siteId,
          period,
          dateOverride,
          createSiteStatementRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'StatementApi.statementControllerCreateByGroupUidAndSiteId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} period
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async statementControllerFindByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      period: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<SiteStatement>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.statementControllerFindByGroupUidAndSiteId(
          groupUid,
          siteId,
          period,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'StatementApi.statementControllerFindByGroupUidAndSiteId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * StatementApi - factory interface
 * @export
 */
export const StatementApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = StatementApiFp(configuration);
  return {
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} period
     * @param {string} dateOverride
     * @param {CreateSiteStatementRequest} createSiteStatementRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerCreateByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      period: number,
      dateOverride: string,
      createSiteStatementRequest: CreateSiteStatementRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteStatement> {
      return localVarFp
        .statementControllerCreateByGroupUidAndSiteId(
          groupUid,
          siteId,
          period,
          dateOverride,
          createSiteStatementRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} groupUid
     * @param {number} siteId
     * @param {number} period
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statementControllerFindByGroupUidAndSiteId(
      groupUid: string,
      siteId: number,
      period: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteStatement> {
      return localVarFp
        .statementControllerFindByGroupUidAndSiteId(
          groupUid,
          siteId,
          period,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * StatementApi - object-oriented interface
 * @export
 * @class StatementApi
 * @extends {BaseAPI}
 */
export class StatementApi extends BaseAPI {
  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {number} period
   * @param {string} dateOverride
   * @param {CreateSiteStatementRequest} createSiteStatementRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof StatementApi
   */
  public statementControllerCreateByGroupUidAndSiteId(
    groupUid: string,
    siteId: number,
    period: number,
    dateOverride: string,
    createSiteStatementRequest: CreateSiteStatementRequest,
    options?: RawAxiosRequestConfig
  ) {
    return StatementApiFp(this.configuration)
      .statementControllerCreateByGroupUidAndSiteId(
        groupUid,
        siteId,
        period,
        dateOverride,
        createSiteStatementRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} groupUid
   * @param {number} siteId
   * @param {number} period
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof StatementApi
   */
  public statementControllerFindByGroupUidAndSiteId(
    groupUid: string,
    siteId: number,
    period: number,
    options?: RawAxiosRequestConfig
  ) {
    return StatementApiFp(this.configuration)
      .statementControllerFindByGroupUidAndSiteId(
        groupUid,
        siteId,
        period,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * StatsApi - axios parameter creator
 * @export
 */
export const StatsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statsControllerGenerateCsv: async (
      groupId: number,
      date: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('statsControllerGenerateCsv', 'groupId', groupId);
      // verify required parameter 'date' is not null or undefined
      assertParamExists('statsControllerGenerateCsv', 'date', date);
      const localVarPath = `/sites/stats/csv`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (date !== undefined) {
        localVarQueryParameter['date'] = date;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * StatsApi - functional programming interface
 * @export
 */
export const StatsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = StatsApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async statsControllerGenerateCsv(
      groupId: number,
      date: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.statsControllerGenerateCsv(
          groupId,
          date,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['StatsApi.statsControllerGenerateCsv']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * StatsApi - factory interface
 * @export
 */
export const StatsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = StatsApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} date
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    statsControllerGenerateCsv(
      groupId: number,
      date: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .statsControllerGenerateCsv(groupId, date, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * StatsApi - object-oriented interface
 * @export
 * @class StatsApi
 * @extends {BaseAPI}
 */
export class StatsApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {string} date
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof StatsApi
   */
  public statsControllerGenerateCsv(
    groupId: number,
    date: string,
    options?: RawAxiosRequestConfig
  ) {
    return StatsApiFp(this.configuration)
      .statsControllerGenerateCsv(groupId, date, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * TariffApi - axios parameter creator
 * @export
 */
export const TariffApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {CreateTariffRequest} createTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerCreateByGroupId: async (
      groupId: number,
      createTariffRequest: CreateTariffRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('tariffControllerCreateByGroupId', 'groupId', groupId);
      // verify required parameter 'createTariffRequest' is not null or undefined
      assertParamExists(
        'tariffControllerCreateByGroupId',
        'createTariffRequest',
        createTariffRequest
      );
      const localVarPath = `/tariffs`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createTariffRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerDeleteByGroupId: async (
      groupId: number,
      tariffId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('tariffControllerDeleteByGroupId', 'groupId', groupId);
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'tariffControllerDeleteByGroupId',
        'tariffId',
        tariffId
      );
      const localVarPath = `/tariffs/{tariffId}`.replace(
        `{${'tariffId'}}`,
        encodeURIComponent(String(tariffId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerFindAll: async (
      groupId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('tariffControllerFindAll', 'groupId', groupId);
      const localVarPath = `/tariffs`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerFindByGroupIdAndTariffId: async (
      groupId: number,
      tariffId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'tariffControllerFindByGroupIdAndTariffId',
        'groupId',
        groupId
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'tariffControllerFindByGroupIdAndTariffId',
        'tariffId',
        tariffId
      );
      const localVarPath = `/tariffs/{tariffId}`.replace(
        `{${'tariffId'}}`,
        encodeURIComponent(String(tariffId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateTariffRequest} createTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerUpdateByGroupId: async (
      groupId: number,
      tariffId: number,
      createTariffRequest: CreateTariffRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('tariffControllerUpdateByGroupId', 'groupId', groupId);
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'tariffControllerUpdateByGroupId',
        'tariffId',
        tariffId
      );
      // verify required parameter 'createTariffRequest' is not null or undefined
      assertParamExists(
        'tariffControllerUpdateByGroupId',
        'createTariffRequest',
        createTariffRequest
      );
      const localVarPath = `/tariffs/{tariffId}`.replace(
        `{${'tariffId'}}`,
        encodeURIComponent(String(tariffId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createTariffRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * TariffApi - functional programming interface
 * @export
 */
export const TariffApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = TariffApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {CreateTariffRequest} createTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async tariffControllerCreateByGroupId(
      groupId: number,
      createTariffRequest: CreateTariffRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Tariff>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.tariffControllerCreateByGroupId(
          groupId,
          createTariffRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TariffApi.tariffControllerCreateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async tariffControllerDeleteByGroupId(
      groupId: number,
      tariffId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.tariffControllerDeleteByGroupId(
          groupId,
          tariffId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TariffApi.tariffControllerDeleteByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async tariffControllerFindAll(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Tariff>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.tariffControllerFindAll(
          groupId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TariffApi.tariffControllerFindAll']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async tariffControllerFindByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Tariff>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.tariffControllerFindByGroupIdAndTariffId(
          groupId,
          tariffId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'TariffApi.tariffControllerFindByGroupIdAndTariffId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateTariffRequest} createTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async tariffControllerUpdateByGroupId(
      groupId: number,
      tariffId: number,
      createTariffRequest: CreateTariffRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.tariffControllerUpdateByGroupId(
          groupId,
          tariffId,
          createTariffRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TariffApi.tariffControllerUpdateByGroupId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * TariffApi - factory interface
 * @export
 */
export const TariffApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = TariffApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {CreateTariffRequest} createTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerCreateByGroupId(
      groupId: number,
      createTariffRequest: CreateTariffRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Tariff> {
      return localVarFp
        .tariffControllerCreateByGroupId(groupId, createTariffRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerDeleteByGroupId(
      groupId: number,
      tariffId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .tariffControllerDeleteByGroupId(groupId, tariffId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerFindAll(
      groupId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Tariff>> {
      return localVarFp
        .tariffControllerFindAll(groupId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerFindByGroupIdAndTariffId(
      groupId: number,
      tariffId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Tariff> {
      return localVarFp
        .tariffControllerFindByGroupIdAndTariffId(groupId, tariffId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {number} groupId
     * @param {number} tariffId
     * @param {CreateTariffRequest} createTariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffControllerUpdateByGroupId(
      groupId: number,
      tariffId: number,
      createTariffRequest: CreateTariffRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .tariffControllerUpdateByGroupId(
          groupId,
          tariffId,
          createTariffRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * TariffApi - object-oriented interface
 * @export
 * @class TariffApi
 * @extends {BaseAPI}
 */
export class TariffApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {CreateTariffRequest} createTariffRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TariffApi
   */
  public tariffControllerCreateByGroupId(
    groupId: number,
    createTariffRequest: CreateTariffRequest,
    options?: RawAxiosRequestConfig
  ) {
    return TariffApiFp(this.configuration)
      .tariffControllerCreateByGroupId(groupId, createTariffRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} tariffId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TariffApi
   */
  public tariffControllerDeleteByGroupId(
    groupId: number,
    tariffId: number,
    options?: RawAxiosRequestConfig
  ) {
    return TariffApiFp(this.configuration)
      .tariffControllerDeleteByGroupId(groupId, tariffId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TariffApi
   */
  public tariffControllerFindAll(
    groupId: number,
    options?: RawAxiosRequestConfig
  ) {
    return TariffApiFp(this.configuration)
      .tariffControllerFindAll(groupId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} tariffId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TariffApi
   */
  public tariffControllerFindByGroupIdAndTariffId(
    groupId: number,
    tariffId: number,
    options?: RawAxiosRequestConfig
  ) {
    return TariffApiFp(this.configuration)
      .tariffControllerFindByGroupIdAndTariffId(groupId, tariffId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {number} groupId
   * @param {number} tariffId
   * @param {CreateTariffRequest} createTariffRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TariffApi
   */
  public tariffControllerUpdateByGroupId(
    groupId: number,
    tariffId: number,
    createTariffRequest: CreateTariffRequest,
    options?: RawAxiosRequestConfig
  ) {
    return TariffApiFp(this.configuration)
      .tariffControllerUpdateByGroupId(
        groupId,
        tariffId,
        createTariffRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * TariffPodApi - axios parameter creator
 * @export
 */
export const TariffPodApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} tariffId
     * @param {AssignTariffPodsRequest} assignTariffPodsRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffPodControllerUpdateByGroupIdAndTariffId: async (
      groupId: number,
      groupUid: string,
      tariffId: number,
      assignTariffPodsRequest: AssignTariffPodsRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'tariffPodControllerUpdateByGroupIdAndTariffId',
        'groupId',
        groupId
      );
      // verify required parameter 'groupUid' is not null or undefined
      assertParamExists(
        'tariffPodControllerUpdateByGroupIdAndTariffId',
        'groupUid',
        groupUid
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'tariffPodControllerUpdateByGroupIdAndTariffId',
        'tariffId',
        tariffId
      );
      // verify required parameter 'assignTariffPodsRequest' is not null or undefined
      assertParamExists(
        'tariffPodControllerUpdateByGroupIdAndTariffId',
        'assignTariffPodsRequest',
        assignTariffPodsRequest
      );
      const localVarPath = `/tariffs/{tariffId}/pods`.replace(
        `{${'tariffId'}}`,
        encodeURIComponent(String(tariffId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (groupUid !== undefined) {
        localVarQueryParameter['groupUid'] = groupUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        assignTariffPodsRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * TariffPodApi - functional programming interface
 * @export
 */
export const TariffPodApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    TariffPodApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} tariffId
     * @param {AssignTariffPodsRequest} assignTariffPodsRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async tariffPodControllerUpdateByGroupIdAndTariffId(
      groupId: number,
      groupUid: string,
      tariffId: number,
      assignTariffPodsRequest: AssignTariffPodsRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.tariffPodControllerUpdateByGroupIdAndTariffId(
          groupId,
          groupUid,
          tariffId,
          assignTariffPodsRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'TariffPodApi.tariffPodControllerUpdateByGroupIdAndTariffId'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * TariffPodApi - factory interface
 * @export
 */
export const TariffPodApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = TariffPodApiFp(configuration);
  return {
    /**
     *
     * @param {number} groupId
     * @param {string} groupUid
     * @param {number} tariffId
     * @param {AssignTariffPodsRequest} assignTariffPodsRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    tariffPodControllerUpdateByGroupIdAndTariffId(
      groupId: number,
      groupUid: string,
      tariffId: number,
      assignTariffPodsRequest: AssignTariffPodsRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .tariffPodControllerUpdateByGroupIdAndTariffId(
          groupId,
          groupUid,
          tariffId,
          assignTariffPodsRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * TariffPodApi - object-oriented interface
 * @export
 * @class TariffPodApi
 * @extends {BaseAPI}
 */
export class TariffPodApi extends BaseAPI {
  /**
   *
   * @param {number} groupId
   * @param {string} groupUid
   * @param {number} tariffId
   * @param {AssignTariffPodsRequest} assignTariffPodsRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TariffPodApi
   */
  public tariffPodControllerUpdateByGroupIdAndTariffId(
    groupId: number,
    groupUid: string,
    tariffId: number,
    assignTariffPodsRequest: AssignTariffPodsRequest,
    options?: RawAxiosRequestConfig
  ) {
    return TariffPodApiFp(this.configuration)
      .tariffPodControllerUpdateByGroupIdAndTariffId(
        groupId,
        groupUid,
        tariffId,
        assignTariffPodsRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * UserApi - axios parameter creator
 * @export
 */
export const UserApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerAcceptTermsAndConditions: async (
      adminUid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'adminUid' is not null or undefined
      assertParamExists(
        'userControllerAcceptTermsAndConditions',
        'adminUid',
        adminUid
      );
      const localVarPath = `/user/terms-and-conditions/acceptance`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (adminUid !== undefined) {
        localVarQueryParameter['adminUid'] = adminUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerFindByUserId: async (
      adminUid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'adminUid' is not null or undefined
      assertParamExists('userControllerFindByUserId', 'adminUid', adminUid);
      const localVarPath = `/user`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (adminUid !== undefined) {
        localVarQueryParameter['adminUid'] = adminUid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} adminUid
     * @param {UpdateUserRequest} updateUserRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUpdateByUserUid: async (
      adminUid: string,
      updateUserRequest: UpdateUserRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'adminUid' is not null or undefined
      assertParamExists('userControllerUpdateByUserUid', 'adminUid', adminUid);
      // verify required parameter 'updateUserRequest' is not null or undefined
      assertParamExists(
        'userControllerUpdateByUserUid',
        'updateUserRequest',
        updateUserRequest
      );
      const localVarPath = `/user`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (adminUid !== undefined) {
        localVarQueryParameter['adminUid'] = adminUid;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateUserRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * UserApi - functional programming interface
 * @export
 */
export const UserApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = UserApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerAcceptTermsAndConditions(
      adminUid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerAcceptTermsAndConditions(
          adminUid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UserApi.userControllerAcceptTermsAndConditions']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerFindByUserId(
      adminUid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<User>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerFindByUserId(
          adminUid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UserApi.userControllerFindByUserId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} adminUid
     * @param {UpdateUserRequest} updateUserRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userControllerUpdateByUserUid(
      adminUid: string,
      updateUserRequest: UpdateUserRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userControllerUpdateByUserUid(
          adminUid,
          updateUserRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UserApi.userControllerUpdateByUserUid']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * UserApi - factory interface
 * @export
 */
export const UserApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = UserApiFp(configuration);
  return {
    /**
     *
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerAcceptTermsAndConditions(
      adminUid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .userControllerAcceptTermsAndConditions(adminUid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} adminUid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerFindByUserId(
      adminUid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<User> {
      return localVarFp
        .userControllerFindByUserId(adminUid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} adminUid
     * @param {UpdateUserRequest} updateUserRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userControllerUpdateByUserUid(
      adminUid: string,
      updateUserRequest: UpdateUserRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .userControllerUpdateByUserUid(adminUid, updateUserRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * UserApi - object-oriented interface
 * @export
 * @class UserApi
 * @extends {BaseAPI}
 */
export class UserApi extends BaseAPI {
  /**
   *
   * @param {string} adminUid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApi
   */
  public userControllerAcceptTermsAndConditions(
    adminUid: string,
    options?: RawAxiosRequestConfig
  ) {
    return UserApiFp(this.configuration)
      .userControllerAcceptTermsAndConditions(adminUid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} adminUid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApi
   */
  public userControllerFindByUserId(
    adminUid: string,
    options?: RawAxiosRequestConfig
  ) {
    return UserApiFp(this.configuration)
      .userControllerFindByUserId(adminUid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} adminUid
   * @param {UpdateUserRequest} updateUserRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApi
   */
  public userControllerUpdateByUserUid(
    adminUid: string,
    updateUserRequest: UpdateUserRequest,
    options?: RawAxiosRequestConfig
  ) {
    return UserApiFp(this.configuration)
      .userControllerUpdateByUserUid(adminUid, updateUserRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * VersionApi - axios parameter creator
 * @export
 */
export const VersionApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/version`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * VersionApi - functional programming interface
 * @export
 */
export const VersionApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = VersionApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.versionControllerGetVersion(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VersionApi.versionControllerGetVersion']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * VersionApi - factory interface
 * @export
 */
export const VersionApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = VersionApiFp(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .versionControllerGetVersion(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * VersionApi - object-oriented interface
 * @export
 * @class VersionApi
 * @extends {BaseAPI}
 */
export class VersionApi extends BaseAPI {
  /**
   *
   * @summary get application version
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VersionApi
   */
  public versionControllerGetVersion(options?: RawAxiosRequestConfig) {
    return VersionApiFp(this.configuration)
      .versionControllerGetVersion(options)
      .then((request) => request(this.axios, this.basePath));
  }
}
