{"id": "direct-debit", "name": "Direct Debit", "sections": [{"title": "Account details", "fields": [{"id": "accountNumber", "label": "Account Number", "type": "bankAccountNumber", "validators": [{"type": "required"}]}, {"id": "sortCode", "label": "Sort Code", "type": "sortCode", "validators": [{"type": "required"}]}, {"id": "nameOnAccount", "label": "Name on Account", "type": "text", "validators": [{"type": "required"}]}, {"id": "emailAddress", "label": "Email Address", "type": "email", "validators": [{"type": "required"}]}, {"id": "phoneNumber", "label": "Phone number", "type": "phoneNumber", "validators": [{"type": "required"}]}]}, {"title": "Billing Address", "fields": [{"id": "country", "label": "Country", "type": "select", "values": [{"value": "en", "label": "England"}, {"value": "sl", "label": "Scotland"}], "validators": [{"type": "required"}]}, {"id": "addressLine1", "label": "Address Line 1", "type": "text", "validators": [{"type": "required"}]}, {"id": "addressLine2", "label": "Address Line 2", "type": "text"}, {"id": "cityOrTown", "label": "City or Town", "type": "text", "validators": [{"type": "required"}]}, {"id": "postCode", "label": "Post Code", "type": "postCode", "validators": [{"type": "required"}]}, {"id": " confirmationStatement<PERSON><PERSON>ck", "type": "checkbox", "validators": [{"type": "required"}], "values": [{"value": true, "label": "I confirm I am the account holder and the only person required to authorise debits from this account."}]}, {"id": " directDebitGuarantee", "type": "infoMarkdown", "values": [{"value": "By pressing 'sign' I understand that payments are protected by the [Direct Debit Guarantee](launchDirectDebitDepositGuaranteeModal)."}]}]}]}