{"id": "abc-123-456", "name": "Form", "sections": [{"title": "Your details", "fields": [{"id": "dateOfBirth", "label": "Date of birth", "type": "dateOfBirth", "validators": [{"type": "required"}]}, {"id": "maritalStatus", "label": "Marital Status", "type": "radio", "values": [{"value": "married", "label": "Married"}, {"value": "single", "label": "Single"}, {"value": "cohabiting", "label": "Cohabiting"}, {"value": "divorced", "label": "Divorced"}, {"value": "separated", "label": "Separated"}, {"value": "widowed", "label": "Widowed"}, {"value": "other", "label": "Other"}], "validators": [{"type": "required"}]}, {"id": "residentialStatus", "label": "Residential Status", "type": "radio", "values": [{"value": "homeowner", "label": "Homeowner"}, {"value": "councilTenant", "label": "Council Tenant"}, {"value": "privateTenant", "label": "Private Tenant"}, {"value": "other", "label": "Other"}, {"value": "livingWithParentsOrFamily", "label": "Living with parents or family"}, {"value": "houseShare", "label": "House share"}], "validators": [{"type": "required"}]}, {"id": "dependants", "label": "Number of dependants (incl. adult dependants)", "type": "inlineRadio", "dataType": "int", "values": [{"value": 0, "label": "0"}, {"value": 1, "label": "1"}, {"value": 2, "label": "2"}, {"value": 3, "label": "3+"}], "validators": [{"type": "required"}]}]}, {"title": "Employment & Income", "fields": [{"id": "employmentStatus", "label": "Employment Status", "type": "select", "values": [{"value": "fullTime", "label": "Employed - Full time"}, {"value": "partTime", "label": "Employed - Part time"}, {"value": "onBenefits", "label": "On benefits"}, {"value": "retired", "label": "Retired"}, {"value": "selfEmployed", "label": "Self employed"}, {"value": "temporaryEmployed", "label": "Temporary employed"}, {"value": "homemaker", "label": "Homemaker"}], "validators": [{"type": "required"}]}, {"id": "salary", "label": "Monthly take-home salary", "placeholder": "E.g. £1,520", "type": "currency", "validators": [{"type": "required"}]}]}, {"title": "Monthly expenditure", "subtitle": "If you share any expenses, please enter **your contributions**. Take into consideration any increases in the next 6 months (e.g., new car payments).", "fields": [{"id": "monthlyRent", "label": "Your monthly rent, mortgage & utility bills", "help": {"title": "Monthly rent, mortgage & utility bills", "content": "This is **your contribution** toward:\n- Rent and/or mortgage payments, as well as payments for council tax, gas, electricity, water, and other energy bills."}, "type": "currency", "validators": [{"type": "required"}]}, {"id": "monthlyTravelLivingExpenses", "label": "Your monthly travel & living expenses", "help": {"title": "Your monthly travel & living expenses", "content": "This is **your contribution** toward:\n- Transport costs (public transport, car/van expenses, fuel, servicing)\n- Holidays, leisure and recreation (including streaming services and reading materials)\n- Medical expenses\n- Education (including school trips and ad hoc costs)\n- Clothing, and personal care (toiletries, beauty products, hairdressing, etc.)"}, "type": "currency", "validators": [{"type": "required"}]}, {"id": "monthlyHouseholdExpenses", "label": "Your monthly household expenses", "help": {"title": "Your monthly household expenses", "content": "This is **your contribution** toward:\n- Regular household maintenance (cleaning and repairs) including your contribution to phone and internet costs"}, "type": "currency", "validators": [{"type": "required"}]}, {"id": "monthlyCreditPayments", "label": "Your monthly credit payments", "help": {"title": "Your monthly credit commitments", "content": "These are **payments for:**\n- Payday loads and other loans, credit cards, store cards, home credit, catalogues, retail finance, car finance, insurances etc..."}, "type": "currency", "validators": [{"type": "required"}]}]}]}