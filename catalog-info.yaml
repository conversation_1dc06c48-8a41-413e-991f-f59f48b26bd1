apiVersion: backstage.io/v1alpha1
kind: Location
metadata:
  name: experience-monorepo
  description: The various APIs/docs/services available within the experience monorepo
spec:
  type: url
  targets:
    - ./apps/commercial/destination/site-admin-api/backstage.yaml
    - ./apps/commercial/destination/site-admin-webapp/backstage.yaml
    - ./apps/commercial/internal/site-admin-api/backstage.yaml
    - ./apps/commercial/internal/site-admin-webapp/backstage.yaml
    - ./apps/commercial/ocpi-service/api/backstage.yaml
    - ./apps/commercial/statement-service/api/backstage.yaml
    - ./apps/commercial/statement-service/webapp/backstage.yaml
    - ./apps/data-platform/api/backstage.yaml
    - ./apps/mobile/driver-account-api/backstage.yaml
    - ./apps/mobile/installer-api/backstage.yaml
    - ./apps/mobile/installer-bff/backstage.yaml
    - ./apps/mobile/mobile-api/backstage.yaml
    - ./apps/mobile/billing-api/backstage.yaml
    - ./apps/mobile/notifications-api/backstage.yaml
    - ./apps/mobile/payments-api/backstage.yaml
    - ./apps/mobile/rewards-api/backstage.yaml
    - ./apps/mobile/subscriptions-api/backstage.yaml
    - ./docs/domain/events/catalog-info.yaml
