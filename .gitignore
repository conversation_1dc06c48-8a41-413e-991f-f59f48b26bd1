# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
*.log
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
/typings
apps/coverage
npm-debug.log
testem.log
yarn-error.log

# System Files
.DS_Store
Thumbs.db

# local env files
**/.env
**/.env*.local

# Sentry
.sentryclirc

# Nx
.nx
migrations.json

# Next.js
.next
__ENV.js

# revoke command list of users
apps/support/gip-admin/src/users.ts

# mkdocs
docs/data-platform/site
docs/data-platform/.structurizr
docs/data-platform/architecture/target.json
docs/domain/architecture/.structurizr/
docs/domain/architecture/now.json
docs/domain/architecture/commercial/now.json
docs/domain/architecture/data-platform/now.json
docs/domain/architecture/mobile/now.json
.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md
libs/mobile/**/axios/src/docs
libs/mobile/**/**/api/src/docs
