#!/bin/bash

# Rebuild the crowdin.yml file

# Static header of the crowdin.yml file
cat <<EOF > crowdin.yml
# This file is automatically generated by the crowdin.update.sh script
project_id_env: CROWDIN_PROJECT_ID
api_token_env: CROWDIN_PERSONAL_TOKEN
base_url: https://pod-point.api.crowdin.com
preserve_hierarchy: true
files:
EOF

# Find command to locate files and append them to crowdin.yml
find . -type f  -not -path './node_modules*' -and \( -iname 'en.json' -or -iname 'en.ts' -or -path "*/en/*.json" \) | sort | while read -r file; do
    # Remove leading './' from the file path
    formatted_file=$(echo "$file" | sed 's|^\./|/|')
    # Replace 'en' with '%two_letters_code%' for the translation path
    translation_path=$(echo "$formatted_file" | sed 's|/en/|/%two_letters_code%/|g' | sed 's|/en.json$|/%two_letters_code%.json|')
    # Append the file entry to crowdin.yml
    echo "  - source: $formatted_file" >> crowdin.yml
    echo "    translation: $translation_path" >> crowdin.yml
done

echo "crowdin.yml file has been generated."
