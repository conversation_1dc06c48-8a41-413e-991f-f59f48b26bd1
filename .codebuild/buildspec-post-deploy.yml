# Note that CodeBuild is connected to GitHub using <PERSON><PERSON><PERSON> (currently <PERSON>) in the experience-prod (************) account.
# https://eu-west-1.console.aws.amazon.com/codesuite/codebuild/projects/destination-post-deploy/edit/source?region=eu-west-1

version: 0.2

env:
  variables:
    CYPRESS_INSTALL_BINARY: 0

phases:
  install:
    commands:
      - . ./.codebuild/scripts/golang-install.sh
      - echo "Go version $(go version)"
      - . ./.codebuild/scripts/node-install.sh
      - echo "Node version $(node --version)"
      - npm ci
  pre_build:
    commands:
      - echo Start post-deploy pre_build...
      - echo git ref=$CODEBUILD_SOURCE_VERSION
  build:
    commands:
      - echo Run Smoke tests
      - npm run smoke-test -- --configuration=ecs
