# Note that CodeBuild is connected to GitHub using <PERSON><PERSON><PERSON> (currently <PERSON>) in the experience-dev (************) account.
# Note that CodeBuild is connected to GitHub using <PERSON><PERSON><PERSON> (currently <PERSON>) in the experience-staging (************) account.
# https://eu-west-1.console.aws.amazon.com/codesuite/codebuild/projects/destination-performance-test

version: 0.2

env:
  variables:
    CYPRESS_INSTALL_BINARY: 0

phases:
  install:
    commands:
      - . ./.codebuild/scripts/golang-install.sh
      - echo "Go version $(go version)"
      - . ./.codebuild/scripts/node-install.sh
      - echo "Node version $(node --version)"
      - . ./.codebuild/scripts/cert-install.sh
      - npm ci
  pre_build:
    commands:
      - echo Start performance test stage pre_build...
      - echo git ref=$CODEBUILD_SOURCE_VERSION
  build:
    commands:
      - echo Run Performance tests
      - npm run performance-test -- --configuration=ecs
