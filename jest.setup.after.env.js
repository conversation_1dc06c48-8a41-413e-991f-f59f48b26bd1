// This mocks Reacts internal useId hook to generate stable test ids for HeadlessUI components - this should reduce flakiness particularly with snapshot testing.
// Inspiration from HeadlessUI's own test suite: https://github.com/tailwindlabs/headlessui/blob/main/packages/%40headlessui-react/src/hooks/__mocks__/use-id.ts

const React = require('react');

let idCounter = 0;

const useId = () => {
  const [id] = React.useState(() => idCounter++);
  return `:test-id-${id}`;
};

beforeEach(() => {
  idCounter = 0;
});

jest.spyOn(React, 'useId').mockImplementation(useId);
