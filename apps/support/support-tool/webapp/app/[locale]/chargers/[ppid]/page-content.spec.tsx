import 'whatwg-fetch';
import {
  PageContent as ChargerPageContent,
  PageContentProps,
} from './page-content';
import { DelegatedControlChargingStationResponseDtoStatusEnum } from '@experience/shared/axios/smart-charging-service-client';
import { LocationType } from '@experience/shared/axios/assets-api-client';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import {
  mockChargeHistory,
  mockChargeOverrides,
  mockChargeSchedules,
  mockChargingStationProgrammes,
  mockCommercialAttributes,
  mockConfiguration,
  mockConnectivityStatus,
  mockCurrentFirmware,
  mockDelegatedControlStatus,
  mockInstallationDetails,
  mockLinkedUsers,
  mockSalesforceAsset,
  mockSummary,
  mockTariffSuppliers,
  mockTariffs,
  mockVehicles,
  mockWifiCredentials,
} from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../test-utils';
import { setIn } from 'immutable';
import MockDate from 'mockdate';

const mockRouterPush = jest.fn();

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: (url: string) => mockRouterPush(url),
  }),
  useSearchParams: () => ({ get: jest.fn() }),
}));

const mockRevalidatePath = jest.fn();
jest.mock('next/cache', () => ({
  revalidatePath: (path: string) => mockRevalidatePath(path),
}));

const singleSocketSummary = setIn(mockSummary, ['evses'], [{ evseId: 1 }]);
const multiSocketSummary = setIn(
  mockSummary,
  ['evses'],
  [{ evseId: 1 }, { evseId: 2 }, { evseId: 3 }]
);

const defaultProps: PageContentProps = {
  chargeHistory: mockChargeHistory.data,
  chargeOverrides: mockChargeOverrides,
  chargeSchedules: mockChargeSchedules.data,
  chargingStationProgrammes: mockChargingStationProgrammes,
  configuration: mockConfiguration.data,
  connectivityStatus: mockConnectivityStatus,
  commercialAttributes: undefined,
  currentFirmware: mockCurrentFirmware,
  delegatedControlStatus: mockDelegatedControlStatus,
  evseId: 1,
  installationDetails: mockInstallationDetails,
  linkedUsers: mockLinkedUsers,
  name: mockCommercialAttributes?.name,
  salesforceAsset: mockSalesforceAsset,
  socket: 'A',
  subscriptions: [],
  summary: singleSocketSummary,
  tariffs: mockTariffs.data,
  tariffSuppliers: mockTariffSuppliers,
  vehicles: mockVehicles.data,
  wifiCredentials: mockWifiCredentials,
};

describe('Charger page content', () => {
  const prevEnv = process.env;

  beforeEach(() => {
    MockDate.set('2025-06-17T12:09:49Z');
    process.env.NEXT_PUBLIC_ADMIN_TOOL_BASE_URL = 'https://admin.pod-point.com';
  });

  afterEach(() => {
    process.env = prevEnv;
    MockDate.reset();
  });

  it('should be defined', () => {
    const { baseElement } = renderWithProviders(
      <ChargerPageContent {...defaultProps} />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it.each([
    ['with a single socket', defaultProps.summary, undefined],
    ['with multiple sockets', multiSocketSummary, undefined],
    [
      'with commercial attributes',
      defaultProps.summary,
      mockCommercialAttributes,
    ],
    [
      'when the show tariffs and vehicles flag is true',
      defaultProps.summary,
      undefined,
    ],
  ])('should match snapshot %s', (_, summary, commercialAttributes) => {
    const { baseElement } = renderWithProviders(
      <ChargerPageContent
        {...setIn(defaultProps, ['commercialAttributes'], commercialAttributes)}
        summary={summary}
      />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot when only summary is present', () => {
    const { baseElement } = renderWithProviders(
      <ChargerPageContent
        chargeHistory={undefined}
        chargeOverrides={undefined}
        chargeSchedules={undefined}
        chargingStationProgrammes={undefined}
        configuration={undefined}
        connectivityStatus={undefined}
        commercialAttributes={undefined}
        currentFirmware={undefined}
        delegatedControlStatus={undefined}
        evseId={1}
        installationDetails={undefined}
        linkedUsers={undefined}
        name={undefined}
        socket={'A'}
        subscriptions={[]}
        summary={mockSummary}
        salesforceAsset={undefined}
        tariffs={undefined}
        tariffSuppliers={undefined}
        vehicles={undefined}
        wifiCredentials={undefined}
      />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render a multi socket header if there is more than one socket', async () => {
    renderWithProviders(
      <ChargerPageContent {...defaultProps} summary={multiSocketSummary} />
    );

    const socketTabs = screen.getAllByRole('button', { name: /Socket/ });
    expect(socketTabs).toHaveLength(3);
    fireEvent.click(socketTabs[1]);

    await waitFor(() => {
      expect(mockRouterPush).toHaveBeenCalledWith(
        `/chargers/${mockSummary.ppid}?socket=B`
      );
    });
  });

  it('should render the user header as a link', () => {
    renderWithProviders(<ChargerPageContent {...defaultProps} />);

    const [expectedLink] = screen.getAllByRole('link', {
      name: 'John Doe - <EMAIL>',
    });

    expect(expectedLink).toBeInTheDocument();
    expect(expectedLink).toHaveAttribute(
      'href',
      '/accounts/a3450f38-217a-4d0c-8eec-b7d63c6fd2e0/details'
    );
  });

  it.each([false, true])(
    'should render the icon and text for domestic locations with is public %s',
    (isPublic: boolean) => {
      renderWithProviders(
        <ChargerPageContent
          {...setIn(defaultProps, ['summary', 'location'], {
            type: LocationType.Domestic,
            isPublic: isPublic,
          })}
        />
      );
      expect(screen.getByText('Domestic')).toBeInTheDocument();
      expect(screen.getByTitle('Domestic location')).toBeInTheDocument();
    }
  );

  it.each([
    [false, 'No', 5],
    [true, 'Yes', 7],
  ])(
    'should render the icon and text for commercial locations with is public %s',
    async (isPublic, title, length) => {
      renderWithProviders(
        <ChargerPageContent
          {...setIn(defaultProps, ['summary', 'location'], {
            type: LocationType.Commercial,
            isPublic: isPublic,
          })}
        />
      );
      expect(screen.getByText('Commercial')).toBeInTheDocument();
      expect(screen.getByTitle('Commercial location')).toBeInTheDocument();
      expect(screen.getByText('Public:')).toBeInTheDocument();
      expect(screen.getByText('Confirm:')).toBeInTheDocument();
      expect(screen.getByText('Tariff assigned:')).toBeInTheDocument();
      expect(screen.getByText('RFID reader:')).toBeInTheDocument();
      expect(screen.queryByText('Subscription:')).not.toBeInTheDocument();
      expect(await screen.findAllByTitle(title)).toHaveLength(length);
    }
  );

  it('should not render sub-heading when no location is present', () => {
    renderWithProviders(
      <ChargerPageContent
        {...setIn(defaultProps, ['summary', 'location'], undefined)}
      />
    );
    expect(screen.queryByText('Commercial')).not.toBeInTheDocument();
    expect(screen.queryByText('Domestic')).not.toBeInTheDocument();
    expect(screen.queryByText('Public')).not.toBeInTheDocument();
    expect(screen.queryByText('Private')).not.toBeInTheDocument();
  });

  it('should not render modes for commercial chargers', () => {
    renderWithProviders(
      <ChargerPageContent
        {...setIn(defaultProps, ['summary', 'location'], {
          type: LocationType.Commercial,
        })}
      />
    );
    expect(screen.queryByText('Modes')).not.toBeInTheDocument();
  });

  it.each([
    [
      'smart mode when no charge overrides are present',
      { ...setIn(defaultProps, ['chargeOverrides'], []) },
      'true',
    ],
    [
      'smart mode when charge override is present with end date',
      defaultProps,
      'true',
    ],
    [
      'manual mode when charge override is present with no end date',
      {
        ...setIn(
          defaultProps,
          ['chargeOverrides'],
          [{ ...mockChargeOverrides[0], endAt: null }]
        ),
      },
      'false',
    ],
  ])('should show %s', (_, props, smartMode) => {
    renderWithProviders(<ChargerPageContent {...props} />);
    expect(
      screen.getByRole('switch', { name: 'toggle-smart-mode' })
    ).toHaveAttribute('aria-checked', smartMode);
  });

  it('should render the correct vehicle if one is plugged in', () => {
    const [vehicle] = mockVehicles.data;
    const { vehicleInformation } = vehicle.vehicle;

    const pluggedInVehicle = {
      ...vehicle,
      isPluggedInToThisCharger: true,
      vehicle: {
        ...vehicle.vehicle,
        vehicleInformation: {
          ...vehicleInformation,
          brand: 'BYD',
          model: 'Dolphin',
          modelVariant: undefined,
        },
      },
    };

    const unpluggedVehicle = {
      ...vehicle,
      isPluggedInToThisCharger: false,
      vehicle: {
        ...vehicle.vehicle,
        vehicleInformation: {
          ...vehicleInformation,
          brand: 'Tesla',
          model: 'CyberTruck',
          modelVariant: undefined,
        },
      },
    };

    renderWithProviders(
      <ChargerPageContent
        {...setIn(
          defaultProps,
          ['vehicles'],
          [unpluggedVehicle, pluggedInVehicle]
        )}
      />
    );

    expect(screen.getByText('BYD Dolphin')).toBeInTheDocument();
    expect(screen.queryByText('Tesla CyberTruck')).not.toBeInTheDocument();
  });

  it('should show the tariffs and vehicles widgets when the charge is under delegated control', () => {
    renderWithProviders(
      <ChargerPageContent
        {...setIn(defaultProps, ['delegatedControlStatus'], {
          ...mockDelegatedControlStatus,
          status: DelegatedControlChargingStationResponseDtoStatusEnum.Active,
        })}
      />
    );
    expect(
      screen.getByRole('heading', { name: 'Tariffs' })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('heading', { name: 'Vehicle' })
    ).toBeInTheDocument();
  });

  it('should not show the tariffs and vehicles widgets when the charge is not under delegated control', () => {
    renderWithProviders(
      <ChargerPageContent
        {...setIn(defaultProps, ['delegatedControlStatus'], {
          ...mockDelegatedControlStatus,
          status: DelegatedControlChargingStationResponseDtoStatusEnum.Inactive,
        })}
      />
    );
    expect(
      screen.queryByRole('heading', { name: 'Tariffs' })
    ).not.toBeInTheDocument();
    expect(
      screen.queryByRole('heading', { name: 'Vehicle' })
    ).not.toBeInTheDocument();
  });

  it('should render the commercial subscription icon if the feature flag is on', () => {
    renderWithProviders(
      <ChargerPageContent
        {...setIn(defaultProps, ['summary', 'location'], {
          type: LocationType.Commercial,
        })}
      />,
      {
        features: { chargersOnSubscription: true },
      }
    );

    expect(screen.getByText('Subscription:')).toBeInTheDocument();
  });
});
