import {
  Account<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Account<PERSON>harger,
  AccountDetails,
  AccountFactor,
  AccountPartial,
  AccountSuppressedStatus,
} from './accounts.types';
import { AccountNotFoundError } from './account-not-found.error';
import {
  AuthApi,
  UserDetailsDto,
  UsersApi,
} from '@experience/driver-account-api/api-client';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import {
  TEST_ACCOUNTS,
  TEST_ACCOUNT_ACTIVITY,
  X_APP_NAME,
} from './accounts.constants';
import { UpdateAccountDetailsDto, UpdateAccountEmailDto } from './accounts.dto';

@Injectable()
export class AccountsService {
  private readonly logger = new Logger(AccountsService.name);

  constructor(
    private readonly usersApi: UsersApi,
    private readonly authApi: AuthApi
  ) {}

  async searchForAccounts(searchString: string): Promise<AccountPartial[]> {
    const trimmedSearchString = searchString.trim();
    this.logger.log(
      { searchString: trimmedSearchString },
      'getting accounts based on the given search query'
    );

    try {
      const { data } = await this.usersApi.userControllerGetByFilter(
        undefined,
        trimmedSearchString,
        undefined
      );

      return data.map((user) => ({
        authId: user.uid,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        locale: user.locale,
      }));
    } catch (error) {
      this.logger.error(
        { error },
        'failed to get search response from users api'
      );
    }

    return [];
  }

  async getAccountDetails(authId: string): Promise<AccountDetails> {
    this.logger.log({ authId }, 'getting account details for user');

    const { data, status } = await this.usersApi.userControllerGetUser(authId);

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    // Extract values which we need to transform, as well as any values we don't need and spread any remaining untouched values
    const {
      // Transformed
      uid,
      emailVerified,
      first_name,
      last_name,
      lastSignInTimestamp,
      accountCreationTimestamp,
      // Removed
      preferences,
      // Remaining
      ...user
    } = data;

    return {
      ...user,
      authId: uid,
      organisations: [],
      isEmailVerified: emailVerified,
      firstName: first_name,
      lastName: last_name,
      lastSignInTimestamp: lastSignInTimestamp
        ? new Date(lastSignInTimestamp).getTime()
        : null,
      accountCreationTimestamp: new Date(accountCreationTimestamp).getTime(),
    };
  }

  async getAccountFactors(authId: string): Promise<AccountFactor[]> {
    this.logger.log({ authId }, 'getting acccount two factor details');

    const { data, status } =
      await this.authApi.retrieveFactorControllerGetFactors(authId);

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    return data.map((factor) => ({
      id: factor.id,
      phoneNumberCountryCode: factor.phoneNumber.slice(0, 3),
      phoneNumberLastThree: factor.phoneNumber.slice(-3),
      enrollmentTime: factor.enrollmentTime,
    }));
  }

  private async updateAccount(
    authId: string,
    toMerge: Partial<UserDetailsDto>
  ) {
    const { data, status } = await this.usersApi.userControllerGetUser(authId);

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    return this.usersApi.userControllerUpdateUser(authId, {
      first_name: toMerge.first_name ?? data.first_name,
      last_name: toMerge.last_name ?? data.last_name,
      locale: toMerge.locale ?? data.locale,
    });
  }

  async updateAccountDetails(
    authId: string,
    updates: UpdateAccountDetailsDto
  ): Promise<AccountPartial> {
    this.logger.log({ authId }, 'updating account details for user');

    const { data: updateResponse, status: updateResponseStatus } =
      await this.updateAccount(authId, {
        first_name: updates.firstName,
        last_name: updates.lastName,
      });

    if (!updateResponse || updateResponseStatus === 404) {
      throw new AccountNotFoundError();
    }

    return {
      authId,
      email: updateResponse.email,
      firstName: updateResponse.first_name,
      lastName: updateResponse.last_name,
      locale: updateResponse.locale,
    };
  }

  async updateAccountLocale(
    authId: string,
    locale: string
  ): Promise<{ locale: string }> {
    this.logger.log({ authId, locale }, 'updating locale for user');

    const { data, status } = await this.updateAccount(authId, { locale });

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    return {
      locale: data.locale,
    };
  }

  async updateAccountEmail(
    authId: string,
    request: UpdateAccountEmailDto
  ): Promise<void> {
    this.logger.log({ authId }, 'updating email address for user');

    const { data, status } = await this.usersApi.userControllerGetUser(authId);

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    const { email: userCurrentEmail } = data;

    await this.authApi.verifyAndChangeEmailControllerUpdateEmail(
      {
        email: userCurrentEmail,
        newEmail: request.newEmail,
      },
      X_APP_NAME
    );
  }

  async enableUser(authId: string): Promise<AccountPartial> {
    this.logger.log({ authId }, 'enabling account details for user');

    const { data, status } = await this.usersApi.userControllerEnable(authId);

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    return {
      authId,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      locale: data.locale,
    };
  }

  async getAccountActivity(authId: string): Promise<AccountActivityEvent[]> {
    const account = TEST_ACCOUNTS.find((account) => account.authId === authId);
    if (!account) {
      throw new NotFoundException();
    }

    return TEST_ACCOUNT_ACTIVITY;
  }

  async getAccountChargers(authId: string): Promise<AccountCharger[]> {
    this.logger.log({ authId }, 'getting chargers for user');

    const { data, status } = await this.usersApi.userControllerGetChargers(
      authId
    );

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    return data;
  }

  async getAccountSuppressedStatus(
    authId: string
  ): Promise<AccountSuppressedStatus> {
    this.logger.log({ authId }, 'getting suppressed status for user');

    const { data, status } =
      await this.usersApi.userControllerGetSuppressedStatus(authId);

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    return data;
  }

  async updateAccountSuppressedStatus(
    authId: string
  ): Promise<AccountSuppressedStatus> {
    this.logger.log({ authId }, 'updating suppressed status for user');

    const { data, status } =
      await this.usersApi.userControllerUpdateSuppressedStatus(authId, {
        status: null,
      });

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    return data;
  }

  async resendEmailVerification(authId: string) {
    this.logger.log({ authId }, 'resending email verification for user');

    const { data, status } = await this.usersApi.userControllerGetUser(authId);

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    const { email } = data;

    await this.authApi.emailVerificationControllerSendEmailVerification(
      {
        email,
      },
      X_APP_NAME
    );
  }

  async resetPassword(authId: string) {
    this.logger.log({ authId }, 'resetting password for user');

    const { data, status } = await this.usersApi.userControllerGetUser(authId);

    if (!data || status === 404) {
      throw new AccountNotFoundError();
    }

    await this.authApi.passwordResetControllerSendPasswordReset(
      {
        email: data.email,
      },
      X_APP_NAME
    );
  }

  async deleteAccount(authId: string, force: boolean) {
    this.logger.log({ authId }, 'about to delete user');
    await this.usersApi.userControllerSoftDelete(authId, force);
  }

  async unlinkCharger(authId: string, ppid: string) {
    this.logger.log({ authId, ppid }, ' unlink user from charger');
    const { status } = await this.usersApi.userControllerUnLinkCharger(
      authId,
      ppid
    );
    if (status === 404) {
      throw new AccountNotFoundError();
    }
  }
}
