import { AccountNotFoundError } from './account-not-found.error';
import { AccountsService } from './accounts.service';
import { AuthApi, UsersApi } from '@experience/driver-account-api/api-client';
import { Logger } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { X_APP_NAME } from './accounts.constants';
import { mockDeep } from 'jest-mock-extended';
import type { AxiosResponse } from 'axios';

describe('AccountsService', () => {
  let service: AccountsService;
  let authApi: AuthApi;
  let usersApi: UsersApi;

  const logger = mockDeep<Logger>();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      providers: [
        AccountsService,
        {
          provide: AuthApi,
          useValue: new AuthApi(),
        },
        {
          provide: UsersApi,
          useValue: new UsersApi(),
        },
      ],
    }).compile();

    service = module.get<AccountsService>(AccountsService);
    authApi = module.get<AuthApi>(AuthApi);
    usersApi = module.get<UsersApi>(UsersApi);

    module.useLogger(logger);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(authApi).toBeDefined();
    expect(usersApi).toBeDefined();
  });

  describe('searchForAccounts()', () => {
    it('trims the search string', async () => {
      const usersSpy = jest
        .spyOn(usersApi, 'userControllerGetByFilter')
        .mockReturnValue(Promise.resolve({ data: [] } as AxiosResponse));

      await service.searchForAccounts('   <EMAIL>      ');
      expect(usersSpy).toHaveBeenCalledWith(
        undefined,
        '<EMAIL>',
        undefined
      );
    });

    it('calls the usersApi to get users by email fuzzy matching', async () => {
      const usersSpy = jest
        .spyOn(usersApi, 'userControllerGetByFilter')
        .mockReturnValue(Promise.resolve({ data: [] } as AxiosResponse));

      await service.searchForAccounts('<EMAIL>');

      expect(usersSpy).toHaveBeenCalledTimes(1);
      expect(usersSpy).toHaveBeenCalledWith(
        undefined,
        '<EMAIL>',
        undefined
      );
    });

    it('transforms the response', async () => {
      jest.spyOn(usersApi, 'userControllerGetByFilter').mockResolvedValue({
        data: [
          {
            uid: '39741a9b-9dea-4254-9f18-9e319887b89e',
            first_name: 'Example',
            last_name: 'User',
            email: '<EMAIL>',
            locale: 'en',
          },
        ],
      } as AxiosResponse);

      const res = await service.searchForAccounts('<EMAIL>');

      expect(res).toStrictEqual([
        {
          authId: '39741a9b-9dea-4254-9f18-9e319887b89e',
          firstName: 'Example',
          lastName: 'User',
          email: '<EMAIL>',
          locale: 'en',
        },
      ]);
    });
  });

  describe('getAccountDetails()', () => {
    it('calls the usersApi to get user by auth ID', async () => {
      const usersSpy = jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(Promise.resolve({ data: {} } as AxiosResponse));

      await service.getAccountDetails('39741a9b-9dea-4254-9f18-9e319887b89e');

      expect(usersSpy).toHaveBeenCalledTimes(1);
      expect(usersSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );
    });

    it('throws an AccountNotFoundError if there is no data returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(
          Promise.resolve({ data: undefined, status: 200 } as AxiosResponse)
        );

      await expect(
        service.getAccountDetails('39741a9b-9dea-4254-9f18-9e319887b89e')
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );

      await expect(
        service.getAccountDetails('39741a9b-9dea-4254-9f18-9e319887b89e')
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('transforms the data returned by the usersApi into the correct format', async () => {
      const lastSignInDate = new Date('2024-05-07');
      const createdDate = new Date('2024-05-07');

      jest.spyOn(usersApi, 'userControllerGetUser').mockReturnValue(
        Promise.resolve({
          data: {
            uid: '39741a9b-9dea-4254-9f18-9e319887b89e',
            first_name: 'Example',
            last_name: 'User',
            locale: 'en',
            email: '<EMAIL>',
            emailVerified: true,
            balance: {
              amount: 500,
              currency: 'GBP',
            },
            lastSignInTimestamp: lastSignInDate.toISOString(),
            accountCreationTimestamp: createdDate.toISOString(),
            status: 'active',
          },
        } as AxiosResponse)
      );

      const res = await service.getAccountDetails(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );

      expect(res).toStrictEqual({
        authId: '39741a9b-9dea-4254-9f18-9e319887b89e',
        firstName: 'Example',
        lastName: 'User',
        email: '<EMAIL>',
        isEmailVerified: true,
        organisations: [],
        status: 'active',
        balance: {
          amount: 500,
          currency: 'GBP',
        },
        lastSignInTimestamp: lastSignInDate.getTime(),
        accountCreationTimestamp: createdDate.getTime(),
        locale: 'en',
      });
    });
  });

  describe('getAccountFactors()', () => {
    it('calls the auth API to get user by auth ID', async () => {
      const authSpy = jest
        .spyOn(authApi, 'retrieveFactorControllerGetFactors')
        .mockReturnValue(Promise.resolve({ data: [] } as AxiosResponse));

      await service.getAccountFactors('39741a9b-9dea-4254-9f18-9e319887b89e');

      expect(authSpy).toHaveBeenCalledTimes(1);
      expect(authSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );
    });

    it('throws an AccountNotFoundError if there is no data returned by the usersApi', async () => {
      jest
        .spyOn(authApi, 'retrieveFactorControllerGetFactors')
        .mockReturnValue(
          Promise.resolve({ data: undefined, status: 200 } as AxiosResponse)
        );

      await expect(
        service.getAccountFactors('39741a9b-9dea-4254-9f18-9e319887b89e')
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi', async () => {
      jest
        .spyOn(authApi, 'retrieveFactorControllerGetFactors')
        .mockReturnValue(
          Promise.resolve({ data: [], status: 404 } as AxiosResponse)
        );

      await expect(
        service.getAccountFactors('39741a9b-9dea-4254-9f18-9e319887b89e')
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('returns a subset of the telephone number', async () => {
      const authSpy = jest
        .spyOn(authApi, 'retrieveFactorControllerGetFactors')
        .mockReturnValue(
          Promise.resolve({
            data: [
              {
                id: '919276fd-bbad-438f-ad48-a2d2c22d0812',
                phoneNumber: '+*************',
                enrollmentTime: 'Fri, 22 Sep 2017 01:49:58 GMT',
              },
            ],
            status: 200,
          } as AxiosResponse)
        );

      const res = await service.getAccountFactors(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );

      expect(authSpy).toHaveBeenCalledTimes(1);
      expect(authSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );

      expect(res).toStrictEqual([
        {
          id: '919276fd-bbad-438f-ad48-a2d2c22d0812',
          phoneNumberCountryCode: '+44',
          phoneNumberLastThree: '456',
          enrollmentTime: 'Fri, 22 Sep 2017 01:49:58 GMT',
        },
      ]);
    });
  });

  describe('updateAccountDetails()', () => {
    it('calls the usersApi to get user by auth ID, then update user by auth ID with the given details', async () => {
      const getUserSpy = jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(
          Promise.resolve({
            data: {
              first_name: 'Existing',
              last_name: 'Name',
              locale: 'en',
            },
          } as AxiosResponse)
        );
      const updateUserSpy = jest
        .spyOn(usersApi, 'userControllerUpdateUser')
        .mockReturnValue(Promise.resolve({ data: {} } as AxiosResponse));

      await service.updateAccountDetails(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        {
          firstName: 'Mobile',
          lastName: 'Tester',
        }
      );

      expect(getUserSpy).toHaveBeenCalledTimes(1);
      expect(getUserSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );
      expect(updateUserSpy).toHaveBeenCalledTimes(1);
      expect(updateUserSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        {
          first_name: 'Mobile',
          last_name: 'Tester',
          locale: 'en',
        }
      );
    });

    it('throws an AccountNotFoundError if there is no data returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(
          Promise.resolve({ data: undefined, status: 200 } as AxiosResponse)
        );
      jest
        .spyOn(usersApi, 'userControllerUpdateUser')
        .mockReturnValue(
          Promise.resolve({ data: undefined, status: 200 } as AxiosResponse)
        );

      await expect(
        service.updateAccountDetails('39741a9b-9dea-4254-9f18-9e319887b89e', {
          firstName: 'Mobile',
          lastName: 'Tester',
        })
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );
      jest
        .spyOn(usersApi, 'userControllerUpdateUser')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );

      await expect(
        service.updateAccountDetails('39741a9b-9dea-4254-9f18-9e319887b89e', {
          firstName: 'Mobile',
          lastName: 'Tester',
        })
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('transforms the data returned by the usersApi into the correct format', async () => {
      jest.spyOn(usersApi, 'userControllerGetUser').mockReturnValue(
        Promise.resolve({
          data: { locale: 'en' },
          status: 200,
        } as AxiosResponse)
      );
      jest.spyOn(usersApi, 'userControllerUpdateUser').mockReturnValue(
        Promise.resolve({
          data: {
            first_name: 'Mobile',
            last_name: 'Tester',
            locale: 'en',
            email: '<EMAIL>',
          },
          status: 200,
        } as AxiosResponse)
      );

      const res = await service.updateAccountDetails(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        {
          firstName: 'Mobile',
          lastName: 'Tester',
        }
      );

      expect(res).toStrictEqual({
        firstName: 'Mobile',
        lastName: 'Tester',
        authId: '39741a9b-9dea-4254-9f18-9e319887b89e',
        locale: 'en',
        email: '<EMAIL>',
      });
    });
  });

  describe('updateAccountLocale()', () => {
    it('calls the usersApi to get user by auth ID, then update user by auth ID with the given locale', async () => {
      const getUserSpy = jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(
          Promise.resolve({
            data: {
              first_name: 'Existing',
              last_name: 'Name',
              locale: 'en',
            },
          } as AxiosResponse)
        );
      const updateUserSpy = jest
        .spyOn(usersApi, 'userControllerUpdateUser')
        .mockReturnValue(Promise.resolve({ data: {} } as AxiosResponse));

      await service.updateAccountLocale(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        'es'
      );

      expect(getUserSpy).toHaveBeenCalledTimes(1);
      expect(getUserSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );
      expect(updateUserSpy).toHaveBeenCalledTimes(1);
      expect(updateUserSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        {
          first_name: 'Existing',
          last_name: 'Name',
          locale: 'es',
        }
      );
    });

    it('throws an AccountNotFoundError if there is no data returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(
          Promise.resolve({ data: undefined, status: 200 } as AxiosResponse)
        );
      jest
        .spyOn(usersApi, 'userControllerUpdateUser')
        .mockReturnValue(
          Promise.resolve({ data: undefined, status: 200 } as AxiosResponse)
        );

      await expect(
        service.updateAccountLocale(
          '39741a9b-9dea-4254-9f18-9e319887b89e',
          'es'
        )
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );
      jest
        .spyOn(usersApi, 'userControllerUpdateUser')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );

      await expect(
        service.updateAccountLocale(
          '39741a9b-9dea-4254-9f18-9e319887b89e',
          'es'
        )
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('transforms the data returned by the usersApi into the correct format', async () => {
      jest.spyOn(usersApi, 'userControllerGetUser').mockReturnValue(
        Promise.resolve({
          data: { first_name: 'Mobile', last_name: 'Tester' },
          status: 200,
        } as AxiosResponse)
      );
      jest.spyOn(usersApi, 'userControllerUpdateUser').mockReturnValue(
        Promise.resolve({
          data: {
            first_name: 'Mobile',
            last_name: 'Tester',
            locale: 'en',
            email: '<EMAIL>',
          },
          status: 200,
        } as AxiosResponse)
      );

      const res = await service.updateAccountLocale(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        'es'
      );

      expect(res).toStrictEqual({
        locale: 'en',
      });
    });
  });

  describe('updateAccountEmail()', () => {
    it('finds users existing email, and submits it to usersApi to update to new email', async () => {
      const authId = '39741a9b-9dea-4254-9f18-9e319887b89e';

      const getUserSpy = jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockResolvedValue({
          data: { email: '<EMAIL>' },
          status: 200,
        } as AxiosResponse);

      const updateEmailSpy = jest
        .spyOn(authApi, 'verifyAndChangeEmailControllerUpdateEmail')
        .mockResolvedValue({ data: null, status: 202 } as AxiosResponse);

      await service.updateAccountEmail(authId, {
        newEmail: '<EMAIL>',
      });

      expect(getUserSpy).toHaveBeenCalledTimes(1);
      expect(getUserSpy).toHaveBeenCalledWith(authId);
      expect(updateEmailSpy).toHaveBeenCalledTimes(1);
      expect(updateEmailSpy).toHaveBeenCalledWith(
        {
          email: '<EMAIL>',
          newEmail: '<EMAIL>',
        },
        X_APP_NAME
      );
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockResolvedValue({ data: {}, status: 404 } as AxiosResponse);

      await expect(
        service.updateAccountEmail('39741a9b-9dea-4254-9f18-9e319887b89e', {
          newEmail: '<EMAIL>',
        })
      ).rejects.toThrow(AccountNotFoundError);
    });
  });

  describe('enableUser()', () => {
    it('calls the usersApi to enable user by auth ID with the given details', async () => {
      const usersSpy = jest
        .spyOn(usersApi, 'userControllerEnable')
        .mockReturnValue(Promise.resolve({ data: {} } as AxiosResponse));

      await service.enableUser('39741a9b-9dea-4254-9f18-9e319887b89e');

      expect(usersSpy).toHaveBeenCalledTimes(1);
      expect(usersSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );
    });

    it('throws an AccountNotFoundError if there is no data returned by the usersApi.userControllerEnable', async () => {
      jest
        .spyOn(usersApi, 'userControllerEnable')
        .mockReturnValue(
          Promise.resolve({ data: undefined, status: 200 } as AxiosResponse)
        );

      await expect(
        service.enableUser('39741a9b-9dea-4254-9f18-9e319887b89e')
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi.userControllerEnable', async () => {
      jest
        .spyOn(usersApi, 'userControllerEnable')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );

      await expect(
        service.enableUser('39741a9b-9dea-4254-9f18-9e319887b89e')
      ).rejects.toThrow(AccountNotFoundError);
    });

    it('transforms the data returned by the usersApi into the correct format', async () => {
      jest.spyOn(usersApi, 'userControllerEnable').mockReturnValue(
        Promise.resolve({
          data: {
            first_name: 'Mobile',
            last_name: 'Tester',
            locale: 'en',
            email: '<EMAIL>',
          },
          status: 200,
        } as AxiosResponse)
      );

      const res = await service.enableUser(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );

      expect(res).toStrictEqual({
        firstName: 'Mobile',
        lastName: 'Tester',
        authId: '39741a9b-9dea-4254-9f18-9e319887b89e',
        locale: 'en',
        email: '<EMAIL>',
      });
    });
  });

  describe('getAccountChargers()', () => {
    it("calls the usersApi to get the user's chargers by auth ID", async () => {
      const usersSpy = jest
        .spyOn(usersApi, 'userControllerGetChargers')
        .mockReturnValue(
          Promise.resolve({ data: [], status: 200 } as AxiosResponse)
        );

      await service.getAccountChargers('39741a9b-9dea-4254-9f18-9e319887b89e');

      expect(usersSpy).toHaveBeenCalledTimes(1);
      expect(usersSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetChargers')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );

      await expect(
        service.getAccountChargers('39741a9b-9dea-4254-9f18-9e319887b89e')
      ).rejects.toThrow(AccountNotFoundError);
    });
  });

  describe('getAccountSuppressedStatus()', () => {
    it("calls the usersApi to get the user's suppressed status by auth ID", async () => {
      const usersSpy = jest
        .spyOn(usersApi, 'userControllerGetSuppressedStatus')
        .mockReturnValue(
          Promise.resolve({
            data: { status: 'BOUNCE' },
            status: 200,
          } as AxiosResponse)
        );

      await service.getAccountSuppressedStatus(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );

      expect(usersSpy).toHaveBeenCalledTimes(1);
      expect(usersSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );
    });

    it('returns the value from the usersApi', async () => {
      jest.spyOn(usersApi, 'userControllerGetSuppressedStatus').mockReturnValue(
        Promise.resolve({
          data: { status: 'BOUNCE' },
          status: 200,
        } as AxiosResponse)
      );

      const res = await service.getAccountSuppressedStatus(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );

      expect(res).toStrictEqual({ status: 'BOUNCE' });
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerGetSuppressedStatus')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );

      await expect(
        service.getAccountSuppressedStatus(
          '39741a9b-9dea-4254-9f18-9e319887b89e'
        )
      ).rejects.toThrow(AccountNotFoundError);
    });
  });

  describe('updateSuppressedStatus()', () => {
    it("calls the usersApi to update the user's suppressed status by auth ID", async () => {
      const updateStatusSpy = jest
        .spyOn(usersApi, 'userControllerUpdateSuppressedStatus')
        .mockReturnValue(
          Promise.resolve({
            data: { status: null },
            status: 200,
          } as AxiosResponse)
        );

      await service.updateAccountSuppressedStatus(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );

      expect(updateStatusSpy).toHaveBeenCalledTimes(1);
      expect(updateStatusSpy).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        { status: null }
      );
    });

    it('returns the value from the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerUpdateSuppressedStatus')
        .mockReturnValue(
          Promise.resolve({
            data: { status: null },
            status: 200,
          } as AxiosResponse)
        );

      const res = await service.updateAccountSuppressedStatus(
        '39741a9b-9dea-4254-9f18-9e319887b89e'
      );

      expect(res).toStrictEqual({ status: null });
    });

    it('throws an AccountNotFoundError if there is a 404 returned by the usersApi', async () => {
      jest
        .spyOn(usersApi, 'userControllerUpdateSuppressedStatus')
        .mockReturnValue(
          Promise.resolve({ data: {}, status: 404 } as AxiosResponse)
        );

      await expect(
        service.updateAccountSuppressedStatus(
          '39741a9b-9dea-4254-9f18-9e319887b89e'
        )
      ).rejects.toThrow(AccountNotFoundError);
    });
  });

  describe('resendEmailVerification()', () => {
    it('get the email from the usersApi, then uses the authApi to resend the email verification', async () => {
      const authId = '123456';
      const getUserSpy = jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockResolvedValue({
          data: { email: '<EMAIL>' },
          status: 200,
        } as AxiosResponse);

      const updateEmailSpy = jest
        .spyOn(authApi, 'emailVerificationControllerSendEmailVerification')
        .mockResolvedValue({
          status: 202,
        } as AxiosResponse);

      await service.resendEmailVerification(authId);

      expect(getUserSpy).toHaveBeenCalledTimes(1);
      expect(updateEmailSpy).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        X_APP_NAME
      );
    });

    it('should throw a AccountNotFoundError if authId does not exist', async () => {
      const authId = '123456';
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValue({
        status: 404,
      } as AxiosResponse);

      await expect(service.resendEmailVerification(authId)).rejects.toThrow(
        AccountNotFoundError
      );
    });
  });

  describe('resetPassword()', () => {
    it("get the email from the usersApi, then uses the authApi to reset user's password", async () => {
      const authId = '123456';
      const getUserSpy = jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockResolvedValue({
          data: { email: '<EMAIL>' },
          status: 200,
        } as AxiosResponse);

      const updateEmailSpy = jest
        .spyOn(authApi, 'passwordResetControllerSendPasswordReset')
        .mockResolvedValue({
          status: 202,
        } as AxiosResponse);

      await service.resetPassword(authId);

      expect(getUserSpy).toHaveBeenCalledTimes(1);
      expect(updateEmailSpy).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        X_APP_NAME
      );
    });

    it('should throw a AccountNotFoundError if authId does not exist', async () => {
      const authId = '123456';
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValue({
        status: 404,
      } as AxiosResponse);

      await expect(service.resetPassword(authId)).rejects.toThrow(
        AccountNotFoundError
      );
    });
  });

  describe('deleteAccount()', () => {
    it('should delete a user', async () => {
      jest.spyOn(usersApi, 'userControllerSoftDelete').mockReturnValue(
        Promise.resolve({
          data: {},
          status: 200,
        } as AxiosResponse)
      );

      await service.deleteAccount(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        false
      );

      expect(usersApi.userControllerSoftDelete).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        false
      );
    });
  });

  describe('unlinkAccount()', () => {
    it('should unlink a user', async () => {
      const mockUnlinkCharger = jest
        .spyOn(usersApi, 'userControllerUnLinkCharger')
        .mockReturnValue(
          Promise.resolve({
            data: {},
            status: 200,
          } as AxiosResponse)
        );

      await service.unlinkCharger(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        'PSL-12345'
      );

      expect(mockUnlinkCharger).toHaveBeenCalledWith(
        '39741a9b-9dea-4254-9f18-9e319887b89e',
        'PSL-12345'
      );
    });
  });
});
