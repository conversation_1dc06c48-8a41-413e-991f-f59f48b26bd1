import {
  AccountActivityEvent,
  AccountCharger,
  AccountDetails,
  AccountFactor,
  AccountPartial,
  AccountSuppressedStatus,
} from './accounts.types';

export const X_APP_NAME = 'identity.podenergy.com';

// Temporary stubs until we have real account data
export const TEST_ACCOUNTS: AccountDetails[] = [
  {
    authId: 'feb1a2-3285bca-23571def',
    email: '<EMAIL>',
    firstName: 'George',
    lastName: 'Mickleburgh',
    organisations: [],
    isEmailVerified: true,
    status: 'active',
    balance: {
      amount: 5,
      currency: 'GBP',
    },
    lastSignInTimestamp: Date.now(),
    accountCreationTimestamp: Date.now(),
    locale: 'en',
  },
];

export const TEST_ACCOUNT_FACTORS: AccountFactor[] = [
  {
    id: '919276fd-bbad-438f-ad48-a2d2c22d0812',
    phoneNumberCountryCode: '+44',
    phoneNumberLastThree: '456',
    enrollmentTime: 'Fri, 22 Sep 2017 01:49:58 GMT',
  },
];

export const TEST_ACCOUNTS_SEARCH_RESULTS: AccountPartial[] = TEST_ACCOUNTS.map(
  ({ authId, email, firstName, lastName, locale }) => ({
    authId,
    email,
    firstName,
    lastName,
    locale,
  })
);

export const TEST_ACCOUNT_ACTIVITY: AccountActivityEvent[] = [
  {
    timestamp: Date.now(),
    event: 'Top up',
    details: '£5 top up with **** 4567',
    status: 'failed',
  },
  {
    timestamp: Date.now(),
    event: 'Password reset',
    details: 'Sent to ***@gmail.com',
    status: 'success',
  },
  {
    timestamp: Date.now(),
    event: 'Login',
    details: 'Mobile app login',
    status: 'success',
  },
];

export const TEST_ACCOUNT_CHARGERS: AccountCharger[] = [
  {
    ppid: 'PSL-607125',
  },
];

export const TEST_ACCOUNT_SUPPRESSED_STATUS: AccountSuppressedStatus = {
  status: 'BOUNCE',
};
