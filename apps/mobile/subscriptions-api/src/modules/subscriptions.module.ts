import { ActionService } from '../application/services/action.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DatabaseModule } from './database.module';
import {
  DelegatedControlChargingStationsApi,
  Configuration as SmartChargingServiceClientConfiguration,
} from '@experience/shared/axios/smart-charging-service-client';
import { DependencyInjectionToken } from './constants';
import { LoanManagementSystemService } from '../application/services/loan-management-system.service';
import { Module } from '@nestjs/common';
import { SUBSCRIPTION_EVENTS_INCOMING_ROUTES } from '../interfaces/sqs-consumers/subscription-events-incoming/routes';
import { SalesforceClient } from '@experience/shared/salesforce/client';
import { SalesforceService } from '../application/services/salesforce.service';
import { SlickClient } from '@experience/mobile/slick/client';

import {
  Configuration,
  UsersApi,
} from '@experience/driver-account-api/api-client';
import { EmailModule } from './email.module';
import { RewardWalletsApi } from '@experience/mobile/rewards-api/axios';
import { SqsEventRouterModule } from '@experience/mobile/nest/sqs-event-router';
import { SubscriptionsController } from '../interfaces/http/subscriptions.controller';
import { SubscriptionsService } from '../application/services/subscriptions.service';
import { TypeOrmActionRepository } from '../infrastructure/repositories/type-orm/action.repository';
import { TypeOrmSubscriptionRepository } from '../infrastructure/repositories/type-orm/subscription.repository';
import { TypeOrmTransactionProvider } from '@experience/mobile/nest/typeorm-transactions';
import { UserService } from '../application/services/user.service';
import axios from 'axios';

@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    EmailModule,
    SqsEventRouterModule.register(
      'SUBSCRIPTIONS_API_INCOMING_EVENTS_QUEUE_URL',
      SUBSCRIPTION_EVENTS_INCOMING_ROUTES
    ),
  ],
  controllers: [SubscriptionsController],
  providers: [
    SubscriptionsService,
    ActionService,
    UserService,
    SalesforceService,
    LoanManagementSystemService,
    {
      inject: [ConfigService],
      provide: SlickClient,
      useFactory: async (configService: ConfigService) =>
        new SlickClient({
          baseUrl: configService.getOrThrow('SLICK_API_BASE_URL') as string,
          apiToken: configService.getOrThrow('SLICK_API_TOKEN') as string,
          userAgent: 'SlickClient',
          maxRetry: configService.get<number>('SLICK_API_MAX_RETRY') ?? 3,
        }),
    },
    {
      inject: [ConfigService],
      provide: SalesforceClient,
      useFactory: async (configService: ConfigService) =>
        new SalesforceClient({
          audience: configService.getOrThrow(
            'SALESFORCE_AUTH_AUDIENCE'
          ) as string,
          clientId: configService.getOrThrow(
            'SALESFORCE_AUTH_CLIENT_ID'
          ) as string,
          instanceUrl: configService.getOrThrow(
            'SALESFORCE_INSTANCE_URL'
          ) as string,
          privateKey: configService
            .getOrThrow('SALESFORCE_AUTH_PRIVATE_KEY')
            .replace(/\\n/g, '\n'),
          username: configService.getOrThrow(
            'SALESFORCE_AUTH_USERNAME'
          ) as string,
          userAgent: 'PodPoint-SubscriptionsApi',
          maxRetry: 5,
        }),
    },
    TypeOrmSubscriptionRepository,
    TypeOrmActionRepository,
    TypeOrmTransactionProvider,
    {
      provide: DependencyInjectionToken.SUBSCRIPTIONS_REPOSITORY,
      useExisting: TypeOrmSubscriptionRepository,
    },
    {
      provide: DependencyInjectionToken.ACTION_REPOSITORY,
      useExisting: TypeOrmActionRepository,
    },
    {
      provide: DependencyInjectionToken.TRANSACTION_PROVIDER,
      useExisting: TypeOrmTransactionProvider,
    },
    {
      inject: [ConfigService],
      provide: DelegatedControlChargingStationsApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({ timeout: 10000 });
        return new DelegatedControlChargingStationsApi(
          new SmartChargingServiceClientConfiguration(),
          configService.get('SMART_CHARGING_SERVICE_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: UsersApi,
      useFactory: async (configService: ConfigService) =>
        new UsersApi(
          new Configuration({
            basePath: configService.get('DRIVER_ACCOUNT_API_BASE_URL'),
          })
        ),
    },
    {
      inject: [ConfigService],
      provide: RewardWalletsApi,
      useFactory: async (configService: ConfigService) =>
        new RewardWalletsApi(
          new Configuration({
            basePath: configService.get('REWARDS_API_BASE_URL'),
          })
        ),
    },
    ...SUBSCRIPTION_EVENTS_INCOMING_ROUTES,
  ],
})
export class SubscriptionsModule {}
