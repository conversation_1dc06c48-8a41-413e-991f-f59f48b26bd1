import {
  ActionDocumentCodeType,
  ActionEntity,
} from '../../domain/entities/action.entity';
import { ActionService } from './action.service';
import { CreateSubscriptionApplicationDTO } from '../dto/create-subscription.application.dto';
import { DependencyInjectionToken } from '../../modules/constants';
import { Inject, Logger, forwardRef } from '@nestjs/common';

import {
  BaseOrderEntity,
  OrderOrigin,
  SelfServiceOrderEntity,
} from '../../domain/entities/order.entity';
import {
  CouldNotFindEntityError,
  TransactionProviderInterface,
} from '@experience/mobile/clean-architecture';
import { EmailService } from './email.service';
import { FatalApplicationError } from '../errors/fatal-application.error';
import { LoanManagementSystemService } from './loan-management-system.service';
import { MissingDataError } from '../errors/missing-data.error';
import { NoApplicationIdError } from '../errors/no-application-id.error';
import { PPIDAlreadyOnSubscriptionError } from '../errors/ppid-already-on-subscription.error';
import { PersistedSubscriptionWithActionsDTO } from '../dto/persisted-subscription-with-actions.dto';
import { PlanDetails, PlanType } from '../../domain/entities/plan.entity';
import { Readable } from 'stream';
import { RewardWalletsApi } from '@experience/mobile/rewards-api/axios';
import {
  SubscriptionDirectDebit,
  SubscriptionDocument,
} from '../../domain/types/subscription.types';
import {
  SubscriptionEntity,
  SubscriptionId,
  SubscriptionStatus,
} from '../../domain/entities/subscription.entity';
import { SubscriptionNotActiveError } from '../errors/subscription-not-active.error';
import { SubscriptionRepositoryInterface } from '../../domain/repositories/subscription.repository.interface';
import { UnknownDocumentError } from '../errors/unknown-document.error';
import { UnpersistedActionEntityFactory } from '../../domain/entity-factories/unpersisted-action-entity.factory';
import { UpdateSubscriptionDTO } from '../dto/update-subscription.dto';
import { UserNotLinkedToChargerError } from '../errors/user-not-linked-to-charger.error';
import { UsersApi } from '@experience/driver-account-api/api-client';
import dayjs from 'dayjs';

export class SubscriptionsService {
  private readonly logger = new Logger(SubscriptionsService.name);

  constructor(
    @Inject(DependencyInjectionToken.SUBSCRIPTIONS_REPOSITORY)
    private readonly repository: SubscriptionRepositoryInterface,
    @Inject(forwardRef(() => ActionService))
    private readonly actionService: ActionService,
    private readonly loanManagementSystemService: LoanManagementSystemService,
    @Inject(DependencyInjectionToken.TRANSACTION_PROVIDER)
    private readonly transactionProvider: TransactionProviderInterface,
    private readonly usersApi: UsersApi,
    private readonly rewardWalletsApi: RewardWalletsApi,
    private readonly emailService: EmailService
  ) {}

  private applyMilesRenewalDate(
    subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>
  ): SubscriptionEntity<BaseOrderEntity, PlanDetails> {
    if (subscription.activatedAt) {
      const yearsDifference = dayjs(Date.now()).diff(
        subscription.activatedAt,
        'years'
      );
      const milesRenewalDate = dayjs(subscription.activatedAt)
        .add(yearsDifference + 1, 'year')
        .toDate();

      this.logger.log(
        {
          subscriptionId: subscription.id,
          yearsDifference,
          milesRenewalDate,
        },
        'calculated miles renewal date'
      );

      subscription.plan.details.milesRenewalDate = milesRenewalDate;
    }

    return subscription;
  }

  /**
   * @returns null if subscription cannot be found
   */
  async readById(
    subscriptionId: SubscriptionId
  ): Promise<PersistedSubscriptionWithActionsDTO | null> {
    const maybeSubscription = await this.repository.read(subscriptionId);

    if (!maybeSubscription) {
      return null;
    }

    const actions = await this.actionService.findBySubscription(
      maybeSubscription
    );

    return new PersistedSubscriptionWithActionsDTO({
      subscription: this.applyMilesRenewalDate(maybeSubscription),
      actions,
    });
  }

  /**
   * Searches for subscriptions by userId and/or ppid.
   * - If `userId` is provided, returns all subscriptions associated with that user.
   * - If `ppid` is provided, returns all subscriptions linked to that `ppid`.
   * - If neither `userId` nor `ppid` is provided, returns an empty array.
   *
   * @param {{ppid?:string, userId?:string}} criteria - The search criteria.
   * @param {string} [criteria.userId] - The Firebase user ID to filter subscriptions by (optional).
   * @param {string} [criteria.ppid] - The provider's payment identifier to filter subscriptions by (optional).
   * @returns {Promise<PersistedSubscriptionWithActionsDTO[]>} A promise that resolves to an array of matching subscriptions (empty if none found).
   * @throws `FatalRepositoryError` If the search query fails due to an ORM error.
   */
  async getByPpidAndOrUserId(criteria: {
    ppid?: string;
    userId?: string;
  }): Promise<PersistedSubscriptionWithActionsDTO[]> {
    this.logger.debug(criteria, 'Executing search for subscriptions');

    const { userId, ppid } = criteria;

    let subscriptions: SubscriptionEntity<BaseOrderEntity, PlanDetails>[] = [];
    if (userId) {
      subscriptions = await this.repository.getByUserId({ userId });
    }
    if (ppid) {
      subscriptions = await this.repository.getByPpid({ ppid });
    }

    const subscriptionsWithActions: PersistedSubscriptionWithActionsDTO[] = [];

    for (const subscription of this.orderSubscriptionsByStatus(subscriptions)) {
      const actions = await this.actionService.findBySubscription(subscription);

      subscriptionsWithActions.push(
        new PersistedSubscriptionWithActionsDTO({
          subscription: this.applyMilesRenewalDate(subscription),
          actions,
        })
      );
    }
    return subscriptionsWithActions;
  }

  private orderSubscriptionsByStatus(
    subscriptions: SubscriptionEntity<BaseOrderEntity, PlanDetails>[]
  ): SubscriptionEntity<BaseOrderEntity, PlanDetails>[] {
    const statusSortOrder: SubscriptionStatus[] = [
      SubscriptionStatus.ACTIVE,
      SubscriptionStatus.PENDING,
      SubscriptionStatus.REJECTED,
      SubscriptionStatus.SUSPENDED,
      SubscriptionStatus.ENDED,
      SubscriptionStatus.CANCELLED,
    ];

    return subscriptions.sort(
      (a, b) =>
        statusSortOrder.indexOf(a.status) - statusSortOrder.indexOf(b.status) ||
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  private isSelfServiceOrder(
    order: BaseOrderEntity
  ): order is SelfServiceOrderEntity {
    return order.origin === OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE;
  }

  private async isUserLinkedToCharger(
    userId: string,
    ppid: string
  ): Promise<boolean> {
    const { data } = await this.usersApi.userControllerGetChargers(userId);

    return data.some((charger) => charger.ppid === ppid);
  }

  /**
   * @throws `CouldNotPersistEntityError` if subscription could not be created
   */
  async createNew<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    newSubscriptionParams: CreateSubscriptionApplicationDTO<
      TOrderEntity,
      TPlanDetails
    >
  ): Promise<PersistedSubscriptionWithActionsDTO> {
    this.logger.log(
      {
        newSubscriptionParams,
      },
      'Executing new subscription creation'
    );

    if (
      this.isSelfServiceOrder(newSubscriptionParams.protoSubscription.order)
    ) {
      const { userId } = newSubscriptionParams.protoSubscription;
      const { ppid } = newSubscriptionParams.protoSubscription.order;

      const subscriptions = await this.getByPpidAndOrUserId({
        ppid,
      });

      if (
        subscriptions.some(
          ({ subscription }) =>
            subscription.status === SubscriptionStatus.ACTIVE
        )
      ) {
        this.logger.error(
          {
            ppid,
          },
          'ppid already used on active subscription - can not create subscription'
        );
        throw new PPIDAlreadyOnSubscriptionError();
      }

      if (!(await this.isUserLinkedToCharger(userId, ppid))) {
        this.logger.error(
          {
            ppid,
            userId,
          },
          'user not linked to charger - can not create subscription'
        );

        throw new UserNotLinkedToChargerError();
      }
    }

    const subscription = await this.repository.create({
      userId: newSubscriptionParams.protoSubscription.userId,
      plan: newSubscriptionParams.protoSubscription.plan,
      status: SubscriptionStatus.PENDING,
      order: newSubscriptionParams.protoSubscription.order,
      activatedAt: null,
    });

    const actions = await this.createDefaultActionsForNewSubscription(
      subscription,
      newSubscriptionParams
    );

    return new PersistedSubscriptionWithActionsDTO({
      subscription: this.applyMilesRenewalDate(subscription),
      actions,
    });
  }

  async transferSubscription(
    subscriptionId: SubscriptionId,
    parameters: UpdateSubscriptionDTO
  ): Promise<PersistedSubscriptionWithActionsDTO> {
    this.logger.log(
      { subscriptionId, parameters },
      'transferring subscription'
    );

    const subscription = await this.readById(subscriptionId);

    if (!subscription) {
      this.logger.error(
        { subscriptionId, parameters },
        'no subscription found matching id'
      );

      throw new CouldNotFindEntityError(
        {
          id: subscriptionId,
        },
        'could not find subscription'
      );
    }

    if (subscription.subscription.status !== SubscriptionStatus.ACTIVE) {
      this.logger.error(
        {
          subscriptionId,
          parameters,
          status: subscription.subscription.status,
        },
        'subscription found for id was not active'
      );

      throw new CouldNotFindEntityError(
        {
          id: subscriptionId,
        },
        'could not find active subscription'
      );
    }

    if (
      subscription.subscription.plan.details.type !== PlanType.POD_DRIVE_REWARDS
    ) {
      this.logger.error(
        {
          subscriptionId,
          parameters,
          type: subscription.subscription.plan.details.type,
        },
        'subscription found for id was not pod drive rewards'
      );

      throw new CouldNotFindEntityError(
        {
          id: subscriptionId,
        },
        'could not find active pod drive rewards subscription'
      );
    }

    if (subscription.subscription.userId === parameters.userId) {
      this.logger.error(
        {
          subscriptionId,
          parameters,
          subscriptionUserId: subscription.subscription.userId,
        },
        'user already owns subscription'
      );

      return subscription;
    }

    const { data: chargers } = await this.usersApi.userControllerGetChargers(
      parameters.userId
    );

    const ppid =
      await this.actionService.getLinkChargerActionPpidBySubscriptionId(
        subscriptionId
      );

    if (!ppid) {
      this.logger.error(
        { subscriptionId, parameters },
        'PPID was not populated in link charger action'
      );

      throw new CouldNotFindEntityError(
        {
          id: subscriptionId,
        },
        'could not find ppid linked to subscription'
      );
    }

    if (!chargers.some((charger) => ppid === charger.ppid)) {
      this.logger.error(
        { subscriptionId, parameters },
        'User was not linked to charger associated with this subscription'
      );

      throw new CouldNotFindEntityError(
        {
          id: subscriptionId,
        },
        'could not find charger linked to user matching ppid linked to subscription'
      );
    }

    const { data: existingUserWallet } =
      await this.rewardWalletsApi.walletControllerRead(
        subscription.subscription.userId
      );

    if (existingUserWallet.subscriptionId !== subscription.subscription.id) {
      this.logger.error(
        { subscriptionId, parameters, existingUserWallet },
        'subscription id on wallet did not match subscription'
      );

      throw new FatalApplicationError(
        'Subscription ID on user wallet did not match'
      );
    }

    let updatedSubscription = subscription.subscription;

    await this.transactionProvider.withTransaction(async () => {
      updatedSubscription = await this.repository.updateSubscriptionUserId(
        subscriptionId,
        parameters.userId
      );

      await this.rewardWalletsApi.walletControllerTransferWallet(
        subscription.subscription.userId,
        {
          userId: parameters.userId,
        }
      );
    });

    await this.emailService.notifyChargerSubscriptionTransfer([
      subscription.subscription.userId,
      parameters.userId,
    ]);

    return {
      subscription: updatedSubscription,
      actions: subscription.actions,
    };
  }

  private async createDefaultActionsForNewSubscription<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    subscription: SubscriptionEntity<TOrderEntity, TPlanDetails>,
    newSubscriptionParams: CreateSubscriptionApplicationDTO<
      TOrderEntity,
      TPlanDetails
    >
  ): Promise<ActionEntity[]> {
    switch (subscription.plan.details.type) {
      case PlanType.POD_DRIVE: {
        const actions = [];

        const payUpfrontFeeAction = await this.actionService.create(
          UnpersistedActionEntityFactory.createPayupFrontFee({
            subscriptionId: subscription.id,
            dependsOn: [],
          })
        );
        actions.push(payUpfrontFeeAction);

        if (!newSubscriptionParams.homeSurveyUrl) {
          throw new FatalApplicationError(
            'No home survey URL provided. Can not create action'
          );
        }

        const completeSurveyAction = await this.actionService.create(
          UnpersistedActionEntityFactory.createCompleteSurveyAction({
            subscriptionId: subscription.id,
            surveyUrl: newSubscriptionParams.homeSurveyUrl,
            dependsOn: [payUpfrontFeeAction],
          })
        );
        actions.push(completeSurveyAction);

        const affordabilityCheckAction = await this.actionService.create(
          UnpersistedActionEntityFactory.createAffordabilityCheckAction({
            subscriptionId: subscription.id,
            dependsOn: [completeSurveyAction],
          })
        );
        actions.push(affordabilityCheckAction);

        const setupDirectDebitAction = await this.actionService.create(
          UnpersistedActionEntityFactory.createSetupDirectDebitAction({
            subscriptionId: subscription.id,
            dependsOn: [affordabilityCheckAction],
          })
        );
        actions.push(setupDirectDebitAction);

        let installDependsOn = setupDirectDebitAction;
        const signDocumentsAction = await this.actionService.create(
          UnpersistedActionEntityFactory.createSignDocumentsAction({
            subscriptionId: subscription.id,
            dependsOn: [setupDirectDebitAction],
          })
        );
        actions.push(signDocumentsAction);
        installDependsOn = signDocumentsAction;
        this.logger.log(
          { subscription },
          'TMW mode is disabled, sign documents action created'
        );

        const installChargerAction = await this.actionService.create(
          UnpersistedActionEntityFactory.createCreateInstallChargingStationAction(
            {
              subscriptionId: subscription.id,
              dependsOn: [installDependsOn],
            }
          )
        );
        actions.push(installChargerAction);

        return actions;
      }

      case PlanType.POD_DRIVE_REWARDS: {
        const actions = [];

        const signRewardsToSAction = await this.actionService.create(
          UnpersistedActionEntityFactory.createSignRewardsTOSAction({
            subscriptionId: subscription.id,
            dependsOn: [],
          })
        );

        actions.push(signRewardsToSAction);

        const linkExistingChargerAction = await this.actionService.create(
          UnpersistedActionEntityFactory.createLinkExistingChargerAction({
            subscriptionId: subscription.id,
            dependsOn: [signRewardsToSAction],
          })
        );

        actions.push(linkExistingChargerAction);

        return actions;
      }

      default:
        return [];
    }
  }

  async getSubscriptionByOrderId(
    orderId: string
  ): Promise<SubscriptionEntity<BaseOrderEntity, PlanDetails> | null> {
    return this.repository.getSubscriptionByOrderId(orderId);
  }

  async doesMatchingSubscriptionExist(
    orderId: string,
    type: string
  ): Promise<boolean> {
    const subscription = await this.repository.getSubscriptionByOrderId(
      orderId
    );

    return subscription?.plan.details.type == type;
  }

  async getPendingSubscriptionByOrderId(
    orderId: string
  ): Promise<SubscriptionEntity<BaseOrderEntity, PlanDetails> | null> {
    return this.repository.getPendingSubscriptionByOrderId(orderId);
  }

  async activateSubscription(subscriptionId: string) {
    return this.repository.activateSubscription(subscriptionId);
  }

  async updateSubscriptionStatus(
    subscriptionId: string,
    status: SubscriptionStatus
  ) {
    return await this.repository.updateSubscriptionStatus(
      subscriptionId,
      status
    );
  }

  async cancelSubscriptionByApplicationId(
    applicationId: number
  ): Promise<SubscriptionEntity<BaseOrderEntity, PlanDetails>> {
    this.logger.log({ applicationId }, 'attempting to cancel subscription');

    const { subscriptionId } =
      await this.actionService.getAffordabilityActionByApplicationId(
        applicationId
      );

    this.logger.log(
      {
        applicationId,
        subscriptionId,
      },
      'found action with application id, cancelling related subscription'
    );

    const subscription = await this.updateSubscriptionStatus(
      subscriptionId,
      SubscriptionStatus.CANCELLED
    );

    this.logger.log(
      {
        applicationId,
        subscriptionId,
      },
      'successfully cancelled subscription'
    );

    return subscription;
  }

  async getByActionId<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    actionId: ActionEntity['id']
  ): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails> | null> {
    return this.repository.getByActionId(actionId);
  }

  async deleteSubscription(subscriptionId: SubscriptionId) {
    this.logger.log(
      { subscriptionId },
      'attempting to cancel and delete subscription'
    );

    return this.repository.cancelAndDeleteSubscription(subscriptionId);
  }

  private async getApplicationIdBySubscriptionId(
    subscriptionId: string
  ): Promise<number> {
    const subscription = await this.readById(subscriptionId);

    if (!subscription) {
      this.logger.error(
        { subscriptionId },
        'no subscription found matching id'
      );

      throw new CouldNotFindEntityError(
        {
          subscriptionId,
        },
        'could not find subscription'
      );
    }

    if (subscription.subscription.status !== SubscriptionStatus.ACTIVE) {
      this.logger.error(
        { subscriptionId, status: subscription.subscription.status },
        'subscription was not active.  No direct debit details will be returned'
      );

      throw new SubscriptionNotActiveError('subscription was not active');
    }

    const applicationId =
      await this.actionService.getAffordabilityApplicationId(subscriptionId);

    if (!applicationId) {
      this.logger.error(
        { subscriptionId },
        'unable to retrieve applicationId from check affordability action'
      );

      throw new NoApplicationIdError('application id was not populated');
    }

    return applicationId;
  }

  async getSubscriptionDirectDebit(
    subscriptionId: SubscriptionId
  ): Promise<SubscriptionDirectDebit> {
    this.logger.log(
      { subscriptionId },
      'retrieving subscription direct debit details'
    );

    const applicationId = await this.getApplicationIdBySubscriptionId(
      subscriptionId
    );

    return await this.loanManagementSystemService.getApplicationDirectDebitDetails(
      applicationId
    );
  }

  private async getRawSubscriptionDocuments(subscriptionId: string) {
    const applicationId = await this.getApplicationIdBySubscriptionId(
      subscriptionId
    );

    return await this.loanManagementSystemService.getApplicationDocuments(
      applicationId
    );
  }

  private isValidDocumentTag(tag: string): tag is ActionDocumentCodeType {
    return Object.values(ActionDocumentCodeType).map(String).includes(tag);
  }

  async getSubscriptionDocuments(
    subscriptionId: string
  ): Promise<SubscriptionDocument[]> {
    this.logger.log({ subscriptionId }, 'retrieving subscription documents');

    const documents = await this.getRawSubscriptionDocuments(subscriptionId);

    return documents.map((document) => {
      if (
        !document.signByLink ||
        document.signByLink.tag === null ||
        !this.isValidDocumentTag(document.signByLink.tag)
      ) {
        throw new MissingDataError('Invalid Document');
      }

      return {
        issued: new Date(document.signByLink.signedAt).toISOString(),
        link: `/subscriptions/${subscriptionId}/documents/LMS-${document.id}`,
        format: 'PDF',
        active: true,
        type: document.signByLink.tag,
      };
    });
  }

  async getSubscriptionDocument(
    subscriptionId: string,
    documentId: number
  ): Promise<Readable> {
    this.logger.log({ documentId }, 'retrieving subscription document');

    const documents = await this.getRawSubscriptionDocuments(subscriptionId);

    if (!documents.some((doc) => doc.id === documentId)) {
      throw new UnknownDocumentError();
    }

    const { response: document } =
      await this.loanManagementSystemService.getDocument(documentId);

    this.logger.log(
      { expiryTime: document.expiry_time },
      'got document token from slick'
    );

    return await this.loanManagementSystemService.downloadDocumentByToken(
      document.token
    );
  }

  async cancelPendingSCRSelfServiceSubscriptions(ppid: string) {
    await this.repository.cancelPendingSCRSelfServiceSubscriptions(ppid);
  }
}
