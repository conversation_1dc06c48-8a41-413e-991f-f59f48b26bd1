import { ActionContextError } from '../../domain/errors/action-context-error';
import {
  ActionData,
  ActionEntity,
  ActionStatus,
  ActionStatusDisplayName,
  ActionType,
  ActionTypeDisplayName,
  UnpersistedActionEntity,
} from '../../domain/entities/action.entity';
import { ActionRepositoryInterface } from '../../domain/repositories/action.repository.interface';
import {
  AffordabilityCheck,
  CompleteInstall,
  DirectDebit,
} from '../../domain/types/action.types';
import { AffordabilityCheckFailedError } from '../../domain/errors/affordability-check-failed.error';
import { ApplicationUpdate } from '@experience/mobile/slick/client';
import {
  BaseOrderEntity,
  OrderOrigin,
  SalesforceOrderEntity,
  SelfServiceOrderEntity,
} from '../../domain/entities/order.entity';
import {
  CouldNotFindEntityError,
  InvalidEntityError,
  TransactionProviderInterface,
} from '@experience/mobile/clean-architecture';
import { DelegatedControlChargingStationsApi } from '@experience/shared/axios/smart-charging-service-client';
import { DependencyInjectionToken } from '../../modules/constants';
import { DirectDebitCheckFailed } from '../../domain/errors/direct-debit-check-failed.error';
import { FatalApplicationError } from '../errors/fatal-application.error';
import { IncompleteDependencyError } from '../../domain/errors/incomplete-dependency.error';
import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { LoanManagementSystemService } from './loan-management-system.service';
import { MissingDataError } from '../errors/missing-data.error';
import {
  PlanDetails,
  PodDrivePlanDetails,
  PodDriveRewardsPlanDetails,
} from '../../domain/entities/plan.entity';
import { RewardWalletsApi } from '@experience/mobile/rewards-api/axios';
import { SalesforceService } from './salesforce.service';
import { SubscriptionAction } from '@experience/mobile/driver-account/database';
import {
  SubscriptionEntity,
  SubscriptionId,
  SubscriptionStatus,
} from '../../domain/entities/subscription.entity';
import { SubscriptionsService } from './subscriptions.service';
import { UsersApi } from '@experience/driver-account-api/api-client';

interface ActionContext<
  TOrderEntity extends BaseOrderEntity,
  TPlanDetails extends PlanDetails
> {
  action: ActionEntity | null;
  subscription: SubscriptionEntity<TOrderEntity, TPlanDetails> | null;
  subscriptionActions: ActionEntity[] | null;
}

@Injectable()
export class ActionService {
  private readonly logger = new Logger(ActionService.name);

  constructor(
    @Inject(DependencyInjectionToken.ACTION_REPOSITORY)
    private readonly repository: ActionRepositoryInterface,
    @Inject(DependencyInjectionToken.TRANSACTION_PROVIDER)
    private readonly transactionProvider: TransactionProviderInterface,
    @Inject(forwardRef(() => SubscriptionsService))
    private readonly subscriptionsService: SubscriptionsService,
    private readonly salesforceService: SalesforceService,
    private readonly delegatedControlChargingStationClient: DelegatedControlChargingStationsApi,
    private readonly loanManagementSystemService: LoanManagementSystemService,
    private readonly usersApi: UsersApi,
    private readonly rewardWalletsApi: RewardWalletsApi
  ) {}

  /**
   * @throws `CouldNotPersistEntityError` if Action could not be created
   */
  async create(protoAction: UnpersistedActionEntity): Promise<ActionEntity> {
    this.logger.log(
      {
        protoAction,
      },
      'Executing new action creation'
    );

    return await this.repository.create(protoAction);
  }

  async readById(actionId: ActionEntity['id']): Promise<ActionEntity | null> {
    this.logger.debug(actionId, 'Executing get subscription action by id');

    return await this.repository.read(actionId);
  }

  async findBySubscription(
    params: Pick<SubscriptionEntity<BaseOrderEntity, PlanDetails>, 'id'>
  ): Promise<ActionEntity[]> {
    this.logger.log(
      {
        params,
      },
      'Finding actions by subscription (id)'
    );

    const actions = await this.repository.findBySubscriptionId(params.id);
    return this.sortActionsByImportance(actions);
  }

  async getAffordabilityApplicationId(
    subscriptionId: SubscriptionId
  ): Promise<number | null> {
    this.logger.log(
      { subscriptionId },
      'retrieving subscription affordability application id'
    );

    const action = this.findInSubscriptionActionsOrFail(
      await this.findBySubscription({ id: subscriptionId }),
      ActionType.CHECK_AFFORDABILITY_V1
    );

    this.verifyActionTypeOrFail(action, ActionType.CHECK_AFFORDABILITY_V1);

    return action.data.applicationId;
  }

  async getAffordabilityActionByApplicationId(
    applicationId: number
  ): Promise<ActionEntity> {
    return await this.findFirstActionByTypeAndApplicationIdOrFail(
      ActionType.CHECK_AFFORDABILITY_V1,
      applicationId
    );
  }

  async getLinkChargerActionPpidBySubscriptionId(
    subscriptionId: string
  ): Promise<string | null> {
    const action = this.findInSubscriptionActionsOrFail(
      await this.findBySubscription({ id: subscriptionId }),
      ActionType.LINK_EXISTING_CHARGER_V1
    );

    this.verifyActionTypeOrFail(action, ActionType.LINK_EXISTING_CHARGER_V1);

    return action.data.ppid;
  }

  private async updateSignDocumentsActionDocuments(
    actions: ActionEntity[],
    applicationId: number
  ) {
    const signDocumentsAction = this.findInSubscriptionActionsOrFail(
      actions,
      ActionType.SIGN_DOCUMENTS_V1,
      ActionStatus.PENDING
    );

    this.verifyActionTypeOrFail(
      signDocumentsAction,
      ActionType.SIGN_DOCUMENTS_V1
    );

    const documents =
      await this.loanManagementSystemService.getApplicationDocumentsToSign(
        applicationId
      );

    this.logger.log({ signDocumentsAction });

    await this.repository.update(signDocumentsAction, {
      data: {
        type: ActionType.SIGN_DOCUMENTS_V1,
        documents,
      },
    });
  }

  async completeAffordabilityCheckAction(
    actionId: ActionEntity['id'],
    data: AffordabilityCheck
  ): Promise<ActionEntity> {
    return await this.processAction(
      { params: { actionId }, name: 'completeAffordabilityCheck' },
      {
        setup: async () => {
          const action = await this.findActionOrFail(
            actionId,
            ActionType.CHECK_AFFORDABILITY_V1
          );

          const subscription = await this.findActionSubscriptionOrFail<
            SalesforceOrderEntity,
            PodDrivePlanDetails
          >(action.id);

          const subscriptionActions = await this.findBySubscription({
            id: action.subscriptionId,
          });

          return { action, subscription, subscriptionActions };
        },
        process: async (
          action: ActionEntity,
          subscription: SubscriptionEntity<
            SalesforceOrderEntity,
            PodDrivePlanDetails
          >,
          actions: ActionEntity[]
        ) => {
          this.verifyActionTypeOrFail(
            action,
            ActionType.CHECK_AFFORDABILITY_V1
          );

          const result =
            await this.loanManagementSystemService.createApplication(
              data,
              subscription
            );

          if (this.salesforceService.isSalesforceOrder(subscription.order)) {
            await this.salesforceService.setApplicationUrl(
              subscription.order,
              result.response.id
            );
          }

          const decisionResult =
            await this.loanManagementSystemService.getApplicationDecision(
              result.response?.id
            );

          const decision =
            decisionResult.response?.decision?.toLowerCase() ?? 'decline';

          const status =
            decision === 'accept' ? ActionStatus.SUCCESS : ActionStatus.FAILURE;

          if (status != ActionStatus.SUCCESS) {
            this.logger.log(
              { subscriptionId: action.subscriptionId, decision, status },
              'affordability check failed, setting subscription to REJECTED'
            );

            await this.transactionProvider.withTransaction(
              async () => {
                await this.repository.update(action, {
                  data: {
                    type: ActionType.CHECK_AFFORDABILITY_V1,
                    applicationId: result.response.id,
                    loanId: action.data.loanId,
                  },
                });

                action.data.applicationId = result.response.id;
              },
              {
                nestedBehaviour: 'new_transaction',
              }
            );

            if (this.salesforceService.isSalesforceOrder(subscription.order)) {
              await this.salesforceService.setInterventionReason(
                subscription.order,
                'affordability'
              );
            }

            throw new AffordabilityCheckFailedError(
              'affordability check failed'
            );
          }

          await this.updateSignDocumentsActionDocuments(
            actions,
            result.response.id
          );

          return await this.repository.update(action, {
            status,
            data: {
              type: ActionType.CHECK_AFFORDABILITY_V1,
              applicationId: result.response.id,
              loanId: action.data.loanId,
            },
          });
        },
      }
    );
  }

  async completeSetupDirectDebitAction(
    subscriptionId: SubscriptionId,
    actionId: ActionEntity['id'],
    data: DirectDebit
  ): Promise<ActionEntity> {
    return await this.processAction(
      {
        params: { subscriptionId, actionId },
        name: 'completeSetupDirectDebitAction',
      },
      {
        setup: async () => {
          const action = await this.findActionOrFail(
            actionId,
            ActionType.SETUP_DIRECT_DEBIT_V1
          );

          const subscription = await this.findActionSubscriptionOrFail<
            SalesforceOrderEntity,
            PodDrivePlanDetails
          >(action.id);

          const subscriptionActions = await this.findBySubscription(
            subscription
          );

          return { action, subscription, subscriptionActions };
        },
        process: async (
          action: ActionEntity,
          subscription: SubscriptionEntity<
            SalesforceOrderEntity,
            PodDrivePlanDetails
          >,
          actions: ActionEntity[]
        ) => {
          const checkAffordabilityAction = this.findInSubscriptionActionsOrFail(
            actions,
            ActionType.CHECK_AFFORDABILITY_V1,
            ActionStatus.SUCCESS
          );

          const applicationId = this.extractActionApplicationIdOrFail(
            checkAffordabilityAction
          );

          const applicationUpdatePayload: ApplicationUpdate = {
            id: applicationId,
            account_number: data.accountNumber,
            sortcode: data.sortCode,
            account_holder_name: data.accountName,
            joint_account: data.requiresMoreThanOneSignatory ? '1' : '0',
          };

          await this.loanManagementSystemService.updateApplication(
            applicationUpdatePayload,
            subscription
          );

          return await this.repository.update(action, {
            status: ActionStatus.SUCCESS,
          });
        },
      }
    );
  }

  async completeHomeSurveyAction(params: {
    orderId?: string;
    actionId?: string;
  }): Promise<ActionEntity> {
    return await this.processAction(
      { params, name: 'completeHomeSurveyAction' },
      {
        setup: async () => {
          if (params.orderId) {
            const { orderId } = params;

            const subscription = await this.findOrderSubscriptionOrFail(
              orderId
            );

            const subscriptionActions = await this.findBySubscription(
              subscription
            );

            const action = this.findInSubscriptionActionsOrFail(
              subscriptionActions,
              ActionType.COMPLETE_HOME_SURVEY_V1
            );

            return { action, subscription, subscriptionActions };
          }

          if (params.actionId) {
            const { actionId } = params;

            const action = await this.findActionOrFail(
              actionId,
              ActionType.COMPLETE_HOME_SURVEY_V1
            );

            const subscription = await this.findActionSubscriptionOrFail(
              action.id
            );

            const subscriptionActions = await this.findBySubscription(
              subscription
            );

            return { action, subscription, subscriptionActions };
          }

          throw new ActionContextError(
            'complete home survey did not have orderId or actionId'
          );
        },
        process: async (action: ActionEntity) =>
          await this.repository.update(action, {
            status: ActionStatus.SUCCESS,
          }),
      }
    );
  }

  async completeInstallAction({
    orderId,
    ppid,
    postcode,
    mpan,
  }: CompleteInstall): Promise<void> {
    await this.processAction(
      { params: { orderId, ppid }, name: 'completeInstallAction' },
      {
        setup: async () => {
          const subscription = await this.findOrderPendingSubscriptionOrFail(
            orderId
          );

          const subscriptionActions = await this.findBySubscription(
            subscription
          );

          const action = this.findInSubscriptionActionsOrFail(
            subscriptionActions,
            ActionType.INSTALL_CHARGING_STATION_V1,
            ActionStatus.PENDING
          );

          return { action, subscription, subscriptionActions };
        },
        process: async (
          action: ActionEntity,
          subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>,
          actions: ActionEntity[]
        ) => {
          const affordability = this.findInSubscriptionActionsOrFail(
            actions,
            ActionType.CHECK_AFFORDABILITY_V1,
            ActionStatus.SUCCESS
          );

          const { applicationId } = affordability.data as {
            applicationId: number;
          };

          this.logger.log(
            { subscriptionId: subscription.id },
            'releasing application'
          );

          await this.loanManagementSystemService.releaseApplication(
            applicationId
          );

          this.logger.log(
            { subscriptionId: subscription.id },
            'activating subscription'
          );

          await this.subscriptionsService.activateSubscription(subscription.id);

          this.logger.log(
            { subscriptionId: subscription.id },
            'creating delegated control charging station'
          );

          if (this.salesforceService.isSalesforceOrder(subscription.order)) {
            await this.delegatedControlChargingStationClient.createDelegatedControlChargingStation(
              ppid,
              {
                providerName: 'dreev',
                status: 'PENDING',
                // Needed to remove empty strings, ESLint doesn't realise that
                // empty strings are falsy
                // eslint-disable-next-line
                mpan: !!mpan ? mpan : subscription.order.mpan,
                // eslint-disable-next-line
                postcode: !!postcode
                  ? postcode
                  : subscription.order.address.postcode,
              }
            );
          }

          this.logger.log(
            { subscriptionId: subscription.id },
            'linking user to charger'
          );

          await this.usersApi.userControllerLinkCharger(
            subscription.userId,
            ppid
          );

          this.logger.log(
            { subscriptionId: subscription.id },
            'creating rewards wallet'
          );

          await this.rewardWalletsApi.walletControllerUpsertWallet(
            subscription.userId,
            {
              subscriptionId: subscription.id,
              type: 'POD_DRIVE',
            }
          );

          this.verifyActionTypeOrFail(
            affordability,
            ActionType.CHECK_AFFORDABILITY_V1
          );

          await this.repository.update(affordability, {
            data: {
              type: ActionType.CHECK_AFFORDABILITY_V1,
              loanId:
                await this.loanManagementSystemService.getApplicationLoanId(
                  applicationId
                ),
              applicationId: affordability.data.applicationId,
            },
          });

          this.verifyActionTypeOrFail(
            action,
            ActionType.INSTALL_CHARGING_STATION_V1
          );

          await this.repository.update(action, {
            status: ActionStatus.SUCCESS,
            data: {
              ...action.data,
              ppid,
            },
          });
        },
      }
    );
  }

  async completeSignDocumentsAction(applicationId: number): Promise<void> {
    await this.processAction(
      { params: { applicationId }, name: 'completeSignDocumentsAction' },
      {
        setup: async () => {
          const checkAffordabilityAction =
            await this.findFirstActionByTypeAndApplicationIdOrFail(
              ActionType.CHECK_AFFORDABILITY_V1,
              applicationId
            );

          const subscription = await this.findActionSubscriptionOrFail(
            checkAffordabilityAction.id
          );

          const subscriptionActions = await this.findBySubscription(
            subscription
          );

          const action = this.findInSubscriptionActionsOrFail(
            subscriptionActions,
            ActionType.SIGN_DOCUMENTS_V1,
            ActionStatus.PENDING
          );

          return { action, subscription, subscriptionActions };
        },
        process: async (
          action: ActionEntity,
          subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>
        ) => {
          await this.processSignDocumentsAction(
            action,
            subscription,
            applicationId
          );
        },
      }
    );
  }

  async completeSignRewardsTOSAction(params: {
    revision: string;
    actionId: string;
  }): Promise<ActionEntity> {
    return await this.processAction(
      { params, name: 'completeSignRewardsTOSAction' },
      {
        setup: async () => {
          const subscription = await this.findActionSubscriptionOrFail(
            params.actionId
          );
          const subscriptionActions = await this.findBySubscription(
            subscription
          );
          const action = this.findInSubscriptionActionsOrFail(
            subscriptionActions,
            ActionType.SIGN_REWARDS_TOS_V1,
            ActionStatus.PENDING
          );

          return { action, subscription, subscriptionActions };
        },
        process: async (action) =>
          await this.repository.update(action, {
            status: ActionStatus.SUCCESS,
            data: {
              type: ActionType.SIGN_REWARDS_TOS_V1,
              revision: params.revision,
            },
          }),
      }
    );
  }

  async completeLinkExistingChargerAction(
    subscriptionId: string,
    actionId: string
  ): Promise<ActionEntity> {
    return await this.processAction<
      SelfServiceOrderEntity,
      PodDriveRewardsPlanDetails
    >(
      {
        params: { subscriptionId, actionId },
        name: 'completeLinkExistingChargerAction',
      },
      {
        setup: async () => {
          const action = await this.findActionOrFail(
            actionId,
            ActionType.LINK_EXISTING_CHARGER_V1
          );

          const subscription = await this.findActionSubscriptionOrFail<
            SelfServiceOrderEntity,
            PodDriveRewardsPlanDetails
          >(action.id);

          if (subscription.id !== subscriptionId) {
            throw new FatalApplicationError('subscriptionId mismatch');
          }

          const subscriptionActions = await this.findBySubscription(
            subscription
          );

          return { action, subscription, subscriptionActions };
        },
        process: async (
          action: ActionEntity,
          subscription: SubscriptionEntity<
            SelfServiceOrderEntity,
            PodDriveRewardsPlanDetails
          >
        ) => {
          if (
            subscription.order.origin !==
            OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE
          ) {
            throw new FatalApplicationError(
              'only self serviced subscriptions can perform this action'
            );
          }

          await this.repository.update(action, {
            data: {
              type: ActionType.LINK_EXISTING_CHARGER_V1,
              ppid: subscription.order.ppid,
            },
          });

          await this.delegatedControlChargingStationClient.createDelegatedControlChargingStation(
            subscription.order.ppid,
            {
              providerName: 'axle',
              postcode: 'WC1X 8HB',
              mpan: 'todo-once-known',
              // TODO: we will need postcode and mpan, which will come from
              // CONFIRM_CHARGER_ADDRESS_AND_MPAN_V1 but it does not exist yet
            }
          );

          await this.rewardWalletsApi.walletControllerUpsertWallet(
            subscription.userId,
            {
              subscriptionId,
              type: 'POD_DRIVE',
            }
          );

          await this.repository.update(action, {
            status: ActionStatus.SUCCESS,
          });

          await this.subscriptionsService.activateSubscription(subscription.id);

          await this.subscriptionsService.cancelPendingSCRSelfServiceSubscriptions(
            subscription.order.ppid
          );
        },
      }
    );
  }

  async syncSignDocumentsAction(
    subscriptionId: string,
    actionId: string
  ): Promise<ActionEntity> {
    return await this.processAction(
      {
        params: { subscriptionId, actionId },
        name: 'syncSignDocumentsAction',
      },
      {
        setup: async () => {
          const action = await this.findActionOrFail(
            actionId,
            ActionType.SIGN_DOCUMENTS_V1
          );
          const subscription = await this.findActionSubscriptionOrFail(
            action.id
          );

          if (subscriptionId !== subscription.id) {
            throw new FatalApplicationError('subscriptionId mismatch');
          }

          const subscriptionActions = await this.findBySubscription(
            subscription
          );

          return {
            action,
            subscription,
            subscriptionActions,
          };
        },
        process: async (
          action: ActionEntity,
          subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>,
          subscriptionActions: ActionEntity[]
        ) => {
          const affordabilityAction = this.findInSubscriptionActionsOrFail(
            subscriptionActions,
            ActionType.CHECK_AFFORDABILITY_V1,
            ActionStatus.SUCCESS
          );
          const applicationId =
            this.extractActionApplicationIdOrFail(affordabilityAction);

          return await this.processSignDocumentsAction(
            action,
            subscription,
            applicationId
          );
        },
      }
    );
  }

  async processManualAffordabilityApproval(applicationId: number) {
    await this.processAction(
      {
        params: { applicationId },
        name: 'processManualAffordabilityApproval',
      },
      {
        setup: async () => {
          const action = await this.findFirstActionByTypeAndApplicationIdOrFail(
            ActionType.CHECK_AFFORDABILITY_V1,
            applicationId
          );

          const subscription = await this.findActionSubscriptionOrFail(
            action.id
          );

          const subscriptionActions = await this.findBySubscription(
            subscription
          );

          return { action, subscription, subscriptionActions };
        },
        process: async (
          action: ActionEntity,
          subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>,
          actions: ActionEntity[]
        ) => {
          await this.repository.update(action, {
            status: ActionStatus.SUCCESS,
          });

          await this.subscriptionsService.updateSubscriptionStatus(
            action.subscriptionId,
            SubscriptionStatus.PENDING
          );

          if (this.salesforceService.isSalesforceOrder(subscription.order)) {
            await this.salesforceService.setInterventionReason(
              subscription.order,
              'affordability_recovered'
            );
          }

          await this.updateSignDocumentsActionDocuments(actions, applicationId);
        },
      }
    );
  }

  /**
   * Sort actions by importance.
   *
   * Sorting priority
   * - depended on actions first
   * - created at
   *
   * @throws `MissingDataError` if passed actions have a dependency that is not present in the provided data.
   */
  sortActionsByImportance(unsortedActions: ActionEntity[]): ActionEntity[] {
    // Build dependency graph
    const visited = new Set();
    const sorted: ActionEntity[] = [];
    let visitorLimit = unsortedActions.length * 2; // Prevent infinite loops
    const visit = (action: ActionEntity) => {
      if (visitorLimit-- <= 0) {
        throw new FatalApplicationError(
          `Visitor limit exceeded while sorting actions, possible circular dependency detected`
        );
      }
      if (visited.has(action.id)) {
        return;
      }
      if (action.dependsOn) {
        for (const partialDependency of action.dependsOn) {
          const dependency = unsortedActions.find(
            (a) => a.id === partialDependency.id
          );
          if (!dependency) {
            throw new MissingDataError(
              `Missing required action: id:${partialDependency.id}, could not resolve actions order`
            );
          }
          if (dependency.id === action.id) {
            continue; // Skip self-dependency
          }
          visit(dependency);
        }
      }
      visited.add(action.id);
      sorted.push(action);
    };

    // Sort actions by createdAt for stable ordering
    const actionsByCreated = [...unsortedActions].sort(
      (a, b) => (a.createdAt?.getTime() ?? 0) - (b.createdAt?.getTime() ?? 0)
    );

    try {
      for (const action of actionsByCreated) {
        visit(action);
      }
      return sorted;
    } catch (error) {
      this.logger.error(
        { error, unsortedActions },
        'Error while sorting actions by importance'
      );
      return actionsByCreated; // Fallback to original order if error occurs
    }
  }

  private async processSignDocumentsAction(
    action: ActionEntity,
    subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>,
    applicationId: number
  ) {
    this.verifyActionTypeOrFail(action, ActionType.SIGN_DOCUMENTS_V1);

    const slickDocuments =
      await this.loanManagementSystemService.getApplicationDocuments(
        applicationId
      );

    const signedSlickDocumentCodes = new Set<string>();

    slickDocuments.forEach((document) => {
      if (document.type !== 'sign_by_link' || !document.signByLink) {
        return;
      }

      const { tag, status } = document.signByLink;

      if (typeof tag === 'string' && status === 'signed') {
        signedSlickDocumentCodes.add(tag);
      }
    });

    // only update if new documents have been signed
    let updated = false;

    const documents = action.data.documents.map((document) => {
      let { signed } = document;

      if (!signed && signedSlickDocumentCodes.has(document.code)) {
        updated = true;
        signed = true;
      }

      return {
        ...document,
        signed,
      };
    });

    if (!updated) {
      return action;
    }

    if (updated) {
      const allDocumentsSigned = documents.every((doc) => doc.signed);

      if (
        allDocumentsSigned &&
        this.salesforceService.isSalesforceOrder(subscription.order)
      ) {
        await this.salesforceService.setApplicationComplete(subscription.order);
      }

      return await this.repository.update(action, {
        status: allDocumentsSigned
          ? ActionStatus.SUCCESS
          : ActionStatus.PENDING,
        data: {
          ...action.data,
          documents,
        },
      });
    }
  }

  private findInSubscriptionActionsOrFail(
    actions: ActionEntity[],
    type: ActionType,
    status?: ActionStatus
  ): ActionEntity {
    let result = this.filterActionsByType(actions, type);

    if (status) {
      result = this.filterActionsByStatus(result, status);
    }

    if (result.length === 0 || result[0].data.type !== type) {
      throw new CouldNotFindEntityError(
        {
          type,
          status,
          subscriptionId: actions[0]?.subscriptionId,
        },
        `could not find ${status ? ActionStatusDisplayName[status] : ''} ${
          ActionTypeDisplayName[type]
        }`
      );
    }

    this.logger.log(
      {
        result,
        type,
        status,
      },
      'got action to complete'
    );

    return result[0];
  }

  private async findFirstActionByTypeAndApplicationIdOrFail(
    type: ActionType,
    applicationId: number
  ): Promise<ActionEntity> {
    const actions = await this.repository.findByApplicationId(
      type,
      applicationId
    );

    if (actions.length === 0) {
      throw new CouldNotFindEntityError(
        {
          type,
          applicationId,
        },
        'could not find action by application ID'
      );
    }

    return actions[0];
  }

  private filterActionsByType(
    actions: ActionEntity[],
    type: ActionType
  ): ActionEntity[] {
    return actions.filter((action) => action.data.type === type);
  }

  private filterActionsByStatus(
    actions: ActionEntity[],
    status: ActionStatus
  ): ActionEntity[] {
    return actions.filter((action) => action.status === status);
  }

  private resolveDependsOn = (
    action: ActionEntity,
    actions: ActionEntity[]
  ): ActionEntity[] => {
    const dependants: ActionEntity[] = [];

    for (const dependant of action.dependsOn) {
      const dependantAction = actions.find(({ id }) => id === dependant.id);

      if (!dependantAction) {
        // TODO: should this error, it could just silent fail... or lookup the missing action in the repository.
        throw new MissingDataError(
          `Missing required action: id:${dependant.id}, could not resolve actions order`
        );
      }

      if (dependantAction === action) {
        continue;
      }

      dependants.push(dependantAction);
    }

    return dependants;
  };

  private failOnIncompleteDependent(
    action: ActionEntity,
    actions: ActionEntity[]
  ): void {
    const dependants = this.resolveDependsOn(action, actions);

    for (const dependant of dependants) {
      if (dependant.status !== ActionStatus.SUCCESS) {
        throw new IncompleteDependencyError(
          {
            actionId: action.id,
            subscriptionId: action.subscriptionId,
          },
          `action depends on ${dependant.data.type} but ${dependant.data.type} status is ${dependant.status}`
        );
      }
    }
  }

  private async findActionSubscriptionOrFail<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(actionId: string): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>> {
    const subscription = await this.subscriptionsService.getByActionId<
      TOrderEntity,
      TPlanDetails
    >(actionId);

    if (!subscription) {
      throw new CouldNotFindEntityError(
        {
          subscription: { action: { id: actionId } },
        },
        'could not get subscription by action ID'
      );
    }

    return subscription;
  }

  private async findOrderSubscriptionOrFail(
    orderId: string
  ): Promise<SubscriptionEntity<BaseOrderEntity, PlanDetails>> {
    const subscription =
      await this.subscriptionsService.getSubscriptionByOrderId(orderId);

    if (!subscription) {
      throw new CouldNotFindEntityError(
        { order: { id: orderId } },
        'could not get subscription by order ID'
      );
    }

    this.logger.log(
      {
        subscription,
        orderId,
      },
      'got subscription by order ID'
    );

    return subscription;
  }

  private async findOrderPendingSubscriptionOrFail(orderId: string) {
    const subscription =
      await this.subscriptionsService.getPendingSubscriptionByOrderId(orderId);

    if (!subscription) {
      throw new CouldNotFindEntityError(
        {
          order: { id: orderId },
        },
        'could not get pending subscription by order ID'
      );
    }

    this.logger.log(
      {
        subscription,
        orderId,
      },
      'got pending subscription by order ID'
    );

    return subscription;
  }

  private async findActionOrFail(
    actionId: string,
    type: ActionType
  ): Promise<ActionEntity> {
    const action = await this.readById(actionId);

    if (!action) {
      throw new CouldNotFindEntityError(
        { notFoundActionId: actionId },
        'could not get action by action ID'
      );
    }

    this.verifyActionTypeOrFail(action, type);

    return action;
  }

  private verifyActionTypeOrFail<T extends ActionType>(
    action: ActionEntity,
    type: T
  ): asserts action is ActionEntity & {
    data: Extract<ActionData, { type: T }>;
  } {
    if (action.data.type != type) {
      throw new InvalidEntityError(
        { actionId: action.id },
        `action was not ${ActionTypeDisplayName[type]}`
      );
    }
  }

  private extractActionApplicationIdOrFail(action: ActionEntity) {
    const { applicationId } = action.data as { applicationId: number };

    if (!applicationId) {
      throw new CouldNotFindEntityError(
        {
          type: ActionType.CHECK_AFFORDABILITY_V1,
          status: ActionStatus.SUCCESS,
          subscriptionId: action.subscriptionId,
        },
        `could not find completed applicationId of check affordability action`
      );
    }

    return applicationId;
  }

  private async processAction<
    TOrder extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    context: { params?: Record<string, unknown>; name?: string },
    parameters: {
      setup: () => Promise<ActionContext<TOrder, TPlanDetails>>;
      process: (
        action: ActionEntity,
        subscription: SubscriptionEntity<TOrder, TPlanDetails>,
        actions: ActionEntity[]
      ) => Promise<ActionEntity | void>;
    }
  ): Promise<ActionEntity> {
    let actionContext: ActionContext<TOrder, TPlanDetails> = {
      action: null,
      subscription: null,
      subscriptionActions: null,
    };

    try {
      this.logger.log({ context }, 'attempting to process action');

      return await this.transactionProvider.withTransaction(async () => {
        actionContext = await parameters.setup();
        this.verifyContextPopulatedOrFail(actionContext);

        const { action, subscription, subscriptionActions } = actionContext;
        await this.failOnIncompleteDependent(action, subscriptionActions);

        return (
          (await parameters.process(
            action,
            subscription,
            subscriptionActions
          )) ?? action
        );
      });
    } catch (error) {
      this.logger.error(
        {
          error,
          context,
          actionContext,
        },
        'failed to process action'
      );

      const action = await this.handleActionError(error, actionContext);

      if (this.isRethrowableError(error) || action === null) {
        throw error;
      }

      return action;
    }
  }

  private async handleActionError<
    TOrder extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(error: unknown, context: ActionContext<TOrder, TPlanDetails>) {
    return await this.transactionProvider.withTransaction(async () => {
      if (this.isRejectSubscriptionError(error)) {
        if (context.subscription) {
          this.logger.log(
            { subscriptionId: context.subscription.id },
            'Rejecting subscription'
          );

          await this.subscriptionsService.updateSubscriptionStatus(
            context.subscription.id,
            SubscriptionStatus.REJECTED
          );
        }
      }

      if (this.isFailActionError(error)) {
        if (context.action) {
          this.logger.log({ actionId: context.action.id }, 'Failing action');

          return await this.repository.update(context.action, {
            status: ActionStatus.FAILURE,
          });
        }
      }

      return context.action;
    });
  }

  private isFailActionError(error: unknown) {
    return !(error instanceof IncompleteDependencyError);
  }

  private isRejectSubscriptionError(error: unknown) {
    return !(error instanceof IncompleteDependencyError);
  }

  private isRethrowableError(error: unknown) {
    return !(
      error instanceof AffordabilityCheckFailedError ||
      error instanceof DirectDebitCheckFailed
    );
  }

  private verifyContextPopulatedOrFail<
    TOrder extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    context: ActionContext<TOrder, TPlanDetails>
  ): asserts context is ActionContext<TOrder, TPlanDetails> & {
    action: ActionEntity;
    subscription: SubscriptionEntity<BaseOrderEntity, PlanDetails>;
    subscriptionActions: SubscriptionAction[];
  } {
    if (
      context.action == null ||
      context.subscription == null ||
      context.subscriptionActions == null
    ) {
      throw new ActionContextError('Context was unpopulated');
    }
  }
}
