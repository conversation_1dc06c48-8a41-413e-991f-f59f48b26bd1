import {
  ActionDocumentCodeType,
  ActionEntity,
  ActionOwner,
  ActionStatus,
  ActionType,
  SignDocumentsActionData,
} from '../../domain/entities/action.entity';
import { ActionRepositoryInterface } from '../../domain/repositories/action.repository.interface';
import { ActionService } from './action.service';
import { ApplicationDecisionResponse } from '@experience/mobile/slick/client';
import {
  CouldNotFindEntityError,
  CouldNotPersistEntityError,
  InvalidEntityError,
} from '@experience/mobile/clean-architecture';
import { DateTime } from 'luxon';
import { DelegatedControlChargingStationsApi } from '@experience/shared/axios/smart-charging-service-client';
import { FatalApplicationError } from '../errors/fatal-application.error';
import { IncompleteDependencyError } from '../../domain/errors/incomplete-dependency.error';
import { LoanManagementSystemError } from '../../domain/errors/loan-management-system.error';
import { LoanManagementSystemService } from './loan-management-system.service';
import { MOCK_AFFORDABILITY_CHECK } from '../../domain/types/__fixtures__/action.types.fixture';
import {
  MOCK_APPLICATION_ID,
  MOCK_CHECK_AFFORDABILITY_ACTION,
  MOCK_CHECK_AFFORDABILITY_ACTION_SUCCESS,
  MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA,
  MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_FAILED,
  MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS,
  MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
  MOCK_LINK_EXISTING_CHARGER_ACTION,
  MOCK_LINK_EXISTING_CHARGER_ACTION_SUCCESS,
  MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY,
  MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS,
  MOCK_SCR_SUBSCRIPTION_ENTITY,
  MOCK_SETUP_DIRECT_DEBIT_ACTION,
  MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS,
  MOCK_SETUP_DIRECT_DEBIT_ACTION_WITH_DATA,
  MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
  MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS,
  MOCK_SIGN_REWARDS_TOS_ACTION,
  MOCK_SIGN_REWARDS_TOS_ACTION_SUCCESS,
  MOCK_SUBSCRIPTION_ENTITY,
  MOCK_SUBSCRIPTION_ENTITY_ID,
  MOCK_SURVEY_ACTION_ENTITY,
  MOCK_SURVEY_ACTION_ENTITY_SUCCESS,
} from '../../domain/entities/__fixtures__/entity.fixtures';
import { MOCK_SURVEY_ACTION_ID } from '../../interfaces/http/dto/__fixtures__/dto.fixtures';
import { PersistedSubscriptionWithActionsDTO } from '../dto/persisted-subscription-with-actions.dto';
import { SalesforceService } from './salesforce.service';
import {
  SubscriptionEntity,
  SubscriptionStatus,
} from '../../domain/entities/subscription.entity';
import { SubscriptionsService } from './subscriptions.service';

import { MockTransactionProvider } from '@experience/mobile/nest/typeorm-transactions';
import { PodDrivePlanDetails } from '../../domain/entities/plan.entity';
import { RewardWalletsApi } from '@experience/mobile/rewards-api/axios';
import {
  SalesforceOrderEntity,
  SelfServiceOrderEntity,
} from '../../domain/entities/order.entity';
import { UnpersistedActionEntityFactory } from '../../domain/entity-factories/unpersisted-action-entity.factory';
import { UsersApi } from '@experience/driver-account-api/api-client';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { createMock } from '@golevelup/ts-jest';
import { plainToInstance } from 'class-transformer';
import { v4 } from 'uuid';

describe(ActionService.name, () => {
  let mockedActionRepository: jest.Mocked<ActionRepositoryInterface>;
  let mockedSubscriptionsService: jest.Mocked<SubscriptionsService>;
  let mockedSalesforceService: jest.Mocked<SalesforceService>;
  let mockedDelegatedControlChargingStationClient: jest.Mocked<DelegatedControlChargingStationsApi>;
  let mockedLoanManagementSystemService: jest.Mocked<LoanManagementSystemService>;
  let mockedUsersApi: jest.Mocked<UsersApi>;
  let mockedRewardWalletsApi: jest.Mocked<RewardWalletsApi>;
  let service: ActionService;

  const mockedTransactionProvider = new MockTransactionProvider();

  beforeEach(() => {
    mockedActionRepository = createMock();
    mockedSubscriptionsService = createMock();
    mockedSalesforceService = createMock();
    mockedDelegatedControlChargingStationClient = createMock();
    mockedLoanManagementSystemService = createMock();
    mockedUsersApi = createMock();
    mockedRewardWalletsApi = createMock();

    service = new ActionService(
      mockedActionRepository,
      mockedTransactionProvider,
      mockedSubscriptionsService,
      mockedSalesforceService,
      mockedDelegatedControlChargingStationClient,
      mockedLoanManagementSystemService,
      mockedUsersApi,
      mockedRewardWalletsApi
    );

    jest.restoreAllMocks();
  });

  describe(ActionService.prototype.readById, () => {
    it('calls the readById method in the repository with the correct params', async () => {
      const actionId = v4();
      await service.readById(actionId);

      expect(mockedActionRepository.read).toHaveBeenCalledTimes(1);
      expect(mockedActionRepository.read).toHaveBeenCalledWith(actionId);
    });

    it('returns the found subscription from the repository', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SURVEY_ACTION_ENTITY,
      });

      const actual = await service.readById(MOCK_SURVEY_ACTION_ID);

      expect(actual).toEqual(MOCK_SURVEY_ACTION_ENTITY);
    });

    it('returns null from the repository when not found', async () => {
      mockedActionRepository.read.mockResolvedValueOnce(null);

      const actual = await service.readById(v4());

      expect(actual).toBeNull();
    });
  });

  describe(ActionService.prototype.create, () => {
    it('calls the create method in the repository with the correct params', async () => {
      const unpersistedActionEntity =
        UnpersistedActionEntityFactory.createCompleteSurveyAction({
          subscriptionId: v4(),
          surveyUrl: 'http://0.0.0.0/',
        });

      await service.create(unpersistedActionEntity);

      expect(mockedActionRepository.create).toHaveBeenCalledTimes(1);
      expect(mockedActionRepository.create).toHaveBeenCalledWith(
        unpersistedActionEntity
      );
    });

    it('returns the new subscription from the repository', async () => {
      const unpersistedActionEntity =
        UnpersistedActionEntityFactory.createCompleteSurveyAction({
          subscriptionId: v4(),
          surveyUrl: 'http://0.0.0.0/',
        });

      mockedActionRepository.create.mockResolvedValueOnce(
        MOCK_SURVEY_ACTION_ENTITY
      );

      const actual = await service.create(unpersistedActionEntity);

      expect(actual).toEqual(MOCK_SURVEY_ACTION_ENTITY);
    });

    it('throws CouldNotPersistEntityError from repository', async () => {
      const unpersistedActionEntity =
        UnpersistedActionEntityFactory.createCompleteSurveyAction({
          subscriptionId: v4(),
          surveyUrl: 'http://0.0.0.0/',
        });

      const expectedError = new CouldNotPersistEntityError(
        unpersistedActionEntity,
        'this is a test'
      );

      mockedActionRepository.create.mockRejectedValueOnce(expectedError);

      await expect(service.create(unpersistedActionEntity)).rejects.toThrow(
        expectedError
      );
    });
  });

  describe(ActionService.prototype.getAffordabilityApplicationId, () => {
    it('throws a could not find entity exception if check affordability action cannot be found', async () => {
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([]);

      await expect(
        service.getAffordabilityApplicationId(MOCK_SUBSCRIPTION_ENTITY.id)
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('returns the application id from the check affordability action', async () => {
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS },
        { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
      ]);

      expect(
        await service.getAffordabilityApplicationId(MOCK_SUBSCRIPTION_ENTITY.id)
      ).toEqual(MOCK_APPLICATION_ID);
    });
  });

  describe(ActionService.prototype.completeInstallAction, () => {
    it("throws a CouldNotFindEntityError if unable to get the user's pending subscription", async () => {
      mockedSubscriptionsService.getPendingSubscriptionByOrderId.mockResolvedValueOnce(
        null
      );

      const ORDER_ID = v4();

      await expect(
        service.completeInstallAction({
          orderId: ORDER_ID,
          ppid: 'PSL-123456',
          mpan: '132456789',
          postcode: 'SW1A 1AA',
        })
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws a CouldNotFindEntityError if unable to get a PENDING INSTALL_CHARGING_STATION action', async () => {
      mockedSubscriptionsService.getPendingSubscriptionByOrderId.mockResolvedValueOnce(
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS },
        new ActionEntity({
          ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
          status: ActionStatus.SUCCESS,
        }),
      ]);

      await expect(
        service.completeInstallAction({
          orderId: MOCK_SUBSCRIPTION_ENTITY.order.id,
          ppid: 'PSL-123456',
          mpan: '132456789',
          postcode: 'SW1A 1AA',
        })
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws a LoanManagementSystemError and sets the subscription to REJECTED if we fail to release application', async () => {
      mockedSubscriptionsService.getPendingSubscriptionByOrderId.mockResolvedValueOnce(
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS },
        { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.releaseApplication.mockRejectedValueOnce(
        new LoanManagementSystemError('oops')
      );

      await expect(
        service.completeInstallAction({
          orderId: MOCK_SUBSCRIPTION_ENTITY.order.id,
          ppid: 'PSL-123456',
          mpan: '132456789',
          postcode: 'SW1A 1AA',
        })
      ).rejects.toThrow(LoanManagementSystemError);

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(1);
      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
        {
          status: ActionStatus.FAILURE,
        }
      );

      expect(
        mockedSubscriptionsService.updateSubscriptionStatus
      ).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ENTITY.id,
        SubscriptionStatus.REJECTED
      );
    });

    it('throws a LoanManagementSystemError and set the subscription to REJECTED if we fail to update the contract ID', async () => {
      mockedSubscriptionsService.getPendingSubscriptionByOrderId.mockResolvedValueOnce(
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS },
        { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.getApplicationLoanId.mockRejectedValueOnce(
        new LoanManagementSystemError('oops')
      );

      await expect(
        service.completeInstallAction({
          orderId: MOCK_SUBSCRIPTION_ENTITY.order.id,
          ppid: 'PSL-123456',
          mpan: '132456789',
          postcode: 'SW1A 1AA',
        })
      ).rejects.toThrow(LoanManagementSystemError);

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(1);
      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
        {
          status: ActionStatus.FAILURE,
        }
      );

      expect(
        mockedSubscriptionsService.updateSubscriptionStatus
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.updateSubscriptionStatus
      ).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ENTITY.id,
        SubscriptionStatus.REJECTED
      );
    });

    it('throws IncompleteDependencyError if dependency action status is not SUCCESS', async () => {
      mockedSubscriptionsService.getPendingSubscriptionByOrderId.mockResolvedValueOnce(
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
        { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
      ]);

      await expect(
        service.completeInstallAction({
          orderId: MOCK_SUBSCRIPTION_ENTITY.order.id,
          ppid: 'PSL-123456',
          mpan: '132456789',
          postcode: 'SW1A 1AA',
        })
      ).rejects.toThrow(IncompleteDependencyError);
    });

    it('marks the action as COMPLETED with the PPID and activates the subscription', async () => {
      mockedSubscriptionsService.getPendingSubscriptionByOrderId.mockResolvedValueOnce(
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS },
        { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.getApplicationLoanId.mockResolvedValue(
        123456
      );

      await service.completeInstallAction({
        orderId: MOCK_SUBSCRIPTION_ENTITY.order.id,
        ppid: 'PSL-123456',
        mpan: '132456789',
        postcode: 'SW1A 1AA',
      });

      expect(
        mockedLoanManagementSystemService.releaseApplication
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.releaseApplication
      ).toHaveBeenCalledWith(
        (
          MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA.data as {
            applicationId: number;
          }
        ).applicationId
      );

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(2);

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS,
        {
          data: {
            type: ActionType.CHECK_AFFORDABILITY_V1,
            loanId: 123456,
            applicationId: MOCK_APPLICATION_ID,
          },
        }
      );

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
        {
          status: ActionStatus.SUCCESS,
          data: {
            ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY.data,
            ppid: 'PSL-123456',
          },
        }
      );

      expect(
        mockedSubscriptionsService.activateSubscription
      ).toHaveBeenCalledTimes(1);

      expect(
        mockedSubscriptionsService.activateSubscription
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ENTITY.id);

      expect(
        mockedDelegatedControlChargingStationClient.createDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);

      expect(
        mockedDelegatedControlChargingStationClient.createDelegatedControlChargingStation
      ).toHaveBeenCalledWith('PSL-123456', {
        mpan: '132456789',
        postcode: 'SW1A 1AA',
        providerName: 'dreev',
        status: 'PENDING',
      });

      expect(mockedUsersApi.userControllerLinkCharger).toHaveBeenCalledTimes(1);
      expect(mockedUsersApi.userControllerLinkCharger).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ENTITY.userId,
        'PSL-123456'
      );

      expect(
        mockedRewardWalletsApi.walletControllerUpsertWallet
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedRewardWalletsApi.walletControllerUpsertWallet
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ENTITY.userId, {
        subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
        type: MOCK_SUBSCRIPTION_ENTITY.plan.details.type,
      });
    });

    it.each([undefined, null, ''])(
      "uses the subscription's order mpan and postcode if value is $1",
      async (value) => {
        mockedSubscriptionsService.getPendingSubscriptionByOrderId.mockResolvedValueOnce(
          { ...MOCK_SUBSCRIPTION_ENTITY }
        );

        mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
          { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
          { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
          { ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS },
          { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
          { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS },
          { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
        ]);

        mockedLoanManagementSystemService.getApplicationLoanId.mockResolvedValue(
          123456
        );

        await service.completeInstallAction({
          orderId: MOCK_SUBSCRIPTION_ENTITY.order.id,
          ppid: 'PSL-123456',
          mpan: value,
          postcode: value,
        });

        expect(
          mockedDelegatedControlChargingStationClient.createDelegatedControlChargingStation
        ).toHaveBeenCalledWith('PSL-123456', {
          mpan: MOCK_SUBSCRIPTION_ENTITY.order.mpan,
          postcode: MOCK_SUBSCRIPTION_ENTITY.order.address.postcode,
          providerName: 'dreev',
          status: 'PENDING',
        });
      }
    );
  });

  describe(ActionService.prototype.completeSignDocumentsAction, () => {
    it('throws CouldNotFindEntityError if no CHECK_AFFORDABILITY action is found or the applicationId', async () => {
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([]);

      await expect(service.completeSignDocumentsAction(1982)).rejects.toThrow(
        CouldNotFindEntityError
      );
    });

    it('throws CouldNotFindEntityError if no subscription is found for the applicationId', async () => {
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        {
          subscriptionId: 1982,
        } as unknown as ActionEntity,
      ]);
      mockedSubscriptionsService.readById.mockResolvedValueOnce({
        subscription: undefined,
      } as unknown as PersistedSubscriptionWithActionsDTO);

      await expect(service.completeSignDocumentsAction(1982)).rejects.toThrow(
        CouldNotFindEntityError
      );
    });

    it('throws CouldNotFindEntityError if no SIGN_DOCUMENTS action is found for the applicationId', async () => {
      const subscriptionId = v4();
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        {
          subscriptionId,
        } as ActionEntity,
      ]);
      mockedSubscriptionsService.readById.mockResolvedValueOnce({
        subscription: { id: subscriptionId },
      } as unknown as PersistedSubscriptionWithActionsDTO);
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([]);

      await expect(service.completeSignDocumentsAction(1982)).rejects.toThrow(
        CouldNotFindEntityError
      );
    });

    it('throws IncompleteDependencyError if dependency action status is not SUCCESS', async () => {
      const subscriptionId = v4();
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        {
          subscriptionId,
        } as ActionEntity,
      ]);
      mockedSubscriptionsService.readById.mockResolvedValueOnce({
        subscription: { id: subscriptionId },
      } as unknown as PersistedSubscriptionWithActionsDTO);
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      await expect(service.completeSignDocumentsAction(1982)).rejects.toThrow(
        IncompleteDependencyError
      );
    });

    it('leaves action pending if not all documents have been signed', async () => {
      const subscriptionId = v4();

      const order = {
        id: v4(),
      };

      const signDocumentsAction = {
        id: v4(),
        dependsOn: [],
        data: {
          type: ActionType.SIGN_DOCUMENTS_V1,
          documents: [
            {
              signingUrl: 'https://sell.you-soul.here',
              signed: false,
              code: 'ha',
            },
            {
              signingUrl: 'https://sell.you-soul.here',
              signed: false,
              code: 'rca',
            },
          ],
        },
        status: ActionStatus.PENDING,
      } as unknown as ActionEntity;

      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        {
          subscriptionId,
        } as ActionEntity,
      ]);
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        id: subscriptionId,
        order,
      } as SubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>);
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        signDocumentsAction,
      ]);

      mockedLoanManagementSystemService.getApplicationDocuments.mockResolvedValueOnce(
        [
          {
            id: 1,
            createdAt: 123,
            type: 'sign_by_link',
            signByLink: {
              signedAt: 123,
              status: 'signed',
              tag: 'ha',
            },
          },
        ]
      );

      await service.completeSignDocumentsAction(1982);

      expect(
        mockedSalesforceService.setApplicationComplete
      ).not.toHaveBeenCalled();

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        signDocumentsAction,
        {
          data: {
            type: ActionType.SIGN_DOCUMENTS_V1,
            documents: [
              {
                signingUrl: 'https://sell.you-soul.here',
                signed: true,
                code: ActionDocumentCodeType.HA,
              },
              {
                signingUrl: 'https://sell.you-soul.here',
                signed: false,
                code: ActionDocumentCodeType.RCA,
              },
            ],
          },
          status: ActionStatus.PENDING,
        }
      );
    });

    it('marks action complete and calls Salesforce and sets setup complete to true when all docs are signed', async () => {
      const subscriptionId = v4();

      const order = {
        id: v4(),
      };

      const signDocumentsAction = {
        id: v4(),
        dependsOn: [],
        data: {
          type: ActionType.SIGN_DOCUMENTS_V1,
          documents: [
            {
              signingUrl: 'https://sell.you-soul.here',
              signed: false,
              code: 'ha',
            },
          ],
        },
        status: ActionStatus.PENDING,
      } as unknown as ActionEntity;

      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        {
          subscriptionId,
        } as ActionEntity,
      ]);
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        id: subscriptionId,
        order,
      } as SubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>);
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        signDocumentsAction,
      ]);

      mockedLoanManagementSystemService.getApplicationDocuments.mockResolvedValueOnce(
        [
          {
            id: 1,
            createdAt: 123,
            type: 'sign_by_link',
            signByLink: {
              signedAt: 123,
              status: 'signed',
              tag: 'ha',
            },
          },
        ]
      );

      await service.completeSignDocumentsAction(1982);

      expect(
        mockedSalesforceService.setApplicationComplete
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSalesforceService.setApplicationComplete
      ).toHaveBeenCalledWith(order as unknown as SalesforceOrderEntity);

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        signDocumentsAction,
        {
          status: ActionStatus.SUCCESS,
          data: {
            type: ActionType.SIGN_DOCUMENTS_V1,
            documents: [
              {
                signingUrl: 'https://sell.you-soul.here',
                signed: true,
                code: ActionDocumentCodeType.HA,
              },
            ],
          },
        }
      );
    });
  });

  describe(ActionService.prototype.syncSignDocumentsAction, () => {
    it('throws CouldNotFindEntityError if action is not found', async () => {
      mockedActionRepository.read.mockResolvedValueOnce(null);

      await expect(service.syncSignDocumentsAction(v4(), v4())).rejects.toThrow(
        CouldNotFindEntityError
      );
    });

    it('throws CouldNotFindEntityError if subscription is not found', async () => {
      const subscriptionId = v4();
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
        subscriptionId,
      } as unknown as ActionEntity);
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce(null);

      await expect(
        service.syncSignDocumentsAction(subscriptionId, v4())
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws FatalApplicationError if action subscription is missmatch with passed subscriptionId', async () => {
      const subscriptionId = v4();
      const expectedSubscriptionId = v4();
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
        subscriptionId,
      } as unknown as ActionEntity);
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        id: subscriptionId,
      } as unknown as SubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>);

      await expect(
        service.syncSignDocumentsAction(expectedSubscriptionId, v4())
      ).rejects.toThrow(FatalApplicationError);
    });

    it('throws IncompleteDependencyError if dependency action status is not SUCCESS', async () => {
      const subscriptionId = v4();
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
        subscriptionId,
      } as ActionEntity);
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        id: subscriptionId,
      } as unknown as SubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>);
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      await expect(
        service.syncSignDocumentsAction(subscriptionId, v4())
      ).rejects.toThrow(IncompleteDependencyError);
    });

    it('throws CouldNotFindEntityError if affordability action is not found with the applicationId', async () => {
      const subscriptionId = v4();
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
        subscriptionId,
      } as ActionEntity);
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        id: subscriptionId,
      } as unknown as SubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>);
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      await expect(
        service.syncSignDocumentsAction(subscriptionId, v4())
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('leaves action pending if not all documents have been signed', async () => {
      const subscriptionId = v4();

      const order = {
        id: v4(),
      };

      const signDocumentsAction = {
        id: v4(),
        dependsOn: [],
        data: {
          type: ActionType.SIGN_DOCUMENTS_V1,
          documents: [
            {
              signingUrl: 'https://sell.you-soul.here',
              signed: false,
              code: 'ha',
            },
            {
              signingUrl: 'https://sell.you-soul.here',
              signed: false,
              code: 'rca',
            },
          ],
        },
        status: ActionStatus.PENDING,
      } as unknown as ActionEntity;

      mockedActionRepository.read.mockResolvedValueOnce(signDocumentsAction);
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        id: subscriptionId,
        order,
      } as unknown as SubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>);
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        signDocumentsAction,
      ]);

      mockedLoanManagementSystemService.getApplicationDocuments.mockResolvedValueOnce(
        [
          {
            id: 1,
            createdAt: 123,
            type: 'sign_by_link',
            signByLink: {
              signedAt: 123,
              status: 'signed',
              tag: 'ha',
            },
          },
        ]
      );

      const data: SignDocumentsActionData = {
        type: ActionType.SIGN_DOCUMENTS_V1,
        documents: [
          {
            signingUrl: 'https://sell.you-soul.here',
            signed: true,
            code: ActionDocumentCodeType.HA,
          },
          {
            signingUrl: 'https://sell.you-soul.here',
            signed: false,
            code: ActionDocumentCodeType.RCA,
          },
        ],
      };
      mockedActionRepository.update.mockResolvedValueOnce({
        ...signDocumentsAction,
        status: ActionStatus.PENDING,
        data,
      });

      const response = await service.syncSignDocumentsAction(
        subscriptionId,
        v4()
      );

      expect(
        mockedSalesforceService.setApplicationComplete
      ).not.toHaveBeenCalled();

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        signDocumentsAction,
        {
          data,
          status: ActionStatus.PENDING,
        }
      );

      expect(response).toMatchObject({ status: ActionStatus.PENDING, data });
    });

    it('marks action complete and calls Salesforce and sets setup complete to true when all docs are signed', async () => {
      const subscriptionId = v4();

      const order = {
        id: v4(),
      };

      const signDocumentsAction = {
        id: v4(),
        dependsOn: [],
        data: {
          type: ActionType.SIGN_DOCUMENTS_V1,
          documents: [
            {
              signingUrl: 'https://sell.you-soul.here',
              signed: false,
              code: 'ha',
            },
          ],
        },
        status: ActionStatus.PENDING,
      } as unknown as ActionEntity;

      mockedActionRepository.read.mockResolvedValueOnce(signDocumentsAction);
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        id: subscriptionId,
        order,
      } as unknown as SubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>);
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS },
        signDocumentsAction,
      ]);

      mockedLoanManagementSystemService.getApplicationDocuments.mockResolvedValueOnce(
        [
          {
            id: 1,
            createdAt: 123,
            type: 'sign_by_link',
            signByLink: { signedAt: 123, status: 'signed', tag: 'ha' },
          },
        ]
      );

      const data: SignDocumentsActionData = {
        type: ActionType.SIGN_DOCUMENTS_V1,
        documents: [
          {
            signingUrl: 'https://sell.you-soul.here',
            signed: true,
            code: ActionDocumentCodeType.HA,
          },
        ],
      };

      mockedActionRepository.update.mockResolvedValueOnce({
        ...signDocumentsAction,
        status: ActionStatus.SUCCESS,
        data,
      });

      const response = await service.syncSignDocumentsAction(
        subscriptionId,
        signDocumentsAction.id
      );

      expect(
        mockedSalesforceService.setApplicationComplete
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSalesforceService.setApplicationComplete
      ).toHaveBeenCalledWith(order as unknown as SalesforceOrderEntity);

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        signDocumentsAction,
        {
          status: ActionStatus.SUCCESS,
          data,
        }
      );

      expect(response).toMatchObject({ status: ActionStatus.SUCCESS, data });
    });
  });

  describe(ActionService.prototype.completeHomeSurveyAction, () => {
    it("throws a CouldNotFindEntityError if unable to get the user's subscription using order id", async () => {
      mockedSubscriptionsService.getSubscriptionByOrderId.mockResolvedValueOnce(
        null
      );

      const ORDER_ID = v4();

      await expect(
        service.completeHomeSurveyAction({ orderId: ORDER_ID })
      ).rejects.toThrow('could not get subscription by order ID');
    });

    it('throws a CouldNotFindEntityError if unable to get a HOME_SURVEY_COMPLETE action using order id', async () => {
      const MOCK_SUB_NO_ACTION = {
        ...MOCK_SUBSCRIPTION_ENTITY,
        actions: [],
      };

      mockedSubscriptionsService.getSubscriptionByOrderId.mockResolvedValueOnce(
        MOCK_SUB_NO_ACTION
      );

      await expect(
        service.completeHomeSurveyAction({
          orderId: MOCK_SUB_NO_ACTION.order.id,
        })
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws IncompleteDependencyError if dependency action status is not SUCCESS using order id', async () => {
      mockedSubscriptionsService.getSubscriptionByOrderId.mockResolvedValueOnce(
        {
          ...MOCK_SUBSCRIPTION_ENTITY,
        }
      );

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY },
        { ...MOCK_SURVEY_ACTION_ENTITY },
      ]);

      await expect(
        service.completeHomeSurveyAction({
          orderId: MOCK_SUBSCRIPTION_ENTITY.order.id,
        })
      ).rejects.toThrow(IncompleteDependencyError);
    });

    it('throws a CouldNotFindEntityError if unable to get a HOME_SURVEY_COMPLETE action using action id', async () => {
      mockedActionRepository.read.mockResolvedValueOnce(null);

      await expect(
        service.completeHomeSurveyAction({
          actionId: MOCK_SURVEY_ACTION_ENTITY.id,
        })
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it("throws a CouldNotFindEntityError if unable to get the user's subscription using action id", async () => {
      mockedActionRepository.read.mockResolvedValueOnce(
        MOCK_SURVEY_ACTION_ENTITY
      );
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce(null);

      const actionId = v4();

      await expect(
        service.completeHomeSurveyAction({ actionId })
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws IncompleteDependencyError if dependency action status is not SUCCESS using action id', async () => {
      mockedActionRepository.read.mockResolvedValueOnce(
        MOCK_SURVEY_ACTION_ENTITY
      );
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY },
        { ...MOCK_SURVEY_ACTION_ENTITY },
      ]);

      await expect(
        service.completeHomeSurveyAction({
          actionId: MOCK_SURVEY_ACTION_ENTITY.id,
        })
      ).rejects.toThrow(IncompleteDependencyError);
    });

    it.each([
      [ActionStatus.FAILURE, { actionId: MOCK_SURVEY_ACTION_ENTITY.id }],
      [ActionStatus.PENDING, { actionId: MOCK_SURVEY_ACTION_ENTITY.id }],
      [ActionStatus.SUCCESS, { actionId: MOCK_SURVEY_ACTION_ENTITY.id }],
      [ActionStatus.FAILURE, { orderId: MOCK_SUBSCRIPTION_ENTITY.order.id }],
      [ActionStatus.PENDING, { orderId: MOCK_SUBSCRIPTION_ENTITY.order.id }],
      [ActionStatus.SUCCESS, { orderId: MOCK_SUBSCRIPTION_ENTITY.order.id }],
    ])(
      'marks the action as SUCCESS when in given status for request (status = %p, request = %p)',
      async (status, request) => {
        const surveyAction = { ...MOCK_SURVEY_ACTION_ENTITY, status };

        mockedSubscriptionsService.getPendingSubscriptionByOrderId.mockResolvedValueOnce(
          {
            ...MOCK_SUBSCRIPTION_ENTITY,
          }
        );

        mockedActionRepository.read.mockResolvedValueOnce(surveyAction);

        mockedSubscriptionsService.getByActionId.mockResolvedValueOnce(
          MOCK_SUBSCRIPTION_ENTITY
        );

        mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
          { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
          { ...surveyAction },
        ]);

        await service.completeHomeSurveyAction(request);

        expect(mockedActionRepository.update).toHaveBeenCalledTimes(1);
        expect(mockedActionRepository.update).toHaveBeenCalledWith(
          surveyAction,
          {
            status: ActionStatus.SUCCESS,
          }
        );
      }
    );
  });

  describe(
    ActionService.prototype.getAffordabilityActionByApplicationId,
    () => {
      it('finds the first matching affordability action and returns the action', async () => {
        mockedActionRepository.findByApplicationId.mockResolvedValue([
          MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA,
        ]);

        const res = await service.getAffordabilityActionByApplicationId(
          MOCK_APPLICATION_ID
        );

        expect(res).toStrictEqual(MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA);
      });
    }
  );

  describe(
    ActionService.prototype.getLinkChargerActionPpidBySubscriptionId,
    () => {
      it('throws an error if the link charging action can not be found', async () => {
        mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([]);

        await expect(
          service.getLinkChargerActionPpidBySubscriptionId(
            MOCK_SUBSCRIPTION_ENTITY.id
          )
        ).rejects.toThrow(CouldNotFindEntityError);
      });

      it('retrieves the ppid from the link charging action', async () => {
        mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
          MOCK_LINK_EXISTING_CHARGER_ACTION_SUCCESS,
        ]);

        expect(
          await service.getLinkChargerActionPpidBySubscriptionId(
            MOCK_SUBSCRIPTION_ENTITY.id
          )
        ).toBe('PSL-123456');
      });
    }
  );

  describe(ActionService.prototype.completeAffordabilityCheckAction, () => {
    it('throws a CouldNotFindEntityError if the action cannot be found', async () => {
      mockedActionRepository.read.mockResolvedValueOnce(null);

      await expect(
        service.completeAffordabilityCheckAction(v4(), MOCK_AFFORDABILITY_CHECK)
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws a LoanManagementSystemError if the API response returns a non-200 code', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_CHECK_AFFORDABILITY_ACTION,
      });
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
      ]);

      mockedLoanManagementSystemService.createApplication.mockRejectedValueOnce(
        new LoanManagementSystemError('oops')
      );

      await expect(
        service.completeAffordabilityCheckAction(v4(), MOCK_AFFORDABILITY_CHECK)
      ).rejects.toThrow(LoanManagementSystemError);
    });

    it('throws an InvalidEntityError if the action provided was not check affordability', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
      });

      await expect(
        service.completeAffordabilityCheckAction(v4(), MOCK_AFFORDABILITY_CHECK)
      ).rejects.toThrow(InvalidEntityError);
    });

    it('throws IncompleteDependencyError if dependency action status is not SUCCESS', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_CHECK_AFFORDABILITY_ACTION,
      });
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      await expect(
        service.completeAffordabilityCheckAction(
          MOCK_CHECK_AFFORDABILITY_ACTION.id,
          MOCK_AFFORDABILITY_CHECK
        )
      ).rejects.toThrow(IncompleteDependencyError);
    });

    it('sets the subscription to rejected if the affordability check fails', async () => {
      const applicationId = 1;

      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_CHECK_AFFORDABILITY_ACTION,
      });
      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
      ]);

      mockedLoanManagementSystemService.createApplication.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            id: applicationId,
            reference: '123456789',
            uuid: '123456789',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDecision.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            decision: 'decline',
          },
        }
      );

      const transactionSpy = jest.spyOn(
        MockTransactionProvider.prototype,
        'withTransaction'
      );

      await service.completeAffordabilityCheckAction(
        MOCK_CHECK_AFFORDABILITY_ACTION.id,
        MOCK_AFFORDABILITY_CHECK
      );

      expect(
        mockedLoanManagementSystemService.createApplication
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.createApplication
      ).toHaveBeenCalledWith(
        { ...MOCK_AFFORDABILITY_CHECK },
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      expect(
        mockedSubscriptionsService.updateSubscriptionStatus
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.updateSubscriptionStatus
      ).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION.subscriptionId,
        SubscriptionStatus.REJECTED
      );

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(2);

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION,
        {
          data: {
            type: ActionType.CHECK_AFFORDABILITY_V1,
            applicationId,
            loanId: null,
          },
        }
      );

      expect(transactionSpy).toHaveBeenCalledWith(expect.any(Function));

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION,
        {
          status: ActionStatus.FAILURE,
        }
      );
    });

    it('sets the salesforce application id when the application is created', async () => {
      const applicationId = 1;

      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_CHECK_AFFORDABILITY_ACTION,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });

      mockedLoanManagementSystemService.createApplication.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            id: applicationId,
            reference: '123456789',
            uuid: '123456789',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDecision.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            decision: 'accept',
          },
        }
      );

      await service.completeAffordabilityCheckAction(
        MOCK_CHECK_AFFORDABILITY_ACTION.id,
        MOCK_AFFORDABILITY_CHECK
      );

      expect(mockedSalesforceService.setApplicationUrl).toHaveBeenCalledTimes(
        1
      );

      expect(mockedSalesforceService.setApplicationUrl).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ENTITY.order,
        applicationId
      );
    });

    it('sets the status and applicationId appropriately if slick returns accepted', async () => {
      const applicationId = 1;

      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_CHECK_AFFORDABILITY_ACTION,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });

      mockedLoanManagementSystemService.createApplication.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            id: applicationId,
            reference: '123456789',
            uuid: '123456789',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDecision.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            decision: 'accept',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDocumentsToSign.mockResolvedValueOnce(
        [
          {
            signingUrl: 'https://sell.you-soul.here',
            signed: false,
            code: ActionDocumentCodeType.RCA,
          },
          {
            signingUrl: 'https://sell.you-soul.here',
            signed: false,
            code: ActionDocumentCodeType.HA,
          },
        ]
      );

      mockedActionRepository.update.mockResolvedValue(
        MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA
      );

      const result = await service.completeAffordabilityCheckAction(
        MOCK_CHECK_AFFORDABILITY_ACTION.id,
        MOCK_AFFORDABILITY_CHECK
      );

      expect(
        mockedLoanManagementSystemService.createApplication
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.createApplication
      ).toHaveBeenCalledWith(
        { ...MOCK_AFFORDABILITY_CHECK },
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(2);

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
        {
          data: {
            type: ActionType.SIGN_DOCUMENTS_V1,
            documents: [
              {
                signingUrl: 'https://sell.you-soul.here',
                signed: false,
                code: ActionDocumentCodeType.RCA,
              },
              {
                signingUrl: 'https://sell.you-soul.here',
                signed: false,
                code: ActionDocumentCodeType.HA,
              },
            ],
          },
        }
      );

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION,
        {
          status: ActionStatus.SUCCESS,
          data: {
            ...MOCK_CHECK_AFFORDABILITY_ACTION.data,
            applicationId,
            loanId: null,
          },
        }
      );

      expect(result).toEqual(MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA);
    });

    it('sets the Salesforce intervention reason to affordability if Slick check fails', async () => {
      const applicationId = 1;

      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_CHECK_AFFORDABILITY_ACTION,
      });

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.createApplication.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            id: applicationId,
            reference: '123456789',
            uuid: '123456789',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDecision.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            decision: 'decline',
          },
        }
      );

      await service.completeAffordabilityCheckAction(
        MOCK_CHECK_AFFORDABILITY_ACTION.id,
        MOCK_AFFORDABILITY_CHECK
      );

      expect(
        mockedSalesforceService.setInterventionReason
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSalesforceService.setInterventionReason
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ENTITY.order, 'affordability');
    });

    it('sets the status appropriately if slick does not return accepted', async () => {
      const applicationId = 1;

      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_CHECK_AFFORDABILITY_ACTION,
      });

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.createApplication.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            id: applicationId,
            reference: '123456789',
            uuid: '123456789',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDecision.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            decision: 'decline',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDocumentsToSign.mockRejectedValueOnce(
        new LoanManagementSystemError('oops')
      );

      await service.completeAffordabilityCheckAction(
        MOCK_CHECK_AFFORDABILITY_ACTION.id,
        MOCK_AFFORDABILITY_CHECK
      );

      expect(
        mockedLoanManagementSystemService.createApplication
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.createApplication
      ).toHaveBeenCalledWith(
        { ...MOCK_AFFORDABILITY_CHECK },
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(2);

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION,
        {
          data: {
            type: ActionType.CHECK_AFFORDABILITY_V1,
            applicationId,
            loanId: null,
          },
        }
      );

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION,
        {
          status: ActionStatus.FAILURE,
        }
      );
    });

    it('still returns the action if affordability check fails', async () => {
      const applicationId = 1;

      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_CHECK_AFFORDABILITY_ACTION,
      });

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      mockedActionRepository.update.mockResolvedValue(
        MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_FAILED
      );

      mockedLoanManagementSystemService.createApplication.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            id: applicationId,
            reference: '123456789',
            uuid: '123456789',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDecision.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: {
            decision: 'decline',
          },
        }
      );

      mockedLoanManagementSystemService.getApplicationDocumentsToSign.mockRejectedValueOnce(
        new LoanManagementSystemError('oops')
      );

      const res = await service.completeAffordabilityCheckAction(
        MOCK_CHECK_AFFORDABILITY_ACTION.id,
        MOCK_AFFORDABILITY_CHECK
      );

      expect(res).toStrictEqual(
        MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_FAILED
      );

      expect(
        mockedLoanManagementSystemService.createApplication
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.createApplication
      ).toHaveBeenCalledWith(
        { ...MOCK_AFFORDABILITY_CHECK },
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(2);

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION,
        {
          data: {
            type: ActionType.CHECK_AFFORDABILITY_V1,
            applicationId,
            loanId: null,
          },
        }
      );

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION,
        {
          status: ActionStatus.FAILURE,
        }
      );
    });
  });

  describe(ActionService.prototype.completeSetupDirectDebitAction, () => {
    it('throws a CouldNotFindEntityError if the action cannot be found', async () => {
      mockedActionRepository.read.mockResolvedValueOnce(null);

      await expect(
        service.completeSetupDirectDebitAction(v4(), v4(), {
          sortCode: '00-00-00',
          accountNumber: '123456',
          accountName: 'foo',
          requiresMoreThanOneSignatory: true,
          understandsDirectDebitGuarantee: true,
        })
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws a LoanManagementSystemError if the API response returns a non-200 code', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SETUP_DIRECT_DEBIT_ACTION,
      });

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        plainToInstance(ActionEntity, {
          ...MOCK_CHECK_AFFORDABILITY_ACTION,
          status: ActionStatus.SUCCESS,
          data: {
            ...MOCK_CHECK_AFFORDABILITY_ACTION.data,
            applicationId: 12345,
          },
        }),
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.updateApplication.mockRejectedValueOnce(
        new LoanManagementSystemError('oops')
      );

      await expect(
        service.completeSetupDirectDebitAction(v4(), v4(), {
          sortCode: '00-00-00',
          accountNumber: '123456',
          accountName: 'foo',
          requiresMoreThanOneSignatory: true,
          understandsDirectDebitGuarantee: true,
        })
      ).rejects.toThrow(LoanManagementSystemError);
    });

    it('throws a IncompleteDependencyError if no completed CHECK_AFFORDABILITY action is found', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SETUP_DIRECT_DEBIT_ACTION,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
      ]);

      await expect(
        service.completeSetupDirectDebitAction(v4(), v4(), {
          sortCode: '00-00-00',
          accountNumber: '123456',
          accountName: 'foo',
          requiresMoreThanOneSignatory: true,
          understandsDirectDebitGuarantee: true,
        })
      ).rejects.toThrow(IncompleteDependencyError);
    });

    it('sets the status and applicationId appropriately if Slick returns accepted', async () => {
      const applicationId = 1;

      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SETUP_DIRECT_DEBIT_ACTION,
      });

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        plainToInstance(ActionEntity, {
          ...MOCK_CHECK_AFFORDABILITY_ACTION,
          status: ActionStatus.SUCCESS,
          data: {
            ...MOCK_CHECK_AFFORDABILITY_ACTION.data,
            applicationId,
          },
        }),
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.updateApplication.mockResolvedValueOnce(
        {
          code: 200,
          message: 'Success',
          response: null,
        }
      );

      const applicationDecisionResponse = {
        code: 200,
        message: 'Success',
        response: {
          decision: 'accept',
          open_banking_url: null,
        },
      } as ApplicationDecisionResponse;

      mockedLoanManagementSystemService.getApplicationDecision.mockResolvedValueOnce(
        applicationDecisionResponse
      );

      mockedActionRepository.update.mockResolvedValueOnce(
        MOCK_SETUP_DIRECT_DEBIT_ACTION_WITH_DATA
      );

      const result = await service.completeSetupDirectDebitAction(
        MOCK_SUBSCRIPTION_ENTITY_ID,
        '12ce3c19-2e05-4726-9f08-2d6416efc21f',
        {
          sortCode: '00-00-00',
          accountNumber: '123456',
          accountName: 'foo',
          requiresMoreThanOneSignatory: true,
          understandsDirectDebitGuarantee: true,
        }
      );

      expect(result).toEqual(MOCK_SETUP_DIRECT_DEBIT_ACTION_WITH_DATA);

      expect(
        mockedLoanManagementSystemService.updateApplication
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.updateApplication
      ).toHaveBeenCalledWith(
        {
          id: 1,
          account_holder_name: 'foo',
          account_number: '123456',
          sortcode: '00-00-00',
          joint_account: '1',
        },
        { ...MOCK_SUBSCRIPTION_ENTITY }
      );

      expect(
        mockedSubscriptionsService.updateSubscriptionStatus
      ).not.toHaveBeenCalled();
    });

    it('throws IncompleteDependencyError if dependency action status is not SUCCESS', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SETUP_DIRECT_DEBIT_ACTION,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
      ]);

      await expect(
        service.completeSetupDirectDebitAction(
          MOCK_SUBSCRIPTION_ENTITY_ID,
          '12ce3c19-2e05-4726-9f08-2d6416efc21f',
          {
            sortCode: '00-00-00',
            accountNumber: '123456',
            accountName: 'foo',
            requiresMoreThanOneSignatory: true,
            understandsDirectDebitGuarantee: true,
          }
        )
      ).rejects.toThrow(IncompleteDependencyError);
    });

    it('throws an error if slick does not successfully update the application', async () => {
      const applicationId = 1;

      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SETUP_DIRECT_DEBIT_ACTION,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        plainToInstance(ActionEntity, {
          ...MOCK_CHECK_AFFORDABILITY_ACTION,
          status: ActionStatus.SUCCESS,
          data: {
            ...MOCK_CHECK_AFFORDABILITY_ACTION.data,
            applicationId,
          },
        }),
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.updateApplication.mockRejectedValueOnce(
        new LoanManagementSystemError('oops')
      );

      await expect(
        service.completeSetupDirectDebitAction(
          MOCK_SUBSCRIPTION_ENTITY_ID,
          '12ce3c19-2e05-4726-9f08-2d6416efc21f',
          {
            sortCode: '00-00-00',
            accountNumber: '123456',
            accountName: 'foo',
            requiresMoreThanOneSignatory: true,
            understandsDirectDebitGuarantee: true,
          }
        )
      ).rejects.toThrow(LoanManagementSystemError);
    });
  });

  describe(ActionService.prototype.completeSignRewardsTOSAction, () => {
    it('throws a CouldNotFindEntityError if the action cannot be found', async () => {
      mockedActionRepository.read.mockResolvedValueOnce(null);

      await expect(
        service.completeSignRewardsTOSAction({
          actionId: 'e4100aeb-bba7-4110-bc9a-92e3306090c8',
          revision: 'test',
        })
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('sets the status to SUCCESS', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_SIGN_REWARDS_TOS_ACTION,
      });

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        MOCK_SIGN_REWARDS_TOS_ACTION,
      ]);

      mockedActionRepository.update.mockResolvedValueOnce(
        MOCK_SIGN_REWARDS_TOS_ACTION_SUCCESS
      );

      await service.completeSignRewardsTOSAction({
        actionId: MOCK_SIGN_REWARDS_TOS_ACTION.id,
        revision: 'test',
      });

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(1);
      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_SIGN_REWARDS_TOS_ACTION,
        {
          status: ActionStatus.SUCCESS,
          data: {
            type: ActionType.SIGN_REWARDS_TOS_V1,
            revision: 'test',
          },
        }
      );
    });
  });

  describe(ActionService.prototype.completeLinkExistingChargerAction, () => {
    it('throws a CouldNotFindEntityError if the action cannot be found', async () => {
      mockedActionRepository.read.mockResolvedValueOnce(null);

      await expect(
        service.completeLinkExistingChargerAction(
          '6a911f96-c60d-48b6-954b-43cbad17ec47',
          'e4100aeb-bba7-4110-bc9a-92e3306090c8'
        )
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws a FatalApplicationError if the given subscription ID does not match one found', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_LINK_EXISTING_CHARGER_ACTION,
      });

      await expect(
        service.completeLinkExistingChargerAction(
          'random-id',
          'e4100aeb-bba7-4110-bc9a-92e3306090c8'
        )
      ).rejects.toThrow(FatalApplicationError);
    });

    it('assigns the PPID to the action, enrols in DC, creates a wallet and activates the subscription', async () => {
      mockedActionRepository.read.mockResolvedValueOnce({
        ...MOCK_LINK_EXISTING_CHARGER_ACTION,
      });

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce(
        MOCK_SCR_SUBSCRIPTION_ENTITY
      );

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        MOCK_LINK_EXISTING_CHARGER_ACTION,
      ]);

      mockedActionRepository.update.mockResolvedValueOnce(
        MOCK_LINK_EXISTING_CHARGER_ACTION_SUCCESS
      );

      await service.completeLinkExistingChargerAction(
        MOCK_SCR_SUBSCRIPTION_ENTITY.id,
        MOCK_LINK_EXISTING_CHARGER_ACTION.id
      );

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(2);
      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_LINK_EXISTING_CHARGER_ACTION,
        {
          data: {
            type: ActionType.LINK_EXISTING_CHARGER_V1,
            ppid: (MOCK_SCR_SUBSCRIPTION_ENTITY.order as SelfServiceOrderEntity)
              .ppid,
          },
        }
      );

      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_LINK_EXISTING_CHARGER_ACTION,
        {
          status: ActionStatus.SUCCESS,
        }
      );

      expect(
        mockedDelegatedControlChargingStationClient.createDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedDelegatedControlChargingStationClient.createDelegatedControlChargingStation
      ).toHaveBeenCalledWith(MOCK_SCR_SUBSCRIPTION_ENTITY.order.ppid, {
        providerName: 'axle',
        postcode: 'WC1X 8HB',
        mpan: 'todo-once-known',
      });

      expect(
        mockedRewardWalletsApi.walletControllerUpsertWallet
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedRewardWalletsApi.walletControllerUpsertWallet
      ).toHaveBeenCalledWith(MOCK_SCR_SUBSCRIPTION_ENTITY.userId, {
        subscriptionId: MOCK_SCR_SUBSCRIPTION_ENTITY.id,
        type: 'POD_DRIVE',
      });

      expect(
        mockedSubscriptionsService.activateSubscription
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.activateSubscription
      ).toHaveBeenCalledWith(MOCK_SCR_SUBSCRIPTION_ENTITY.id);

      expect(
        mockedSubscriptionsService.cancelPendingSCRSelfServiceSubscriptions
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.cancelPendingSCRSelfServiceSubscriptions
      ).toHaveBeenCalledWith(MOCK_SCR_SUBSCRIPTION_ENTITY.order.ppid);
    });
  });

  describe(ActionService.prototype.processManualAffordabilityApproval, () => {
    it('throws a CouldNotFindEntityError if the check affordability action cannot be found', async () => {
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([]);

      await expect(
        service.processManualAffordabilityApproval(1234)
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws CouldNotFindEntityError if no subscription is found for the applicationId', async () => {
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        MOCK_CHECK_AFFORDABILITY_ACTION,
      ]);

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce(null);

      await expect(
        service.processManualAffordabilityApproval(1234)
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws an IncompleteDependencyError if dependent actions are not in SUCCESS status', async () => {
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        MOCK_CHECK_AFFORDABILITY_ACTION,
      ]);

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
        { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
      ]);

      await expect(
        service.processManualAffordabilityApproval(1234)
      ).rejects.toThrow(IncompleteDependencyError);
    });

    it('sets the salesforce intervention reason to affordability_recovered on success', async () => {
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        MOCK_CHECK_AFFORDABILITY_ACTION,
      ]);

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });
      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
        { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
      ]);

      await service.processManualAffordabilityApproval(1234);

      expect(
        mockedSalesforceService.setInterventionReason
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSalesforceService.setInterventionReason
      ).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ENTITY.order,
        'affordability_recovered'
      );
    });

    it('completes the check affordability action and updates subscription status on success', async () => {
      mockedActionRepository.findByApplicationId.mockResolvedValueOnce([
        MOCK_CHECK_AFFORDABILITY_ACTION,
      ]);

      mockedSubscriptionsService.getByActionId.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
      });

      mockedActionRepository.findBySubscriptionId.mockResolvedValueOnce([
        { ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS },
        { ...MOCK_SURVEY_ACTION_ENTITY_SUCCESS },
        { ...MOCK_CHECK_AFFORDABILITY_ACTION },
        { ...MOCK_SETUP_DIRECT_DEBIT_ACTION },
        { ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY },
        { ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY },
      ]);

      mockedLoanManagementSystemService.getApplicationDocumentsToSign.mockResolvedValueOnce(
        [
          {
            signingUrl: 'https://sell.you-soul.here',
            signed: false,
            code: ActionDocumentCodeType.RCA,
          },
          {
            signingUrl: 'https://sell.you-soul.here',
            signed: false,
            code: ActionDocumentCodeType.HA,
          },
        ]
      );

      await service.processManualAffordabilityApproval(1234);

      expect(mockedActionRepository.update).toHaveBeenCalledTimes(2);
      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_CHECK_AFFORDABILITY_ACTION,
        {
          status: ActionStatus.SUCCESS,
        }
      );
      expect(mockedActionRepository.update).toHaveBeenCalledWith(
        MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
        {
          data: {
            type: ActionType.SIGN_DOCUMENTS_V1,
            documents: [
              {
                signingUrl: 'https://sell.you-soul.here',
                signed: false,
                code: ActionDocumentCodeType.RCA,
              },
              {
                signingUrl: 'https://sell.you-soul.here',
                signed: false,
                code: ActionDocumentCodeType.HA,
              },
            ],
          },
        }
      );

      expect(
        mockedSubscriptionsService.updateSubscriptionStatus
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.updateSubscriptionStatus
      ).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ENTITY.id,
        SubscriptionStatus.PENDING
      );
    });
  });

  describe(ActionService.prototype.sortActionsByImportance, () => {
    it('returns an Array', () => {
      expect(Array.isArray(service.sortActionsByImportance([]))).toBe(true);
    });

    it('returns in order of created at ascending', () => {
      const newest = new ActionEntity({
        id: v4(),
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          surveyUrl: '',
        },
        dependsOn: [],
        subscriptionId: v4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      const oldest = new ActionEntity({
        id: v4(),
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.INSTALL_CHARGING_STATION_V1,
          ppid: null,
        },
        dependsOn: [],
        subscriptionId: v4(),
        createdAt: DateTime.now().minus({ minutes: 5 }).toJSDate(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      expect(service.sortActionsByImportance([newest, oldest])).toEqual([
        oldest,
        newest,
      ]);
    });

    it('returns in order of dependency', () => {
      const parent = new ActionEntity({
        id: v4(),
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          surveyUrl: '',
        },
        dependsOn: [],
        subscriptionId: v4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      const dependant = new ActionEntity({
        id: v4(),
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.INSTALL_CHARGING_STATION_V1,
          ppid: null,
        },
        dependsOn: [parent],
        subscriptionId: v4(),
        createdAt: DateTime.now().minus({ minutes: 5 }).toJSDate(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      expect(service.sortActionsByImportance([dependant, parent])).toEqual([
        parent,
        dependant,
      ]);
    });

    it('returns in order of dependency and createdAt', () => {
      const one = new ActionEntity({
        id: 'one',
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          surveyUrl: '',
        },
        dependsOn: [],
        subscriptionId: v4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      const dependsOnOne = new ActionEntity({
        id: 'dependsOnOne',
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.INSTALL_CHARGING_STATION_V1,
          ppid: null,
        },
        dependsOn: [one],
        subscriptionId: v4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      const oldest = new ActionEntity({
        id: 'oldest',
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          surveyUrl: '',
        },
        dependsOn: [],
        subscriptionId: v4(),
        createdAt: DateTime.now().minus({ minutes: 5 }).toJSDate(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      expect(
        service.sortActionsByImportance([dependsOnOne, oldest, one])
      ).toEqual([oldest, one, dependsOnOne]);
    });

    it('returns deduped', () => {
      const createdAt = new Date();
      const one = new ActionEntity({
        id: 'one',
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.SETUP_DIRECT_DEBIT_V1,
        },
        dependsOn: [],
        subscriptionId: v4(),
        createdAt,
        updatedAt: new Date(),
        deletedAt: null,
      });

      const dependsOnOne = new ActionEntity({
        id: 'dependsOnOne',
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.INSTALL_CHARGING_STATION_V1,
          ppid: null,
        },
        dependsOn: [one],
        subscriptionId: v4(),
        createdAt,
        updatedAt: new Date(),
        deletedAt: null,
      });

      const alsoDependsOnOne = new ActionEntity({
        id: 'alsoDependsOnOne',
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          surveyUrl: '',
        },
        dependsOn: [one],
        subscriptionId: v4(),
        createdAt,
        updatedAt: new Date(),
        deletedAt: null,
      });

      const actual = service.sortActionsByImportance([
        dependsOnOne,
        alsoDependsOnOne,
        one,
      ]);

      expect(actual).toHaveLength(3);
      // this will always be the depended on, in index 0
      expect(actual[0]).toBe(one);
      expect(actual.slice(1)).toEqual(
        expect.arrayContaining([alsoDependsOnOne, dependsOnOne])
      );
    });

    it('gracefully fallsback on self-dependency', () => {
      const onesId = 'onesId';

      const one = new ActionEntity({
        id: onesId,
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          surveyUrl: '',
        },
        dependsOn: [{ id: onesId }],
        subscriptionId: v4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      expect(service.sortActionsByImportance([one])).toEqual([one]);
    });

    it('gracefully fallsback on circular dependency', () => {
      // Dependency order: one -> two -> three -> one -> ...infinity
      const onesId = 'onesId',
        twosId = 'twosId',
        threesId = 'threesId';

      const one = new ActionEntity({
        id: onesId,
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          surveyUrl: '',
        },
        dependsOn: [{ id: twosId }],
        subscriptionId: v4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      const two = new ActionEntity({
        id: twosId,
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.INSTALL_CHARGING_STATION_V1,
          ppid: null,
        },
        dependsOn: [{ id: threesId }],
        subscriptionId: v4(),
        createdAt: DateTime.now().minus({ minutes: 5 }).toJSDate(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      const three = new ActionEntity({
        id: threesId,
        owner: ActionOwner.USER,
        status: ActionStatus.PENDING,
        data: {
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          surveyUrl: '',
        },
        dependsOn: [{ id: onesId }],
        subscriptionId: v4(),
        createdAt: DateTime.now().minus({ minutes: 10 }).toJSDate(),
        updatedAt: new Date(),
        deletedAt: null,
      });
      const answer = service.sortActionsByImportance([two, three, one]);
      // circular dependency should revert to created at (oldest) first
      expect(answer).toEqual([three, two, one]);
    });

    it('successfully sorts when dates conflict with depends on order', () => {
      const unsorted = [
        {
          id: 'b35bf02d-7fbf-454e-8996-20da2786e156',
          type: ActionType.SIGN_DOCUMENTS_V1,
          createdAt: new Date('2025-06-12T11:16:15.681Z'),
          dependsOn: [{ id: '8c2afb38-6102-45b7-a1bc-d1b541f956fa' }],
        },
        {
          id: '8c2afb38-6102-45b7-a1bc-d1b541f956fa',
          type: ActionType.SETUP_DIRECT_DEBIT_V1,
          createdAt: new Date('2025-05-30T15:18:21.179Z'),
          dependsOn: [{ id: '205e04f6-4598-4f73-995e-aa21b7f44f48' }],
        },
        {
          id: 'a1536915-8e80-424e-8eef-402099e927eb',
          type: ActionType.PAY_UPFRONT_FEE_V1,
          createdAt: new Date('2025-05-30T15:18:21.161Z'),
          dependsOn: [],
        },
        {
          id: '9b6e83f6-9f6b-4c5d-8cfa-b700f13c4582',
          type: ActionType.INSTALL_CHARGING_STATION_V1,
          createdAt: new Date('2025-05-30T15:18:21.184Z'),
          dependsOn: [{ id: 'b35bf02d-7fbf-454e-8996-20da2786e156' }],
        },
        {
          id: '272b33b0-0ac8-4abd-9e24-710bc4f0718b',
          type: ActionType.COMPLETE_HOME_SURVEY_V1,
          createdAt: new Date('2025-05-30T15:18:21.167Z'),
          dependsOn: [{ id: 'a1536915-8e80-424e-8eef-402099e927eb' }],
        },
        {
          id: '205e04f6-4598-4f73-995e-aa21b7f44f48',
          type: ActionType.CHECK_AFFORDABILITY_V1,
          createdAt: new Date('2025-05-30T15:18:21.173Z'),
          dependsOn: [{ id: '272b33b0-0ac8-4abd-9e24-710bc4f0718b' }],
        },
      ] as unknown as ActionEntity[];

      const expected = [
        ActionType.PAY_UPFRONT_FEE_V1,
        ActionType.COMPLETE_HOME_SURVEY_V1,
        ActionType.CHECK_AFFORDABILITY_V1,
        ActionType.SETUP_DIRECT_DEBIT_V1,
        ActionType.SIGN_DOCUMENTS_V1,
        ActionType.INSTALL_CHARGING_STATION_V1,
      ];

      const sorted = service
        .sortActionsByImportance(unsorted)
        .map((a) => (a as unknown as { type: string }).type);

      expect(sorted).toEqual(expected);
    });
  });
});
