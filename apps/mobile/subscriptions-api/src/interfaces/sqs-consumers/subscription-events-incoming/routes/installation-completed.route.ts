import { ActionService } from '../../../../application/services/action.service';
import {
  BaseEventRoute,
  SNSRawMessage,
  isSNS,
} from '@experience/mobile/nest/sqs-event-router';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { InstallCompletedEvent } from '../../sqs-consumers.types';

const ASSETS_TEST_ORDER = '00262075';

@Injectable()
export class InstallationCompletedRoute implements BaseEventRoute {
  private readonly logger = new Logger(InstallationCompletedRoute.name);

  constructor(
    private readonly actionService: ActionService,
    private readonly configService: ConfigService
  ) {}

  private isValid(message: SNSRawMessage): message is SNSRawMessage {
    if (message.TopicArn.split(':').pop() !== 'cs-lifecycle-events') {
      return false;
    }

    const payload: InstallCompletedEvent = JSON.parse(message.Message);

    return (
      payload?.type === 'CS.Lifecycle.Installed' &&
      payload?.data.order.type === 'SUBSCRIPTION'
    );
  }

  async handleEvent(message: unknown): Promise<boolean> {
    if (!isSNS(message)) {
      return false;
    }

    if (!this.isValid(message)) {
      return false;
    }

    const { data }: InstallCompletedEvent = JSON.parse(message.Message);

    if (
      this.configService.get('ENVIRONMENT') === 'stage' &&
      data.order.id === ASSETS_TEST_ORDER
    ) {
      this.logger.warn({ data }, 'order is assets test order, not processing');

      return true;
    }

    this.logger.log({ data }, 'processing installation completed event');

    await this.actionService.completeInstallAction({
      orderId: data.order.id,
      ppid: data.chargingStation.id,
      mpan: data.location.mpan,
      postcode: data.address.postcode,
    });

    return true;
  }
}
