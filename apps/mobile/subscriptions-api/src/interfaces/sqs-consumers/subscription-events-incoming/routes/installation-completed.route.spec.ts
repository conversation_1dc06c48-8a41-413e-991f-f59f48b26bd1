import { ActionService } from '../../../../application/services/action.service';
import { ConfigService } from '@nestjs/config';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { InstallCompletedEvent } from '../../sqs-consumers.types';
import { InstallationCompletedRoute } from './installation-completed.route';

describe(InstallationCompletedRoute.name, () => {
  let actionService: DeepMocked<ActionService>;
  let configService: DeepMocked<ConfigService>;
  let route: InstallationCompletedRoute;

  beforeEach(() => {
    actionService = createMock<ActionService>();
    configService = createMock<ConfigService>();

    route = new InstallationCompletedRoute(actionService, configService);
  });

  afterEach(() => jest.resetAllMocks());

  describe(InstallationCompletedRoute.prototype.handleEvent, () => {
    it('returns false if given message is not SNS format', async () => {
      const res = await route.handleEvent({ invalid: true });

      expect(res).toBeFalsy();

      expect(actionService.completeInstallAction).not.toHaveBeenCalled();
    });

    it('returns false if not a valid cs-lifecycle-events event', async () => {
      const res = await route.handleEvent({
        Type: 'Notification',
        TopicArn: 'random-topic',
        Message: '',
        Timestamp: '2025-03-18T00:00:00.000Z',
        Signature: '',
        SigningCertURL: 'https://example.com',
        UnsubscribeURL: 'https://example.com',
      });

      expect(res).toBeFalsy();

      expect(actionService.completeInstallAction).not.toHaveBeenCalled();
    });

    it('returns false if is a cs-lifecycle-events event with invalid type', async () => {
      const res = await route.handleEvent({
        Type: 'Notification',
        TopicArn: 'arn:aws:sns:eu-west-1:123456:cs-lifecycle-events',
        Message: JSON.stringify({
          type: 'Invalid.Type',
          data: {
            order: {
              id: 'abc123',
              type: 'SUBSCRIPTION',
            },
            chargingStation: {
              id: 'PSL-123456',
            },
          },
        }),
        Timestamp: '2025-03-18T00:00:00.000Z',
        Signature: '',
        SigningCertURL: 'https://example.com',
        UnsubscribeURL: 'https://example.com',
      });

      expect(res).toBeFalsy();

      expect(actionService.completeInstallAction).not.toHaveBeenCalled();
    });

    it('returns false if is a cs-lifecycle-events event with non SUBSCRIPTION type', async () => {
      const res = await route.handleEvent({
        Type: 'Notification',
        TopicArn: 'arn:aws:sns:eu-west-1:123456:cs-lifecycle-events',
        Message: JSON.stringify({
          type: 'Invalid.Type',
          data: {
            order: {
              id: 'abc123',
              type: 'something-else',
            },
            chargingStation: {
              id: 'PSL-123456',
            },
          },
        }),
        Timestamp: '2025-03-18T00:00:00.000Z',
        Signature: '',
        SigningCertURL: 'https://example.com',
        UnsubscribeURL: 'https://example.com',
      });

      expect(res).toBeFalsy();

      expect(actionService.completeInstallAction).not.toHaveBeenCalled();
    });

    it('returns true but DOES NOT call the action service if valid assets test order event', async () => {
      configService.get.mockReturnValue('stage');

      const message: InstallCompletedEvent = {
        eventId: '672effe2-143b-4cf3-9af3-f00850ca2ccf',
        publishedAt: '2025-04-14T16:45:00.000Z',
        type: 'CS.Lifecycle.Installed',
        routingKey: 'CS.Lifecycle.Installed.123456',
        data: {
          order: {
            id: '********',
            type: 'SUBSCRIPTION',
          },
          chargingStation: {
            id: 'PSL-123456',
          },
          address: {
            postcode: 'SW1A 1AA',
          },
          location: {
            mpan: 'S 01 801 101 22 6130 5588 165',
          },
        },
        metadata: {},
      };

      const res = await route.handleEvent({
        Type: 'Notification',
        TopicArn: 'arn:aws:sns:eu-west-1:123456:cs-lifecycle-events',
        Message: JSON.stringify(message),
        Timestamp: '2025-03-18T00:00:00.000Z',
        Signature: '',
        SigningCertURL: 'https://example.com',
        UnsubscribeURL: 'https://example.com',
      });

      expect(res).toBeTruthy();

      expect(actionService.completeInstallAction).not.toHaveBeenCalled();

      expect(configService.get).toHaveBeenCalledTimes(1);
      expect(configService.get).toHaveBeenCalledWith('ENVIRONMENT');
    });

    it('returns true and calls the action service if valid event', async () => {
      configService.get.mockReturnValue('stage');

      const message: InstallCompletedEvent = {
        eventId: '672effe2-143b-4cf3-9af3-f00850ca2ccf',
        publishedAt: '2025-04-14T16:45:00.000Z',
        type: 'CS.Lifecycle.Installed',
        routingKey: 'CS.Lifecycle.Installed.123456',
        data: {
          order: {
            id: 'abc123',
            type: 'SUBSCRIPTION',
          },
          chargingStation: {
            id: 'PSL-123456',
          },
          address: {
            postcode: 'SW1A 1AA',
          },
          location: {
            mpan: 'S 01 801 101 22 6130 5588 165',
          },
        },
        metadata: {},
      };

      const res = await route.handleEvent({
        Type: 'Notification',
        TopicArn: 'arn:aws:sns:eu-west-1:123456:cs-lifecycle-events',
        Message: JSON.stringify(message),
        Timestamp: '2025-03-18T00:00:00.000Z',
        Signature: '',
        SigningCertURL: 'https://example.com',
        UnsubscribeURL: 'https://example.com',
      });

      expect(res).toBeTruthy();

      expect(actionService.completeInstallAction).toHaveBeenCalledTimes(1);
      expect(actionService.completeInstallAction).toHaveBeenCalledWith({
        orderId: 'abc123',
        ppid: 'PSL-123456',
        mpan: 'S 01 801 101 22 6130 5588 165',
        postcode: 'SW1A 1AA',
      });

      expect(configService.get).toHaveBeenCalledTimes(1);
      expect(configService.get).toHaveBeenCalledWith('ENVIRONMENT');
    });
  });
});
