import { ActionEntity } from '../entities/action.entity';
import {
  BaseOrderEntity,
  SalesforceOrderEntity,
} from '../entities/order.entity';
import {
  CreateRepositoryInterface,
  DeleteRepositoryInterface,
  ReadRepositoryInterface,
} from '@experience/mobile/clean-architecture';
import { PlanDetails, PodDrivePlanDetails } from '../entities/plan.entity';
import {
  SubscriptionEntity,
  SubscriptionStatus,
} from '../entities/subscription.entity';

export interface SubscriptionRepositoryInterface
  extends ReadRepositoryInterface<
      SubscriptionEntity<BaseOrderEntity, PlanDetails>
    >,
    CreateRepositoryInterface<SubscriptionEntity<BaseOrderEntity, PlanDetails>>,
    DeleteRepositoryInterface<
      SubscriptionEntity<BaseOrderEntity, PlanDetails>
    > {
  getByUserId<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(params: {
    userId: string;
  }): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>[]>;

  getByPpid<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(params: {
    ppid: string;
  }): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>[]>;

  getSubscriptionByOrderId(
    orderId: string
  ): Promise<SubscriptionEntity<
    SalesforceOrderEntity,
    PodDrivePlanDetails
  > | null>;

  getPendingSubscriptionByOrderId(
    orderId: string
  ): Promise<SubscriptionEntity<
    SalesforceOrderEntity,
    PodDrivePlanDetails
  > | null>;

  activateSubscription<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    subscriptionId: string
  ): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>>;

  updateSubscriptionStatus<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    subscriptionId: string,
    status: SubscriptionStatus
  ): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>>;

  updateSubscriptionUserId<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    subscriptionId: string,
    userId: string
  ): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>>;

  getByActionId<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    actionId: ActionEntity['id']
  ): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails> | null>;

  cancelAndDeleteSubscription<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    subscriptionId: SubscriptionEntity<TOrderEntity, TPlanDetails>['id']
  ): Promise<void>;

  cancelPendingSCRSelfServiceSubscriptions(ppid: string): Promise<void>;
}
