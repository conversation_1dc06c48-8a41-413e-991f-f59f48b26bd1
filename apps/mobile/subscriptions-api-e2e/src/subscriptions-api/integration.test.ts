/* eslint @nx/enforce-module-boundaries: 0 */
import {
  ActionDocumentCodeType,
  ActionEntity,
  ActionOwner,
  ActionStatus,
  ActionType,
  UnpersistedActionEntity,
} from '../../../subscriptions-api/src/domain/entities/action.entity';
import { ActionRepositoryInterface } from 'apps/mobile/subscriptions-api/src/domain/repositories/action.repository.interface';
import { ActionService } from '../../../subscriptions-api/src/application/services/action.service';
import { AppModule } from '../../../subscriptions-api/src/app.module';
import { Client } from 'pg';
import { CreateSubscriptionApplicationDTO } from '../../../subscriptions-api/src/application/dto/create-subscription.application.dto';
import { DependencyInjectionToken } from 'apps/mobile/subscriptions-api/src/modules/constants';
import { DriverAccountDbConfigMap } from '@experience/mobile/driver-account/database';
import { HttpResponse, http } from 'msw';
import { INestApplication } from '@nestjs/common';
import {
  OrderOrigin,
  SalesforceOrderEntity,
} from '../../../subscriptions-api/src/domain/entities/order.entity';
import {
  PlanAllowancePeriod,
  PlanType,
  PodDrivePlanDetails,
  UnpersistedPlanEntity,
} from '../../../subscriptions-api/src/domain/entities/plan.entity';
import {
  SubscriptionStatus,
  UnpersistedSubscriptionEntity,
} from '../../../subscriptions-api/src/domain/entities/subscription.entity';
import { SubscriptionsService } from '../../../subscriptions-api/src/application/services/subscriptions.service';
import { bootstrap } from '@experience/shared/nest/utils';
import { createServer } from '@mswjs/http-middleware';
import { handlers as driverAccountApiHandlers } from '@experience/driver-account-api/msw-client';
import { handlers as rewardsApiHandlers } from '@experience/mobile/rewards-api/msw';
import { slickHandlers } from '@experience/mobile/slick/client';
import { handlers as smartChargingServiceHandlers } from '@experience/shared/axios/smart-charging-service-client-msw';
import { v4 } from 'uuid';
import axios from 'axios';
import request from 'supertest';

const PORT = 5120;
const BASE_URL_NEUTRAL = `http://localhost:${PORT}`;

const findByType = (actions: ActionEntity[], type: string) => {
  const action = actions.find((action) => action.data.type === type);

  if (!action) {
    throw new Error(`Action type ${type} not found in ${actions}`);
  }

  return action;
};

const salesforceHandlers = [
  http.post('/services/oauth2/token', () =>
    HttpResponse.json({}, { status: 200 })
  ),

  http.patch(
    '/services/data/v63.0/sobjects/Order/OrderNumber/:orderReference',
    () => HttpResponse.json({}, { status: 200 })
  ),
];

const pendingInstallationActions = [
  'PAY_UPFRONT_FEE_V1',
  'INSTALL_CHARGING_STATION_V1',
];
describe('subscriptions api', () => {
  jest.setTimeout(180_000);

  let db: Client;

  let nestApi: INestApplication;
  let actionService: ActionService;
  let subscriptionService: SubscriptionsService;
  let actionRepository: ActionRepositoryInterface;

  describe('app module', () => {
    beforeAll(async () => {
      createServer(...slickHandlers).listen(7777);
      createServer(...salesforceHandlers).listen(7778);
      createServer(...smartChargingServiceHandlers).listen(7779);
      createServer(...driverAccountApiHandlers).listen(5104);
      createServer(...rewardsApiHandlers).listen(5111);

      nestApi = await bootstrap({
        module: AppModule,
        port: PORT,
        rawBody: true,
      });

      const driverAccountDbConfig: DriverAccountDbConfigMap = JSON.parse(
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        process.env.DRIVER_ACCOUNT_DB_CONFIG!
      );

      db = new Client({
        port: driverAccountDbConfig.migrate.port,
        host: driverAccountDbConfig.migrate.host,
        database: driverAccountDbConfig.migrate.database,
        user: driverAccountDbConfig.migrate.username,
        password: driverAccountDbConfig.migrate.password,
      });

      await db.connect();
      actionService = nestApi.get(ActionService);
      subscriptionService = nestApi.get(SubscriptionsService);
      actionRepository = nestApi.get(
        DependencyInjectionToken.ACTION_REPOSITORY
      );
    });

    afterAll(async () => {
      await nestApi.close();
    });

    beforeEach(async () => {
      // clean slate
      await db.query('DELETE FROM subscriptions.subscriptions;');
    });

    describe('health controller', () => {
      it('should perform a health check', async () => {
        const response = await axios.get(`${BASE_URL_NEUTRAL}/health`);
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });
    });

    describe('/subscriptions', () => {
      describe('POST', () => {
        it('creates a new Pod Drive Rewards subscription', async () => {
          const response = await request(nestApi.getHttpServer())
            .post('/subscriptions')
            .send({
              userId: '470aee3d-d959-4ff5-a00f-9957ff580485',
              planType: 'POD_DRIVE_REWARDS',
              order: {
                ppid: 'PSL-456789',
              },
            });

          expect(response.status).toEqual(201);
          expect(response.body).toStrictEqual({
            id: expect.any(String),
            status: 'PENDING',
            userId: '470aee3d-d959-4ff5-a00f-9957ff580485',
            actions: [
              expect.objectContaining({
                type: 'SIGN_REWARDS_TOS_V1',
                owner: 'USER',
                status: 'PENDING',
                data: {
                  revision: null,
                },
              }),
              expect.objectContaining({
                type: 'LINK_EXISTING_CHARGER_V1',
                owner: 'USER',
                status: 'PENDING',
                data: {
                  ppid: null,
                },
              }),
            ],
            activatedAt: null,
            createdAt: expect.any(String),
            updatedAt: expect.any(String),
            deletedAt: null,
            order: expect.objectContaining({
              origin: 'POD_DRIVE_REWARDS_SELF_SERVE',
              ppid: 'PSL-456789',
            }),
            plan: expect.objectContaining({
              allowanceMiles: 3000,
              allowancePeriod: 'ANNUAL',
              milesRenewalDate: null,
              rateMilesPerKwh: 3.5,
              ratePencePerMile: 2.3,
              type: 'POD_DRIVE_REWARDS',
            }),
          });
        });

        it('does not allow you to create a subscription for a charger which already has an active subscription', async () => {
          const createResponse1 = await request(nestApi.getHttpServer())
            .post('/subscriptions')
            .send({
              userId: '470aee3d-d959-4ff5-a00f-9957ff580485',
              planType: 'POD_DRIVE_REWARDS',
              order: {
                ppid: 'PSL-456789',
              },
            });

          expect(createResponse1.status).toEqual(201);

          const signRewardsActionId = createResponse1.body.actions.find(
            (a: { type: string }) => a.type === 'SIGN_REWARDS_TOS_V1'
          )?.id;

          const signRewardsResponse = await request(nestApi.getHttpServer())
            .patch(
              `/subscriptions/${createResponse1.body.id}/actions/${signRewardsActionId}`
            )
            .send({
              type: 'SIGN_REWARDS_TOS_V1',
              data: {
                revision: 'example',
              },
            });

          expect(signRewardsResponse.status).toEqual(200);

          const linkExistingChargerActionId = createResponse1.body.actions.find(
            (a: { type: string }) => a.type === 'LINK_EXISTING_CHARGER_V1'
          )?.id;

          const linkExistingChargerResponse = await request(
            nestApi.getHttpServer()
          )
            .patch(
              `/subscriptions/${createResponse1.body.id}/actions/${linkExistingChargerActionId}`
            )
            .send({
              type: 'LINK_EXISTING_CHARGER_V1',
            });

          expect(linkExistingChargerResponse.status).toEqual(200);

          const createResponse2 = await request(nestApi.getHttpServer())
            .post('/subscriptions')
            .send({
              userId: '470aee3d-d959-4ff5-a00f-9957ff580485',
              planType: 'POD_DRIVE_REWARDS',
              order: {
                ppid: 'PSL-456789',
              },
            });

          expect(createResponse2.status).toEqual(409);
        });

        it('cancels any pending subscriptions against a PPID once one becomes active', async () => {
          const createResponse1 = await request(nestApi.getHttpServer())
            .post('/subscriptions')
            .send({
              userId: '470aee3d-d959-4ff5-a00f-9957ff580485',
              planType: 'POD_DRIVE_REWARDS',
              order: {
                ppid: 'PSL-456789',
              },
            });

          expect(createResponse1.status).toEqual(201);

          const createResponse2 = await request(nestApi.getHttpServer())
            .post('/subscriptions')
            .send({
              userId: '470aee3d-d959-4ff5-a00f-9957ff580485',
              planType: 'POD_DRIVE_REWARDS',
              order: {
                ppid: 'PSL-456789',
              },
            });

          expect(createResponse2.status).toEqual(201);

          const signRewardsActionId = createResponse1.body.actions.find(
            (a: { type: string }) => a.type === 'SIGN_REWARDS_TOS_V1'
          )?.id;

          const signRewardsResponse = await request(nestApi.getHttpServer())
            .patch(
              `/subscriptions/${createResponse1.body.id}/actions/${signRewardsActionId}`
            )
            .send({
              type: 'SIGN_REWARDS_TOS_V1',
              data: {
                revision: 'example',
              },
            });

          expect(signRewardsResponse.status).toEqual(200);

          const linkExistingChargerActionId = createResponse1.body.actions.find(
            (a: { type: string }) => a.type === 'LINK_EXISTING_CHARGER_V1'
          )?.id;

          const linkExistingChargerResponse = await request(
            nestApi.getHttpServer()
          )
            .patch(
              `/subscriptions/${createResponse1.body.id}/actions/${linkExistingChargerActionId}`
            )
            .send({
              type: 'LINK_EXISTING_CHARGER_V1',
            });

          expect(linkExistingChargerResponse.status).toEqual(200);

          const getResponse1 = await request(nestApi.getHttpServer()).get(
            `/subscriptions/${createResponse1.body.id}`
          );

          expect(getResponse1.status).toEqual(200);
          expect(getResponse1.body.status).toEqual('ACTIVE');

          const getResponse2 = await request(nestApi.getHttpServer()).get(
            `/subscriptions/${createResponse2.body.id}`
          );

          expect(getResponse2.status).toEqual(200);
          expect(getResponse2.body.status).toEqual('CANCELLED');
        });
      });

      describe('/subscriptions/:subscriptionId/actions/:actionId', () => {
        describe('GET', () => {
          it('returns 404 if actionId does not exist', async () => {
            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${v4()}/actions/${v4()}`
            );

            expect(response.status).toEqual(404);
          });

          it('should return 400 if subscriptionId is not a valid UUID', async () => {
            await request(nestApi.getHttpServer())
              .get(`/subscriptions/not-a-valid-id/actions/${v4()}`)
              .expect(400)
              .expect((response) => {
                expect(response.body).toEqual({
                  error: 'Bad Request',
                  message: ['subscriptionId must be a UUID'],
                  statusCode: 400,
                });
              });
          });

          it('should return 400 if actionId is not a valid UUID', async () => {
            await request(nestApi.getHttpServer())
              .get(`/subscriptions/${v4()}/actions/not-a-valid-id`)
              .expect(400)
              .expect((response) => {
                expect(response.body).toEqual({
                  error: 'Bad Request',
                  message: ['actionId must be a UUID'],
                  statusCode: 400,
                });
              });
          });

          it('returns a 404 if the action belongs to a different subscription', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const created = await subscriptionService.createNew(payload);

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/dfe00dd6-f501-49d0-96e6-f0279c0a4e2d/actions/${created.actions[0].id}`
            );

            expect(response.status).toEqual(404);
          });

          it('returns 200 if actionId does exist', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const created = await subscriptionService.createNew(payload);

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${created.subscription.id}/actions/${created.actions[0].id}`
            );

            expect(response.status).toEqual(200);
          });
        });

        describe('PATCH', () => {
          it('returns 400 if the input is bad', async () => {
            const body = {
              data: {
                foo: 'bar',
              },
            };

            const response = await request(nestApi.getHttpServer())
              .patch(`/subscriptions/${v4()}/actions/${v4()}`)
              .send(body);

            expect(response.status).toEqual(400);
          });

          it('returns 404 if the action does not exist', async () => {
            const body = {
              type: 'CHECK_AFFORDABILITY_V1',
              data: {
                title: 'MR',
                firstName: 'john',
                lastName: 'smith',
                email: '<EMAIL>',
                phoneNumber: '+447234567891',
                dateOfBirth: '1986-01-01',
                maritalStatus: 'SINGLE',
                residentialStatus: 'LIVING_WITH_PARENTS',
                employmentStatus: 'FULL_TIME',
                dependencies: '3+',
                billingAddress: {
                  number: '222',
                  street: "Gray's Inn Road",
                  town: 'London',
                  postcode: 'WC1X 8HB',
                },
                monthlyTakeHomePay: 1500,
                monthlyHousePayments: 500,
                monthlyTravelAndLivingExpenses: 100,
                monthlyCreditPayments: 250,
                monthlyHouseholdExpenses: 250,
                circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
              },
            };

            const response = await request(nestApi.getHttpServer())
              .patch(`/subscriptions/${v4()}/actions/${v4()}`)
              .send(body);

            expect(response.status).toEqual(404);
          });

          it('returns 200 and updated action on success', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const {
              actions,
              subscription: { id: subscriptionId },
            } = await subscriptionService.createNew(payload);

            const actionId = findByType(actions, 'CHECK_AFFORDABILITY_V1')?.id;

            const surveyAction = findByType(actions, 'COMPLETE_HOME_SURVEY_V1');

            await actionRepository.update(surveyAction, {
              status: ActionStatus.SUCCESS,
            });

            const updateBody = {
              type: 'CHECK_AFFORDABILITY_V1',
              data: {
                title: 'MR',
                firstName: 'john',
                lastName: 'smith',
                email: '<EMAIL>',
                phoneNumber: '+447234567891',
                dateOfBirth: '1986-01-01',
                maritalStatus: 'SINGLE',
                residentialStatus: 'LIVING_WITH_PARENTS',
                employmentStatus: 'FULL_TIME',
                dependencies: '3+',
                billingAddress: {
                  number: '222',
                  street: "Gray's Inn Road",
                  town: 'London',
                  postcode: 'WC1X 8HB',
                },
                monthlyTakeHomePay: 1500,
                monthlyHousePayments: 500,
                monthlyTravelAndLivingExpenses: 100,
                monthlyCreditPayments: 250,
                monthlyHouseholdExpenses: 250,
                circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
              },
            };

            const response = await request(nestApi.getHttpServer())
              .patch(`/subscriptions/${subscriptionId}/actions/${actionId}`)
              .send(updateBody);

            expect(response.status).toEqual(200);
            expect(response.body).toEqual({
              id: expect.any(String),
              subscriptionId: expect.any(String),
              type: 'CHECK_AFFORDABILITY_V1',
              owner: 'USER',
              status: 'SUCCESS',
              dependsOn: [expect.any(String)],
              data: {
                applicationId: 1,
                loanId: null,
              },
            });
          });

          it('returns 200 and updated action on success of setup direct debit', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const {
              actions,
              subscription: { id: subscriptionId },
            } = await subscriptionService.createNew(payload);

            const checkAffordabilityActionId = findByType(
              actions,
              'CHECK_AFFORDABILITY_V1'
            )?.id;

            const surveyAction = findByType(actions, 'COMPLETE_HOME_SURVEY_V1');

            await actionRepository.update(surveyAction, {
              status: ActionStatus.SUCCESS,
            });

            const checkAffordabilityUpdateBody = {
              type: 'CHECK_AFFORDABILITY_V1',
              data: {
                title: 'MR',
                firstName: 'john',
                lastName: 'smith',
                email: '<EMAIL>',
                phoneNumber: '+447234567891',
                dateOfBirth: '1986-01-01',
                maritalStatus: 'SINGLE',
                residentialStatus: 'LIVING_WITH_PARENTS',
                employmentStatus: 'FULL_TIME',
                dependencies: '3+',
                billingAddress: {
                  number: '222',
                  street: "Gray's Inn Road",
                  town: 'London',
                  postcode: 'WC1X 8HB',
                },
                monthlyTakeHomePay: 1500,
                monthlyHousePayments: 500,
                monthlyTravelAndLivingExpenses: 100,
                monthlyCreditPayments: 250,
                monthlyHouseholdExpenses: 250,
                circumstancesRequireSupport: 'NO_SUPPORT_REQUESTED',
              },
            };

            await request(nestApi.getHttpServer())
              .patch(
                `/subscriptions/${subscriptionId}/actions/${checkAffordabilityActionId}`
              )
              .send(checkAffordabilityUpdateBody);

            const setupDirectDebitActionId = findByType(
              actions,
              'SETUP_DIRECT_DEBIT_V1'
            )?.id;

            const setupDirectDebitUpdateBody = {
              type: 'SETUP_DIRECT_DEBIT_V1',
              data: {
                accountNumber: '********',
                sortCode: '123456',
                accountName: 'barry white',
                requiresMoreThanOneSignatory: true,
                understandsDirectDebitGuarantee: true,
              },
            };

            const response = await request(nestApi.getHttpServer())
              .patch(
                `/subscriptions/${subscriptionId}/actions/${setupDirectDebitActionId}`
              )
              .send(setupDirectDebitUpdateBody);

            expect(response.status).toEqual(200);
            expect(response.body).toEqual({
              id: expect.any(String),
              subscriptionId: expect.any(String),
              type: 'SETUP_DIRECT_DEBIT_V1',
              owner: 'USER',
              status: 'SUCCESS',
              dependsOn: [expect.any(String)],
              data: {},
            });
          });

          it('returns 200 and updated action on success call complete home survey', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const {
              actions,
              subscription: { id: subscriptionId },
            } = await subscriptionService.createNew(payload);

            const surveyAction = findByType(actions, 'COMPLETE_HOME_SURVEY_V1');

            const response = await request(nestApi.getHttpServer())
              .patch(
                `/subscriptions/${subscriptionId}/actions/${surveyAction.id}`
              )
              .send({ type: 'COMPLETE_HOME_SURVEY_V1', data: {} });

            expect(response.body).toEqual({
              id: expect.any(String),
              subscriptionId: expect.any(String),
              type: 'COMPLETE_HOME_SURVEY_V1',
              owner: 'USER',
              status: 'SUCCESS',
              dependsOn: [expect.any(String)],
              data: {
                surveyUrl: expect.any(String),
              },
            });

            expect(response.status).toEqual(200);
          });

          it('returns 200 and updated action on success call sync documents', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const {
              actions,
              subscription: { id: subscriptionId },
            } = await subscriptionService.createNew(payload);

            const signDocumentsAction = findByType(
              actions,
              'SIGN_DOCUMENTS_V1'
            );
            const surveyAction = findByType(actions, 'COMPLETE_HOME_SURVEY_V1');
            const affordabilityAction = findByType(
              actions,
              'CHECK_AFFORDABILITY_V1'
            );
            const setupDirectDebitAction = findByType(
              actions,
              'SETUP_DIRECT_DEBIT_V1'
            );

            await actionRepository.update(signDocumentsAction, {
              data: {
                type: ActionType.SIGN_DOCUMENTS_V1,
                documents: [
                  {
                    code: ActionDocumentCodeType.RCA,
                    signed: true,
                    signingUrl: 'https://example.com/rca',
                  },
                ],
              },
            });
            await actionRepository.update(surveyAction, {
              status: ActionStatus.SUCCESS,
            });
            await actionRepository.update(affordabilityAction, {
              status: ActionStatus.SUCCESS,
              data: {
                type: ActionType.CHECK_AFFORDABILITY_V1,
                applicationId: 1,
                loanId: null,
              },
            });
            await actionRepository.update(setupDirectDebitAction, {
              status: ActionStatus.SUCCESS,
            });

            const response = await request(nestApi.getHttpServer())
              .patch(
                `/subscriptions/${subscriptionId}/actions/${signDocumentsAction.id}`
              )
              .send({ type: 'SIGN_DOCUMENTS_V1', data: {} });

            expect(response.status).toEqual(200);
            expect(response.body).toEqual({
              id: expect.any(String),
              subscriptionId: expect.any(String),
              type: 'SIGN_DOCUMENTS_V1',
              owner: 'USER',
              status: 'PENDING',
              dependsOn: [expect.any(String)],
              data: {
                documents: [expect.any(Object)],
              },
            });
          });
        });
      });

      describe('/subscriptions/:subscriptionId', () => {
        describe('GET', () => {
          it('returns 404 if subscriptionId does not exist', async () => {
            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${v4()}`
            );

            expect(response.status).toEqual(404);
          });

          it('return 400 if id is not a valid uuid', async () => {
            await request(nestApi.getHttpServer())
              .get('/subscriptions/123')
              .expect(400)
              .expect((response) => {
                expect(response.body).toEqual({
                  error: 'Bad Request',
                  message: ['subscriptionId must be a UUID'],
                  statusCode: 400,
                });
              });
          });

          it('returns 200 if subscriptionId does exist', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription, actions } =
              await subscriptionService.createNew(payload);

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${subscription.id}`
            );

            expect(response.status).toEqual(200);

            const payUpfrontFeeAction = findByType(
              actions,
              'PAY_UPFRONT_FEE_V1'
            );
            const completeSurveyAction = findByType(
              actions,
              'COMPLETE_HOME_SURVEY_V1'
            );
            const affordabilityCheckAction = findByType(
              actions,
              'CHECK_AFFORDABILITY_V1'
            );
            const setupDirectDebitAction = findByType(
              actions,
              'SETUP_DIRECT_DEBIT_V1'
            );
            const signLoanAgreementAction = findByType(
              actions,
              'SIGN_DOCUMENTS_V1'
            );

            expect(response.body).toEqual({
              id: subscription.id,
              userId: subscription.userId,
              order: {
                id: subscription.order.id,
                origin: subscription.order.origin,
                orderedAt: expect.any(String),
                address: {
                  line1: "222 Gray's Inn Road",
                  line2: null,
                  line3: null,
                  postcode: 'WC1X 8HB',
                },
                email: '<EMAIL>',
                firstName: 'Mobile',
                lastName: 'Tester',
                mpan: 'S 01 801 101 22 6130 5588 165',
                phoneNumber: '02072474114',
                eCommerceId: 'dr3l2e8dhu::2415',
              },
              status: 'PENDING',
              activatedAt: null,
              createdAt: expect.any(String),
              updatedAt: expect.any(String),
              deletedAt: null,
              plan: {
                id: expect.any(String),
                allowanceMiles: 10000,
                allowancePeriod: 'ANNUAL',
                monthlyFeePounds: 35,
                upfrontFeePounds: 99,
                contractDurationMonths: 18,
                productCode: 'pod_drive_product_01',
                rateMilesPerKwh: 3.5,
                ratePencePerMile: 2.28,
                type: 'POD_DRIVE',
                milesRenewalDate: null,
              },
              actions: [
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'PAY_UPFRONT_FEE_V1',
                  owner: 'USER',
                  status: 'SUCCESS',
                  dependsOn: [],
                  data: {},
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'COMPLETE_HOME_SURVEY_V1',
                  owner: 'USER',
                  status: 'PENDING',
                  dependsOn: [payUpfrontFeeAction?.id],
                  data: {
                    surveyUrl: 'https://pod-point.com',
                  },
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'CHECK_AFFORDABILITY_V1',
                  owner: 'USER',
                  status: 'PENDING',
                  dependsOn: [completeSurveyAction?.id],
                  data: {
                    applicationId: null,
                    loanId: null,
                  },
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'SETUP_DIRECT_DEBIT_V1',
                  owner: 'USER',
                  status: 'PENDING',
                  dependsOn: [affordabilityCheckAction?.id],
                  data: {},
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'SIGN_DOCUMENTS_V1',
                  owner: 'USER',
                  status: 'PENDING',
                  dependsOn: [setupDirectDebitAction?.id],
                  data: {
                    documents: [],
                  },
                },
                {
                  id: expect.any(String),
                  subscriptionId: expect.any(String),
                  type: 'INSTALL_CHARGING_STATION_V1',
                  owner: 'SYSTEM',
                  status: 'PENDING',
                  dependsOn: [signLoanAgreementAction?.id],
                  data: {
                    ppid: null,
                  },
                },
              ],
            });
          });
        });

        describe('DELETE', () => {
          it('returns a 404 if the subscription does not exist', async () => {
            const { statusCode } = await request(
              nestApi.getHttpServer()
            ).delete(
              '/subscriptions/c6678d03-92b9-4838-af04-104eec70ac07?mode=debug'
            );

            expect(statusCode).toEqual(404);
          });

          it('return 400 if id is not a valid uuid', async () => {
            await request(nestApi.getHttpServer())
              .delete('/subscriptions/123')
              .expect(400)
              .expect((response) => {
                expect(response.body).toEqual({
                  error: 'Bad Request',
                  message: ['subscriptionId must be a UUID'],
                  statusCode: 400,
                });
              });
          });

          it("sets the subscription's status to cancelled, deleting it and associated actions and plan", async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription } = await subscriptionService.createNew(
              payload
            );

            expect(subscription.status).not.toEqual('CANCELLED');

            const COUNT_ACTIONS =
              'SELECT COUNT(*) FROM subscriptions.actions WHERE subscription_id = $1 AND deleted_at IS NULL';
            const COUNT_PLANS =
              'SELECT COUNT(*) FROM subscriptions.plans WHERE subscription_id = $1 AND deleted_at IS NULL';
            const GET_SUBSCRIPTION =
              'SELECT * FROM subscriptions.subscriptions WHERE id = $1';

            const actionsBefore = await db.query(COUNT_ACTIONS, [
              subscription.id,
            ]);

            expect(actionsBefore.rows[0].count).toEqual('6');

            const planBefore = await db.query(COUNT_PLANS, [subscription.id]);

            expect(planBefore.rows[0].count).toEqual('1');

            const { statusCode } = await request(
              nestApi.getHttpServer()
            ).delete(`/subscriptions/${subscription.id}?mode=debug`);

            expect(statusCode).toEqual(200);

            const subscriptionAfter = await db.query(GET_SUBSCRIPTION, [
              subscription.id,
            ]);

            expect(subscriptionAfter.rows[0].status).toEqual('CANCELLED');

            const actionsAfter = await db.query(COUNT_ACTIONS, [
              subscription.id,
            ]);

            expect(actionsAfter.rows[0].count).toEqual('0');

            const planAfter = await db.query(COUNT_PLANS, [subscription.id]);

            expect(planAfter.rows[0].count).toEqual('0');
          });
        });
      });

      describe('/subscriptions/:subscriptionId/direct-debit', () => {
        describe('GET', () => {
          it('returns 404 if subscription not found', async () => {
            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${v4()}/direct-debit`
            );

            expect(response.status).toEqual(404);
          });

          it('retrieves direct debit details', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription, actions } =
              await subscriptionService.createNew(payload);

            const checkAffordabilityAction = findByType(
              actions,
              'CHECK_AFFORDABILITY_V1'
            );

            await actionRepository.update(checkAffordabilityAction, {
              status: ActionStatus.SUCCESS,
              data: {
                type: ActionType.CHECK_AFFORDABILITY_V1,
                applicationId: 123,
                loanId: null,
              },
            });

            await subscriptionService.activateSubscription(subscription.id);

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${subscription.id}/direct-debit`
            );

            expect(response.status).toEqual(200);
            expect(response.body).toEqual({
              accountNumberLastDigits: '4567',
              monthlyPaymentDay: 1,
              nameOnAccount: 'Mr John Smith',
              sortCodeLastDigits: '80',
            });
          });
        });
      });

      describe('/subscriptions/:subscriptionId/documents', () => {
        describe('GET', () => {
          it('returns 404 if subscription not found', async () => {
            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${v4()}/documents`
            );

            expect(response.status).toEqual(404);
          });

          it('retrieves documents', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription, actions } =
              await subscriptionService.createNew(payload);

            const checkAffordabilityAction = findByType(
              actions,
              'CHECK_AFFORDABILITY_V1'
            );

            await actionRepository.update(checkAffordabilityAction, {
              status: ActionStatus.SUCCESS,
              data: {
                type: ActionType.CHECK_AFFORDABILITY_V1,
                applicationId: 123,
                loanId: null,
              },
            });

            await subscriptionService.activateSubscription(subscription.id);

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${subscription.id}/documents`
            );

            expect(response.status).toEqual(200);
            expect(response.body).toEqual({
              documents: [
                {
                  active: true,
                  format: 'PDF',
                  issued: expect.any(String),
                  link: `/subscriptions/${subscription.id}/documents/LMS-15`,
                  type: 'ha',
                },
              ],
            });
          });
        });
      });

      describe('/subscriptions/:subscriptionId/documents/:documentId', () => {
        describe('GET', () => {
          it('returns 404 if subscription not found', async () => {
            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${v4()}/documents/LMS-15`
            );

            expect(response.status).toEqual(404);
          });

          it('returns 404 if subscription found but unknown document', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription, actions } =
              await subscriptionService.createNew(payload);

            const checkAffordabilityAction = findByType(
              actions,
              'CHECK_AFFORDABILITY_V1'
            );

            await actionRepository.update(checkAffordabilityAction, {
              status: ActionStatus.SUCCESS,
              data: {
                type: ActionType.CHECK_AFFORDABILITY_V1,
                applicationId: 123,
                loanId: null,
              },
            });

            await subscriptionService.activateSubscription(subscription.id);

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${subscription.id}/documents/LMS-9999999`
            );

            expect(response.status).toEqual(404);
          });

          it('downloads document', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription, actions } =
              await subscriptionService.createNew(payload);

            const checkAffordabilityAction = findByType(
              actions,
              'CHECK_AFFORDABILITY_V1'
            );

            await actionRepository.update(checkAffordabilityAction, {
              status: ActionStatus.SUCCESS,
              data: {
                type: ActionType.CHECK_AFFORDABILITY_V1,
                applicationId: 123,
                loanId: null,
              },
            });

            await subscriptionService.activateSubscription(subscription.id);

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions/${subscription.id}/documents/LMS-15`
            );

            expect(response.status).toEqual(200);
            expect(response.headers['content-type']).toEqual('application/pdf');
          });
        });
      });

      describe('/subscriptions/?userId', () => {
        describe('GET', () => {
          it('returns empty array if userId does not exist', async () => {
            const response = await request(nestApi.getHttpServer()).get(
              '/subscriptions?userId=fake-news'
            );

            expect(response.body).toEqual({
              subscriptions: [],
            });
          });

          it('returns 200 if userId does exist', async () => {
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription } = await subscriptionService.createNew(
              payload
            );

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions?userId=${subscription.userId}`
            );
            expect(response.status).toEqual(200);
            expect(response.body).toEqual({
              subscriptions: [
                {
                  id: subscription.id,
                  userId: subscription.userId,
                  order: {
                    ...subscription.order,
                    orderedAt: subscription.order.orderedAt.toISOString(),
                  },
                  status: subscription.status,
                  activatedAt: null,
                  createdAt: expect.any(String),
                  updatedAt: expect.any(String),
                  deletedAt: null,
                  plan: {
                    id: expect.any(String),
                    allowanceMiles: 10000,
                    allowancePeriod: 'ANNUAL',
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    productCode: 'pod_drive_product_01',
                    rateMilesPerKwh: 3.5,
                    ratePencePerMile: 2.28,
                    type: 'POD_DRIVE',
                    milesRenewalDate: null,
                  },
                  actions: [
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'PAY_UPFRONT_FEE_V1',
                      owner: 'USER',
                      status: 'SUCCESS',
                      dependsOn: [],
                      data: {},
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'COMPLETE_HOME_SURVEY_V1',
                      owner: 'USER',
                      status: 'PENDING',
                      dependsOn: [expect.any(String)],
                      data: {
                        surveyUrl: 'https://pod-point.com',
                      },
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'CHECK_AFFORDABILITY_V1',
                      owner: 'USER',
                      status: 'PENDING',
                      dependsOn: [expect.any(String)],
                      data: {
                        applicationId: null,
                        loanId: null,
                      },
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'SETUP_DIRECT_DEBIT_V1',
                      owner: 'USER',
                      status: 'PENDING',
                      dependsOn: [expect.any(String)],
                      data: {},
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'SIGN_DOCUMENTS_V1',
                      owner: 'USER',
                      status: 'PENDING',
                      dependsOn: [expect.any(String)],
                      data: {
                        documents: [],
                      },
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'INSTALL_CHARGING_STATION_V1',
                      owner: 'SYSTEM',
                      status: 'PENDING',
                      dependsOn: [expect.any(String)],
                      data: {
                        ppid: null,
                      },
                    },
                  ],
                },
              ],
            });
          });
        });
      });

      describe('/subscriptions/?ppid', () => {
        describe('GET', () => {
          it('returns empty array if ppid does not exist', async () => {
            const response = await request(nestApi.getHttpServer()).get(
              '/subscriptions?userId=fake-news'
            );

            expect(response.body).toEqual({
              subscriptions: [],
            });
          });

          it('returns the subscriptions behind a ppid', async () => {
            const ppid = 'PSL-123457';

            // create subscription
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'a873e8ae-789a-495d-a340-26096ca2b87f',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription, actions } =
              await subscriptionService.createNew(payload);

            for (const action of actions) {
              if (!pendingInstallationActions.includes(action.data.type)) {
                await actionRepository.update(action, {
                  status: ActionStatus.SUCCESS,
                });
              }
            }

            await actionService.completeInstallAction({
              orderId: subscription.order.id,
              ppid,
              mpan: (subscription.order as SalesforceOrderEntity).mpan,
              postcode: (subscription.order as SalesforceOrderEntity).address
                .postcode,
            });

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions?ppid=${ppid}`
            );

            expect(response.status).toEqual(200);
            expect(response.body).toEqual({
              subscriptions: [
                {
                  id: expect.any(String),
                  userId: subscription.userId,
                  order: {
                    id: subscription.order.id,
                    origin: subscription.order.origin,
                    orderedAt: expect.any(String),
                    address: {
                      line1: "222 Gray's Inn Road",
                      line2: null,
                      line3: null,
                      postcode: 'WC1X 8HB',
                    },
                    email: '<EMAIL>',
                    firstName: 'Mobile',
                    lastName: 'Tester',
                    mpan: 'S 01 801 101 22 6130 5588 165',
                    phoneNumber: '02072474114',
                    eCommerceId: 'dr3l2e8dhu::2415',
                  },
                  status: 'ACTIVE',
                  activatedAt: expect.any(String),
                  createdAt: expect.any(String),
                  updatedAt: expect.any(String),
                  deletedAt: null,
                  plan: {
                    id: expect.any(String),
                    allowanceMiles: 10000,
                    allowancePeriod: 'ANNUAL',
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    productCode: 'pod_drive_product_01',
                    rateMilesPerKwh: 3.5,
                    ratePencePerMile: 2.28,
                    type: 'POD_DRIVE',
                    milesRenewalDate: expect.any(String),
                  },
                  actions: [
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'PAY_UPFRONT_FEE_V1',
                      owner: 'USER',
                      status: 'SUCCESS',
                      dependsOn: [],
                      data: {},
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'COMPLETE_HOME_SURVEY_V1',
                      owner: 'USER',
                      status: 'SUCCESS',
                      dependsOn: [expect.any(String)],
                      data: {
                        surveyUrl: 'https://pod-point.com',
                      },
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'CHECK_AFFORDABILITY_V1',
                      owner: 'USER',
                      status: 'SUCCESS',
                      dependsOn: [expect.any(String)],
                      data: {
                        applicationId: null,
                        loanId: ********90,
                      },
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'SETUP_DIRECT_DEBIT_V1',
                      owner: 'USER',
                      status: 'SUCCESS',
                      dependsOn: [expect.any(String)],
                      data: {},
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'SIGN_DOCUMENTS_V1',
                      owner: 'USER',
                      status: 'SUCCESS',
                      dependsOn: [expect.any(String)],
                      data: {
                        documents: [],
                      },
                    },
                    {
                      id: expect.any(String),
                      subscriptionId: expect.any(String),
                      type: 'INSTALL_CHARGING_STATION_V1',
                      owner: 'SYSTEM',
                      status: 'SUCCESS',
                      dependsOn: [expect.any(String)],
                      data: {
                        ppid: 'PSL-123457',
                      },
                    },
                  ],
                },
              ],
            });
          });

          it('returns the actions in the correct order', async () => {
            // create subscription
            const payload = new CreateSubscriptionApplicationDTO({
              protoSubscription: new UnpersistedSubscriptionEntity<
                SalesforceOrderEntity,
                PodDrivePlanDetails
              >({
                status: SubscriptionStatus.PENDING,
                activatedAt: null,
                userId: 'userId',
                order: {
                  id: 'id',
                  origin: OrderOrigin.SALESFORCE,
                  orderedAt: new Date(),
                  address: {
                    line1: "222 Gray's Inn Road",
                    line2: null,
                    line3: null,
                    postcode: 'WC1X 8HB',
                  },
                  email: '<EMAIL>',
                  firstName: 'Mobile',
                  lastName: 'Tester',
                  mpan: 'S 01 801 101 22 6130 5588 165',
                  phoneNumber: '02072474114',
                  eCommerceId: 'dr3l2e8dhu::2415',
                },
                plan: new UnpersistedPlanEntity({
                  details: {
                    productCode: 'pod_drive_product_01',
                    allowanceMiles: 10_000,
                    allowancePeriod: PlanAllowancePeriod.ANNUAL,
                    monthlyFeePounds: 35,
                    upfrontFeePounds: 99,
                    contractDurationMonths: 18,
                    ratePencePerMile: 2.28,
                    rateMilesPerKwh: 3.5,
                    type: PlanType.POD_DRIVE,
                    milesRenewalDate: null,
                  },
                }),
              }),
              homeSurveyUrl: 'https://pod-point.com',
            });

            const { subscription } = await subscriptionService.createNew(
              payload
            );

            const action3 = await actionService.create(
              new UnpersistedActionEntity({
                owner: ActionOwner.SYSTEM,
                status: ActionStatus.PENDING,
                subscriptionId: subscription.id,
                data: {
                  type: ActionType.COMPLETE_HOME_SURVEY_V1,
                  surveyUrl: 'https://pod-point.com',
                },
                dependsOn: [],
              })
            );

            const action4 = await actionService.create(
              new UnpersistedActionEntity({
                owner: ActionOwner.SYSTEM,
                status: ActionStatus.PENDING,
                subscriptionId: subscription.id,
                data: {
                  type: ActionType.COMPLETE_HOME_SURVEY_V1,
                  surveyUrl: '',
                },
                dependsOn: [action3],
              })
            );

            const response = await request(nestApi.getHttpServer()).get(
              `/subscriptions?userId=${subscription.userId}`
            );

            expect(response.status).toEqual(200);
            expect(response.body.subscriptions[0].actions).toEqual([
              {
                id: expect.any(String),
                subscriptionId: expect.any(String),
                type: 'PAY_UPFRONT_FEE_V1',
                owner: 'USER',
                status: 'SUCCESS',
                dependsOn: [],
                data: {},
              },
              {
                id: expect.any(String),
                subscriptionId: expect.any(String),
                type: 'COMPLETE_HOME_SURVEY_V1',
                owner: 'USER',
                status: 'PENDING',
                dependsOn: [expect.any(String)],
                data: {
                  surveyUrl: 'https://pod-point.com',
                },
              },
              {
                id: expect.any(String),
                subscriptionId: expect.any(String),
                type: 'CHECK_AFFORDABILITY_V1',
                owner: 'USER',
                status: 'PENDING',
                dependsOn: [expect.any(String)],
                data: {
                  applicationId: null,
                  loanId: null,
                },
              },
              {
                id: expect.any(String),
                subscriptionId: expect.any(String),
                type: 'SETUP_DIRECT_DEBIT_V1',
                owner: 'USER',
                status: 'PENDING',
                dependsOn: [expect.any(String)],
                data: {},
              },
              {
                id: expect.any(String),
                subscriptionId: expect.any(String),
                type: 'SIGN_DOCUMENTS_V1',
                owner: 'USER',
                status: 'PENDING',
                dependsOn: [expect.any(String)],
                data: {
                  documents: [],
                },
              },
              {
                id: expect.any(String),
                subscriptionId: expect.any(String),
                type: 'INSTALL_CHARGING_STATION_V1',
                owner: 'SYSTEM',
                status: 'PENDING',
                dependsOn: [expect.any(String)],
                data: {
                  ppid: null,
                },
              },
              {
                id: action3.id,
                subscriptionId: action3.subscriptionId,
                type: action3.data.type,
                owner: action3.owner,
                status: action3.status,
                dependsOn: [],
                data: {
                  // @ts-expect-error - this is here
                  surveyUrl: action3.data?.surveyUrl,
                },
              },
              {
                id: action4.id,
                subscriptionId: action4.subscriptionId,
                type: action4.data.type,
                owner: action4.owner,
                status: action4.status,
                dependsOn: [action3.id],
                data: {
                  // @ts-expect-error - this is here
                  surveyUrl: action4.data?.surveyUrl,
                },
              },
            ]);
          });
        });
      });
    });
  });
});
