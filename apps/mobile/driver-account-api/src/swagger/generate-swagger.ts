import { API_VERSION } from '../constants';
import { AppModule } from '../app/app.module';
import { VersioningType } from '@nestjs/common';
import { generateSwaggerDocument } from '@experience/shared/nest/dev-utils';
import { getSwaggerDocs } from './swagger';

void generateSwaggerDocument(
  AppModule,
  getSwaggerDocs,
  './libs/mobile/driver-account/api/contract',
  {
    operationIdFactory: (a, b) => `${a}_${b}`,
  },
  {
    defaultVersion: API_VERSION,
    type: VersioningType.URI,
  }
);
