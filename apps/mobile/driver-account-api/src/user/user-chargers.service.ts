import {
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { Op } from 'sequelize';
import {
  PodAddresses,
  PodLocations,
  PodUnitUser,
  PodUnits,
  Users,
} from '@experience/shared/sequelize/podadmin';
import { SmartChargingService } from '../smart-charging-service/smart-charging.service';
import { UserChargerDto } from '@experience/mobile/driver-account/domain/user';
import { XdpService } from '../xdp/xdp.service';

@Injectable()
export class UserChargersService {
  private readonly logger = new Logger(UserChargersService.name);

  constructor(
    @Inject('USERS_REPOSITORY')
    private readonly usersRepository: typeof Users,
    @Inject('POD_UNIT_USER_REPOSITORY')
    private readonly podUnitUserRepository: typeof PodUnitUser,
    @Inject('POD_UNIT_REPOSITORY')
    private readonly podUnitRepository: typeof PodUnits,
    private readonly xdpService: XdpService,
    private readonly smartChargingService: SmartChargingService
  ) {}

  private async getOtherLinkedUsers(
    ppid: string,
    authId: string
  ): Promise<string[]> {
    const otherLinkedUsers = await this.podUnitRepository.findOne({
      nest: true,
      where: { ppid },
      include: [
        {
          model: PodUnitUser,
          as: 'podUnitUsers',
          include: [
            {
              model: Users,
              as: 'user',
              attributes: ['authId'],
              where: {
                authId: {
                  [Op.ne]: authId,
                },
              },
            },
          ],
        },
      ],
    });

    return otherLinkedUsers.podUnitUsers.map((u) => u.user.authId);
  }

  async getChargersForUser(
    authId: string,
    includeOtherLinkedUsers?: boolean
  ): Promise<UserChargerDto[]> {
    try {
      const users = await this.usersRepository.findAll({
        nest: true,
        where: { authId },
        attributes: ['id'],
        include: [
          {
            model: PodUnitUser,
            as: 'podUnitUsers',
            attributes: ['unit_id', 'createdAt'],
            include: [
              {
                model: PodUnits,
                as: 'unit',
                attributes: ['id', 'ppid'],
                include: [
                  {
                    model: PodLocations,
                    as: 'podLocation',
                    attributes: ['timezone'],
                  },
                ],
              },
            ],
          },
        ],
      });

      if (!users) {
        throw new NotFoundException();
      }

      if (users.length === 0) {
        return [];
      }

      return await Promise.all(
        users[0].podUnitUsers
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
          .map(async (podUnitUser) => {
            const data: UserChargerDto = {
              ppid: podUnitUser?.unit?.ppid ?? '0',
              unitId: podUnitUser?.unit?.id ?? 0,
              timezone: podUnitUser?.unit?.podLocation?.timezone ?? 'Etc/UTC',
              linkedAt: podUnitUser?.createdAt.toISOString(),
            };

            if (includeOtherLinkedUsers) {
              try {
                data.otherLinkedUsers = await this.getOtherLinkedUsers(
                  data.ppid,
                  authId
                );
              } catch (error) {
                this.logger.error({ ppid: data.ppid, error });
              }
            }

            return data;
          })
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error({ error, authId }, 'Failed to get chargers for user');

      return [];
    }
  }

  //TODO: calm logging once validated
  async linkChargerToUser(uid: string, ppid: string): Promise<void> {
    this.logger.log({ ppid, uid }, 'linking charger to user');

    const users = await this.usersRepository.findAll({
      nest: true,
      where: {
        authId: uid,
        deletedAt: null,
      },
      include: [
        {
          model: PodUnitUser,
          as: 'podUnitUsers',
          attributes: ['unitId', 'userId'],
          include: [
            {
              model: PodUnits,
              as: 'unit',
              attributes: ['ppid', 'deletedAt'],
            },
          ],
        },
      ],
    });

    if (!Array.isArray(users) || users.length === 0) {
      this.logger.log({ ppid, uid }, 'no active user found');
      throw new NotFoundException();
    }

    // this should never happen but JIC
    if (users.length > 1) {
      this.logger.log({ ppid, uid, users }, 'duplicated users found');
      throw new InternalServerErrorException();
    }

    const [user] = users;
    // filter out links to soft-deleted units
    user.podUnitUsers = user.podUnitUsers.filter((pu) => !pu.unit.deletedAt);

    // If the user has already linked to this charging station, do nothing.
    if (user.podUnitUsers.some((pu) => pu.unit.ppid === ppid)) {
      this.logger.log({ ppid, uid }, 'is already linked to this user');
      return;
    }

    if (user.podUnitUsers.length >= 2) {
      this.logger.log(
        { ppid, uid },
        'cannot link more than two units to a user'
      );
      throw new UnprocessableEntityException(
        'User has reached unit link limit'
      );
    }

    const unit = await this.podUnitRepository.findOne({
      where: {
        ppid,
        deletedAt: null,
      },
      nest: true,
      include: [
        {
          model: PodUnitUser,
          as: 'podUnitUsers',
          include: [
            {
              model: Users,
              as: 'user',
              attributes: ['authId'],
            },
          ],
        },
        {
          model: PodLocations,
          as: 'podLocation',
          include: [
            {
              model: PodAddresses,
              as: 'address',
            },
          ],
        },
      ],
    });

    if (!unit) {
      this.logger.log({ ppid, uid }, 'unit does not exist');
      throw new NotFoundException(`${ppid} does not exist`);
    }

    if (unit.podUnitUsers.length > 0) {
      this.logger.warn({ ppid, uid }, 'is already linked to another user');
    }

    const location: PodLocations | null = unit.podLocation;

    if (!location || location.id == null) {
      this.logger.log({ ppid, uid }, 'cannot link without location');
      throw new UnprocessableEntityException(
        'Linking not supported for this unit'
      );
    }

    if (location.isHome !== true || location.address?.groupId != null) {
      this.logger.log(
        {
          ppid,
          uid,
          isHome: location.isHome,
          groupId: location.address?.groupId,
        },
        'cannot link a commercial unit to home user'
      );
      throw new UnprocessableEntityException(
        'Linking not supported for this unit'
      );
    }

    // Link the user to the unit
    const created = await this.podUnitUserRepository.create({
      userId: user.id,
      unitId: unit.id,
    });

    this.logger.log(
      { ppid, uid, createdLink: created.toJSON() },
      'link created'
    );

    await this.xdpService.linkUser(uid, ppid);
    this.logger.log({ ppid, uid }, 'xdp link created');

    await this.smartChargingService.setDefaultSchedules(ppid);
    this.logger.log({ ppid, uid }, 'set default schedules');
  }

  async unLinkChargerFromUser(authId: string, ppid: string): Promise<void> {
    try {
      const { unitId, userId } = await this.usersRepository
        .findOne({
          attributes: ['id'],
          nest: true,
          where: { authId },
          include: [
            {
              model: PodUnitUser,
              as: 'podUnitUsers',
              attributes: ['unitId', 'userId'],
              include: [
                {
                  model: PodUnits,
                  as: 'unit',
                  attributes: ['ppid'],
                },
              ],
            },
          ],
        })
        .then((user) => {
          if (!user) {
            throw new NotFoundException();
          }
          return user.podUnitUsers.find(
            (podUnitUser) => podUnitUser.unit.ppid === ppid
          );
        });

      await this.podUnitUserRepository.destroy({ where: { unitId, userId } });
    } catch (error) {
      this.logger.error(
        { error, authId, ppid },
        'Failed to unlink charger from user'
      );
      throw error;
    }
  }
}
