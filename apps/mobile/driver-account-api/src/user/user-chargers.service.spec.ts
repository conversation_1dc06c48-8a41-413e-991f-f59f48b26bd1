import {
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { O } from 'ts-toolbelt';
import {
  PodLocations,
  PodUnitUser,
  PodUnits,
  PodadminSequelizeModule,
  Users,
} from '@experience/shared/sequelize/podadmin';
import { SmartChargingService } from '../smart-charging-service/smart-charging.service';
import { Test } from '@nestjs/testing';
import { UserChargersService } from './user-chargers.service';
import { XdpService } from '../xdp/xdp.service';
import { createMock } from '@golevelup/ts-jest';

import { Op } from 'sequelize';
import { v4 } from 'uuid';

describe('UserChargersService', () => {
  let service: UserChargersService;
  let mockedUsersRepository: jest.Mocked<typeof Users>;
  let mockedXdpService: jest.Mocked<XdpService>;
  let mockedSmartChargingService: jest.Mocked<SmartChargingService>;
  let mockedPodUnitUserRepository: jest.Mocked<typeof PodUnitUser>;
  let mockedPodUnitRepository: jest.Mocked<typeof PodUnits>;

  const AUTH_ID = '2a0f86f8-b607-4d95-93da-e7ad868a6ada';
  const OTHER_LINKED_USERS = [
    'ab51b6fa-183f-45c8-bcdb-7de404037e23',
    '4b4f1362-67c1-4f23-89e5-6287c1e54d91',
  ];

  const ppid = 'PSL-456789';
  const unitId = 12345;
  const userId = 67890;

  beforeEach(async () => {
    mockedXdpService = createMock();
    mockedSmartChargingService = createMock();
    const module = await Test.createTestingModule({
      imports: [PodadminSequelizeModule],
      providers: [
        UserChargersService,
        {
          provide: 'USERS_REPOSITORY',
          useValue: createMock<jest.Mocked<typeof Users>>(),
        },
        {
          provide: 'POD_UNIT_USER_REPOSITORY',
          useValue: createMock<jest.Mocked<typeof PodUnitUser>>(),
        },
        {
          provide: 'POD_UNIT_REPOSITORY',
          useValue: createMock<jest.Mocked<typeof PodUnits>>(),
        },
        {
          provide: XdpService,
          useValue: mockedXdpService,
        },
        {
          provide: SmartChargingService,
          useValue: mockedSmartChargingService,
        },
      ],
    }).compile();

    module.useLogger(false);

    service = module.get<UserChargersService>(UserChargersService);
    mockedUsersRepository =
      module.get<jest.Mocked<typeof Users>>('USERS_REPOSITORY');
    mockedPodUnitUserRepository = module.get<jest.Mocked<typeof PodUnitUser>>(
      'POD_UNIT_USER_REPOSITORY'
    );
    mockedPodUnitRepository = module.get<jest.Mocked<typeof PodUnits>>(
      'POD_UNIT_REPOSITORY'
    );

    mockedUsersRepository.findAll.mockResolvedValue([
      {
        authId: AUTH_ID,
        podUnitUsers: [
          {
            createdAt: new Date('2024-01-01T00:00:00.000Z'),
            unit: {
              id: 1,
              ppid: 'PSL-456789',
              podLocation: {
                timezone: 'Etc/UTC',
              },
            },
          },
        ],
      } as Users,
    ]);

    mockedPodUnitRepository.findOne.mockResolvedValue({
      podUnitUsers: [
        {
          user: {
            authId: OTHER_LINKED_USERS[0],
          },
        },
        {
          user: {
            authId: OTHER_LINKED_USERS[1],
          },
        },
      ],
    } as PodUnits);
  });

  describe('getChargersForUser()', () => {
    it('gets the user by their auth id', async () => {
      await service.getChargersForUser(AUTH_ID);

      expect(mockedUsersRepository.findAll).toHaveBeenCalledTimes(1);
      expect(mockedUsersRepository.findAll).toHaveBeenCalledWith({
        nest: true,
        where: {
          authId: AUTH_ID,
        },
        attributes: ['id'],
        include: [
          {
            model: PodUnitUser,
            as: 'podUnitUsers',
            attributes: ['unit_id', 'createdAt'],
            include: [
              {
                model: PodUnits,
                as: 'unit',
                attributes: ['id', 'ppid'],
                include: [
                  {
                    model: PodLocations,
                    as: 'podLocation',
                    attributes: ['timezone'],
                  },
                ],
              },
            ],
          },
        ],
      });
    });

    it('transforms the response to the correct format', async () => {
      const res = await service.getChargersForUser(AUTH_ID);

      expect(res).toStrictEqual([
        {
          linkedAt: '2024-01-01T00:00:00.000Z',
          ppid: 'PSL-456789',
          timezone: 'Etc/UTC',
          unitId: 1,
        },
      ]);
    });

    it('returns a NotFoundException if there is no matching user', async () => {
      jest.spyOn(mockedUsersRepository, 'findAll').mockReturnValue(null);

      await expect(service.getChargersForUser(AUTH_ID)).rejects.toThrow(
        NotFoundException
      );
    });

    it('returns an empty array if there is a problem executing the query', async () => {
      jest.spyOn(mockedUsersRepository, 'findAll').mockImplementation(() => {
        throw new Error('Something went wrong!');
      });

      const res = await service.getChargersForUser(AUTH_ID);

      expect(res).toBeInstanceOf(Array);
      expect(res).toHaveLength(0);
    });

    it('includes a list of other linked users if specified', async () => {
      const res = await service.getChargersForUser(AUTH_ID, true);

      expect(res).toStrictEqual([
        {
          linkedAt: '2024-01-01T00:00:00.000Z',
          ppid: 'PSL-456789',
          timezone: 'Etc/UTC',
          unitId: 1,
          otherLinkedUsers: OTHER_LINKED_USERS,
        },
      ]);

      expect(mockedPodUnitRepository.findOne).toHaveBeenCalledTimes(1);
      expect(mockedPodUnitRepository.findOne).toHaveBeenCalledWith({
        nest: true,
        where: { ppid: 'PSL-456789' },
        include: [
          {
            model: PodUnitUser,
            as: 'podUnitUsers',
            include: [
              {
                model: Users,
                as: 'user',
                attributes: ['authId'],
                where: {
                  authId: {
                    [Op.ne]: AUTH_ID,
                  },
                },
              },
            ],
          },
        ],
      });
    });
  });

  describe('unLinkChargerFromUser()', () => {
    it('should unlink a charger from a user', async () => {
      const mockFindUser = jest
        .spyOn(mockedUsersRepository, 'findOne')
        .mockReturnValueOnce(
          Promise.resolve(
            createMock<Users>({
              id: 512,
              podUnitUsers: [{ unitId, userId, unit: { ppid } }],
            })
          )
        );
      const mockDeletePodUnitUser = jest
        .spyOn(mockedPodUnitUserRepository, 'destroy')
        .mockResolvedValueOnce(undefined);

      await service.unLinkChargerFromUser(AUTH_ID, ppid);

      expect(mockFindUser).toHaveBeenCalledWith({
        attributes: ['id'],
        nest: true,
        where: { authId: AUTH_ID },
        include: [
          {
            model: PodUnitUser,
            as: 'podUnitUsers',
            attributes: ['unitId', 'userId'],
            include: [
              {
                model: PodUnits,
                as: 'unit',
                attributes: ['ppid'],
              },
            ],
          },
        ],
      });
      expect(mockDeletePodUnitUser).toHaveBeenCalledWith({
        where: { unitId, userId },
      });
    });

    it('should throw NotFoundException if user is not found', async () => {
      jest
        .spyOn(mockedUsersRepository, 'findOne')
        .mockReturnValueOnce(Promise.resolve(null));

      await expect(
        service.unLinkChargerFromUser(AUTH_ID, ppid)
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw an error if there is a problem executing the query', async () => {
      jest.spyOn(mockedUsersRepository, 'findOne').mockImplementation(() => {
        throw new Error('Something went wrong!');
      });

      await expect(
        service.unLinkChargerFromUser(AUTH_ID, ppid)
      ).rejects.toThrow('Something went wrong!');
    });
  });

  describe('linkChargerToUser()', () => {
    it('throws NotFoundException if no users found', async () => {
      mockedUsersRepository.findAll.mockResolvedValueOnce([]);

      await expect(
        service.linkChargerToUser(v4(), 'PSL-none of your damn business')
      ).rejects.toThrow(NotFoundException);
    });

    it('throws InternalServerErrorException when more than 1 user found', async () => {
      mockedUsersRepository.findAll.mockResolvedValueOnce([
        new Users(),
        new Users(),
      ]);

      await expect(
        service.linkChargerToUser(v4(), 'PSL-none of your damn business')
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('throws NotFoundException if no unit is found', async () => {
      mockedPodUnitRepository.findOne.mockResolvedValueOnce(null);

      await expect(
        service.linkChargerToUser(v4(), 'PSL-none of your damn business')
      ).rejects.toThrow(NotFoundException);
    });

    it('throws UnprocessableEntityException if the location is missing', async () => {
      const mockedPodUnit: O.Partial<PodUnits, 'deep'> = {
        podLocation: null,
        podUnitUsers: [],
      };
      mockedPodUnitRepository.findOne.mockResolvedValueOnce(
        mockedPodUnit as PodUnits
      );

      const mockedUser: O.Partial<Users, 'deep'> = {
        podUnitUsers: [],
      };

      mockedUsersRepository.findAll.mockResolvedValueOnce([
        mockedUser,
      ] as Users[]);

      await expect(
        service.linkChargerToUser(v4(), 'PSL-none of your damn business')
      ).rejects.toThrow(UnprocessableEntityException);
    });

    it('throws UnprocessableEntityException if the location is not home type', async () => {
      const mockedPodUnit: O.Partial<PodUnits, 'deep'> = {
        podLocation: {
          id: 1,
          isHome: false,
        },
        podUnitUsers: [],
      };
      mockedPodUnitRepository.findOne.mockResolvedValueOnce(
        mockedPodUnit as PodUnits
      );

      const mockedUser: O.Partial<Users, 'deep'> = {
        podUnitUsers: [],
      };

      mockedUsersRepository.findAll.mockResolvedValueOnce([
        mockedUser,
      ] as Users[]);

      await expect(
        service.linkChargerToUser(v4(), 'PSL-none of your damn business')
      ).rejects.toThrow(UnprocessableEntityException);
    });

    it('throws UnprocessableEntityException if the location has group', async () => {
      const mockedPodUnit: O.Partial<PodUnits, 'deep'> = {
        podLocation: {
          id: 1,
          isHome: true,
          address: { groupId: 1 },
        },
        podUnitUsers: [],
      };
      mockedPodUnitRepository.findOne.mockResolvedValueOnce(
        mockedPodUnit as PodUnits
      );

      const mockedUser: O.Partial<Users, 'deep'> = {
        podUnitUsers: [],
      };

      mockedUsersRepository.findAll.mockResolvedValueOnce([
        mockedUser,
      ] as Users[]);

      await expect(
        service.linkChargerToUser(v4(), 'PSL-none of your damn business')
      ).rejects.toThrow(UnprocessableEntityException);
    });

    it('links the unit', async () => {
      const authId = v4(),
        userId = 12,
        unitId = 25,
        ppid = 'PSL-1234567';
      const mockedPodUnit: O.Partial<PodUnits, 'deep'> = {
        id: unitId,
        podLocation: {
          id: 1,
          isHome: true,
          address: { groupId: null },
        },
        podUnitUsers: [],
      };
      mockedPodUnitRepository.findOne.mockResolvedValueOnce(
        mockedPodUnit as PodUnits
      );

      const mockedUser: O.Partial<Users, 'deep'> = {
        id: userId,
        podUnitUsers: [],
      };

      mockedUsersRepository.findAll.mockResolvedValueOnce([
        mockedUser,
      ] as Users[]);

      await service.linkChargerToUser(authId, ppid);

      expect(mockedPodUnitUserRepository.create).toHaveBeenCalledWith({
        userId: userId,
        unitId: unitId,
      });

      expect(mockedXdpService.linkUser).toHaveBeenCalledWith(authId, ppid);
      expect(
        mockedSmartChargingService.setDefaultSchedules
      ).toHaveBeenCalledWith(ppid);
    });
  });
});
