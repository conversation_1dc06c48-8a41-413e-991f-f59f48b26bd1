import { SimpleEmailService } from '@experience/shared/nest/aws/ses-module';
import { StartedDockerComposeEnvironment } from 'testcontainers';
import { UserEmailDto } from '@experience/mobile/driver-account/domain/user';
import axios from 'axios';

export const describeUserModule = ({
  baseUrl: BASE_URL_VERSIONED,
  getPodadminDatabase,
}: {
  baseUrl: string;
  getPodadminDatabase: () => StartedDockerComposeEnvironment | undefined;
}) => {
  describe('User module', () => {
    let podadminDatabase: StartedDockerComposeEnvironment;

    const userEmailDto: UserEmailDto = {
      oldEmail: '<EMAIL>',
      newEmail: '<EMAIL>',
    };

    beforeEach(() => {
      podadminDatabase = getPodadminDatabase();
    });

    describe('users controller', () => {
      it('should update a user profile', async () => {
        const uid = '5357be96-1495-4951-8046-c2d59ba76c33';
        const response = await axios.put(`${BASE_URL_VERSIONED}/users/${uid}`, {
          first_name: 'Mobile',
          last_name: 'Non-Tester',
          locale: 'en',
        });
        expect(response.status).toEqual(200);
        expect(response.data).toEqual({
          email: '<EMAIL>',
          first_name: 'Mobile',
          last_name: 'Non-Tester',
          locale: 'en',
          uid: '5357be96-1495-4951-8046-c2d59ba76c33',
        });
      });

      it("should update a user's locale, balance and currency", async () => {
        const uid = '5357be96-1495-4951-8046-c2d59ba76c33';
        const before = await axios.get(`${BASE_URL_VERSIONED}/users/${uid}`);

        await axios.put(`${BASE_URL_VERSIONED}/users/${uid}`, {
          locale: 'es',
        });

        const after = await axios.get(`${BASE_URL_VERSIONED}/users/${uid}`);

        expect(before.status).toEqual(200);
        expect(before.data.locale).toEqual('en');
        expect(before.data.balance.currency).toEqual('GBP');
        expect(before.data.balance.amount).toEqual(472);

        expect(after.status).toEqual(200);
        expect(after.data.locale).toEqual('es');
        expect(after.data.balance.currency).toEqual('EUR');
        expect(after.data.balance.amount).toEqual(394);
      });

      it("should update a user's locale, currency when balance is 0", async () => {
        const uid = '5357be96-1495-4951-8046-c2d59ba76c44';
        const before = await axios.get(`${BASE_URL_VERSIONED}/users/${uid}`);

        await axios.put(`${BASE_URL_VERSIONED}/users/${uid}`, {
          locale: 'es',
        });

        const after = await axios.get(`${BASE_URL_VERSIONED}/users/${uid}`);

        expect(before.status).toEqual(200);
        expect(before.data.locale).toEqual('en');
        expect(before.data.balance.currency).toEqual('GBP');
        expect(before.data.balance.amount).toEqual(0);

        expect(after.status).toEqual(200);
        expect(after.data.locale).toEqual('es');
        expect(after.data.balance.currency).toEqual('EUR');
        expect(after.data.balance.amount).toEqual(0);
      });

      it('should update a user email', async () => {
        const response = await axios.put(
          `${BASE_URL_VERSIONED}/users/email/update`,
          userEmailDto
        );
        expect(response.status).toEqual(200);
        expect(response.data).toBeDefined();
      });

      it('should deactivate user', async () => {
        const uid = '5357be96-1495-4951-8046-c2d59ba76c44';

        const beforeDeletion = await axios.get(
          `${BASE_URL_VERSIONED}/users/${uid}`
        );
        expect(beforeDeletion.status).toEqual(200);
        expect(beforeDeletion.data.deletedAtTimestamp).toBeNull();

        //delete user
        const deletion = await axios.delete(
          `${BASE_URL_VERSIONED}/users/${uid}`
        );

        expect(deletion.status).toEqual(200);

        const afterDeletion = await axios.get(
          `${BASE_URL_VERSIONED}/users/${uid}`
        );
        expect(afterDeletion.status).toEqual(200);
      });

      it('should activate user', async () => {
        const uid = '5357be96-1495-4951-8046-c2d59ba76c66';

        const enable = await axios.put(
          `${BASE_URL_VERSIONED}/users/enable/${uid}`
        );
        expect(enable.status).toEqual(200);
        const afterActivation = await axios.get(
          `${BASE_URL_VERSIONED}/users/${uid}`
        );
        expect(afterActivation.status).toEqual(200);
      });

      it('should get the extended user info', async () => {
        const response = await axios.get(
          `${BASE_URL_VERSIONED}/users/5357be96-1495-4951-8046-c2d59ba76c33`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toEqual({
          email: '<EMAIL>',
          emailVerified: true,
          first_name: 'Mobile',
          last_name: 'Non-Tester',
          locale: 'es',
          uid: '5357be96-1495-4951-8046-c2d59ba76c33',
          status: 'active',
          deletedAtTimestamp: null,
          accountCreationTimestamp: '2023-08-24T10:14:52.000Z',
          lastSignInTimestamp: null,
          paymentProcessorId: 'cus_hfJJeHsFdaV2H3s',
          balance: {
            currency: 'EUR',
            amount: 394,
          },
          preferences: {
            unitOfDistance: 'mi',
          },
        });
      });

      it('should get user by an exact email match', async () => {
        const response = await axios.get(
          `${BASE_URL_VERSIONED}/users?email=<EMAIL>`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toEqual([
          {
            email: '<EMAIL>',
            first_name: 'Mobile',
            last_name: 'Non-Tester',
            locale: 'es',
            uid: '5357be96-1495-4951-8046-c2d59ba76c33',
          },
        ]);
      });

      it('should get user by a fuzzy email match', async () => {
        const response = await axios.get(
          `${BASE_URL_VERSIONED}/users?emailLike=mobile`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toEqual([
          {
            email: '<EMAIL>',
            first_name: 'Mobiler',
            last_name: 'Tester',
            locale: 'en',
            uid: '5357be96-1495-4951-8046-c2d59ba76c66',
          },
          {
            email: '<EMAIL>',
            first_name: 'Mobile',
            last_name: 'Non-Tester',
            locale: 'es',
            uid: '5357be96-1495-4951-8046-c2d59ba76c33',
          },
        ]);
      });

      it("should get the user's chargers", async () => {
        const response = await axios.get(
          `${BASE_URL_VERSIONED}/users/5357be96-1495-4951-8046-c2d59ba76c33/chargers`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toEqual([
          {
            linkedAt: '2024-10-10T14:50:00.000Z',
            ppid: 'PSL-555555',
            timezone: 'Etc/UTC',
            unitId: 462958,
          },
          {
            linkedAt: '2024-05-09T14:25:00.000Z',
            ppid: 'PSL-11111',
            timezone: 'Etc/UTC',
            unitId: 462957,
          },
          {
            linkedAt: '2024-05-08T14:25:00.000Z',
            ppid: 'PSL-123456',
            timezone: 'Etc/UTC',
            unitId: 33667,
          },
        ]);
      });

      it("should get the user's chargers and include other linked users when requested", async () => {
        const response = await axios.get(
          `${BASE_URL_VERSIONED}/users/5357be96-1495-4951-8046-c2d59ba76c33/chargers?includeOtherLinkedUsers=true`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toEqual([
          {
            linkedAt: '2024-10-10T14:50:00.000Z',
            ppid: 'PSL-555555',
            timezone: 'Etc/UTC',
            unitId: 462958,
            otherLinkedUsers: ['a3450f38-217a-4d0c-8eec-b7d63c6fd2e0'],
          },
          {
            linkedAt: '2024-05-09T14:25:00.000Z',
            ppid: 'PSL-11111',
            timezone: 'Etc/UTC',
            unitId: 462957,
            otherLinkedUsers: [],
          },
          {
            linkedAt: '2024-05-08T14:25:00.000Z',
            ppid: 'PSL-123456',
            timezone: 'Etc/UTC',
            unitId: 33667,
            otherLinkedUsers: [],
          },
        ]);
      });

      it('should link user to unit', async () => {
        const uid = '2070c5df-8b3f-4808-afd2-5084d73a6525';
        const ppid = 'PSL-112233';

        const linkResponse = await axios.post(
          `${BASE_URL_VERSIONED}/users/${uid}/chargers/${ppid}`
        );

        expect(linkResponse.status).toEqual(201);

        const afterLink = await axios.get(
          `${BASE_URL_VERSIONED}/users/${uid}/chargers`
        );

        expect(afterLink.status).toEqual(200);
        expect(afterLink.data).toEqual([
          {
            linkedAt: expect.any(String),
            ppid,
            timezone: expect.any(String),
            unitId: 462959,
          },
        ]);
        expect(afterLink.data).toHaveLength(1);
      });

      it('should no-op on a second attempt to link the same user to the same unit', async () => {
        const uid = '2070c5df-8b3f-4808-afd2-5084d73a6525';
        const ppid = 'PSL-112233';

        const linkResponse = await axios.post(
          `${BASE_URL_VERSIONED}/users/${uid}/chargers/${ppid}`
        );

        expect(linkResponse.status).toEqual(201);

        const afterLink = await axios.get(
          `${BASE_URL_VERSIONED}/users/${uid}/chargers`
        );

        expect(afterLink.status).toEqual(200);
        expect(afterLink.data).toEqual([
          {
            linkedAt: expect.any(String),
            ppid,
            timezone: expect.any(String),
            unitId: 462959,
          },
        ]);
        expect(afterLink.data).toHaveLength(1);
      });

      it('should unlink a user from a charger', async () => {
        const uid = '5357be96-1495-4951-8046-c2d59ba76c33';
        const ppid = 'PSL-11111';

        const unlinkResponse = await axios.delete(
          `${BASE_URL_VERSIONED}/users/${uid}/chargers/${ppid}`
        );

        expect(unlinkResponse.status).toEqual(204);

        const afterUnlink = await axios.get(
          `${BASE_URL_VERSIONED}/users/${uid}/chargers`
        );

        expect(afterUnlink.status).toEqual(200);
        expect(afterUnlink.data).toEqual([
          {
            linkedAt: '2024-10-10T14:50:00.000Z',
            ppid: 'PSL-555555',
            timezone: 'Etc/UTC',
            unitId: 462958,
          },
          {
            linkedAt: '2024-05-08T14:25:00.000Z',
            ppid: 'PSL-123456',
            timezone: 'Etc/UTC',
            unitId: 33667,
          },
        ]);
      });

      it('should get list of users by charger ppid', async () => {
        const response = await axios.get(
          `${BASE_URL_VERSIONED}/users?ppid=PSL-123456`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toEqual([
          {
            email: '<EMAIL>',
            first_name: 'Mobile',
            last_name: 'Non-Tester',
            locale: 'es',
            uid: '5357be96-1495-4951-8046-c2d59ba76c33',
          },
        ]);
      });

      it("should get the user's suppressed status", async () => {
        // We do not want to actually call SES
        jest
          .spyOn(SimpleEmailService.prototype, 'getSuppressedDestination')
          .mockReturnValue(Promise.resolve('UNKNOWN'));

        const response = await axios.get(
          `${BASE_URL_VERSIONED}/users/5357be96-1495-4951-8046-c2d59ba76c33/suppressedStatus`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toEqual({
          status: 'UNKNOWN',
        });
      });

      it("should remove the user's suppressed status", async () => {
        // We do not want to actually call SES
        jest
          .spyOn(SimpleEmailService.prototype, 'deleteSuppressedDestination')
          .mockReturnValue(Promise.resolve());

        const response = await axios.put(
          `${BASE_URL_VERSIONED}/users/5357be96-1495-4951-8046-c2d59ba76c33/suppressedStatus`,
          {
            status: null,
          }
        );

        expect(response.status).toEqual(200);
        expect(response.data).toStrictEqual({
          status: null,
        });
      });

      it('should send a track login email', async () => {
        // We do not want to actually call SES
        jest
          .spyOn(SimpleEmailService.prototype, 'deleteSuppressedDestination')
          .mockReturnValue(Promise.resolve());

        const response = await axios.post(
          `${BASE_URL_VERSIONED}/users/login/5357be96-1495-4951-8046-c2d59ba76c33/alert`,
          {
            ipAddress: '2a00:23ee:1320:109f:98e0:dcc6:b917:f126',
            userAgent:
              'FirebaseAuth.iOS/8.15.0 com.podpoint.podpoint/3.26.0 iPhone/17.5.1 hw/iPhone14_5,gzip(gfe),gzip(gfe',
            timestamp: '2020-03-14 00:00:00',
            authId: '5357be96-1495-4951-8046-c2d59ba76c33',
          }
        );

        expect(response.status).toEqual(200);
        expect(response.data).toStrictEqual('');
      });

      it('should throw a 500 error when podadmin db and it will rollback any auth service sql changes', async () => {
        const uid = '5357be96-1495-4951-8046-c2d59ba76c33';
        await podadminDatabase?.stop();
        await expect(
          axios.put(`${BASE_URL_VERSIONED}/users/${uid}`, {
            first_name: 'Mobile',
            last_name: 'Non-Tester-2',
            locale: 'en',
          })
        ).rejects.toThrow('Request failed with status code 500');
      });

      it('should throw a 500 error when podadmin db and it will rollback any auth service sql changes', async () => {
        const userEmailDto2: UserEmailDto = {
          oldEmail: '<EMAIL>',
          newEmail: '<EMAIL>',
        };
        await podadminDatabase?.stop();
        await expect(
          axios.put(`${BASE_URL_VERSIONED}/users/email/update`, userEmailDto2)
        ).rejects.toThrow('Request failed with status code 500');
      });
    });
  });
};
