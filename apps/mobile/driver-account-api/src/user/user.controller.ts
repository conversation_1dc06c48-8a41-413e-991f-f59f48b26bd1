import {
  ApiBadRequestResponse,
  ApiBody,
  ApiCreatedResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import {
  BadRequestException,
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Headers,
  HttpCode,
  Param,
  ParseBoolPipe,
  Post,
  Put,
  Query,
  UseFilters,
  UseGuards,
} from '@nestjs/common';

import {
  CreateUserDto,
  CreateUserResponseDto,
  ExtendedUserInfoResponseDto,
  GetUsersQuery,
  TrackLoginRequest,
  UpdateUserSuppressedStatusDto,
  UserChargerDto,
  UserDetailsDto,
  UserEmailDto,
  UserInfoResponseDto,
  UserSuppressedStatusDto,
} from '@experience/mobile/driver-account/domain/user';
import { LegacyHttpExceptionFilter } from '@experience/mobile/nest/exception';
import { UpdateEmailService } from './update-email-and-revert.service';
import { UserChargersService } from './user-chargers.service';
import { UserEmailEnabledGuard } from '../guard/user-email-enabled.guard';
import { UserService } from './user.service';

@ApiTags('Users')
@Controller({
  version: '1',
  path: 'users',
})
@UseFilters(LegacyHttpExceptionFilter)
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly updateEmailService: UpdateEmailService,
    private readonly userChargersService: UserChargersService
  ) {}

  @Post()
  @ApiOperation({
    summary: 'create a new user',
    description: 'Creates a new user account',
  })
  @ApiOkResponse({
    description: 'Creates a new user account',
    type: CreateUserResponseDto,
  })
  @ApiQuery({
    name: 'reset_password_continue_url',
    type: String,
    description: 'Reset password continue url that is optional',
    required: false,
  })
  createUser(
    @Body() createUser: CreateUserDto,
    @Headers('Accept-Language') language = 'en',
    @Query('reset_password_continue_url') redirectUrl?: string,
    @Headers('x-app-name') appName?: string
  ) {
    return this.userService.createUser(
      createUser,
      language,
      redirectUrl,
      appName
    );
  }

  @Get()
  @ApiOperation({
    summary: 'get user profile by given filters',
    description:
      'Get a user profile by a given filter. Accepts only email at the moment',
  })
  @ApiQuery({
    name: 'email',
    type: String,
    example: '<EMAIL>',
    required: false,
  })
  @ApiQuery({
    name: 'emailLike',
    type: String,
    example: true,
    description: 'Allows for fuzzy matching on the email field',
    required: false,
  })
  @ApiQuery({
    name: 'ppid',
    type: String,
    example: 'PSL-1234',
    description: 'Allows for searching by PPID',
    required: false,
  })
  @ApiOkResponse({
    description: 'Return matching users',
    type: UserInfoResponseDto,
    isArray: true,
  })
  getByFilter(@Query() queryParams: GetUsersQuery) {
    return this.userService.getByFilter({
      email: queryParams.email,
      emailLike: queryParams.emailLike,
      ppid: queryParams.ppid,
    });
  }

  @Get(':uid')
  @ApiParam({ name: 'uid', type: String })
  @ApiOperation({
    summary: 'get user profile by UID',
    description: 'Get a user profile for a given UID',
  })
  @ApiOkResponse({
    description: 'Get a user profile for a given UID',
    type: ExtendedUserInfoResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  getUser(@Param('uid') uid: string) {
    return this.userService.getUser(uid);
  }

  @Put(':uid')
  @ApiParam({ name: 'uid', type: String })
  @ApiOperation({
    summary: 'update user profile by UID',
    description: 'Update a user profile for a given UID',
  })
  @ApiOkResponse({
    description: 'Update a user profile for a given UID',
    type: UserInfoResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  updateUser(@Param('uid') uid: string, @Body() data: UserDetailsDto) {
    return this.userService.updateUser(uid, data);
  }

  @Put('enable/:uid')
  @ApiParam({ name: 'uid', type: String })
  @ApiOperation({
    summary: 'enable user profile by UID',
    description: 'Enable a user profile for a given UID',
  })
  @ApiOkResponse({
    description: 'Enable a user profile for a given UID',
    type: UserInfoResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  enable(@Param('uid') uid: string) {
    return this.userService.enableUser(uid);
  }

  @Put('email/update')
  @ApiOperation({
    summary: 'update user email',
    description: 'Update a user email for an existing email and new email',
  })
  @ApiOkResponse({
    description: 'Update a user email for an existing email and new email',
    type: UserEmailDto,
  })
  @ApiHeader({
    name: 'x-app-name',
    description: 'The name of the requesting app',
    required: false,
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  @UseGuards(UserEmailEnabledGuard)
  async updateEmail(
    @Body() data: UserEmailDto,
    @Headers('Accept-Language') language = 'en',
    @Headers('x-app-name') appName?: string
  ) {
    await this.updateEmailService.updateEmail(data, language, appName);
  }

  @Get(':uid/chargers')
  @ApiParam({ name: 'uid', type: String })
  @ApiQuery({ name: 'includeOtherLinkedUsers', type: Boolean, required: false })
  @ApiOperation({
    summary: "get user's chargers by their UID",
    description: "Get a user's chargers by their  UID",
  })
  @ApiOkResponse({
    description: "Get a user's chargers by their UID",
    type: [UserChargerDto],
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  async getChargers(
    @Param('uid') uid: string,
    @Query('includeOtherLinkedUsers', new ParseBoolPipe({ optional: true }))
    includeOtherLinkedUsers?: boolean
  ): Promise<UserChargerDto[]> {
    return this.userChargersService.getChargersForUser(
      uid,
      includeOtherLinkedUsers
    );
  }

  @Delete(':uid/chargers/:ppid')
  @HttpCode(204)
  @ApiParam({ name: 'uid', type: String })
  @ApiParam({ name: 'ppid', type: String })
  @ApiOperation({
    summary: 'unlink a charger from a user',
    description: 'Unlink a charger from a user',
  })
  @ApiOkResponse({
    description: 'Unlink a charger from a user',
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  async unLinkCharger(
    @Param('uid') uid: string,
    @Param('ppid') ppid: string
  ): Promise<void> {
    return this.userChargersService.unLinkChargerFromUser(uid, ppid);
  }

  @Post(':uid/chargers/:ppid')
  @ApiParam({ name: 'uid', type: String })
  @ApiParam({ name: 'ppid', type: String })
  @ApiCreatedResponse({
    description: 'linked a unit to a user successfully',
  })
  @ApiNotFoundResponse({
    description: 'unit/user not found',
  })
  @ApiInternalServerErrorResponse({
    description: 'unknown error',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Unable to link unit because of unit/user misconfiguration',
  })
  @ApiForbiddenResponse({
    description: 'unit/user cannot be linked',
  })
  async linkCharger(
    @Param('uid') uid: string,
    @Param('ppid') ppid: string
  ): Promise<void> {
    await this.userChargersService.linkChargerToUser(uid, ppid);
  }

  @Get(':uid/suppressedStatus')
  @ApiParam({ name: 'uid', type: String })
  @ApiOperation({
    summary: "get a user's suppressed status by their UID",
    description: "get a user's suppressed status by their UID",
  })
  @ApiOkResponse({
    description: "get a user's suppressed status by their UID",
    type: UserSuppressedStatusDto,
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  async getSuppressedStatus(
    @Param('uid') uid: string
  ): Promise<UserSuppressedStatusDto> {
    return this.userService.getSuppressedStatus(uid);
  }

  @Put(':uid/suppressedStatus')
  @ApiParam({ name: 'uid', type: String })
  @ApiOperation({
    summary: 'remove a user from the SES suppression list',
    description: 'remove a user from the SES suppression list',
  })
  @ApiOkResponse({
    description: "remove a user's suppression status",
    type: UpdateUserSuppressedStatusDto,
  })
  @ApiBadRequestResponse({
    description: 'Thrown when a status other than null is supplied',
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Thrown when an unknown error occurs',
  })
  async updateSuppressedStatus(
    @Param('uid') uid: string,
    @Body() { status }: UpdateUserSuppressedStatusDto
  ): Promise<UpdateUserSuppressedStatusDto> {
    if (status !== null) {
      throw new BadRequestException('status may only be set to null');
    }

    return this.userService.removeSuppressionStatus(uid);
  }

  @Delete(':uid')
  @ApiParam({ name: 'uid', type: String })
  @ApiOperation({
    summary: 'delete a user',
    description: 'delete a user',
  })
  @ApiOkResponse({
    description: 'delete a user',
  })
  @ApiNotFoundResponse({
    description: 'Thrown when user not found',
  })
  @ApiQuery({
    name: 'force',
    type: Boolean,
    example: true,
    description: 'Allows to disable users when they have outstanding balance',
    required: false,
  })
  async softDelete(
    @Param('uid') uid: string,
    @Query('force', new DefaultValuePipe(false), ParseBoolPipe) force: boolean
  ): Promise<void> {
    await this.userService.deleteUser(uid, force);
  }

  @Post('login/:uid/alert')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Generates an email of when and from which ip the user logged in',
    description:
      'Generates an email of when and from which ip the user logged in',
  })
  @ApiBody({ type: TrackLoginRequest })
  async trackLogin(
    @Param('uid') uid: string,
    @Body() trackLoginRequest: TrackLoginRequest
  ): Promise<void> {
    await this.userService.trackLogin(uid, trackLoginRequest);
  }
}
