{"name": "driver-account-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mobile/driver-account-api/src", "projectType": "application", "tags": ["mobile", "package"], "implicitDependencies": ["driver-account-api-axios", "driver-account-api-nestjs", "mobile-account-maizzle"], "namedInputs": {"projectSpecificFiles": ["{workspaceRoot}/.aws/**/driver-account-api/*", "{workspaceRoot}/assets/mobile-account/**/*", "{workspaceRoot}/assets/driver-account-api/**/*"]}, "targets": {"build-api": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/mobile/driver-account-api", "main": "apps/mobile/driver-account-api/src/main.ts", "tsConfig": "apps/mobile/driver-account-api/tsconfig.app.json", "webpackConfig": "apps/mobile/driver-account-api/webpack.config.js", "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false}, "swagger": {"main": "{projectRoot}/src/swagger/generate-swagger.ts"}}}, "build": {"executor": "nx:run-commands", "options": {"commands": ["cp -R libs/mobile/nest/auth/messages/. assets/driver-account-api/i18n", "nx run driver-account-api:build-api"]}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "driver-account-api:build"}, "configurations": {"production": {"buildTarget": "driver-account-api:build:production"}}}, "remove-stale-tokens": {"executor": "@nx/js:node", "options": {"buildTarget": "driver-account-api:build", "args": ["remove-stale-tokens"]}, "configurations": {"production": {"buildTarget": "driver-account-api:build:production"}}}, "serve:dependencies": {"executor": "nx:run-commands", "options": {"commands": ["nx run shared-test-db-experience:compose:recreate", "nx run shared-test-db-auth:compose:recreate", "nx run shared-test-db-podadmin:compose:recreate"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/mobile/driver-account-api/jest.config.ts", "passWithNoTests": false}}, "smoke-test": {"executor": "nx:run-commands", "options": {"args": "--baseUrl=http://host.docker.internal:5104", "commands": ["docker run --rm -i -e BASE_URL={args.baseUrl} grafana/k6 run - <apps/mobile/driver-account-api/smoke-test.js"], "parallel": false}, "configurations": {"ecs": {"args": "--baseUrl=http://driver-account-api.destination.cluster.com:5104"}}}, "migrate-users": {"executor": "nx:run-commands", "options": {"command": "ts-node --project apps/mobile/driver-account-api/tsconfig.app.json --require tsconfig-paths/register apps/mobile/driver-account-api/src/scripts/migrate-users.ts"}}, "auth-sync-verify": {"executor": "nx:run-commands", "options": {"command": "ts-node --project apps/mobile/driver-account-api/tsconfig.app.json --require tsconfig-paths/register apps/mobile/driver-account-api/src/scripts/auth-sync-verify.ts"}}, "update-user-email": {"executor": "nx:run-commands", "options": {"command": "ts-node --project apps/mobile/driver-account-api/tsconfig.app.json --require tsconfig-paths/register apps/mobile/driver-account-api/src/scripts/update-user-email.ts"}}, "migrate-auth-to-experience": {"executor": "nx:run-commands", "options": {"command": "ts-node --project apps/mobile/driver-account-api/tsconfig.app.json --require tsconfig-paths/register apps/mobile/driver-account-api/src/scripts/migrate-auth-to-experience.ts"}}, "compose": {"executor": "nx:run-commands", "options": {"cwd": "apps/mobile/driver-account-api"}, "configurations": {"up": {"command": "docker compose up --detach"}, "down": {"command": "docker compose down"}, "remove": {"command": "docker compose rm --force --stop"}, "recreate": {"command": "docker compose up --detach --force-recreate --renew-anon-volumes"}}}, "generate-swagger": {"executor": "@nx/js:node", "options": {"buildTarget": "{projectName}:build-api:swagger", "watch": false}}}}