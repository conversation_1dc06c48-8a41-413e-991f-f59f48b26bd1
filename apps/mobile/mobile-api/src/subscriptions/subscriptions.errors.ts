export enum SubscriptionsError {
  FAILED_TO_GET_SUBSCRIPTIONS_ERROR = 'FAILED_TO_GET_SUBSCRIPTIONS_ERROR',
  FAILED_TO_UPDATE_SUBSCRIPTION_ACTION_ERROR = 'FAILED_TO_UPDATE_SUBSCRIPTION_ACTION_ERROR',
  CONFIRMATION_OF_PAYEE_VALIDATION_ERROR = 'CONFIRMATION_OF_PAYEE_VALIDATION_ERROR',
  USER_DOES_NOT_OWN_SUBSCRIPTION_ERROR = 'USER_DOES_NOT_OWN_SUBSCRIPTION_ERROR',
  ACTION_DOES_NOT_EXIST_ERROR = 'ACTION_DOES_NOT_EXIST_ERROR',
  FAILED_TO_GET_SUBSCRIPTION_ERROR = 'FAILED_TO_GET_SUBSCRIPTION_ERROR',
  FAILED_TO_GET_SUBSCRIPTION_ACTION_ERROR = 'FAILED_TO_GET_SUBSCRIPTION_ACTION_ERROR',
  SUBSCRIPTION_NOT_FOUND_ERROR = 'SUBSCRIPTION_NOT_FOUND_ERROR',
  UPDATE_SUBSCRIPTION_ACTION_VALIDATION_ERROR = 'UPDATE_SUBSCRIPTION_ACTION_VALIDATION_ERROR',
  GET_SUBSCRIPTION_VALIDATION_ERROR = 'GET_SUBSCRIPTION_VALIDATION_ERROR',
  SUBSCRIPTION_WITH_PPID_ALREADY_ACTIVE_ERROR = 'SUBSCRIPTION_WITH_PPID_ALREADY_ACTIVE_ERROR',
  INVALID_SUBSCRIPTION_CREATE_BODY_ERROR = 'INVALID_SUBSCRIPTION_CREATE_BODY_ERROR',
  FAILED_TO_CREATE_SUBSCRIPTION_ERROR = 'FAILED_TO_CREATE_SUBSCRIPTION_ERROR',
  USER_NOT_LINKED_TO_CHARGER_ERROR = 'USER_NOT_LINKED_TO_CHARGER_ERROR',
}

export class FailedToGetSubscriptionsError extends Error {}
export class FailedToUpdateSubscriptionActionError extends Error {}
export class ConfirmationOfPayeeValidationError extends Error {}
export class UserDoesNotOwnSubscriptionError extends Error {}
export class ActionDoesNotExistError extends Error {}
export class FailedToGetSubscriptionError extends Error {}
export class SubscriptionNotFoundError extends Error {}
export class FailedToGetSubscriptionActionError extends Error {}
export class UpdateSubscriptionActionValidationError extends Error {}
export class GetSubscriptionValidationError extends Error {}
export class SubscriptionWithPPIDAlreadyActiveError extends Error {}
export class UserNotLinkedToChargerError extends Error {}
export class InvalidSubscriptionCreateBodyError extends Error {}
export class FailedToCreateSubscriptionError extends Error {}
