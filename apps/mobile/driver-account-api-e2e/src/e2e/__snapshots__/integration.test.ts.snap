// Jest <PERSON>napshot v1, https://jestjs.io/docs/snapshot-testing

exports[`driver account api Notifications Module GET /tokens successfully gets tokens stored for a given user 1`] = `
[
  {
    "timestamp": "2024-10-28T15:19:00.000Z",
    "token": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4i5j6k7l8m9n0",
  },
]
`;

exports[`driver account api health module health controller should perform a health check 1`] = `
{
  "details": {},
  "error": {},
  "info": {},
  "status": "ok",
}
`;
