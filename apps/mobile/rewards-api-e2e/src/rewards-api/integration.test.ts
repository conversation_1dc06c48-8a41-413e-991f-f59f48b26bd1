// eslint-disable-next-line @nx/enforce-module-boundaries
import { AppModule } from '../../../rewards-api/src/app.module';
import { Client } from 'pg';
import { DriverAccountDbConfigMap } from '@experience/mobile/driver-account/database';
import { HttpResponse, http } from 'msw';
import { INestApplication } from '@nestjs/common';
import { bootstrap } from '@experience/shared/nest/utils';
import { createServer } from '@mswjs/http-middleware';
import { handlers as mswSubscriptionsApiHandlers } from '@experience/mobile/subscriptions-api/msw';
import { v4 } from 'uuid';
import axios, { isAxiosError } from 'axios';

const PORT = 5111;
const BASE_URL_NEUTRAL = `http://localhost:${PORT}`;

const subscriptionsApiHandlers = [
  http.get('/subscriptions', () =>
    HttpResponse.json({
      subscriptions: [
        {
          status: 'ACTIVE',
          plan: {
            id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
            type: 'POD_DRIVE',
            productCode: 'string',
            allowanceMiles: 7500,
            allowancePeriod: 'ANNUAL',
            upfrontFeePounds: 99,
            monthlyFeePounds: 35,
            contractDurationMonths: 18,
            ratePencePerMile: 2.28,
            rateMilesPerKwh: 3.5,
            milesRenewalDate: '2025-06-05T16:19:09.082Z',
          },
        },
      ],
    })
  ),
  http.get('/subscriptions/:id', () =>
    HttpResponse.json({
      status: 'ACTIVE',
      plan: {
        id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
        type: 'POD_DRIVE',
        productCode: 'string',
        allowanceMiles: 7500,
        allowancePeriod: 'ANNUAL',
        upfrontFeePounds: 99,
        monthlyFeePounds: 35,
        contractDurationMonths: 18,
        ratePencePerMile: 2.28,
        rateMilesPerKwh: 3.5,
        milesRenewalDate: '2025-06-05T16:19:09.082Z',
      },
    })
  ),
  ...mswSubscriptionsApiHandlers,
];

describe('rewards api', () => {
  jest.setTimeout(180_000);

  let nestApi: INestApplication;
  let db: Client;

  describe('app module', () => {
    beforeAll(async () => {
      createServer(...subscriptionsApiHandlers).listen(5120);

      nestApi = await bootstrap({
        module: AppModule,
        port: PORT,
      });

      const driverAccountDbConfig: DriverAccountDbConfigMap = JSON.parse(
        process.env.DRIVER_ACCOUNT_DB_CONFIG!
      );

      db = new Client({
        port: driverAccountDbConfig.migrate.port,
        host: driverAccountDbConfig.migrate.host,
        database: driverAccountDbConfig.migrate.database,
        user: driverAccountDbConfig.migrate.username,
        password: driverAccountDbConfig.migrate.password,
      });

      await db.connect();
    });

    afterAll(async () => {
      await db.end();
      await nestApi.close();
    });

    describe('health controller', () => {
      it('should perform a health check', async () => {
        const response = await axios.get(`${BASE_URL_NEUTRAL}/health`);
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });
    });

    describe('reward-wallets', () => {
      describe('PUT /:authId', () => {
        it('creates a wallet', async () => {
          const userId = v4();

          const response = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: 'b997546f-7007-4ed2-a3ba-619e5542f319',
            }
          );

          expect(response.status).toEqual(200);
          expect(response.data).toHaveProperty('id');
          expect(response.data).toHaveProperty('type', 'POD_DRIVE');
          expect(response.data).toHaveProperty(
            'subscriptionId',
            'b997546f-7007-4ed2-a3ba-619e5542f319'
          );

          const { rows: walletRows } = await db.query(
            'SELECT * FROM rewards.wallets WHERE id = $1',
            [response.data.id]
          );

          expect(walletRows.length).toEqual(1);

          const { rows: accountRows } = await db.query(
            'SELECT * FROM rewards.accounts WHERE reward_wallet_id = $1',
            [walletRows[0].id]
          );

          expect(accountRows.length).toEqual(2);

          const { rows: transactionRows } = await db.query(
            'SELECT * FROM rewards.transactions WHERE account_id = $1',
            [accountRows[0].id]
          );

          expect(transactionRows.length).toEqual(1);
        });

        it('updates an already created wallet', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: 'b997546f-7007-4ed2-a3ba-619e5542f319',
            }
          );

          expect(createWalletResponse.status).toEqual(200);

          const { rows: beforeRows } = await db.query(
            'SELECT * FROM rewards.wallets WHERE user_id = $1',
            [userId]
          );

          expect(beforeRows.length).toEqual(1);

          const response = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: '21f11182-6ff3-4941-aae0-25d751d99a38',
            }
          );

          expect(response.status).toEqual(200);
          expect(response.data).toHaveProperty('id');
          expect(response.data).toHaveProperty('type', 'POD_DRIVE');
          expect(response.data).toHaveProperty(
            'subscriptionId',
            '21f11182-6ff3-4941-aae0-25d751d99a38'
          );

          const { rows: afterRows } = await db.query(
            'SELECT * FROM rewards.wallets WHERE user_id = $1',
            [userId]
          );

          expect(afterRows.length).toEqual(1);
          expect(afterRows[0].subscription_id).not.toEqual(
            beforeRows[0].subscription_id
          );
        });
      });

      describe('PATCH /:authId', () => {
        it('transfers a wallet', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: 'b997546f-7007-4ed2-a3ba-619e5542f319',
            }
          );

          expect(createWalletResponse.status).toEqual(200);

          const newUserId = v4();

          const transferWalletResponse = await axios.patch(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              userId: newUserId,
            }
          );

          expect(transferWalletResponse.status).toEqual(200);

          const response = await axios.get(
            `${BASE_URL_NEUTRAL}/reward-wallets/${newUserId}`
          );

          expect(response.status).toEqual(200);
          expect(response.data).toEqual({
            id: createWalletResponse.data.id,
            type: createWalletResponse.data.type,
            subscriptionId: createWalletResponse.data.subscriptionId,
          });
        });
      });

      describe('GET /:authId/rewards', () => {
        it('retrieves rewards details', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: v4(),
            }
          );

          expect(createWalletResponse.status).toBe(200);

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            }
          );

          expect(createTransactionResponse.status).toBe(201);

          const rewardDetailsResponse = await axios.get(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/rewards`
          );

          expect(rewardDetailsResponse.status).toBe(200);
          expect(rewardDetailsResponse.data).toEqual({
            id: expect.any(String),
            type: 'REWARDS',
            balance: {
              amount: 100,
              currency: 'MILES',
            },
          });
        });
      });

      describe('GET /:authId/rewards/transactions', () => {
        it('retrieves rewards details', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: v4(),
            }
          );

          expect(createWalletResponse.status).toBe(200);

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            },
            {
              headers: {
                'x-idempotency-key': '4c6633a4-9ff4-4738-a40d-4f7fbe86afaa',
              },
            }
          );

          expect(createTransactionResponse.status).toBe(201);

          const rewardsTransactionsResponse = await axios.get(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/rewards/transactions`
          );

          expect(rewardsTransactionsResponse.status).toBe(200);
          expect(rewardsTransactionsResponse.data).toEqual({
            transactions: [
              {
                id: expect.any(String),
                amount: 100,
                currency: 'MILES',
                date: expect.any(String),
                status: 'COMPLETED',
                reference: 'REWARDS_ACCRUAL',
                metadata: {
                  chargeId: '4c6633a4-9ff4-4738-a40d-4f7fbe86afaa',
                },
              },
            ],
            meta: {
              lastKey: expect.any(String),
            },
          });
        });
      });

      describe('GET /:authId/allowance', () => {
        it('retrieves allowance details', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: v4(),
            }
          );

          expect(createWalletResponse.status).toBe(200);

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            }
          );

          expect(createTransactionResponse.status).toBe(201);

          const rewardDetailsResponse = await axios.get(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/allowance`
          );

          expect(rewardDetailsResponse.status).toBe(200);
          expect(rewardDetailsResponse.data).toEqual({
            id: expect.any(String),
            type: 'ALLOWANCE',
            balance: {
              amount: 7400,
              currency: 'MILES',
            },
          });
        });
      });

      describe('GET /:authId/allowance/transactions', () => {
        it('retrieves allowance details', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: v4(),
            }
          );

          expect(createWalletResponse.status).toBe(200);

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            },
            {
              headers: {
                'x-idempotency-key': '5500cdf4-2de7-4cc5-afbc-eaa905dc92f1',
              },
            }
          );

          expect(createTransactionResponse.status).toBe(201);

          const rewardsTransactionsResponse = await axios.get(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/allowance/transactions`
          );

          expect(rewardsTransactionsResponse.status).toBe(200);
          expect(rewardsTransactionsResponse.data).toEqual({
            transactions: [
              {
                date: expect.any(String),
                id: expect.any(String),
                amount: -100,
                currency: 'MILES',
                reference: 'REWARDS_ACCRUAL',
                status: 'COMPLETED',
                metadata: {
                  chargeId: '5500cdf4-2de7-4cc5-afbc-eaa905dc92f1',
                },
              },
              {
                date: expect.any(String),
                id: expect.any(String),
                amount: 7500,
                currency: 'MILES',
                reference: 'INITIAL_ALLOWANCE',
                status: 'COMPLETED',
                metadata: null,
              },
            ],
            meta: {
              lastKey: expect.any(String),
            },
          });
        });
      });

      describe('GET /:authId/balances', () => {
        it('retrieves wallet summary balance', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: v4(),
            }
          );

          expect(createWalletResponse.status).toBe(200);

          const createTransactionResponse = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            }
          );

          expect(createTransactionResponse.status).toBe(201);

          const rewardDetailsResponse = await axios.get(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/balance`
          );

          expect(rewardDetailsResponse.status).toBe(200);
          expect(rewardDetailsResponse.data).toEqual({
            allowance: {
              annualAllowanceMiles: 7500,
              balanceMiles: 7400,
            },
            rewards: {
              balanceGbp: 2.28,
              balanceMiles: 100,
            },
          });
        });
      });

      describe('POST /:authId/actions', () => {
        it('processes a REWARD_ACCRUAL', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: 'b997546f-7007-4ed2-a3ba-619e5542f319',
            }
          );

          expect(createWalletResponse.status).toEqual(200);

          const response = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            }
          );

          expect(response.status).toEqual(201);
          expect(response.data).toHaveProperty('id');
          expect(response.data).toHaveProperty('date');
          expect(response.data).toHaveProperty('status', 'COMPLETED');
          expect(response.data).toHaveProperty('currency', 'MILES');
          expect(response.data).toHaveProperty('amount', 100);
          expect(response.data).toHaveProperty('reference', 'REWARDS_ACCRUAL');
        });

        it('does not process a negative REWARD_ACCRUAL', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: 'b997546f-7007-4ed2-a3ba-619e5542f319',
            }
          );

          expect(createWalletResponse.status).toEqual(200);

          try {
            await axios.post(
              `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
              {
                action: 'REWARD_ACCRUAL',
                amount: {
                  amount: -100,
                  currency: 'MILES',
                },
              }
            );
          } catch (error) {
            if (isAxiosError(error)) {
              expect(error.response?.status).toEqual(400);
              expect(error.response?.data).toHaveProperty(
                'message',
                'NEGATIVE_AMOUNT'
              );
            }
          }
        });

        it('processes a REWARD_ACCRUAL with an idempotency key', async () => {
          const idempotencykey = v4();

          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: 'b997546f-7007-4ed2-a3ba-619e5542f319',
            }
          );

          expect(createWalletResponse.status).toEqual(200);

          const response = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            },
            {
              headers: {
                'x-idempotency-key': idempotencykey,
              },
            }
          );

          expect(response.status).toEqual(201);
          expect(response.data).toHaveProperty('id');
          expect(response.data).toHaveProperty('date');
          expect(response.data).toHaveProperty('status', 'COMPLETED');
          expect(response.data).toHaveProperty('currency', 'MILES');
          expect(response.data).toHaveProperty('amount', 100);
          expect(response.data).toHaveProperty('reference', 'REWARDS_ACCRUAL');

          const duplicate = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            },
            {
              headers: {
                'x-idempotency-key': idempotencykey,
              },
            }
          );

          expect(response.data).toEqual(duplicate.data);
        });

        it('processes a REWARD_PAYOUT', async () => {
          const userId = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: 'b997546f-7007-4ed2-a3ba-619e5542f319',
            }
          );

          expect(createWalletResponse.status).toEqual(200);

          await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            }
          );

          const response = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_PAYOUT',
              bankAccountId: '93a03178-ff02-47e9-8cdc-72235425456f',
            }
          );

          expect(response.status).toEqual(201);
          expect(response.data).toHaveProperty('id');
          expect(response.data).toHaveProperty('date');
          expect(response.data).toHaveProperty('status', 'COMPLETED');
          expect(response.data).toHaveProperty('currency', 'MILES');
          expect(response.data).toHaveProperty('amount', 100);
          expect(response.data).toHaveProperty('reference', 'REWARDS_PAYOUT');
        });

        it('processes a REWARD_PAYOUT with an idempotency key', async () => {
          const userId = v4();
          const idempotencyKey = v4();

          const createWalletResponse = await axios.put(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}`,
            {
              type: 'POD_DRIVE',
              subscriptionId: 'b997546f-7007-4ed2-a3ba-619e5542f319',
            }
          );

          expect(createWalletResponse.status).toEqual(200);

          await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_ACCRUAL',
              amount: {
                amount: 100,
                currency: 'MILES',
              },
            }
          );

          const response = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_PAYOUT',
              bankAccountId: '93a03178-ff02-47e9-8cdc-72235425456f',
            },
            {
              headers: {
                'x-idempotency-key': idempotencyKey,
              },
            }
          );

          expect(response.status).toEqual(201);
          expect(response.data).toHaveProperty('id');
          expect(response.data).toHaveProperty('date');
          expect(response.data).toHaveProperty('status', 'COMPLETED');
          expect(response.data).toHaveProperty('currency', 'MILES');
          expect(response.data).toHaveProperty('amount', 100);
          expect(response.data).toHaveProperty('reference', 'REWARDS_PAYOUT');

          const duplicate = await axios.post(
            `${BASE_URL_NEUTRAL}/reward-wallets/${userId}/actions`,
            {
              action: 'REWARD_PAYOUT',
              bankAccountId: '93a03178-ff02-47e9-8cdc-72235425456f',
            },
            {
              headers: {
                'x-idempotency-key': idempotencyKey,
              },
            }
          );

          expect(duplicate.data).toEqual(response.data);
        });
      });
    });
  });
});
