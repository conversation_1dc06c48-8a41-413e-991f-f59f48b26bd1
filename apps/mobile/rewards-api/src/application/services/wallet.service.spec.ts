import { AccountService } from './account.service';
import {
  <PERSON><PERSON>ogger,
  CouldNotFindEntityError,
} from '@experience/mobile/clean-architecture';
import { FatalApplicationError } from '../errors/fatal-application.error';
import {
  MOCK_ALLOWANCE_ACCOUNT_DETAILS_DTO,
  MOCK_REWARDS_ACCOUNT_DETAILS_DTO,
} from '../dto/__fixtures__/account-details.dto.fixtures';
import {
  MOCK_SUBSCRIPTION,
  MOCK_SUBSCRIPTION_PLAN,
} from '../dto/__fixtures__/subscription-plan.dto';
import {
  MOCK_SUBSCRIPTION_ID,
  MOCK_SYSTEM_WALLET_ENTITY,
  MOCK_USER_ID,
  MOCK_WALLET_ENTITY,
} from '../../domain/entities/__fixtures__/wallet.entity.fixtures';
import { MockTransactionProvider } from '@experience/mobile/nest/typeorm-transactions';
import { SubscriptionNotFoundError } from '../errors/subscription-not-found.error';
import { SubscriptionService } from './subscription.service';
import {
  WalletEntity,
  WalletEntityType,
} from '../../domain/entities/wallet.entity';
import { WalletRepositoryInterface } from '../../domain/repositories/wallet.repository.interface';
import { WalletService } from './wallet.service';
import { beforeEach, describe, expect, it } from '@jest/globals';
import { createMock } from '@golevelup/ts-jest';
import { v4 } from 'uuid';

describe(WalletService.name, () => {
  const mockedTransactionProvider = new MockTransactionProvider();

  let mockedLogger: jest.Mocked<CleanLogger>;
  let mockedWalletRepository: jest.Mocked<WalletRepositoryInterface>;
  let mockedAccountService: jest.Mocked<AccountService>;
  let mockedSubscriptionService: jest.Mocked<SubscriptionService>;
  let service: WalletService;

  beforeEach(() => {
    mockedLogger = createMock();
    mockedWalletRepository = createMock();
    mockedAccountService = createMock();
    mockedSubscriptionService = createMock();

    service = new WalletService(
      mockedLogger,
      mockedWalletRepository,
      mockedTransactionProvider,
      mockedAccountService,
      mockedSubscriptionService
    );
  });

  describe(WalletService.prototype.read, () => {
    it('calls the wallet repository with the provided id', async () => {
      const id = 'foo';
      mockedWalletRepository.read.mockResolvedValueOnce(null);

      await service.read(id);

      expect(mockedWalletRepository.read).toHaveBeenCalledWith(id);
    });

    it('returns null if the repository returns null', async () => {
      const id = 'foo';
      mockedWalletRepository.read.mockResolvedValueOnce(null);

      const result = await service.read(id);

      expect(result).toBeNull();
    });

    it('returns the wallet entity if found', async () => {
      const id = 'foo';
      const walletEntity = new WalletEntity({
        id,
        type: WalletEntityType.POD_DRIVE,
        userId: v4(),
        subscriptionId: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });
      mockedWalletRepository.read.mockResolvedValueOnce(walletEntity);

      const result = await service.read(id);

      expect(result).toEqual(walletEntity);
    });
  });

  describe(WalletService.prototype.readByUserId, () => {
    it('calls the wallet repository with the provided id', async () => {
      const id = 'foo';
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(null);

      await service.readByUserId(id);

      expect(mockedWalletRepository.getByUserId).toHaveBeenCalledWith(id);
    });

    it('returns null if the repository returns null', async () => {
      const id = 'foo';
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(null);

      const result = await service.readByUserId(id);

      expect(result).toBeNull();
    });

    it('returns the wallet entity if found', async () => {
      const userId = 'foo';
      const walletEntity = new WalletEntity({
        id: v4(),
        type: WalletEntityType.POD_DRIVE,
        userId,
        subscriptionId: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(walletEntity);

      const result = await service.readByUserId(userId);

      expect(result).toEqual(walletEntity);
    });
  });

  describe(WalletService.prototype.readByUserIdOrFail, () => {
    it('calls the wallet repository with the provided id', async () => {
      const id = 'foo';
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(
        MOCK_WALLET_ENTITY
      );

      expect(await service.readByUserIdOrFail(id)).toEqual(MOCK_WALLET_ENTITY);
      expect(mockedWalletRepository.getByUserId).toHaveBeenCalledWith(id);
    });

    it('throws an error if the repository returns null', async () => {
      const id = 'foo';
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(null);

      await expect(service.readByUserIdOrFail(id)).rejects.toThrow(
        CouldNotFindEntityError
      );
    });
  });

  describe(WalletService.prototype.getWalletBalanceSummary, () => {
    it('rethrows CouldNotFindEntityError from account service get account details for allowance account', async () => {
      const error = new CouldNotFindEntityError({ id: 'test' }, 'oops');

      mockedAccountService.getAccountDetails.mockRejectedValueOnce(error);

      await expect(
        service.getWalletBalanceSummary(MOCK_USER_ID)
      ).rejects.toThrow(error);
    });

    it('rethrows CouldNotFindEntityError from account service get account details for rewards account', async () => {
      const error = new CouldNotFindEntityError({ id: 'test' }, 'oops');

      mockedAccountService.getAccountDetails.mockResolvedValueOnce(
        MOCK_ALLOWANCE_ACCOUNT_DETAILS_DTO
      );
      mockedAccountService.getAccountDetails.mockRejectedValueOnce(error);

      await expect(
        service.getWalletBalanceSummary(MOCK_USER_ID)
      ).rejects.toThrow(error);
    });

    it('rethrows SubscriptionNotFoundError from subscription service get subscription plan by user id', async () => {
      const error = new SubscriptionNotFoundError('uh oh');

      mockedAccountService.getAccountDetails.mockResolvedValueOnce(
        MOCK_ALLOWANCE_ACCOUNT_DETAILS_DTO
      );
      mockedAccountService.getAccountDetails.mockResolvedValueOnce(
        MOCK_REWARDS_ACCOUNT_DETAILS_DTO
      );
      mockedSubscriptionService.getSubscriptionByUserId.mockRejectedValueOnce(
        error
      );

      await expect(
        service.getWalletBalanceSummary(MOCK_USER_ID)
      ).rejects.toThrow(error);
    });

    it('rethrows FatalApplicationError from subscription service get subscription plan by user id', async () => {
      const error = new FatalApplicationError('uh oh');

      mockedAccountService.getAccountDetails.mockResolvedValueOnce(
        MOCK_ALLOWANCE_ACCOUNT_DETAILS_DTO
      );
      mockedAccountService.getAccountDetails.mockResolvedValueOnce(
        MOCK_REWARDS_ACCOUNT_DETAILS_DTO
      );
      mockedSubscriptionService.getSubscriptionByUserId.mockRejectedValueOnce(
        error
      );

      await expect(
        service.getWalletBalanceSummary(MOCK_USER_ID)
      ).rejects.toThrow(error);
    });

    it('returns wallet balance summary', async () => {
      mockedAccountService.getAccountDetails.mockResolvedValueOnce(
        MOCK_ALLOWANCE_ACCOUNT_DETAILS_DTO
      );
      mockedAccountService.getAccountDetails.mockResolvedValueOnce({
        ...MOCK_REWARDS_ACCOUNT_DETAILS_DTO,
        balance: {
          ...MOCK_REWARDS_ACCOUNT_DETAILS_DTO.balance,
          amount: 474,
        },
      });

      mockedSubscriptionService.getSubscriptionByUserId.mockResolvedValueOnce({
        id: MOCK_SUBSCRIPTION.id,
        plan: { ...MOCK_SUBSCRIPTION_PLAN, ratePencePerMile: 2.28 },
      });

      expect(await service.getWalletBalanceSummary(MOCK_USER_ID)).toEqual({
        allowance: {
          annualAllowanceMiles: 7500,
          balanceMiles: 1000,
        },
        rewards: {
          balanceGbp: 10.81,
          balanceMiles: 474,
        },
      });
    });
  });

  describe(WalletService.prototype.upsertWallet, () => {
    it('throws a SubscriptionNotFoundError if no corresponding subscription found', async () => {
      mockedSubscriptionService.getSubscriptionPlanById.mockRejectedValue(
        new SubscriptionNotFoundError()
      );

      await expect(
        service.upsertWallet({
          userId: MOCK_USER_ID,
          subscriptionId: MOCK_SUBSCRIPTION_ID,
          type: WalletEntityType.POD_DRIVE,
        })
      ).rejects.toThrow(SubscriptionNotFoundError);

      expect(mockedWalletRepository.create).not.toHaveBeenCalled();
      expect(mockedWalletRepository.update).not.toHaveBeenCalled();
      expect(
        mockedAccountService.createDefaultUserAccounts
      ).not.toHaveBeenCalled();
    });

    it('creates a new wallet with default accounts if no wallet exists for user', async () => {
      mockedSubscriptionService.getSubscriptionPlanById.mockResolvedValue(
        MOCK_SUBSCRIPTION_PLAN
      );
      mockedWalletRepository.getByUserId.mockResolvedValue(null);
      mockedWalletRepository.create.mockResolvedValue(MOCK_WALLET_ENTITY);
      mockedAccountService.createDefaultUserAccounts.mockResolvedValue();

      await service.upsertWallet({
        userId: MOCK_USER_ID,
        subscriptionId: MOCK_SUBSCRIPTION_ID,
        type: WalletEntityType.POD_DRIVE,
      });

      expect(mockedWalletRepository.getByUserId).toHaveBeenCalledTimes(1);
      expect(mockedWalletRepository.getByUserId).toHaveBeenCalledWith(
        MOCK_WALLET_ENTITY.userId
      );

      expect(mockedWalletRepository.create).toHaveBeenCalledTimes(1);
      expect(mockedWalletRepository.create).toHaveBeenCalledWith({
        userId: MOCK_WALLET_ENTITY.userId,
        subscriptionId: MOCK_WALLET_ENTITY.subscriptionId,
        type: MOCK_WALLET_ENTITY.type,
      });

      expect(
        mockedAccountService.createDefaultUserAccounts
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedAccountService.createDefaultUserAccounts
      ).toHaveBeenCalledWith(MOCK_WALLET_ENTITY.id, MOCK_SUBSCRIPTION_PLAN);

      expect(mockedWalletRepository.update).not.toHaveBeenCalled();
    });

    it('updates and returns a wallet if already exists for user', async () => {
      const NEW_SUBSCRIPTION_ID = '51e53315-3461-48d7-940c-05ff978ba545';

      mockedSubscriptionService.getSubscriptionPlanById.mockResolvedValue(
        MOCK_SUBSCRIPTION_PLAN
      );
      mockedWalletRepository.getByUserId.mockResolvedValue(MOCK_WALLET_ENTITY);
      mockedWalletRepository.update.mockResolvedValue({
        ...MOCK_WALLET_ENTITY,
        subscriptionId: NEW_SUBSCRIPTION_ID,
      });

      await service.upsertWallet({
        userId: MOCK_USER_ID,
        subscriptionId: NEW_SUBSCRIPTION_ID,
        type: WalletEntityType.POD_DRIVE,
      });

      expect(mockedWalletRepository.update).toHaveBeenCalledTimes(1);
      expect(mockedWalletRepository.update).toHaveBeenCalledWith(
        MOCK_WALLET_ENTITY,
        {
          subscriptionId: NEW_SUBSCRIPTION_ID,
          type: WalletEntityType.POD_DRIVE,
        }
      );

      expect(mockedWalletRepository.update).toHaveBeenCalledTimes(1);
      expect(mockedWalletRepository.update).toHaveBeenCalledWith(
        MOCK_WALLET_ENTITY,
        {
          subscriptionId: NEW_SUBSCRIPTION_ID,
          type: WalletEntityType.POD_DRIVE,
        }
      );

      expect(mockedWalletRepository.create).not.toHaveBeenCalled();
      expect(
        mockedAccountService.createDefaultUserAccounts
      ).not.toHaveBeenCalled();
    });
  });

  describe(WalletService.prototype.transferWallet, () => {
    it('throws a CouldNotFindEntityError if wallet can not be found', async () => {
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(null);

      await expect(
        service.transferWallet(MOCK_USER_ID, { userId: MOCK_USER_ID })
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws a FatalApplicationError if destination user already has a wallet', async () => {
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(
        MOCK_WALLET_ENTITY
      );
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(
        MOCK_WALLET_ENTITY
      );

      await expect(
        service.transferWallet(MOCK_USER_ID, { userId: MOCK_USER_ID })
      ).rejects.toThrow(FatalApplicationError);
    });

    it('updates the user id on the origin wallet', async () => {
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(
        MOCK_WALLET_ENTITY
      );
      mockedWalletRepository.getByUserId.mockResolvedValueOnce(null);

      const userId = v4();

      await service.transferWallet(MOCK_USER_ID, { userId });

      expect(mockedWalletRepository.update).toHaveBeenCalledTimes(1);
      expect(mockedWalletRepository.update).toHaveBeenCalledWith(
        MOCK_WALLET_ENTITY,
        {
          userId,
        }
      );
    });
  });

  describe(WalletService.prototype.getSystemWallet, () => {
    it('should get the system wallet', async () => {
      mockedWalletRepository.getByType.mockResolvedValueOnce([
        MOCK_SYSTEM_WALLET_ENTITY,
      ]);

      const result = await service.getSystemWallet();

      expect(result).toEqual(MOCK_SYSTEM_WALLET_ENTITY);
    });

    it('should fail if no system wallet is found', async () => {
      mockedWalletRepository.getByType.mockResolvedValueOnce([]);

      await expect(service.getSystemWallet()).rejects.toThrow(
        CouldNotFindEntityError
      );
    });

    it('should fail if more than one system wallet is found', async () => {
      mockedWalletRepository.getByType.mockResolvedValueOnce([
        MOCK_SYSTEM_WALLET_ENTITY,
        { ...MOCK_SYSTEM_WALLET_ENTITY, id: v4() },
      ]);

      await expect(service.getSystemWallet()).rejects.toThrow(
        FatalApplicationError
      );
    });
  });
});
