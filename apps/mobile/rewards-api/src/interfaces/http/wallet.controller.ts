import { AccountService } from '../../application/services/account.service';
import { AccountTransactionDTO } from './dto/account-transaction.dto';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiHeader,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnprocessableEntityResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  Headers,
  Logger,
  Param,
  Patch,
  Post,
  Put,
  UseInterceptors,
} from '@nestjs/common';
import { HttpCodeInterceptor } from './interceptors/http-code.interceptor';
import { PersistedWalletHttpDto } from './dto/persisted-wallet.dto';
import { TransferWalletDTO } from './dto/transfer-wallet.dto';
import { UpsertWalletDTO } from './dto/upsert-wallet.dto';
import {
  WalletAction,
  WalletActionAccrualDTO,
  WalletActionPayoutDTO,
} from './dto/wallet-action.dto';
import { WalletActionDtoTransformerPipe } from './dto/wallet-action.dto.transformer.pipe';
import { WalletBalanceSummaryHttpDTO } from './dto/wallet-balance-summary';
import { WalletService } from '../../application/services/wallet.service';
import { v4 } from 'uuid';

@ApiTags('Reward Wallets')
@Controller('reward-wallets')
@UseInterceptors(HttpCodeInterceptor)
export class WalletController {
  private readonly logger = new Logger(WalletController.name);

  constructor(
    private readonly walletService: WalletService,
    private readonly accountService: AccountService
  ) {}

  @Get(':userId')
  @ApiOkResponse({
    type: PersistedWalletHttpDto,
  })
  @ApiNotFoundResponse({
    description: 'wallet not found',
  })
  @ApiInternalServerErrorResponse({
    description: 'internal error',
  })
  async read(
    @Param('userId')
    userId: string
  ): Promise<PersistedWalletHttpDto> {
    return PersistedWalletHttpDto.from(
      await this.walletService.readByUserIdOrFail(userId)
    );
  }

  @Put(':userId')
  @ApiOperation({
    summary: 'Upsert Wallet',
    description: 'For a given user, create or update a wallet',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user the wallet belongs to',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'The successfully created or updated wallet',
    type: PersistedWalletHttpDto,
  })
  @ApiBadRequestResponse({
    description:
      'Returned when the request body is invalid or subscription does not exist',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when something unexpected occurs',
  })
  async upsertWallet(
    @Param('userId') userId: string,
    @Body() body: UpsertWalletDTO
  ) {
    this.logger.log({ userId, ...body }, 'upserting wallet');

    const wallet = await this.walletService.upsertWallet({
      userId,
      ...body,
    });

    return PersistedWalletHttpDto.from(wallet);
  }

  @Patch(':userId')
  @ApiOperation({
    summary: 'Transfer Wallet',
    description: 'Transfer a wallet to a given user id',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user the wallet belongs to',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'The successfully updated wallet',
    type: PersistedWalletHttpDto,
  })
  @ApiBadRequestResponse({
    description: 'Returned when the request body is invalid',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when something unexpected occurs',
  })
  async transferWallet(
    @Param('userId') userId: string,
    @Body() body: TransferWalletDTO
  ) {
    return PersistedWalletHttpDto.from(
      await this.walletService.transferWallet(userId, body)
    );
  }

  @Post(':userId/actions')
  @ApiOperation({
    summary: 'Perform Wallet Action',
    description:
      'Based on the request payload, perform an action on the given wallet',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user the wallet belongs to',
    format: 'uuid',
  })
  @ApiExtraModels(WalletActionAccrualDTO, WalletActionPayoutDTO)
  @ApiBody({
    schema: {
      oneOf: [
        { $ref: getSchemaPath(WalletActionAccrualDTO) },
        { $ref: getSchemaPath(WalletActionPayoutDTO) },
      ],
    },
  })
  @ApiHeader({
    name: 'x-idempotency-key',
    required: false,
    description:
      'The idempotency key to use with this operation.  One will be generated if not provided',
    example: 'c8698090-963e-4858-9395-e40345404b7e',
  })
  @ApiCreatedResponse({
    description: 'The successfully processed acton',
    type: AccountTransactionDTO,
  })
  @ApiBadRequestResponse({
    description: 'Returned when the request body is invalid',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when something unexpected occurs',
  })
  @ApiNotFoundResponse()
  @ApiUnprocessableEntityResponse()
  async handleAction(
    @Param('userId') userId: string,
    @Body(
      new WalletActionDtoTransformerPipe(
        WalletActionAccrualDTO,
        WalletActionPayoutDTO
      )
    )
    body: WalletActionAccrualDTO | WalletActionPayoutDTO,
    @Headers('x-idempotency-key') idempotencyKey?: string
  ): Promise<AccountTransactionDTO> {
    this.logger.log(
      { userId, idempotencyKey, ...body },
      'handling wallet action'
    );

    idempotencyKey = idempotencyKey ?? v4();

    switch (body.action) {
      case WalletAction.REWARD_ACCRUAL:
        return AccountTransactionDTO.from(
          await this.accountService.processRewardAccrual(userId, {
            ...body.amount,
            idempotencyKey,
          })
        );
      case WalletAction.REWARD_PAYOUT:
        return AccountTransactionDTO.from(
          await this.accountService.processRewardPayout(
            userId,
            body.bankAccountId,
            idempotencyKey
          )
        );
    }
  }

  @Get(':userId/balance')
  @ApiOkResponse({
    type: WalletBalanceSummaryHttpDTO,
    description: 'Wallet balance summary',
  })
  @ApiNotFoundResponse({
    description: 'Wallet or account not found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal error',
  })
  async getWalletBalanceSummary(
    @Param('userId') userId: string
  ): Promise<WalletBalanceSummaryHttpDTO> {
    return WalletBalanceSummaryHttpDTO.from(
      await this.walletService.getWalletBalanceSummary(userId)
    );
  }
}
