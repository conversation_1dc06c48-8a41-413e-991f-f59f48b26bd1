import { AccountService } from '../../application/services/account.service';
import { AccountTransactionDTO } from './dto/account-transaction.dto';
import { CouldNotFindEntityError } from '@experience/mobile/clean-architecture';
import {
  MOCK_ALLOWANCE_TRANSACTION_ENTITY_IN,
  MOCK_ALLOWANCE_TRANSACTION_ENTITY_OUT,
} from '../../domain/entities/__fixtures__/transaction.entity.fixtures';
import {
  MOCK_BANK_ACCOUNT_ID,
  MOCK_SUBSCRIPTION_ID,
  MOCK_USER_ID,
  MOCK_WALLET_ENTITY,
} from '../../domain/entities/__fixtures__/wallet.entity.fixtures';
import { MOCK_WALLET_BALANCE_SUMMARY_DTO } from '../../application/dto/__fixtures__/walance-balance-summary.dto';
import { PersistedWalletHttpDto } from './dto/persisted-wallet.dto';
import {
  TransactionEntityCurrency,
  TwoWayTransaction,
} from '../../domain/entities/transaction.entity';
import { WalletAction } from './dto/wallet-action.dto';
import { WalletController } from './wallet.controller';
import {
  WalletEntity,
  WalletEntityType,
} from '../../domain/entities/wallet.entity';
import { WalletService } from '../../application/services/wallet.service';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { createMock } from '@golevelup/ts-jest';
import { v4 } from 'uuid';

describe(WalletController.name, () => {
  let mockedWalletService: jest.Mocked<WalletService>;
  let mockedAccountService: jest.Mocked<AccountService>;
  let controller: WalletController;

  beforeEach(() => {
    mockedWalletService = createMock();
    mockedAccountService = createMock();

    controller = new WalletController(
      mockedWalletService,
      mockedAccountService
    );
  });

  describe(WalletController.prototype.read, () => {
    it('throws NotFoundException if the service returns null', async () => {
      mockedWalletService.readByUserIdOrFail.mockRejectedValueOnce(
        new CouldNotFindEntityError({ id: 'foo' }, 'wallet not found')
      );

      await expect(controller.read('foo')).rejects.toThrow(
        CouldNotFindEntityError
      );
    });

    it('returns the presented wallet if it is found', async () => {
      mockedWalletService.readByUserIdOrFail.mockResolvedValueOnce(
        MOCK_WALLET_ENTITY
      );

      const expected = PersistedWalletHttpDto.from(MOCK_WALLET_ENTITY);

      const actual = await controller.read('foo');

      expect(actual).toEqual(expected);
    });
  });

  describe(WalletController.prototype.upsertWallet, () => {
    it('calls the wallet service and returns the persisted wallet', async () => {
      const persistedWalletFromSpy = jest.spyOn(PersistedWalletHttpDto, 'from');

      mockedWalletService.upsertWallet.mockResolvedValue(MOCK_WALLET_ENTITY);

      const res = await controller.upsertWallet(MOCK_USER_ID, {
        type: WalletEntityType.POD_DRIVE,
        subscriptionId: MOCK_SUBSCRIPTION_ID,
      });

      expect(res).toBeInstanceOf(PersistedWalletHttpDto);

      expect(mockedWalletService.upsertWallet).toHaveBeenCalledTimes(1);
      expect(mockedWalletService.upsertWallet).toHaveBeenCalledWith({
        userId: MOCK_USER_ID,
        type: WalletEntityType.POD_DRIVE,
        subscriptionId: MOCK_SUBSCRIPTION_ID,
      });

      expect(persistedWalletFromSpy).toHaveBeenCalledTimes(1);
      expect(persistedWalletFromSpy).toHaveBeenCalledWith(
        (await mockedWalletService.upsertWallet.mock.results[0]
          .value) as WalletEntity
      );
    });
  });

  describe(WalletController.prototype.transferWallet, () => {
    it('transfers wallet from a user to a user', async () => {
      mockedWalletService.transferWallet.mockResolvedValueOnce(
        MOCK_WALLET_ENTITY
      );

      const userId = v4();

      const res = await controller.transferWallet(MOCK_USER_ID, {
        userId,
      });

      expect(res).toEqual({
        id: MOCK_WALLET_ENTITY.id,
        type: MOCK_WALLET_ENTITY.type,
        subscriptionId: MOCK_WALLET_ENTITY.subscriptionId,
      });
    });
  });

  describe(WalletController.prototype.handleAction, () => {
    it('processes reward accrual actions ', async () => {
      const accountTransactionDTOFromSpy = jest.spyOn(
        AccountTransactionDTO,
        'from'
      );

      mockedAccountService.processRewardAccrual.mockResolvedValue([
        MOCK_ALLOWANCE_TRANSACTION_ENTITY_IN,
        MOCK_ALLOWANCE_TRANSACTION_ENTITY_OUT,
      ]);

      const res = await controller.handleAction(MOCK_USER_ID, {
        action: WalletAction.REWARD_ACCRUAL,
        amount: {
          amount: 100,
          currency: TransactionEntityCurrency.MILES,
        },
      });

      expect(res).toBeInstanceOf(AccountTransactionDTO);

      expect(mockedAccountService.processRewardAccrual).toHaveBeenCalledTimes(
        1
      );
      expect(mockedAccountService.processRewardAccrual).toHaveBeenCalledWith(
        MOCK_USER_ID,
        {
          amount: 100,
          currency: TransactionEntityCurrency.MILES,
          idempotencyKey: expect.any(String),
        }
      );

      expect(accountTransactionDTOFromSpy).toHaveBeenCalledTimes(1);
      expect(accountTransactionDTOFromSpy).toHaveBeenCalledWith(
        (await mockedAccountService.processRewardAccrual.mock.results[0]
          .value) as TwoWayTransaction
      );
    });

    it('processes reward accrual actions with defined idempotency key', async () => {
      const accountTransactionDTOFromSpy = jest.spyOn(
        AccountTransactionDTO,
        'from'
      );

      const idempotencyKey = v4();

      mockedAccountService.processRewardAccrual.mockResolvedValue([
        MOCK_ALLOWANCE_TRANSACTION_ENTITY_IN,
        MOCK_ALLOWANCE_TRANSACTION_ENTITY_OUT,
      ]);

      const res = await controller.handleAction(
        MOCK_USER_ID,
        {
          action: WalletAction.REWARD_ACCRUAL,
          amount: {
            amount: 100,
            currency: TransactionEntityCurrency.MILES,
          },
        },
        idempotencyKey
      );

      expect(res).toBeInstanceOf(AccountTransactionDTO);

      expect(mockedAccountService.processRewardAccrual).toHaveBeenCalledTimes(
        1
      );
      expect(mockedAccountService.processRewardAccrual).toHaveBeenCalledWith(
        MOCK_USER_ID,
        {
          amount: 100,
          currency: TransactionEntityCurrency.MILES,
          idempotencyKey: idempotencyKey,
        }
      );

      expect(accountTransactionDTOFromSpy).toHaveBeenCalledTimes(1);
      expect(accountTransactionDTOFromSpy).toHaveBeenCalledWith(
        (await mockedAccountService.processRewardAccrual.mock.results[0]
          .value) as TwoWayTransaction
      );
    });

    it('processes reward withdrawal action', async () => {
      mockedAccountService.processRewardPayout.mockResolvedValue([
        MOCK_ALLOWANCE_TRANSACTION_ENTITY_IN,
        MOCK_ALLOWANCE_TRANSACTION_ENTITY_OUT,
      ]);

      const result = await controller.handleAction(MOCK_USER_ID, {
        action: WalletAction.REWARD_PAYOUT,
        bankAccountId: MOCK_BANK_ACCOUNT_ID,
      });

      expect(result).toBeInstanceOf(AccountTransactionDTO);
      expect(mockedAccountService.processRewardPayout).toHaveBeenCalledTimes(1);
      expect(mockedAccountService.processRewardPayout).toHaveBeenCalledWith(
        MOCK_USER_ID,
        MOCK_BANK_ACCOUNT_ID,
        expect.any(String)
      );
    });

    it('processes reward withdrawal action with defined idempotency key', async () => {
      const idempotencyKey = v4();

      mockedAccountService.processRewardPayout.mockResolvedValue([
        MOCK_ALLOWANCE_TRANSACTION_ENTITY_IN,
        MOCK_ALLOWANCE_TRANSACTION_ENTITY_OUT,
      ]);

      const result = await controller.handleAction(
        MOCK_USER_ID,
        {
          action: WalletAction.REWARD_PAYOUT,
          bankAccountId: MOCK_BANK_ACCOUNT_ID,
        },
        idempotencyKey
      );

      expect(result).toBeInstanceOf(AccountTransactionDTO);
      expect(mockedAccountService.processRewardPayout).toHaveBeenCalledTimes(1);
      expect(mockedAccountService.processRewardPayout).toHaveBeenCalledWith(
        MOCK_USER_ID,
        MOCK_BANK_ACCOUNT_ID,
        idempotencyKey
      );
    });
  });

  describe(WalletController.prototype.getWalletBalanceSummary, () => {
    it('rethrows error thrown from wallet service', async () => {
      const error = new CouldNotFindEntityError({ id: 'test' }, 'oops');

      mockedWalletService.getWalletBalanceSummary.mockRejectedValueOnce(error);

      await expect(
        controller.getWalletBalanceSummary(MOCK_USER_ID)
      ).rejects.toThrow(error);
    });

    it('returns wallet balance summary from service', async () => {
      mockedWalletService.getWalletBalanceSummary.mockResolvedValueOnce(
        MOCK_WALLET_BALANCE_SUMMARY_DTO
      );

      expect(await controller.getWalletBalanceSummary(MOCK_USER_ID)).toEqual(
        MOCK_WALLET_BALANCE_SUMMARY_DTO
      );
    });
  });
});
