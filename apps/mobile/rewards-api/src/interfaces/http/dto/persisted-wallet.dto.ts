import { ApiProperty } from '@nestjs/swagger';
import { WalletEntity } from '../../../domain/entities/wallet.entity';
import { WalletToHttpWalletTransformer } from './wallet-to-http-wallet.transformer';

export class PersistedWalletHttpDto {
  static from(entity: WalletEntity): PersistedWalletHttpDto {
    return new WalletToHttpWalletTransformer().transform(entity);
  }

  @ApiProperty({
    description: 'The id of the wallet',
    example: '2accb786-b998-4d32-9cbf-c473b7402686',
    type: String,
  })
  id: string;

  @ApiProperty({
    description: 'The type of the wallet',
    example: 'POD_DRIVE',
    type: String,
  })
  type: string;

  @ApiProperty({
    description: 'The id of the subscription associated with the wallet',
    example: '2accb786-b998-4d32-9cbf-c473b7402686',
    type: String,
    nullable: true,
  })
  subscriptionId: string | null;
}
