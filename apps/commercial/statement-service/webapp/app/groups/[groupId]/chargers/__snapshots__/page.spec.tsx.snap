// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Chargers page should match snapshot 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Manage chargers on subscription - Test Group Inc.
        </h1>
      </div>
    </header>
    <div
      class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Edit chargers
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                <div
                  class=""
                >
                  <label
                    class="text-md font-bold sr-only block mb-2"
                    for="select-all"
                  >
                    Select All
                  </label>
                </div>
                <input
                  class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral"
                  id="select-all"
                  type="checkbox"
                  value=""
                />
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Name
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                PPID
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Socket
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Site location
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class=""
                >
                  <label
                    class="text-md font-bold sr-only block mb-2"
                    for="select-PG-70500-undefined"
                  >
                    Select PG-70500 undefined
                  </label>
                </div>
                <input
                  class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -mt-1"
                  id="select-PG-70500-undefined"
                  type="checkbox"
                  value=""
                />
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              />
              <td
                class="whitespace-normal px-3 py-4"
              >
                PG-70500
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              />
              <td
                class="whitespace-normal px-3 py-4"
              />
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="flex justify-end items-center"
    >
      <button
        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
        type="button"
      >
        Save changes
      </button>
    </div>
  </div>
</body>
`;
