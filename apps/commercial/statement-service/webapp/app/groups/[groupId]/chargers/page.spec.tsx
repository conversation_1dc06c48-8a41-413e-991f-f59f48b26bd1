import {
  TEST_CHARGER_WITH_POD,
  TEST_GROUP,
} from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { render } from '@testing-library/react';
import Chargers from './page';

jest.mock('next/navigation');
jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Chargers page', () => {
  const defaultParams = new Promise<{ groupId: string }>((resolve) => {
    resolve({ groupId: TEST_GROUP.groupId });
  });

  beforeEach(() => {
    mockRequestHandler
      .mockResolvedValueOnce(TEST_GROUP)
      .mockResolvedValueOnce([TEST_CHARGER_WITH_POD]);
  });

  it('should render successfully', async () => {
    const page = await Chargers({ params: defaultParams });
    expect(page).toBeDefined();
    const { baseElement } = render(page);
    expect(baseElement).toBeTruthy();

    expect(mockRequestHandler).toHaveBeenNthCalledWith(
      1,
      `http://localhost:5102/groups/${TEST_GROUP.groupId}`
    );
    expect(mockRequestHandler).toHaveBeenNthCalledWith(
      2,
      `http://localhost:5102/groups/${TEST_GROUP.groupId}/chargers`
    );
  });

  it('should match snapshot', async () => {
    const page = await Chargers({ params: defaultParams });
    expect(page).toBeDefined();
    const { baseElement } = render(page);
    expect(baseElement).toMatchSnapshot();
  });
});
