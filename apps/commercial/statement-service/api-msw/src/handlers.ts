/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

// Map to store counters for each API endpoint
const apiCounters = new Map();

const next = (apiKey) => {
  let currentCount = apiCounters.get(apiKey) ?? 0;
  if (currentCount === Number.MAX_SAFE_INTEGER - 1) {
    currentCount = 0;
  }
  apiCounters.set(apiKey, currentCount + 1);
  return currentCount;
};

export const handlers = [
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [getHealthControllerCheck200Response(), { status: 200 }],
      [getHealthControllerCheck503Response(), { status: 503 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /health`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/invoices/:invoiceId`, async () => {
    const resultArray = [
      [getInvoiceControllerFindInvoiceById200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /invoices/:invoiceId`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/invoices/:invoiceId/pdf`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /invoices/:invoiceId/pdf`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/invoices/export/ifs`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /invoices/export/ifs`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/invoices/reissue`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /invoices/reissue`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/invoices/:invoiceId/status`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`put /invoices/:invoiceId/status`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/groups`, async () => {
    const resultArray = [
      [getGroupsControllerFindAllGroups200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /groups`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/groups`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /groups`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/groups/site-admin`, async () => {
    const resultArray = [
      [getGroupsControllerFindSiteAdminGroups200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /groups/site-admin`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/groups/:groupId`, async () => {
    const resultArray = [
      [getGroupsControllerFindGroup200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /groups/:groupId`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/groups/:groupId`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /groups/:groupId`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/groups/:groupId/documents`, async () => {
    const resultArray = [
      [
        getDocumentsControllerFindDocumentsByGroupId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /groups/:groupId/documents`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/groups/:groupId/documents`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /groups/:groupId/documents`) % resultArray.length
      ]
    );
  }),
  http.get(
    `${baseURL}/groups/:groupId/documents/:documentId/download`,
    async () => {
      const resultArray = [[undefined, { status: 200 }]];

      return HttpResponse.json(
        ...resultArray[
          next(`get /groups/:groupId/documents/:documentId/download`) %
            resultArray.length
        ]
      );
    }
  ),
  http.get(`${baseURL}/groups/:groupId/statements`, async () => {
    const resultArray = [
      [
        getGroupsStatementsControllerFindStatementsByGroupId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /groups/:groupId/statements`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/groups/:groupId/subscription/chargers`, async () => {
    const resultArray = [
      [
        getSubscriptionControllerFindSubscriptionChargersByGroupId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /groups/:groupId/subscription/chargers`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/groups/:groupId/chargers`, async () => {
    const resultArray = [
      [
        getChargersControllerFindChargersByGroupId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /groups/:groupId/chargers`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/groups/:groupId/chargers`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /groups/:groupId/chargers`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/sites/:siteId`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /sites/:siteId`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/sites/:siteId/chargers`, async () => {
    const resultArray = [
      [
        getSitesControllerFindChargerConfigsWithPods200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /sites/:siteId/chargers`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/sites/:siteId/chargers`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /sites/:siteId/chargers`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/statements`, async () => {
    const resultArray = [
      [getStatementControllerFindAllStatements200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /statements`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/statements`, async () => {
    const resultArray = [
      [getStatementControllerCreateStatement201Response(), { status: 201 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /statements`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/statements/:statementId`, async () => {
    const resultArray = [
      [getStatementControllerFindStatement200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /statements/:statementId`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/statements/:statementId/pdf`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`get /statements/:statementId/pdf`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/stripe/customers`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /stripe/customers`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/stripe/connected-accounts`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /stripe/connected-accounts`) % resultArray.length
      ]
    );
  }),
  http.delete(`${baseURL}/stripe/connected-accounts`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /stripe/connected-accounts`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/stripe/connected-accounts/account-links`, async () => {
    const resultArray = [
      [
        getStripeControllerCreateStripeConnectedAccountLink201Response(),
        { status: 201 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`post /stripe/connected-accounts/account-links`) %
          resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/stripe/subscriptions`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /stripe/subscriptions`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/stripe/subscriptions`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /stripe/subscriptions`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/stripe/subscriptions/:subscriptionId`, async () => {
    const resultArray = [
      [getStripeControllerGetSubscription200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /stripe/subscriptions/:subscriptionId`) % resultArray.length
      ]
    );
  }),
  http.get(
    `${baseURL}/stripe/subscriptions/:subscriptionId/invoices`,
    async () => {
      const resultArray = [
        [
          getStripeControllerGetInvoicesBySubscriptionId200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`get /stripe/subscriptions/:subscriptionId/invoices`) %
            resultArray.length
        ]
      );
    }
  ),
  http.get(
    `${baseURL}/stripe/subscriptions/:subscriptionId/invoices/:invoiceId`,
    async () => {
      const resultArray = [
        [
          getStripeControllerGetSubscriptionInvoice200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(
            `get /stripe/subscriptions/:subscriptionId/invoices/:invoiceId`
          ) % resultArray.length
        ]
      );
    }
  ),
  http.get(
    `${baseURL}/stripe/subscriptions/:subscriptionId/invoices/:invoiceId/pdf`,
    async () => {
      const resultArray = [[undefined, { status: 200 }]];

      return HttpResponse.json(
        ...resultArray[
          next(
            `get /stripe/subscriptions/:subscriptionId/invoices/:invoiceId/pdf`
          ) % resultArray.length
        ]
      );
    }
  ),
  http.post(`${baseURL}/stripe/webhooks`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /stripe/webhooks`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/stripe/webhooks/connect`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /stripe/webhooks/connect`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/work-items`, async () => {
    const resultArray = [
      [getWorkItemControllerFindAllWorkItems200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /work-items`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/work-items/stats`, async () => {
    const resultArray = [
      [getWorkItemControllerCountWorkItems200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /work-items/stats`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/work-items/:workItemId`, async () => {
    const resultArray = [
      [getWorkItemControllerFindWorkItem200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /work-items/:workItemId`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/work-items/:workItemId/user`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /work-items/:workItemId/user`) % resultArray.length
      ]
    );
  }),
  http.delete(`${baseURL}/work-items/:workItemId/user`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /work-items/:workItemId/user`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/work-items/:workItemId/automated`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`put /work-items/:workItemId/automated`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/work-items/:workItemId/status`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`put /work-items/:workItemId/status`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/work-items/:workItemId/statement`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`put /work-items/:workItemId/statement`) % resultArray.length
      ]
    );
  }),
];

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}

export function getHealthControllerCheck503Response() {
  return {
    status: 'error',
    info: { database: { status: 'up' } },
    error: { redis: { status: 'down', message: 'Could not connect' } },
    details: {
      database: { status: 'up' },
      redis: { status: 'down', message: 'Could not connect' },
    },
  };
}

export function getInvoiceControllerFindInvoiceById200Response() {
  return {
    id: faker.string.uuid(),
    invoiceNumber: faker.lorem.words(),
    quoteNumber: faker.lorem.words(),
    invoiceDate: faker.lorem.words(),
    group: {
      accountRef: faker.lorem.words(),
      addressLine1: faker.lorem.words(),
      addressLine2: faker.lorem.words(),
      businessEmail: faker.internet.email(),
      businessName: faker.person.fullName(),
      county: faker.lorem.words(),
      groupId: faker.string.uuid(),
      groupName: faker.person.fullName(),
      hasContractDocument: faker.datatype.boolean(),
      poNumber: faker.lorem.words(),
      postcode: faker.lorem.words(),
      sites: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        automated: faker.datatype.boolean(),
        emails: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => faker.lorem.words()),
        groupId: faker.string.uuid(),
        id: faker.string.uuid(),
        siteId: faker.string.uuid(),
        siteName: faker.person.fullName(),
      })),
      stripeConnectedAccountId: faker.string.uuid(),
      stripeCustomerId: faker.string.uuid(),
      stripeSubscriptionId: faker.string.uuid(),
      stripeSubscriptionStatus: faker.lorem.words(),
      town: faker.lorem.words(),
      transfersEnabled: faker.datatype.boolean(),
      vatRegistered: faker.datatype.boolean(),
    },
    site: {
      automated: faker.datatype.boolean(),
      emails: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.lorem.words()),
      groupId: faker.string.uuid(),
      id: faker.string.uuid(),
      siteId: faker.string.uuid(),
      siteName: faker.person.fullName(),
    },
    statement: {
      id: faker.string.uuid(),
      adjustedFees: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        fee: faker.number.int(),
        ppid: faker.string.uuid(),
      })),
      automaticPayout: faker.datatype.boolean(),
      emails: faker.internet.email(),
      energy: {},
      fees: {},
      groupUid: faker.string.uuid(),
      invoice: {},
      numberOfCharges: faker.number.int(),
      payoutStatus: faker.lorem.words(),
      reference: faker.lorem.words(),
      revenue: {},
      siteAddress: faker.lorem.words(),
      workItem: {
        automated: faker.datatype.boolean(),
        groupUid: faker.string.uuid(),
        groupName: faker.person.fullName(),
        id: faker.string.uuid(),
        month: faker.lorem.words(),
        previousStatement: null,
        siteId: faker.string.uuid(),
        siteName: faker.person.fullName(),
        statement: null,
        status: faker.helpers.arrayElement([
          'New',
          'Ready',
          'Generating',
          'Generated',
          'Sending',
          'Sent',
          'Cancelled',
        ]),
        userId: faker.string.uuid(),
        userName: faker.person.fullName(),
      },
    },
    stripeInvoiceId: faker.string.uuid(),
    stripeInvoiceStatus: faker.lorem.words(),
    stripeInvoiceNumber: faker.lorem.words(),
    hostedInvoiceUrl: faker.internet.url(),
  };
}

export function getGroupsControllerFindAllGroups200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    accountRef: faker.lorem.words(),
    addressLine1: faker.lorem.words(),
    addressLine2: faker.lorem.words(),
    businessEmail: faker.internet.email(),
    businessName: faker.person.fullName(),
    county: faker.lorem.words(),
    groupId: faker.string.uuid(),
    groupName: faker.person.fullName(),
    hasContractDocument: faker.datatype.boolean(),
    poNumber: faker.lorem.words(),
    postcode: faker.lorem.words(),
    sites: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      automated: faker.datatype.boolean(),
      emails: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.lorem.words()),
      groupId: faker.string.uuid(),
      id: faker.string.uuid(),
      siteId: faker.string.uuid(),
      siteName: faker.person.fullName(),
    })),
    stripeConnectedAccountId: faker.string.uuid(),
    stripeCustomerId: faker.string.uuid(),
    stripeSubscriptionId: faker.string.uuid(),
    stripeSubscriptionStatus: faker.lorem.words(),
    town: faker.lorem.words(),
    transfersEnabled: faker.datatype.boolean(),
    vatRegistered: faker.datatype.boolean(),
  }));
}

export function getGroupsControllerFindSiteAdminGroups200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    accountRef: faker.lorem.words(),
    addressLine1: faker.lorem.words(),
    addressLine2: faker.lorem.words(),
    businessEmail: faker.internet.email(),
    businessName: faker.person.fullName(),
    county: faker.lorem.words(),
    groupId: faker.string.uuid(),
    groupName: faker.person.fullName(),
    hasContractDocument: faker.datatype.boolean(),
    poNumber: faker.lorem.words(),
    postcode: faker.lorem.words(),
    sites: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      automated: faker.datatype.boolean(),
      emails: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.lorem.words()),
      groupId: faker.string.uuid(),
      id: faker.string.uuid(),
      siteId: faker.string.uuid(),
      siteName: faker.person.fullName(),
    })),
    stripeConnectedAccountId: faker.string.uuid(),
    stripeCustomerId: faker.string.uuid(),
    stripeSubscriptionId: faker.string.uuid(),
    stripeSubscriptionStatus: faker.lorem.words(),
    town: faker.lorem.words(),
    transfersEnabled: faker.datatype.boolean(),
    vatRegistered: faker.datatype.boolean(),
  }));
}

export function getGroupsControllerFindGroup200Response() {
  return {
    accountRef: faker.lorem.words(),
    addressLine1: faker.lorem.words(),
    addressLine2: faker.lorem.words(),
    businessEmail: faker.internet.email(),
    businessName: faker.person.fullName(),
    county: faker.lorem.words(),
    groupId: faker.string.uuid(),
    groupName: faker.person.fullName(),
    hasContractDocument: faker.datatype.boolean(),
    poNumber: faker.lorem.words(),
    postcode: faker.lorem.words(),
    sites: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      automated: faker.datatype.boolean(),
      emails: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.lorem.words()),
      groupId: faker.string.uuid(),
      id: faker.string.uuid(),
      siteId: faker.string.uuid(),
      siteName: faker.person.fullName(),
    })),
    stripeConnectedAccountId: faker.string.uuid(),
    stripeCustomerId: faker.string.uuid(),
    stripeSubscriptionId: faker.string.uuid(),
    stripeSubscriptionStatus: faker.lorem.words(),
    town: faker.lorem.words(),
    transfersEnabled: faker.datatype.boolean(),
    vatRegistered: faker.datatype.boolean(),
  };
}

export function getDocumentsControllerFindDocumentsByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    startDate: faker.lorem.words(),
    expiryDate: faker.lorem.words(),
    type: faker.helpers.arrayElement([
      'CONTRACT',
      'CONTRACT_SUMMARY',
      'FRAMEWORK_CONTRACT',
      'FRAMEWORK_ORDER',
    ]),
    uploadDate: faker.lorem.words(),
    url: faker.internet.url(),
  }));
}

export function getGroupsStatementsControllerFindStatementsByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: faker.string.uuid(),
    adjustedFees: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      fee: faker.number.int(),
      ppid: faker.string.uuid(),
    })),
    automaticPayout: faker.datatype.boolean(),
    emails: faker.internet.email(),
    energy: {},
    fees: {},
    groupUid: faker.string.uuid(),
    invoice: {},
    numberOfCharges: faker.number.int(),
    payoutStatus: faker.lorem.words(),
    reference: faker.lorem.words(),
    revenue: {},
    siteAddress: faker.lorem.words(),
    workItem: {
      automated: faker.datatype.boolean(),
      groupUid: faker.string.uuid(),
      groupName: faker.person.fullName(),
      id: faker.string.uuid(),
      month: faker.lorem.words(),
      previousStatement: null,
      siteId: faker.string.uuid(),
      siteName: faker.person.fullName(),
      statement: null,
      status: faker.helpers.arrayElement([
        'New',
        'Ready',
        'Generating',
        'Generated',
        'Sending',
        'Sent',
        'Cancelled',
      ]),
      userId: faker.string.uuid(),
      userName: faker.person.fullName(),
    },
  }));
}

export function getSubscriptionControllerFindSubscriptionChargersByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    ppid: faker.string.uuid(),
    socket: faker.helpers.arrayElement(['A', 'B']),
  }));
}

export function getChargersControllerFindChargersByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    ppid: faker.string.uuid(),
    socket: faker.helpers.arrayElement(['A', 'B']),
    name: faker.person.fullName(),
    siteAddress: faker.lorem.words(),
    hasSubscription: faker.datatype.boolean(),
  }));
}

export function getSitesControllerFindChargerConfigsWithPods200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    pod: {
      ageYears: faker.number.int(),
      confirmChargeEnabled: faker.datatype.boolean(),
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      coordinates: {
        latitude: faker.number.int(),
        longitude: faker.number.int(),
      },
      description: faker.lorem.words(),
      id: faker.number.int(),
      installDate: faker.lorem.words(),
      isEvZone: faker.datatype.boolean(),
      isPublic: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      model: faker.lorem.words(),
      mostRecentCharge: {
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      },
      name: faker.person.fullName(),
      ppid: faker.string.uuid(),
      recentCharges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      schedules: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        endDay: faker.number.int(),
        endTime: faker.lorem.words(),
        isActive: faker.datatype.boolean(),
        startDay: faker.number.int(),
        startTime: faker.lorem.words(),
      })),
      schemes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        accountRef: faker.lorem.words(),
        addressLine1: faker.lorem.words(),
        addressLine2: faker.lorem.words(),
        businessEmail: faker.internet.email(),
        businessName: faker.person.fullName(),
        county: faker.lorem.words(),
        groupId: faker.string.uuid(),
        groupName: faker.person.fullName(),
        hasContractDocument: faker.datatype.boolean(),
        poNumber: faker.lorem.words(),
        postcode: faker.lorem.words(),
        sites: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          automated: faker.datatype.boolean(),
          emails: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.lorem.words()),
          groupId: faker.string.uuid(),
          id: faker.string.uuid(),
          siteId: faker.string.uuid(),
          siteName: faker.person.fullName(),
        })),
        stripeConnectedAccountId: faker.string.uuid(),
        stripeCustomerId: faker.string.uuid(),
        stripeSubscriptionId: faker.string.uuid(),
        stripeSubscriptionStatus: faker.lorem.words(),
        town: faker.lorem.words(),
        transfersEnabled: faker.datatype.boolean(),
        vatRegistered: faker.datatype.boolean(),
      })),
      site: {
        automated: faker.datatype.boolean(),
        emails: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => faker.lorem.words()),
        groupId: faker.string.uuid(),
        id: faker.string.uuid(),
        siteId: faker.string.uuid(),
        siteName: faker.person.fullName(),
      },
      sockets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        door: faker.lorem.words(),
        firmwareVersion: faker.lorem.words(),
        isUpdateAvailable: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        serialNumber: faker.lorem.words(),
        status: faker.helpers.arrayElement([
          'Available',
          'Charging',
          'Offline',
          'Unavailable',
        ]),
      })),
      status: faker.lorem.words(),
      supportsConfirmCharge: faker.datatype.boolean(),
      supportsContactless: faker.datatype.boolean(),
      supportsEnergyTariff: faker.datatype.boolean(),
      supportsOcpp: faker.datatype.boolean(),
      supportsPerKwh: faker.datatype.boolean(),
      supportsRfid: faker.datatype.boolean(),
      supportsTariffs: faker.datatype.boolean(),
      tariff: {
        id: faker.number.int(),
        name: faker.person.fullName(),
      },
    },
    fee: faker.number.int(),
    ppid: faker.string.uuid(),
    siteId: faker.string.uuid(),
  }));
}

export function getStatementControllerFindAllStatements200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: faker.string.uuid(),
    adjustedFees: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      fee: faker.number.int(),
      ppid: faker.string.uuid(),
    })),
    automaticPayout: faker.datatype.boolean(),
    emails: faker.internet.email(),
    energy: {},
    fees: {},
    groupUid: faker.string.uuid(),
    invoice: {},
    numberOfCharges: faker.number.int(),
    payoutStatus: faker.lorem.words(),
    reference: faker.lorem.words(),
    revenue: {},
    siteAddress: faker.lorem.words(),
    workItem: {
      automated: faker.datatype.boolean(),
      groupUid: faker.string.uuid(),
      groupName: faker.person.fullName(),
      id: faker.string.uuid(),
      month: faker.lorem.words(),
      previousStatement: null,
      siteId: faker.string.uuid(),
      siteName: faker.person.fullName(),
      statement: null,
      status: faker.helpers.arrayElement([
        'New',
        'Ready',
        'Generating',
        'Generated',
        'Sending',
        'Sent',
        'Cancelled',
      ]),
      userId: faker.string.uuid(),
      userName: faker.person.fullName(),
    },
  }));
}

export function getStatementControllerCreateStatement201Response() {
  return {
    statementId: faker.string.uuid(),
  };
}

export function getStatementControllerFindStatement200Response() {
  return {
    id: faker.string.uuid(),
    adjustedFees: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      fee: faker.number.int(),
      ppid: faker.string.uuid(),
    })),
    automaticPayout: faker.datatype.boolean(),
    emails: faker.internet.email(),
    energy: {},
    fees: {},
    groupUid: faker.string.uuid(),
    invoice: {},
    numberOfCharges: faker.number.int(),
    payoutStatus: faker.lorem.words(),
    reference: faker.lorem.words(),
    revenue: {},
    siteAddress: faker.lorem.words(),
    workItem: {
      automated: faker.datatype.boolean(),
      groupUid: faker.string.uuid(),
      groupName: faker.person.fullName(),
      id: faker.string.uuid(),
      month: faker.lorem.words(),
      previousStatement: null,
      siteId: faker.string.uuid(),
      siteName: faker.person.fullName(),
      statement: null,
      status: faker.helpers.arrayElement([
        'New',
        'Ready',
        'Generating',
        'Generated',
        'Sending',
        'Sent',
        'Cancelled',
      ]),
      userId: faker.string.uuid(),
      userName: faker.person.fullName(),
    },
  };
}

export function getStripeControllerCreateStripeConnectedAccountLink201Response() {
  return faker.lorem.words();
}

export function getStripeControllerGetSubscription200Response() {
  return {};
}

export function getStripeControllerGetInvoicesBySubscriptionId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: faker.string.uuid(),
    amount: faker.number.int(),
    created: faker.lorem.words(),
    due: faker.lorem.words(),
    email: faker.internet.email(),
    hostedInvoiceUrl: faker.internet.url(),
    invoiceNumber: faker.lorem.words(),
    invoicePdfUrl: faker.internet.url(),
    status: faker.lorem.words(),
  }));
}

export function getStripeControllerGetSubscriptionInvoice200Response() {
  return {
    id: faker.string.uuid(),
    amount: faker.number.int(),
    created: faker.lorem.words(),
    due: faker.lorem.words(),
    email: faker.internet.email(),
    hostedInvoiceUrl: faker.internet.url(),
    invoiceNumber: faker.lorem.words(),
    invoicePdfUrl: faker.internet.url(),
    status: faker.lorem.words(),
  };
}

export function getWorkItemControllerFindAllWorkItems200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    automated: faker.datatype.boolean(),
    groupUid: faker.string.uuid(),
    groupName: faker.person.fullName(),
    id: faker.string.uuid(),
    month: faker.lorem.words(),
    previousStatement: {
      id: faker.string.uuid(),
      adjustedFees: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        fee: faker.number.int(),
        ppid: faker.string.uuid(),
      })),
      automaticPayout: faker.datatype.boolean(),
      emails: faker.internet.email(),
      energy: {},
      fees: {},
      groupUid: faker.string.uuid(),
      invoice: {},
      numberOfCharges: faker.number.int(),
      payoutStatus: faker.lorem.words(),
      reference: faker.lorem.words(),
      revenue: {},
      siteAddress: faker.lorem.words(),
      workItem: null,
    },
    siteId: faker.string.uuid(),
    siteName: faker.person.fullName(),
    statement: {
      id: faker.string.uuid(),
      adjustedFees: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        fee: faker.number.int(),
        ppid: faker.string.uuid(),
      })),
      automaticPayout: faker.datatype.boolean(),
      emails: faker.internet.email(),
      energy: {},
      fees: {},
      groupUid: faker.string.uuid(),
      invoice: {},
      numberOfCharges: faker.number.int(),
      payoutStatus: faker.lorem.words(),
      reference: faker.lorem.words(),
      revenue: {},
      siteAddress: faker.lorem.words(),
      workItem: null,
    },
    status: faker.helpers.arrayElement([
      'New',
      'Ready',
      'Generating',
      'Generated',
      'Sending',
      'Sent',
      'Cancelled',
    ]),
    userId: faker.string.uuid(),
    userName: faker.person.fullName(),
  }));
}

export function getWorkItemControllerCountWorkItems200Response() {
  return {
    workitems: {
      new: {
        manual: faker.number.int(),
        automated: faker.number.int(),
        total: faker.number.int(),
      },
      ready: {
        manual: faker.number.int(),
        automated: faker.number.int(),
        total: faker.number.int(),
      },
      generated: {
        manual: faker.number.int(),
        automated: faker.number.int(),
        total: faker.number.int(),
      },
      sent: {
        manual: faker.number.int(),
        automated: faker.number.int(),
        total: faker.number.int(),
      },
    },
  };
}

export function getWorkItemControllerFindWorkItem200Response() {
  return {
    automated: faker.datatype.boolean(),
    groupUid: faker.string.uuid(),
    groupName: faker.person.fullName(),
    id: faker.string.uuid(),
    month: faker.lorem.words(),
    previousStatement: {
      id: faker.string.uuid(),
      adjustedFees: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        fee: faker.number.int(),
        ppid: faker.string.uuid(),
      })),
      automaticPayout: faker.datatype.boolean(),
      emails: faker.internet.email(),
      energy: {},
      fees: {},
      groupUid: faker.string.uuid(),
      invoice: {},
      numberOfCharges: faker.number.int(),
      payoutStatus: faker.lorem.words(),
      reference: faker.lorem.words(),
      revenue: {},
      siteAddress: faker.lorem.words(),
      workItem: null,
    },
    siteId: faker.string.uuid(),
    siteName: faker.person.fullName(),
    statement: {
      id: faker.string.uuid(),
      adjustedFees: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        fee: faker.number.int(),
        ppid: faker.string.uuid(),
      })),
      automaticPayout: faker.datatype.boolean(),
      emails: faker.internet.email(),
      energy: {},
      fees: {},
      groupUid: faker.string.uuid(),
      invoice: {},
      numberOfCharges: faker.number.int(),
      payoutStatus: faker.lorem.words(),
      reference: faker.lorem.words(),
      revenue: {},
      siteAddress: faker.lorem.words(),
      workItem: null,
    },
    status: faker.helpers.arrayElement([
      'New',
      'Ready',
      'Generating',
      'Generated',
      'Sending',
      'Sent',
      'Cancelled',
    ]),
    userId: faker.string.uuid(),
    userName: faker.person.fullName(),
  };
}
