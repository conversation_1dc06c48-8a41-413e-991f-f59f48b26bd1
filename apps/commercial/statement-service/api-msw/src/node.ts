import { HttpResponse, http } from 'msw';
import {
  Statement,
  TEST_GROUP_WITH_SITE,
  TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
  TEST_GROUP_WITH_STRIPE_CUSTOMER,
  TEST_GROUP_WITH_STRIPE_SUBSCRIPTION,
  TEST_STATEMENTS,
  TEST_SUBSCRIPTION_CHARGER,
  TEST_SUBSCRIPTION_INVOICE,
  WorkItem,
  WorkItemStatus,
} from '@experience/commercial/statement-service/shared';
import { TEST_GROUP_WITH_TYPE } from '@experience/commercial/site-admin/typescript/domain-model';
import { TEST_SUBSCRIPTION } from '@experience/shared/nest/stripe';
import { createServer } from '@mswjs/http-middleware';
import { faker } from '@faker-js/faker';
import {
  getWorkItemControllerFindAllWorkItems200Response,
  handlers,
} from './handlers';
import { setIn } from 'immutable';
import { v4 as uuid } from 'uuid';
import dayjs from 'dayjs';
import pino, { Logger } from 'pino';

const logger: Logger = pino();
logger.info(`Faker running with seed: ${faker.seed()}`);

const statuses: WorkItemStatus[] = [
  WorkItemStatus.NEW,
  WorkItemStatus.READY,
  WorkItemStatus.GENERATED,
];
const workListResponse = getWorkItemControllerFindAllWorkItems200Response();

const generateStatement = (): Statement => ({
  id: faker.lorem.slug(1),
  adjustedFees: [...new Array(faker.number.int({ min: 1, max: 2 })).keys()].map(
    () => ({
      fee: faker.number.int({ min: undefined, max: undefined }),
      ppid: faker.lorem.slug(1),
    })
  ),
  automaticPayout: false,
  emails: faker.lorem.slug(1),
  energy: {
    claimedEnergyDelivered: faker.number.int({
      min: undefined,
      max: undefined,
    }),
    energyDelivered: faker.number.int({ min: undefined, max: undefined }),
    paidEnergyDelivered: faker.number.int({ min: undefined, max: undefined }),
  },
  fees: {
    gross: faker.number.int({ min: undefined, max: undefined }),
    net: faker.number.int({ min: undefined, max: undefined }),
    vat: faker.number.int({ min: undefined, max: undefined }),
  },
  groupUid: faker.lorem.slug(1),
  numberOfCharges: faker.number.int({ min: undefined, max: undefined }),
  reference: faker.lorem.slug(1),
  revenue: {
    gross: faker.number.int({ min: undefined, max: undefined }),
    net: faker.number.int({ min: undefined, max: undefined }),
    vat: faker.number.int({ min: undefined, max: undefined }),
  },
  siteAddress: faker.location.streetAddress(),
  workItem: undefined,
});

const generateWorkItems = (
  status: WorkItemStatus,
  automated: boolean,
  userId: string
): WorkItem => ({
  automated,
  id: faker.lorem.slug(1),
  groupUid: faker.lorem.slug(1),
  groupName: faker.person.fullName(),
  siteId: faker.lorem.slug(1),
  siteName: faker.person.fullName(),
  userId,
  userName: faker.person.fullName(),
  statement: generateStatement(),
  previousStatement: generateStatement(),
  status,
  month: faker.date.past().toString(),
});

statuses.forEach((status) =>
  // @ts-ignore
  workListResponse.push(generateWorkItems(status, false, uuid()))
);

workListResponse.push(
  // @ts-ignore
  generateWorkItems(WorkItemStatus.READY, false, null),
  // @ts-ignore
  generateWorkItems(WorkItemStatus.NEW, true, null)
);

//modified handlers need to appear earlier in the array than their unmodified counterparts
handlers.unshift(
  http.get('/work-items', () => HttpResponse.json(workListResponse))
);

handlers.unshift(
  http.get('/statements/4287245c-6a15-4891-afe7-7fcb7dd9fa7f', () =>
    HttpResponse.json({
      id: '4287245c-6a15-4891-afe7-7fcb7dd9fa7f',
      adjustedFees: [{ fee: 100, ppid: '123' }],
      emails: '<EMAIL>',
      energy: {
        claimedEnergyDelivered: 99,
        energyDelivered: 100,
        paidEnergyDelivered: 70,
      },
      fees: { gross: 100, net: 20, vat: 80 },
      numberOfCharges: 1,
      reference: 'Hello',
      revenue: { gross: 100, net: 20, vat: 80 },
      siteAddress: '123 Fake Street',
      workItem: {
        automated: false,
        groupUid: '123',
        groupName: 'Tesco',
        id: '7452',
        month: dayjs('01-01-2024').toDate(),
        previousStatement: null,
        siteId: '3837',
        siteName: 'Easter Road',
        statement: null,
        status: 'Generated',
        userId: '123',
        userName: 'Bill Bailey',
      },
    })
  )
);

handlers.unshift(
  http.get(
    '/statements/*/pdf',
    () =>
      new HttpResponse('Hello!', {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': 'attachment; filename=test.pdf',
        },
      })
  )
);
handlers.unshift(
  http.get(
    '/invoices/*/pdf',
    () =>
      new HttpResponse('Hello!', {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': 'attachment; filename=test.pdf',
        },
      })
  )
);

handlers.unshift(
  http.get('/groups', () =>
    HttpResponse.json([
      TEST_GROUP_WITH_SITE,
      {
        ...TEST_GROUP_WITH_STRIPE_CUSTOMER,
        groupName: 'Test Group Inc. with Stripe Customer',
        groupId: 'd065bf25-5079-49d0-b92f-876268a76b09',
      },
      {
        ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
        groupName: 'Test Group Inc. with Stripe Connected Account',
        groupId: '79dca656-8980-4898-bd56-99d09df4f9ad',
      },
      {
        ...TEST_GROUP_WITH_STRIPE_SUBSCRIPTION,
        groupName: 'Test Group Inc. with Stripe Subscription',
        groupId: '471c2849-27e8-403d-8c79-a192b02eacff',
      },
    ])
  )
);
handlers.unshift(
  http.get('/groups/*', () => HttpResponse.json(TEST_GROUP_WITH_SITE))
);
handlers.unshift(
  http.get('/groups/45c9ef22-b44b-4edd-bb66-392e923950af', () =>
    HttpResponse.json(TEST_GROUP_WITH_STRIPE_SUBSCRIPTION)
  )
);
handlers.unshift(
  http.get('/stripe/subscriptions/sub_1MowQVLkdIwHu7ixeRlqHVzs/invoices', () =>
    HttpResponse.json([TEST_SUBSCRIPTION_INVOICE])
  )
);
handlers.unshift(
  http.get('/groups/*/statements', () => HttpResponse.json(TEST_STATEMENTS))
);
handlers.unshift(
  http.get('/groups/site-admin', () =>
    HttpResponse.json([
      setIn(
        TEST_GROUP_WITH_TYPE,
        ['uid'],
        '036c0720-f908-45fc-ae7c-031a47c2e278'
      ),
      setIn(
        TEST_GROUP_WITH_TYPE,
        ['uid'],
        'd065bf25-5079-49d0-b92f-876268a76b09'
      ),
      setIn(
        TEST_GROUP_WITH_TYPE,
        ['uid'],
        '79dca656-8980-4898-bd56-99d09df4f9ad'
      ),
      setIn(
        TEST_GROUP_WITH_TYPE,
        ['uid'],
        '471c2849-27e8-403d-8c79-a192b02eacff'
      ),
      setIn(
        TEST_GROUP_WITH_TYPE,
        ['uid'],
        '20dca656-8980-4898-bd56-99d09df4f9ad'
      ),
    ])
  )
);
handlers.unshift(
  http.get('/groups/d065bf25-5079-49d0-b92f-876268a76b09', () =>
    HttpResponse.json({
      ...TEST_GROUP_WITH_STRIPE_CUSTOMER,
      groupName: 'Test Group Inc. with Stripe Customer',
      groupId: 'd065bf25-5079-49d0-b92f-876268a76b09',
    })
  ),
  http.get('/groups/79dca656-8980-4898-bd56-99d09df4f9ad', () =>
    HttpResponse.json({
      ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
      groupName: 'Test Group Inc. with Stripe Connected Account',
      groupId: '79dca656-8980-4898-bd56-99d09df4f9ad',
    })
  ),
  http.get('/groups/471c2849-27e8-403d-8c79-a192b02eacff', () =>
    HttpResponse.json({
      ...TEST_GROUP_WITH_STRIPE_SUBSCRIPTION,
      groupName: 'Test Group Inc. with Stripe Subscription',
      groupId: '471c2849-27e8-403d-8c79-a192b02eacff',
    })
  ),
  http.get('/stripe/subscriptions/:subscriptionId', async () =>
    HttpResponse.json(TEST_SUBSCRIPTION)
  ),
  http.get('/groups/:groupId/subscription/chargers', async () =>
    HttpResponse.json([TEST_SUBSCRIPTION_CHARGER])
  ),
  http.get('/groups/:groupId/chargers', async () =>
    HttpResponse.json([TEST_SUBSCRIPTION_CHARGER])
  )
);

createServer(...handlers).listen(5102);
