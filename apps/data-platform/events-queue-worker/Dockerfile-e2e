FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/golang:1.24.6-alpine AS builder

# Create app directory
WORKDIR /workspace

## Add wait script for verifying postgres is ready
ADD https://github.com/ufoscout/docker-compose-wait/releases/download/2.9.0/wait /wait
RUN chmod +x /wait

# Copy source files
COPY go.mod go.mod
COPY go.sum go.sum
RUN go mod download
COPY apps/data-platform apps/data-platform
COPY libs/data-platform/ libs/data-platform/
COPY libs/shared/go/ libs/shared/go/

# Build app
RUN --mount=type=cache,target=/root/.cache/go-build go build -tags timetzdata -o dist/apps/data-platform/events-queue-worker apps/data-platform/events-queue-worker/cmd/main.go

FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/alpine:latest@sha256:4bcff63911fcb4448bd4fdacec207030997caf25e9bea4045fa6c8c44de311d1
WORKDIR /app
COPY --from=builder /wait /wait
COPY --from=builder /workspace/dist/apps/data-platform/events-queue-worker ./
COPY --from=builder /workspace/libs/data-platform/golang-migrate/migration ./libs/data-platform/golang-migrate/migration
COPY --from=builder /workspace/libs/data-platform/golang-migrate/non-prod/migration ./libs/data-platform/golang-migrate/non-prod/migration
COPY --from=builder /workspace/apps/data-platform/events-queue-worker/cmd/config/tracing ./apps/data-platform/events-queue-worker/cmd/config/tracing

# Add RDS CA certs to trust store to be able to connect to RDS over TLS
ADD https://truststore.pki.rds.amazonaws.com/eu-west-1/eu-west-1-bundle.pem eu-west-1-bundle.pem
RUN cat eu-west-1-bundle.pem >> /etc/ssl/certs/ca-certificates.crt && rm eu-west-1-bundle.pem
CMD /wait; ./events-queue-worker -host local; touch /tmp/events-queue-worker
