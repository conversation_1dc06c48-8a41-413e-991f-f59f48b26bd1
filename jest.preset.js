process.env.TZ = 'UTC';
process.env.LANG = 'en-US';

const reflectMetadata = require('reflect-metadata');
const nxPreset = require('@nx/jest/preset').default;
const path = require('path');

module.exports = {
  ...nxPreset,
  ...reflectMetadata,
  clearMocks: true,
  setupFiles: [path.resolve(__dirname, './jest.setup.js')],
  setupFilesAfterEnv: [path.resolve(__dirname, './jest.setup.after.env.js')],
  transformIgnorePatterns: [
    '/node_modules/(?!(flagged|yet-another-react-lightbox|react-photo-album|use-intl|next-intl|hibp)/)',
  ],
  // uncomment this line to focus test failures when running locally:
  // reporters: [['default', { summaryThreshold: 0 }]],
};
