require('reflect-metadata');

const { TextEncoder, TextDecoder } = require('util');
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// https://github.com/nodejs/undici/issues/2512#issuecomment-1953390496
const { ReadableStream } = require('node:stream/web');
global.ReadableStream = ReadableStream;

const { BroadcastChannel } = require('worker_threads');
global.BroadcastChannel = BroadcastChannel;

// https://github.com/jsdom/jsdom/issues/3363
global.structuredClone = (val) => JSON.parse(JSON.stringify(val));

process.env.STRIPE_API_KEY = 'pk_test_123';

// Check if we are in JSDOM testing environment:
if (typeof window !== 'undefined') {
  // Mock matchMedia for react-hot-toast compatibility
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // Deprecated
      removeListener: jest.fn(), // Deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });

  // ResizeObserver isn't available in test environment
  Object.defineProperty(window, 'ResizeObserver', {
    writable: true,
    value: jest.fn().mockImplementation(() => ({
      observe: () => null,
      unobserve: () => null,
      disconnect: () => null,
    })),
  });
}
